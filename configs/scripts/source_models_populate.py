from pathlib import Path
import os
import json
import sys

def getDirectory(rootdir, shared_path, select_module=None):
    if select_module:
        path = Path(rootdir) / select_module
        if path.is_dir():
            print(f"Selected Path: {path}")
            makeJsonArray(path, shared_path)
        else:
            print(f"Error: {select_module} is not a valid directory inside {rootdir}")
    else:
        for path in Path(rootdir).iterdir():
            if path.is_dir():
                print(f"Path: {path}")
                makeJsonArray(path, shared_path)

def makeJsonArray(dirPath, shared_path):
    temp = []
    for file in os.listdir(dirPath):
        print(f"File name: {file}")
        temp.append(file.split('/')[-1].split('.json')[0].replace("__job_config", ""))
    os.makedirs(os.path.dirname(os.path.join(shared_path, str(dirPath).split('/')[-1] + ".json")), exist_ok=True)
    with open(os.path.join(shared_path, str(dirPath).split('/')[-1] + ".json"), "w+") as f:
        json.dump(temp, f)

if __name__ == '__main__':
    select_module = sys.argv[3] if len(sys.argv) > 3 else None
    getDirectory(sys.argv[1], sys.argv[2], select_module)
