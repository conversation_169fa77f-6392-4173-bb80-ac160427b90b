import json
import os
from distutils.util import strtobool
import urllib.parse
import requests
import imghdr


def get_env_variable(name, default=None):
    return os.environ.get(name, default) if (default is not None) else os.environ[name]


def get_image_paths(directory):
    image_paths = []
    for root, _, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            if imghdr.what(file_path) is not None:
                image_paths.append(file_path)
    return image_paths


def delete_all(url, headers):
    data = requests.get(url, headers=headers,
                        verify=bool(strtobool(
                            get_env_variable('API_BASE_URL_SSL_VERIFY', 'True')))).json()[
        'data']
    for each in data:
        print(each["id"])
        response = requests.delete(urllib.parse.urljoin(url, str(each['id'])), headers=headers,
                                   verify=bool(strtobool(
                                       get_env_variable('API_BASE_URL_SSL_VERIFY', 'True'))))
        print(response.status_code)


class DocumentDeploy:
    def __init__(self):
        self.api_base_url = os.environ.get("API_BASE_URL", "http://localhost:8000/sds_mgmnt/document-manager/api/v1/")
        self.base_path = "/opt/airflow/final_document"

    def deploy_document(self):
        with open(os.path.join(self.base_path, "document_deployment_config.json")) as deploy_config:
            data = json.load(deploy_config)
        print("Started deleting images")
        delete_all(urllib.parse.urljoin(self.api_base_url, "media/image/"), headers={})
        print("Deleted images")
        print("Started deleting readme files")
        delete_all(urllib.parse.urljoin(self.api_base_url, "readme/"), headers={})
        print("Deleted all readme files")
        final_image_list = []
        final_readme_list = []
        print("starting image deployment")
        for key in data.keys():
            image_list = get_image_paths(os.path.join(self.base_path, key))
            final_image_list.extend(image_list)
        for order_id, images in enumerate(final_image_list):
            response = requests.post(self.api_base_url + 'media/image/',
                                     files=dict(image=open(images, "rb"), order_id=int(order_id)),
                                     verify=bool(strtobool(
                                         get_env_variable('API_BASE_URL_SSL_VERIFY', 'True'))))
            print(response.status_code)
        print("completed image deployment")
        for key in data.keys():
            for val in data.get(key):
                file_name = os.path.join(self.base_path, key, val)
                final_readme_list.append(file_name)
        print("starting readme deployment")
        for order_id, file in enumerate(final_readme_list, start=1):
            url = urllib.parse.urljoin(self.api_base_url, "readme")
            payload = {'order_id': order_id}
            files = [
                ('content', (file.split("/")[-1], open(file, 'rb'),
                             'application/octet-stream'))
            ]
            response = requests.request("POST", url, data=payload, files=files, verify=bool(strtobool(
                get_env_variable('API_BASE_URL_SSL_VERIFY', 'True'))))
            print(response.status_code)
        print("completed readme deployment")


if __name__ == '__main__':
    conf = DocumentDeploy()
    conf.deploy_document()
