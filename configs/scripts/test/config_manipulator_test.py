import os
import unittest
import json
from pathlib import Path

from scripts.config_manipulator import ConfigMerger


class TestConfigMerger(unittest.TestCase):

    def setUp(self):
        self.base_path = os.path.join(Path(__file__).parents[0].absolute(), "resource")
        self.config_merger = ConfigMerger(self.base_path)
        self.config_merger.merge_configs()
        self.deployment_config_path = os.path.join(self.base_path, "deployment_config.json")
        self.config_merger.update_relationship_models(self.deployment_config_path)

    def test_merge_orchestration_variables(self):
        self.config_merger.merge_orchestration_variables()
        input_unified_orch_file_path = os.path.join(self.base_path, "orchestration_variables/sds_ei_analytics_job_variables.json")
        expected_unified_orch_content = {"sds_ei__host__active_directory__object_guid": {"app_name": "a2593fceaaf04d5d9506b77b3bc26f438b4f438bc3c21bd5bc25384fbb2b7038", "args": ["--spark-service", "spark", "--parsed-interval-start", "{{ ti.xcom_pull(task_ids='sds_ei_host_jobs.find_task_runs_sds_ei__host__active_directory__object_guid', key='ei_entity_start_epoch') }}", "--parsed-interval-end", "{{ str(pendulum.parse(dag_run.conf['data_interval_end']).timestamp()*1000).split('.')[0] }}", "--event-timestamp-end", "{{ get_end_date(data_interval_start, 'day','utc') }}", "--previous-end-epoch", "{{ get_end_date(prev_data_interval_start_success, 'day','utc') if prev_data_interval_start_success is not none else -1 }}", "--config-path", "<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__active_directory__object_guid", "--source-path", "<%SRDM_SCHEMA_NAME%>.microsoft__active_directory", "--inventory-path", "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid"], "class_name": "ai.prevalent.entityinventory.loader.Loader", "conf": {"spark.blacklist.decommissioning.timeout": "600s", "spark.dynamicAllocation.enabled": "false", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.sql.shuffle.partitions": "<%LOADER_SHUFFLE_PARTITIONS%>", "spark.driver.maxResultSize": "200M", "spark.executor.heartbeatInterval": "10000", "spark.ui.port": "4074", "spark.app.name": "sds_ei__host__active_directory__object_guid", "spark.sql.caseSensitive": "true"}, "driver_cores": "<%LOADER_DRIVER_CORES%>", "driver_memory": "<%LOADER_DRIVER_MEMORY%>", "executor_cores": "<%LOADER_EXECUTOR_CORES%>", "executor_memory": "<%LOADER_EXECUTOR_MEMORY%>", "executor_instances": "<%LOADER_EXECUTOR_INSTANCES%>", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.12.jar"}, "sds_ei__identity__active_directory__external_object_id": {"app_name": "d58df5be1bf58855fd4aa2b627d50b7719133adc1e7fbbae86b8e19eac046f25", "args": ["--spark-service", "spark", "--parsed-interval-start", "{{ ti.xcom_pull(task_ids='sds_ei_identity_jobs.find_task_runs_sds_ei__identity__active_directory__external_object_id', key='ei_entity_start_epoch') }}", "--parsed-interval-end", "{{ str(pendulum.parse(dag_run.conf['data_interval_end']).timestamp()*1000).split('.')[0] }}", "--event-timestamp-end", "{{ get_end_date(data_interval_start, 'day','utc') }}", "--previous-end-epoch", "{{ get_end_date(prev_data_interval_start_success, 'day','utc') if prev_data_interval_start_success is not none else -1 }}", "--config-path", "<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__identity__active_directory__external_object_id", "--source-path", "<%SRDM_SCHEMA_NAME%>.microsoft__active_directory", "--inventory-path", "<%EI_SCHEMA_NAME%>.sds_ei__identity__active_directory__external_object_id"], "class_name": "ai.prevalent.entityinventory.loader.Loader", "conf": {"spark.blacklist.decommissioning.timeout": "600s", "spark.dynamicAllocation.enabled": "false", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.sql.shuffle.partitions": "<%LOADER_SHUFFLE_PARTITIONS%>", "spark.driver.maxResultSize": "200M", "spark.executor.heartbeatInterval": "10000", "spark.ui.port": "4074", "spark.app.name": "identity-active-directory-external-object-id", "spark.sql.caseSensitive": "true"}, "driver_cores": "<%LOADER_DRIVER_CORES%>", "driver_memory": "<%LOADER_DRIVER_MEMORY%>", "executor_cores": "<%LOADER_EXECUTOR_CORES%>", "executor_memory": "<%LOADER_EXECUTOR_MEMORY%>", "executor_instances": "<%LOADER_EXECUTOR_INSTANCES%>", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.12.jar"}}
        self.assertTrue(os.path.isfile(input_unified_orch_file_path))

        with open(input_unified_orch_file_path) as file:
            actual_unified_orch_content = json.load(file)

        self.assertEqual(actual_unified_orch_content, expected_unified_orch_content)
        os.remove(input_unified_orch_file_path)

    def test_merge_configs(self):
        input_loader_file_path = os.path.join(self.base_path, "spark_job_configs/source_models/inventory_models/sds_ei__identity__active_directory__external_object_id__job_config.json")
        expected_loader_content = {
            "primaryKey": "external_object_id",
            "origin": "'MS Active Directory'",
            "commonProperties": [
                {
                    "colName": "display_label",
                    "colExpr": "coalesce(account_name,ad_sam_account_name, primary_key)"
                },
                {
                    "colName": "inactivity_period",
                    "colExpr": "180"
                },
                {
                    "colName": "description",
                    "colExpr": "cast(null as string)"
                },
                {
                    "colName": "location",
                    "colExpr": "cast(null as string)"
                }
            ],
            "entitySpecificProperties": [
                {
                    "colName": "identity_provider",
                    "colExpr": "'AAD'"
                },
                {
                    "colName": "account_name",
                    "colExpr": "LOWER(regexp_replace(primary_key,'^User[_]',''))"
                },
                {
                    "colName": "mdm_last_sync_date",
                    "colExpr": "cast(null as bigint)"
                },
                {
                    "colName": "mdm_enrolled_date",
                    "colExpr": "cast(null as bigint)"
                }
            ],
            "sourceSpecificProperties": [
                {
                    "colName": "ad_distinguished_name",
                    "colExpr": "distinguished_name"
                },
                {
                    "colName": "ad_created_date",
                    "colExpr": "when_created_epoch"
                },
                {
                    "colName": "ad_last_sync_date",
                    "colExpr": "last_logon_synced_epoch"
                },
                {
                    "colName": "ad_last_password_change_date",
                    "colExpr": "pwd_last_set_epoch"
                }
            ],
            "entityConfig": {
                "entityClass": "Identity",
                "fieldSpec": {
                    "persistNonNullValue": True
                },
                "lastUpdateFields": [
                    "description",
                    "location",
                    "mdm_last_sync_date"
                ]
            }
        }
        self.assertTrue(os.path.isfile(input_loader_file_path))

        with open(input_loader_file_path) as file:
            actual_loader_content = json.load(file)

        self.assertEqual(actual_loader_content, expected_loader_content)

        input_intrasource_file_path = os.path.join(self.base_path, "spark_job_configs/source_models/intrasource_disambiguated_models/sds_ei__identity__active_directory__job_config.json")
        expected_intrasource_content = {

            "inventoryModelInput": [
                {
                    "path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__active_directory__external_object_id",
                    "name": "sds_ei__identity__active_directory__external_object_id"
                }
            ],
            "disambiguation": {
                "candidateKeys": [
                    "primary_key"
                ],
                "confidenceMatrix": [
                    "sds_ei__identity__active_directory__external_object_id"
                ],
                "excludeValues": [
                    "Unknown",
                    "Other",
                    "-"
                ],
                "strategy": {
                    "rollingUpFields": [],
                    "aggregation": [
                        {
                            "field": "ad_last_password_change_date",
                            "function": "max"
                        },
                        {
                            "field": "ad_last_sync_date",
                            "function": "max"
                        },
                        {
                            "field": "ad_created_date",
                            "function": "min"
                        }
                    ],
                    "valueConfidence": [
                        {
                            "field": "activity_status",
                            "confidenceMatrix": [
                                "Active",
                                "Inactive"
                            ]
                        }
                    ]
                }
            },
            "derivedProperties": [
                {
                    "colName": "origin",
                    "colExpr": "'MS Active Directory'"
                },
                {
                    "colName": "primary_key",
                    "colExpr": "primary_key__resolved"
                }
            ],
            "output": {
                "disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__identity__active_directory",
                "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"
            },
            "entityConfig": {
                "fieldSpec": {
                    "persistNonNullValue": True
                },
                "entityClass": "Identity",
                "lastUpdateFields": [
                    "description",
                    "location",
                    "mdm_last_sync_date"
                ]
            },
            "commonProperties": []
        }

        self.assertTrue(os.path.isfile(input_intrasource_file_path))

        with open(input_intrasource_file_path) as file:
            actual_intrasource_content = json.load(file)

        self.assertEqual(actual_intrasource_content, expected_intrasource_content)


    def test_update_relationship_models(self):
        input_relationship_file_path = os.path.join(self.base_path, "spark_job_configs/source_models/relationship_models/sds_ei__rel__person_has_identity__job_config.json")
        expected_relationship_content = {
            "name": "PERSON_HAS_IDENTITY",
            "inverseRelationshipName": "IDENTITY_ASSOCIATED_WITH_PERSON",
            "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver",
            "interSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_inter_source_resolver",
            "inputSourceInfo": [
                {
                    "sdmPath": "<%SDM_SCHEMA_NAME%>.microsoft__active_directory",
                    "origin": "MS Active Directory",
                    "sourceLoaderConfPath": [
                        "<%ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__person__active_directory__object_guid"
                    ],
                    "targetLoaderConfPath": [
                        "<%ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__identity__active_directory__external_object_id"
                    ]
                }
            ],
            "variablesBasedRelationBuilderStrategySpec": {
                "blockVariables": [
                    "source_p_id",
                    "target_p_id"
                ]
            }
        }
        self.assertTrue(os.path.isfile(input_relationship_file_path))

        with open(input_relationship_file_path) as file:
            actual_relationship_content = json.load(file)

        self.assertEqual(actual_relationship_content, expected_relationship_content)

    def test_get_abs_path(self):
        expected_path = os.path.join(self.base_path, "abc")
        actual_path = self.config_merger.get_abs_path("abc")
        self.assertEqual(expected_path, actual_path)

    def test_update_entity_config(self):
        entity_config = {}
        entity_class = "TestEntity"
        default_spec = {"key": "value"}
        last_update_fields = ["lastField"]

        self.config_merger.update_entity_config(entity_config, entity_class, default_spec, last_update_fields)

        expected_result = {
            "entityConfig": {
                "fieldSpec": {"key": "value"},
                "entityClass": "TestEntity",
                "lastUpdateFields": ["lastField"]
            }
        }

        self.assertEqual(entity_config, expected_result)

    def test_update_common_properties(self):
        inventory_data = {}
        common_properties = [
            {"colName": "prop1", "colExpr": "expr1"},
            {"colName": "prop2", "colExpr": "expr2"}
        ]

        self.config_merger.update_common_properties(inventory_data, common_properties)

        expected_result = {
            "commonProperties": [
                {"colName": "prop1", "colExpr": "expr1"},
                {"colName": "prop2", "colExpr": "expr2"}
            ]
        }

        self.assertEqual(inventory_data, expected_result)

    def test_update_entity_specific_properties(self):
        inventory_data = {
            "entitySpecificProperties": [
            ]
        }
        entity_specific_props = [
            {"colName": "entityProp1", "colExpr": "expr1"},
            {"colName": "entityProp2", "colExpr": "expr2"}
        ]

        self.config_merger.update_entity_specific_properties(inventory_data, entity_specific_props)

        expected_result = {
            "entitySpecificProperties": [
                {"colName": "entityProp1", "colExpr": "expr1"},
                {"colName": "entityProp2", "colExpr": "expr2"}
            ]
        }

        self.assertEqual(inventory_data, expected_result)

    def test_update_field_specs(self):
        inventory_data = {
            "entitySpecificProperties": [
                {"colName": "entityProp1", "colExpr": "expr1"},
                {"colName": "entityProp2", "colExpr": "expr2"},
            ]
        }
        field_level_spec = [
            {"colName": "entityProp1", "fieldsSpec": {"persistNonNullValue": True}}
        ]

        self.config_merger.update_field_specs(inventory_data, field_level_spec)

        expected_result = {
            "entitySpecificProperties": [
                {"colName": "entityProp1", "colExpr": "expr1", "fieldsSpec": {"persistNonNullValue": True}},
                {"colName": "entityProp2", "colExpr": "expr2"},
            ]
        }

        self.assertEqual(inventory_data, expected_result)


if __name__ == '__main__':
    unittest.main()
