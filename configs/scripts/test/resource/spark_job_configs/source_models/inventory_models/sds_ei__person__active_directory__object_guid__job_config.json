{"primaryKey": "object_guid", "filterBy": "sam_account_type='NORMAL_USER_ACCOUNT'", "origin": "'MS Active Directory'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,ad_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(ad_last_sync_date,login_last_date,ad_last_password_change_date,ad_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department"}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(CONCAT_WS(' ',first_name,middle_name,last_name))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_name", "colExpr": "INITCAP(given_name)"}, {"colName": "last_name", "colExpr": "INITCAP(sn)"}, {"colName": "middle_name", "colExpr": "initials"}, {"colName": "email_id", "colExpr": "LOWER(email)"}, {"colName": "manager", "colExpr": "INITCAP(manager_of_employee)"}, {"colName": "employee_id"}, {"colName": "company"}, {"colName": "job_title", "colExpr": "title"}, {"colName": "login_last_date", "colExpr": "last_logon_epoch"}], "sourceSpecificProperties": [{"colName": "aad_user_id", "colExpr": "regexp_replace(external_object_id,'^User[_]','')"}, {"colName": "ad_created_date", "colExpr": "when_created_epoch"}, {"colName": "ad_last_sync_date", "colExpr": "last_logon_synced_epoch"}, {"colName": "ad_last_password_change_date", "colExpr": "pwd_last_set_epoch"}]}