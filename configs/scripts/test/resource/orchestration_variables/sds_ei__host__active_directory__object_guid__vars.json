{"sds_ei__host__active_directory__object_guid": {"app_name": "sds_ei__host__active_directory__object_guid", "args": ["--spark-service", "spark", "--parsed-interval-start", "{{ ti.xcom_pull(task_ids='sds_ei_host_jobs.find_task_runs_sds_ei__host__active_directory__object_guid', key='ei_entity_start_epoch') }}", "--parsed-interval-end", "{{ str(pendulum.parse(dag_run.conf['data_interval_end']).timestamp()*1000).split('.')[0] }}", "--event-timestamp-end", "{{ get_end_date(data_interval_start, 'day','utc') }}", "--previous-end-epoch", "{{ get_end_date(prev_data_interval_start_success, 'day','utc') if prev_data_interval_start_success is not none else -1 }}", "--config-path", "<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__active_directory__object_guid", "--source-path", "<%SRDM_SCHEMA_NAME%>.microsoft__active_directory", "--inventory-path", "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid"], "class_name": "ai.prevalent.entityinventory.loader.Loader", "conf": {"spark.blacklist.decommissioning.timeout": "600s", "spark.dynamicAllocation.enabled": "false", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.sql.shuffle.partitions": "<%LOADER_SHUFFLE_PARTITIONS%>", "spark.driver.maxResultSize": "200M", "spark.executor.heartbeatInterval": "10000", "spark.ui.port": "4074", "spark.app.name": "sds_ei__host__active_directory__object_guid", "spark.sql.caseSensitive": "true"}, "driver_cores": "<%LOADER_DRIVER_CORES%>", "driver_memory": "<%LOADER_DRIVER_MEMORY%>", "executor_cores": "<%LOADER_EXECUTOR_CORES%>", "executor_memory": "<%LOADER_EXECUTOR_MEMORY%>", "executor_instances": "<%LOADER_EXECUTOR_INSTANCES%>", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.12.jar"}}