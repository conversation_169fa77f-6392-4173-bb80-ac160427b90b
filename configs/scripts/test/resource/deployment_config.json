{"spark_job_configs": {"source_models": {"global_entity_config": ["identity_entity_config"], "intrasource_disambiguated_models": ["sds_ei__identity__active_directory"], "inventory_models": ["sds_ei__identity__active_directory__external_object_id", "sds_ei__person__active_directory__object_guid"], "relationship_models": ["sds_ei__rel__person_has_identity"], "intersource_disambiguated_models": ["sds_ei__identity"]}}}