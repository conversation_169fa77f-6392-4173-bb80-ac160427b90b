{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__active_directory", "name": "sds_ei__identity__active_directory"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__globalprotect_vpn__user_id", "name": "sds_ei__identity__globalprotect_vpn__user_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_defender_device_list__aad_device_id", "name": "sds_ei__identity__ms_defender_device_list__aad_device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__saviynt_iga", "name": "sds_ei__identity__saviynt_iga"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__snow_itsm__employee_email", "name": "sds_ei__identity__snow_itsm__employee_email"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__successfactors_hr", "name": "sds_ei__identity__successfactors_hr"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__winevents", "name": "sds_ei__identity__winevents"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_intune", "name": "sds_ei__identity__ms_intune"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_azure_ad", "name": "sds_ei__identity__ms_azure_ad"}], "disambiguation": {"candidateKeys": ["account_name", "ad_sam_account_name"], "confidenceMatrix": ["sds_ei__identity__active_directory", "sds_ei__identity__successfactors_hr", "sds_ei__identity__saviynt_iga", "sds_ei__identity__winevents", "sds_ei__identity__ms_azure_ad", "sds_ei__identity__ms_defender_device_list__aad_device_id", "sds_ei__identity__ms_intune", "sds_ei__identity__snow_itsm__employee_email", "sds_ei__identity__globalprotect_vpn__user_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": ["identity_provider", "identity_type", "origin"], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "login_last_date", "function": "max"}, {"field": "ad_last_sync_date", "function": "max"}, {"field": "ad_created_date", "function": "max"}, {"field": "ad_last_password_change_date", "function": "max"}, {"field": "mdm_last_sync_date", "function": "max"}, {"field": "sf_created_date", "function": "min"}, {"field": "aad_created_date", "function": "min"}, {"field": "account_expires_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__identity", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_inter_source_resolver"}}