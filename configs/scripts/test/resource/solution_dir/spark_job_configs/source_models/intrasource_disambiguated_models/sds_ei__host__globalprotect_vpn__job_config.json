{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__globalprotect_vpn__client_hostname", "name": "client_hostname"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__globalprotect_vpn__device_hostname", "name": "device_hostname"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["client_hostname", "device_hostname"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "login_last_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'GlobalProtect'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__globalprotect_vpn", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}}