{"entityClass": "Vulnerability", "commonProperties": [{"colName": "a", "colExpr": "b", "fieldsSpec": {"isInventoryDerived": true, "postDisambiguationUpdate": true}}, {"colName": "ca", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "normalized_severity", "colExpr": "coalesce(v31_severity, v30_severity, v2_severity)", "fieldsSpec": {"isInventoryDerived": true}}], "lastUpdateFields": ["type", "first_seen_date"]}