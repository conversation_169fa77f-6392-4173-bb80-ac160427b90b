#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to remove all occurrences of "enable_hiding" attribute from data dictionaries.

This script:
1. Scans all JSON files in the sds_data_dictionary directory
2. Recursively removes "enable_hiding" attributes from the JSON structure
3. Creates backups of original files before modification
4. Provides detailed logging of changes made
5. Handles errors gracefully

Usage:
    python enable_hiding_removal.py [--dry-run] [--no-backup]

    --dry-run: Show what would be changed without making actual changes
    --no-backup: Skip creating backup files (not recommended)
"""

import json
import os
import shutil
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, List, Tuple
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enable_hiding_removal.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnableHidingRemover:
    """Class to handle removal of enable_hiding attributes from data dictionaries."""

    def __init__(self, data_dict_path: str, dry_run: bool = False):
        self.data_dict_path = Path(data_dict_path)
        self.dry_run = dry_run
        self.changes_made = []
        self.errors = []

        # Validate path exists
        if not self.data_dict_path.exists():
            raise FileNotFoundError(f"Data dictionary path does not exist: {data_dict_path}")

        logger.info(f"Initialized EnableHidingRemover for path: {self.data_dict_path}")
        logger.info(f"Dry run mode: {self.dry_run}")

    def remove_enable_hiding_recursive(self, obj: Any, path: str = "") -> Tuple[Any, int]:
        """
        Recursively remove 'enable_hiding' attributes from a JSON object.

        Args:
            obj: The JSON object to process
            path: Current path in the JSON structure (for logging)

        Returns:
            Tuple of (modified_object, count_of_removals)
        """
        removals_count = 0

        if isinstance(obj, dict):
            # Create a new dictionary without 'enable_hiding'
            new_dict = {}
            for key, value in obj.items():
                if key == "enable_hiding":
                    removals_count += 1
                    current_path = f"{path}.{key}" if path else key
                    logger.debug(f"Removing enable_hiding at path: {current_path}")
                else:
                    # Recursively process the value
                    new_value, sub_removals = self.remove_enable_hiding_recursive(
                        value, f"{path}.{key}" if path else key
                    )
                    new_dict[key] = new_value
                    removals_count += sub_removals
            return new_dict, removals_count

        elif isinstance(obj, list):
            # Process each item in the list
            new_list = []
            for i, item in enumerate(obj):
                new_item, sub_removals = self.remove_enable_hiding_recursive(
                    item, f"{path}[{i}]"
                )
                new_list.append(new_item)
                removals_count += sub_removals
            return new_list, removals_count

        else:
            # For primitive types, return as-is
            return obj, removals_count

    def create_backup_file(self, file_path: Path) -> Path:
        """
        Create a backup of the original file.

        Args:
            file_path: Path to the file to backup

        Returns:
            Path to the backup file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = file_path.with_suffix(f".backup_{timestamp}{file_path.suffix}")

        if not self.dry_run:
            shutil.copy2(file_path, backup_path)
            logger.info(f"Created backup: {backup_path}")
        else:
            logger.info(f"[DRY RUN] Would create backup: {backup_path}")

        return backup_path

    def process_json_file(self, file_path: Path) -> Dict[str, Any]:
        """
        Process a single JSON file to remove enable_hiding attributes.

        Args:
            file_path: Path to the JSON file

        Returns:
            Dictionary with processing results
        """
        result = {
            'file': str(file_path),
            'processed': False,
            'removals': 0,
            'error': None,
            'backup_created': False
        }

        try:
            logger.info(f"Processing file: {file_path}")

            # Read the original file
            with open(file_path, 'r', encoding='utf-8') as f:
                original_data = json.load(f)

            # Remove enable_hiding attributes
            modified_data, removals_count = self.remove_enable_hiding_recursive(original_data)

            result['removals'] = removals_count

            if removals_count > 0:
                logger.info(f"Found {removals_count} enable_hiding attributes to remove in {file_path.name}")

                if not self.dry_run:
                    # Create backup if requested
                    if self.create_backup:
                        self.create_backup_file(file_path)
                        result['backup_created'] = True

                    # Write the modified data back to the file
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(modified_data, f, indent=4, ensure_ascii=False)

                    logger.info(f"Successfully removed {removals_count} enable_hiding attributes from {file_path.name}")
                else:
                    logger.info(f"[DRY RUN] Would remove {removals_count} enable_hiding attributes from {file_path.name}")

                result['processed'] = True
            else:
                logger.info(f"No enable_hiding attributes found in {file_path.name}")
                result['processed'] = True

        except json.JSONDecodeError as e:
            error_msg = f"JSON decode error in {file_path}: {e}"
            logger.error(error_msg)
            result['error'] = error_msg
            self.errors.append(error_msg)

        except Exception as e:
            error_msg = f"Error processing {file_path}: {e}"
            logger.error(error_msg)
            result['error'] = error_msg
            self.errors.append(error_msg)

        return result

    def get_json_files(self) -> List[Path]:
        """
        Get all JSON files in the data dictionary directory.

        Returns:
            List of Path objects for JSON files
        """
        json_files = list(self.data_dict_path.glob("*.json"))
        logger.info(f"Found {len(json_files)} JSON files to process")
        return json_files

    def process_all_files(self) -> Dict[str, Any]:
        """
        Process all JSON files in the data dictionary directory.

        Returns:
            Summary of processing results
        """
        json_files = self.get_json_files()

        if not json_files:
            logger.warning("No JSON files found to process")
            return {
                'total_files': 0,
                'processed_files': 0,
                'total_removals': 0,
                'errors': [],
                'file_results': []
            }

        file_results = []
        total_removals = 0
        processed_files = 0

        for json_file in json_files:
            result = self.process_json_file(json_file)
            file_results.append(result)

            if result['processed'] and not result['error']:
                processed_files += 1
                total_removals += result['removals']

        summary = {
            'total_files': len(json_files),
            'processed_files': processed_files,
            'total_removals': total_removals,
            'errors': self.errors,
            'file_results': file_results
        }

        return summary

    def print_summary(self, summary: Dict[str, Any]):
        """
        Print a summary of the processing results.

        Args:
            summary: Summary dictionary from process_all_files
        """
        print("\n" + "="*60)
        print("ENABLE_HIDING REMOVAL SUMMARY")
        print("="*60)

        if self.dry_run:
            print("[DRY RUN MODE - No actual changes made]")
            print()

        print(f"Total files found: {summary['total_files']}")
        print(f"Files processed successfully: {summary['processed_files']}")
        print(f"Total enable_hiding attributes removed: {summary['total_removals']}")
        print(f"Errors encountered: {len(summary['errors'])}")

        if summary['errors']:
            print("\nErrors:")
            for error in summary['errors']:
                print(f"  - {error}")

        print("\nFile-by-file results:")
        for result in summary['file_results']:
            status = "✓" if result['processed'] and not result['error'] else "✗"
            file_name = Path(result['file']).name
            removals = result['removals']

            if result['error']:
                print(f"  {status} {file_name}: ERROR - {result['error']}")
            elif removals > 0:
                backup_info = " (backup created)" if result['backup_created'] else ""
                print(f"  {status} {file_name}: {removals} removals{backup_info}")
            else:
                print(f"  {status} {file_name}: No enable_hiding attributes found")

        print("\n" + "="*60)

def main():
    """Main function to run the enable_hiding removal script."""
    parser = argparse.ArgumentParser(
        description="Remove enable_hiding attributes from data dictionary JSON files"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be changed without making actual changes"
    )
    parser.add_argument(
        "--no-backup",
        action="store_true",
        help="Skip creating backup files (not recommended)"
    )
    parser.add_argument(
        "--path",
        type=str,
        default="configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_data_dictionary",
        help="Path to the data dictionary directory"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Initialize the remover
        remover = EnableHidingRemover(
            data_dict_path=args.path,
            create_backup=not args.no_backup,
            dry_run=args.dry_run
        )

        # Process all files
        summary = remover.process_all_files()

        # Print summary
        remover.print_summary(summary)

        # Exit with appropriate code
        if summary['errors']:
            logger.error(f"Script completed with {len(summary['errors'])} errors")
            exit(1)
        else:
            logger.info("Script completed successfully")
            exit(0)

    except Exception as e:
        logger.error(f"Fatal error: {e}")
        exit(1)

if __name__ == "__main__":
    main()