#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze ui_visibility inconsistencies across data dictionary files.

This script:
1. Scans all JSON files in the sds_data_dictionary directory
2. Identifies common fields that appear across multiple dictionaries
3. Finds cases where the same field has different ui_visibility values
4. Provides detailed reporting of inconsistencies

Usage:
    python ui_visibility_analyzer.py [--output-csv] [--verbose]
"""

import json
import os
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, List, Set, Tuple
from collections import defaultdict
import csv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ui_visibility_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UIVisibilityAnalyzer:
    """Class to analyze ui_visibility inconsistencies across data dictionaries."""
    
    def __init__(self, data_dict_path: str):
        self.data_dict_path = Path(data_dict_path)
        self.field_visibility_map = defaultdict(lambda: defaultdict(set))  # field -> visibility_value -> set of entities
        self.entity_fields = defaultdict(set)  # entity -> set of fields
        self.all_fields = set()
        self.inconsistencies = []
        
        # Validate path exists
        if not self.data_dict_path.exists():
            raise FileNotFoundError(f"Data dictionary path does not exist: {data_dict_path}")
        
        logger.info(f"Initialized UIVisibilityAnalyzer for path: {self.data_dict_path}")
    
    def extract_entity_name(self, filename: str) -> str:
        """Extract entity name from filename."""
        # Remove __data_dictionary.json suffix
        if filename.endswith('__data_dictionary.json'):
            return filename[:-len('__data_dictionary.json')]
        return filename.replace('.json', '')
    
    def normalize_visibility_value(self, value: Any) -> str:
        """Normalize ui_visibility values for comparison."""
        if value is None:
            return "None"
        elif value is True:
            return "true"
        elif value is False:
            return "false"
        elif isinstance(value, str):
            if value.lower() == "true":
                return "true"
            elif value.lower() == "false":
                return "false"
            elif value.lower() == "hidden":
                return "hidden"
            else:
                return str(value)
        else:
            return str(value)
    
    def analyze_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyze a single data dictionary file."""
        result = {
            'entity': None,
            'fields_processed': 0,
            'error': None
        }
        
        try:
            entity_name = self.extract_entity_name(file_path.name)
            result['entity'] = entity_name
            
            logger.info(f"Analyzing {entity_name} from {file_path.name}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            attributes = data.get('attributes', {})
            
            for field_name, field_data in attributes.items():
                if isinstance(field_data, dict):
                    self.all_fields.add(field_name)
                    self.entity_fields[entity_name].add(field_name)
                    
                    # Get ui_visibility value
                    ui_visibility = field_data.get('ui_visibility')
                    normalized_visibility = self.normalize_visibility_value(ui_visibility)
                    
                    # Store the mapping
                    self.field_visibility_map[field_name][normalized_visibility].add(entity_name)
                    
                    result['fields_processed'] += 1
            
            logger.debug(f"Processed {result['fields_processed']} fields for {entity_name}")
            
        except json.JSONDecodeError as e:
            error_msg = f"JSON decode error in {file_path}: {e}"
            logger.error(error_msg)
            result['error'] = error_msg
        
        except Exception as e:
            error_msg = f"Error processing {file_path}: {e}"
            logger.error(error_msg)
            result['error'] = error_msg
        
        return result
    
    def find_inconsistencies(self):
        """Find fields with inconsistent ui_visibility values."""
        logger.info("Analyzing ui_visibility inconsistencies...")
        
        for field_name, visibility_map in self.field_visibility_map.items():
            # Only consider fields that appear in multiple entities
            total_entities = sum(len(entities) for entities in visibility_map.values())
            if total_entities > 1:
                # Check if there are multiple different visibility values
                unique_values = set(visibility_map.keys())
                if len(unique_values) > 1:
                    # This field has inconsistent ui_visibility values
                    inconsistency = {
                        'field_name': field_name,
                        'total_entities': total_entities,
                        'visibility_variations': {}
                    }
                    
                    for visibility_value, entities in visibility_map.items():
                        inconsistency['visibility_variations'][visibility_value] = sorted(list(entities))
                    
                    self.inconsistencies.append(inconsistency)
        
        # Sort inconsistencies by field name
        self.inconsistencies.sort(key=lambda x: x['field_name'])
        
        logger.info(f"Found {len(self.inconsistencies)} fields with ui_visibility inconsistencies")
    
    def get_common_fields_stats(self) -> Dict[str, Any]:
        """Get statistics about common fields."""
        common_fields = {}
        
        for field_name in self.all_fields:
            entities_with_field = []
            for entity, fields in self.entity_fields.items():
                if field_name in fields:
                    entities_with_field.append(entity)
            
            if len(entities_with_field) > 1:  # Field appears in multiple entities
                common_fields[field_name] = {
                    'entity_count': len(entities_with_field),
                    'entities': sorted(entities_with_field)
                }
        
        return common_fields
    
    def analyze_all_files(self) -> Dict[str, Any]:
        """Analyze all data dictionary files."""
        json_files = list(self.data_dict_path.glob("*__data_dictionary.json"))
        
        if not json_files:
            logger.warning("No data dictionary files found")
            return {
                'total_files': 0,
                'processed_files': 0,
                'total_fields': 0,
                'common_fields': 0,
                'inconsistent_fields': 0,
                'errors': []
            }
        
        logger.info(f"Found {len(json_files)} data dictionary files to analyze")
        
        file_results = []
        errors = []
        
        for json_file in json_files:
            result = self.analyze_file(json_file)
            file_results.append(result)
            
            if result['error']:
                errors.append(result['error'])
        
        # Find inconsistencies
        self.find_inconsistencies()
        
        # Get common fields statistics
        common_fields = self.get_common_fields_stats()
        
        summary = {
            'total_files': len(json_files),
            'processed_files': len([r for r in file_results if not r['error']]),
            'total_fields': len(self.all_fields),
            'common_fields': len(common_fields),
            'inconsistent_fields': len(self.inconsistencies),
            'errors': errors,
            'file_results': file_results,
            'common_fields_details': common_fields
        }
        
        return summary
    
    def print_summary(self, summary: Dict[str, Any]):
        """Print a summary of the analysis results."""
        print("\n" + "="*80)
        print("UI_VISIBILITY INCONSISTENCY ANALYSIS SUMMARY")
        print("="*80)
        
        print(f"Total files analyzed: {summary['total_files']}")
        print(f"Files processed successfully: {summary['processed_files']}")
        print(f"Total unique fields found: {summary['total_fields']}")
        print(f"Fields appearing in multiple entities: {summary['common_fields']}")
        print(f"Fields with ui_visibility inconsistencies: {summary['inconsistent_fields']}")
        print(f"Errors encountered: {len(summary['errors'])}")
        
        if summary['errors']:
            print("\nErrors:")
            for error in summary['errors']:
                print(f"  - {error}")
        
        if self.inconsistencies:
            print(f"\n{'='*80}")
            print("FIELDS WITH UI_VISIBILITY INCONSISTENCIES")
            print("="*80)
            
            for inconsistency in self.inconsistencies:
                field_name = inconsistency['field_name']
                total_entities = inconsistency['total_entities']
                variations = inconsistency['visibility_variations']
                
                print(f"\n🔍 Field: {field_name}")
                print(f"   Total entities: {total_entities}")
                print(f"   Visibility variations:")
                
                for visibility_value, entities in variations.items():
                    print(f"     • {visibility_value}: {', '.join(entities)} ({len(entities)} entities)")
        else:
            print(f"\n✅ No ui_visibility inconsistencies found!")
        
        print("\n" + "="*80)
    
    def export_to_csv(self, output_file: str = "ui_visibility_inconsistencies.csv"):
        """Export inconsistencies to CSV file."""
        if not self.inconsistencies:
            logger.info("No inconsistencies to export")
            return
        
        output_path = Path(output_file)
        
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['field_name', 'total_entities', 'visibility_value', 'entities', 'entity_count']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            
            for inconsistency in self.inconsistencies:
                field_name = inconsistency['field_name']
                total_entities = inconsistency['total_entities']
                
                for visibility_value, entities in inconsistency['visibility_variations'].items():
                    writer.writerow({
                        'field_name': field_name,
                        'total_entities': total_entities,
                        'visibility_value': visibility_value,
                        'entities': ', '.join(entities),
                        'entity_count': len(entities)
                    })
        
        logger.info(f"Exported inconsistencies to {output_path}")
        print(f"\n📊 Detailed results exported to: {output_path}")

def main():
    """Main function to run the ui_visibility analysis."""
    parser = argparse.ArgumentParser(
        description="Analyze ui_visibility inconsistencies across data dictionary files"
    )
    parser.add_argument(
        "--output-csv",
        action="store_true",
        help="Export results to CSV file"
    )
    parser.add_argument(
        "--path",
        type=str,
        default="configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_data_dictionary",
        help="Path to the data dictionary directory"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Initialize the analyzer
        analyzer = UIVisibilityAnalyzer(data_dict_path=args.path)
        
        # Analyze all files
        summary = analyzer.analyze_all_files()
        
        # Print summary
        analyzer.print_summary(summary)
        
        # Export to CSV if requested
        if args.output_csv:
            analyzer.export_to_csv()
        
        # Exit with appropriate code
        if summary['errors']:
            logger.error(f"Analysis completed with {len(summary['errors'])} errors")
            exit(1)
        else:
            logger.info("Analysis completed successfully")
            exit(0)
    
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        exit(1)

if __name__ == "__main__":
    main()
