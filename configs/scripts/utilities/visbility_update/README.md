# Enable Hiding Removal <PERSON>t

This directory contains scripts to remove all occurrences of the "enable_hiding" attribute from data dictionary JSON files.

## Files

- `enable_hiding_removal.py` - Main script to remove enable_hiding attributes
- `test_enable_hiding_removal.py` - Test script to verify functionality
- `README.md` - This documentation file

## Usage

### Dry Run (Recommended First)
To see what would be changed without making actual modifications:

```bash
python enable_hiding_removal.py --dry-run
```

### Actual Run with Backups (Recommended)
To remove enable_hiding attributes and create backup files:

```bash
python enable_hiding_removal.py
```

### Actual Run without Backups (Not Recommended)
To remove enable_hiding attributes without creating backups:

```bash
python enable_hiding_removal.py --no-backup
```

### Custom Path
To specify a different data dictionary directory:

```bash
python enable_hiding_removal.py --path /path/to/your/data/dictionary/directory
```

### Verbose Logging
To enable detailed debug logging:

```bash
python enable_hiding_removal.py --verbose
```

## Command Line Options

- `--dry-run`: Show what would be changed without making actual changes
- `--no-backup`: Skip creating backup files (not recommended)
- `--path`: Path to the data dictionary directory (default: configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_data_dictionary)
- `--verbose`: Enable verbose logging

## What the Script Does

1. **Scans** all JSON files in the specified data dictionary directory
2. **Recursively searches** through each JSON structure to find "enable_hiding" attributes
3. **Removes** all "enable_hiding" attributes while preserving all other data
4. **Creates backups** of original files before modification (unless --no-backup is used)
5. **Provides detailed logging** of all changes made
6. **Handles errors gracefully** and reports any issues

## Safety Features

- **Dry run mode** to preview changes before making them
- **Automatic backups** with timestamps
- **Comprehensive error handling**
- **Detailed logging** to track all changes
- **JSON validation** to ensure files remain valid after modification

## Test Results

Based on the dry run, the script will process:

- **15 JSON files** in the data dictionary directory
- **506 total enable_hiding attributes** to be removed
- Files with the most attributes to remove:
  - network__data_dictionary.json: 55 removals
  - cloud_compute__data_dictionary.json: 41 removals
  - cloud_storage__data_dictionary.json: 38 removals
  - host__data_dictionary.json: 38 removals

## Backup Files

When backups are created, they follow this naming pattern:
```
original_file.backup_YYYYMMDD_HHMMSS.json
```

For example:
```
host__data_dictionary.backup_20250718_152454.json
```

## Logging

The script creates a log file `enable_hiding_removal.log` with detailed information about:
- Files processed
- Number of attributes removed from each file
- Backup file locations
- Any errors encountered

## Recovery

If you need to restore the original files:
1. Stop using the modified files
2. Copy the backup files back to their original names
3. Remove the `.backup_YYYYMMDD_HHMMSS` suffix from the filenames

## Testing

Run the test script to verify functionality:

```bash
python test_enable_hiding_removal.py
```

This creates temporary test files and verifies that the removal process works correctly.
