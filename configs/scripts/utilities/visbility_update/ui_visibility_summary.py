#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze and summarize ui_visibility inconsistencies patterns.

This script analyzes the CSV output from ui_visibility_analyzer.py and provides
insights about the patterns of inconsistencies.
"""

import csv
import argparse
from collections import defaultdict, Counter
from pathlib import Path

def analyze_csv_patterns(csv_file: str):
    """Analyze patterns in the ui_visibility inconsistencies CSV."""
    
    if not Path(csv_file).exists():
        print(f"Error: CSV file {csv_file} not found. Please run ui_visibility_analyzer.py first.")
        return
    
    # Data structures to store analysis
    field_patterns = defaultdict(list)
    visibility_counts = Counter()
    entity_patterns = defaultdict(set)
    
    # Read the CSV file
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            field_name = row['field_name']
            visibility_value = row['visibility_value']
            entities = row['entities'].split(', ')
            entity_count = int(row['entity_count'])
            
            field_patterns[field_name].append({
                'visibility': visibility_value,
                'entities': entities,
                'count': entity_count
            })
            
            visibility_counts[visibility_value] += entity_count
            
            for entity in entities:
                entity_patterns[entity].add(field_name)
    
    print("="*80)
    print("UI_VISIBILITY INCONSISTENCY PATTERNS ANALYSIS")
    print("="*80)
    
    # 1. Overall visibility value distribution
    print(f"\n📊 VISIBILITY VALUE DISTRIBUTION:")
    print("-" * 40)
    for visibility, count in visibility_counts.most_common():
        percentage = (count / sum(visibility_counts.values())) * 100
        print(f"  {visibility:>8}: {count:>3} occurrences ({percentage:>5.1f}%)")
    
    # 2. Most problematic fields (appearing in many entities with inconsistencies)
    print(f"\n🔥 MOST PROBLEMATIC FIELDS (by entity count):")
    print("-" * 50)
    field_entity_counts = {}
    for field_name, patterns in field_patterns.items():
        total_entities = sum(p['count'] for p in patterns)
        field_entity_counts[field_name] = total_entities
    
    for field_name, count in sorted(field_entity_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {field_name:>30}: {count:>2} entities")
        for pattern in field_patterns[field_name]:
            entities_str = ', '.join(pattern['entities'][:3])  # Show first 3 entities
            if len(pattern['entities']) > 3:
                entities_str += f" (+{len(pattern['entities'])-3} more)"
            print(f"    {pattern['visibility']:>8} -> {entities_str}")
    
    # 3. Entity-specific patterns
    print(f"\n🏢 ENTITIES WITH MOST INCONSISTENT FIELDS:")
    print("-" * 45)
    entity_field_counts = [(entity, len(fields)) for entity, fields in entity_patterns.items()]
    for entity, count in sorted(entity_field_counts, key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {entity:>20}: {count:>2} inconsistent fields")
    
    # 4. Common patterns analysis
    print(f"\n🔍 COMMON INCONSISTENCY PATTERNS:")
    print("-" * 40)
    
    # Pattern 1: Cloud entities vs others
    cloud_entities = {'cloud_account', 'cloud_compute', 'cloud_container', 'cloud_storage', 'finding_cloud'}
    cloud_vs_others = []
    
    for field_name, patterns in field_patterns.items():
        cloud_visibility = None
        other_visibility = None
        
        for pattern in patterns:
            entities_set = set(pattern['entities'])
            if entities_set.issubset(cloud_entities):
                cloud_visibility = pattern['visibility']
            elif entities_set.isdisjoint(cloud_entities):
                other_visibility = pattern['visibility']
        
        if cloud_visibility is not None and other_visibility is not None:
            cloud_vs_others.append({
                'field': field_name,
                'cloud_visibility': cloud_visibility,
                'other_visibility': other_visibility
            })
    
    if cloud_vs_others:
        print(f"\n  📋 Cloud entities vs Non-cloud entities pattern ({len(cloud_vs_others)} fields):")
        cloud_true_other_none = [f for f in cloud_vs_others if f['cloud_visibility'] == 'true' and f['other_visibility'] == 'None']
        cloud_none_other_false = [f for f in cloud_vs_others if f['cloud_visibility'] == 'None' and f['other_visibility'] == 'false']
        
        if cloud_true_other_none:
            print(f"    • Cloud=true, Others=None: {len(cloud_true_other_none)} fields")
            for f in cloud_true_other_none[:5]:  # Show first 5
                print(f"      - {f['field']}")
            if len(cloud_true_other_none) > 5:
                print(f"      ... and {len(cloud_true_other_none)-5} more")
        
        if cloud_none_other_false:
            print(f"    • Cloud=None, Others=false: {len(cloud_none_other_false)} fields")
            for f in cloud_none_other_false[:5]:  # Show first 5
                print(f"      - {f['field']}")
            if len(cloud_none_other_false) > 5:
                print(f"      ... and {len(cloud_none_other_false)-5} more")
    
    # 5. Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    print("-" * 20)
    
    # Find fields that are mostly None but have some true/false
    mostly_none_fields = []
    for field_name, patterns in field_patterns.items():
        none_count = sum(p['count'] for p in patterns if p['visibility'] == 'None')
        total_count = sum(p['count'] for p in patterns)
        if none_count / total_count > 0.6:  # More than 60% are None
            mostly_none_fields.append(field_name)
    
    if mostly_none_fields:
        print(f"\n  1. Fields that are mostly 'None' ({len(mostly_none_fields)} fields):")
        print(f"     Consider setting explicit ui_visibility values for consistency")
        for field in mostly_none_fields[:5]:
            print(f"     - {field}")
        if len(mostly_none_fields) > 5:
            print(f"     ... and {len(mostly_none_fields)-5} more")
    
    # Find fields with mixed true/false (excluding None)
    mixed_boolean_fields = []
    for field_name, patterns in field_patterns.items():
        visibilities = {p['visibility'] for p in patterns}
        if 'true' in visibilities and 'false' in visibilities:
            mixed_boolean_fields.append(field_name)
    
    if mixed_boolean_fields:
        print(f"\n  2. Fields with mixed true/false values ({len(mixed_boolean_fields)} fields):")
        print(f"     These need business logic review to determine correct visibility")
        for field in mixed_boolean_fields[:5]:
            print(f"     - {field}")
        if len(mixed_boolean_fields) > 5:
            print(f"     ... and {len(mixed_boolean_fields)-5} more")
    
    # Cloud-specific recommendations
    if cloud_vs_others:
        print(f"\n  3. Cloud vs Non-cloud pattern ({len(cloud_vs_others)} fields):")
        print(f"     Consider standardizing visibility rules between cloud and non-cloud entities")
        print(f"     Many fields show: Cloud entities=true, Non-cloud entities=None")
    
    print(f"\n" + "="*80)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Analyze ui_visibility inconsistency patterns from CSV"
    )
    parser.add_argument(
        "--csv-file",
        type=str,
        default="ui_visibility_inconsistencies.csv",
        help="Path to the CSV file generated by ui_visibility_analyzer.py"
    )
    
    args = parser.parse_args()
    analyze_csv_patterns(args.csv_file)

if __name__ == "__main__":
    main()
