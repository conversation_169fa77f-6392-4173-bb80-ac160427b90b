#!/usr/bin/env python3
"""
Test script for enable_hiding_removal.py

This script creates test JSON files and verifies that the enable_hiding removal works correctly.
"""

import json
import tempfile
import os
from pathlib import Path
import sys

# Add the current directory to the path so we can import the main script
sys.path.append(os.path.dirname(__file__))

from enable_hiding_removal import EnableHidingRemover

def create_test_data():
    """Create test JSON data with enable_hiding attributes."""
    return {
        "caption": "Test Entity",
        "description": "Test description",
        "attributes": {
            "field1": {
                "caption": "Field 1",
                "type": "string",
                "enable_hiding": True,
                "description": "Test field 1"
            },
            "field2": {
                "caption": "Field 2",
                "type": "integer",
                "enable_hiding": False,
                "range_selection": True
            },
            "field3": {
                "caption": "Field 3",
                "type": "string",
                "description": "Field without enable_hiding"
            },
            "nested_object": {
                "sub_field": {
                    "caption": "Sub Field",
                    "enable_hiding": True,
                    "nested_deeper": {
                        "deep_field": {
                            "enable_hiding": False,
                            "type": "boolean"
                        }
                    }
                }
            }
        },
        "metadata": {
            "enable_hiding": True,
            "version": "1.0"
        }
    }

def test_enable_hiding_removal():
    """Test the enable_hiding removal functionality."""
    print("Testing enable_hiding removal functionality...")

    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create test files
        test_files = []
        for i in range(3):
            test_file = temp_path / f"test_dict_{i}.json"
            test_data = create_test_data()

            # Modify test data slightly for each file
            if i == 1:
                # Remove some enable_hiding from second file
                del test_data["attributes"]["field1"]["enable_hiding"]
            elif i == 2:
                # Add more enable_hiding to third file
                test_data["extra_field"] = {"enable_hiding": True}

            with open(test_file, 'w') as f:
                json.dump(test_data, f, indent=4)

            test_files.append(test_file)

        print(f"Created {len(test_files)} test files in {temp_dir}")

        # Test dry run first
        print("\n--- Testing DRY RUN ---")
        remover = EnableHidingRemover(temp_dir, dry_run=True)
        summary = remover.process_all_files()
        remover.print_summary(summary)

        # Verify files weren't changed in dry run
        for test_file in test_files:
            with open(test_file, 'r') as f:
                data = json.load(f)
            if "enable_hiding" not in str(data):
                print(f"ERROR: {test_file} was modified during dry run!")
                return False

        print("\n--- Testing ACTUAL RUN ---")
        # Test actual run
        remover = EnableHidingRemover(temp_dir, dry_run=False)
        summary = remover.process_all_files()
        remover.print_summary(summary)

        # Verify enable_hiding was removed
        total_found = 0
        for test_file in test_files:
            with open(test_file, 'r') as f:
                data = json.load(f)

            # Recursively check for enable_hiding in the JSON structure
            def count_enable_hiding(obj):
                count = 0
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        if key == "enable_hiding":
                            count += 1
                        else:
                            count += count_enable_hiding(value)
                elif isinstance(obj, list):
                    for item in obj:
                        count += count_enable_hiding(item)
                return count

            found_count = count_enable_hiding(data)
            if found_count > 0:
                print(f"ERROR: {found_count} enable_hiding attributes still found in {test_file}")
                total_found += found_count

        if total_found == 0:
            print("\n✓ SUCCESS: All enable_hiding attributes were removed!")
            return True
        else:
            print(f"ERROR: Found {total_found} remaining enable_hiding attributes")
            return False

if __name__ == "__main__":
    success = test_enable_hiding_removal()
    if success:
        print("\n🎉 All tests passed!")
        exit(0)
    else:
        print("\n❌ Tests failed!")
        exit(1)
