import os
import sys
import json
import re

INVENTORY_MODELS_PATH = "source_models/inventory_models"


def camel_to_snake(name):
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

def collect_properties_keys(root_path):
    properties_keys = set()
    for dirpath, _, filenames in os.walk(root_path):
        for filename in filenames:
            if filename.endswith('.json'):
                file_path = os.path.join(dirpath, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        for key in data.keys():
                            if key.endswith('Properties'):
                                base = key[:-10]  # Remove 'Properties'
                                if base == 'temporary':
                                    continue
                                properties_keys.add(camel_to_snake(base))
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    return properties_keys

def main():
    if len(sys.argv) != 2:
        print("Usage: python collect_properties_group.py <base path>")
        sys.exit(1)

    base_path = sys.argv[1]
    inventory_models_path = os.path.join(base_path, INVENTORY_MODELS_PATH)
    properties_keys = collect_properties_keys(inventory_models_path)
    properties_list = sorted(properties_keys)

    # Ensure 'enrichment' is present
    if 'enrichment' not in properties_list:
        properties_list.append('enrichment')
        properties_list = sorted(properties_list)

    output_dir = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        'orchestration_shared_fs', 'sds-ei-configs', 'ui-configs'
    )
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, 'properties_group.json')

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(properties_list, f, indent=2)
    print(f"Written {len(properties_list)} properties ({properties_list}) to {output_path}")

if __name__ == "__main__":
    main()
