import * as vscode from 'vscode';
import axios from 'axios';

const OPENAI_API_KEY = '***************************************************';
const CHATGPT_ENDPOINT = 'https://api.openai.com/v1/chat/completions';

async function simplifySQL(sqlExpression: string) {
	try {
		const response = await axios.post(
			CHATGPT_ENDPOINT,
			{
				"model": "gpt-3.5-turbo",
				"messages": [{ "role": "assistant", "content": `Simplify the following Spark SQL expression: ${sqlExpression}` }],
				"max_tokens": 500,
				"temperature": 0.7
			},
			{
				headers: {
					'Authorization': `Bearer ${OPENAI_API_KEY}`,
					'Content-Type': 'application/json',
				},
			}
		);

		return response.data.choices[0].message.content.trim();
	} catch (error) {
		return error;
	}
}

export function activate(context: vscode.ExtensionContext) {
	let inventoryModelSchemaValidate = vscode.commands.registerCommand('sds-ei-validator-vsc-extension.SDSInventoryModelSchemaValidate', async () => {
		const editor = vscode.window.activeTextEditor;
		if (!editor) {
			vscode.window.showInformationMessage('No editor is active');
			return;
		}

		const text = editor.document.getText();
		const output = vscode.window.createOutputChannel('Example Output');
		try {
			let d = JSON.parse(text);
			const response = await axios.post('http://localhost:9000/v1/inventorymodel/validate/schema',
				d,
				{ headers: { "Content-Type": "application/json" } });
			console.log('Data posted successfully!');
			output.appendLine(JSON.stringify(response.data, null, 4));
			output.show();
		} catch (error) {
			console.error(String(error));
			const output = vscode.window.createOutputChannel('Example Output');
			output.appendLine(String(error));
			output.show();
		}
	});

	let expressionValidate = vscode.commands.registerCommand('sds-ei-validator-vsc-extension.SDSExpressionValidate', async () => {
		const editor = vscode.window.activeTextEditor;
		if (!editor) {
			vscode.window.showInformationMessage('No editor is active');
			return;
		}

		const selection = editor.selection;
		const text = editor.document.getText(selection);
		const request = {
			"expression": text
		}
		const output = vscode.window.createOutputChannel('Example Output');
		try {
			let d = request;
			const response = await axios.post('http://localhost:9000/v1/inventorymodel/validate/expression',
				d,
				{ headers: { "Content-Type": "application/json" } });
			output.appendLine(JSON.stringify(response.data, null, 4));
			output.show();
		} catch (error) {
			console.log(String(error));
		}
	});

	let expressionSimplify = vscode.commands.registerCommand('sds-ei-validator-vsc-extension.SDSExpressionSimplify', async () => {
		const editor = vscode.window.activeTextEditor;
		if (!editor) {
			vscode.window.showInformationMessage('No editor is active');
			return;
		}

		const output = vscode.window.createOutputChannel('Example Output');

		const selection = editor.selection;
		const text = editor.document.getText(selection);
		try {
			let response = await simplifySQL(text);

			output.appendLine(response);
			output.show();
		} catch (error) {
			console.log(String(error));
			output.appendLine(String(error));
			output.show();
		}
	});

	let startSession = vscode.commands.registerCommand('sds-ei-validator-vsc-extension.SDSStartSession', async () => {

		const document = await vscode.workspace.openTextDocument({
			content: JSON.stringify({
				"spark.executor.instances": "1",
				"spark.executor.memory": "1g",
				"spark.executor.core": "1",
				"spark.kubernetes.container.image": "prevalentai/spark:4-1-0-3.2.3-2.12-iceberg-v1-3-bookworm-12.10-20250428-slim",
				"spark.sql.extensions": "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions",
				"spark.sql.catalog.iceberg_catalog": "org.apache.iceberg.spark.SparkCatalog",
				"spark.sds.hive.catalog": "iceberg_catalog",
				"spark.sql.catalog.iceberg_catalog.type": "hadoop"
			}, null, 4),
			language: 'plaintext'
		});
		const editor = await vscode.window.showTextDocument(document, {
			preview: false,
			viewColumn: vscode.ViewColumn.One
		});

		const output = vscode.window.createOutputChannel('Spark Session Configurations');

		vscode.window.onDidChangeTextEditorSelection(async (changeEvent) => {
			if (changeEvent.textEditor === editor) {
				const text = changeEvent.textEditor.document.getText();
				output.appendLine(text);
			output.show();
			}
		});
	});

	context.subscriptions.push(inventoryModelSchemaValidate, expressionValidate, expressionSimplify, startSession);
}

export function deactivate() { }
