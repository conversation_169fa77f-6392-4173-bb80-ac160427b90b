[comment]: <> (Knowledge Graph)
[comment]: <> (Cloud Container)
[comment]: <> (#entity-inventory/cloud-container)

###### Introduction
Cloud container entity constitutes cloud resources that provide a platform for deploying, managing, and scaling containerized applications. This README provides an overview of various entities falling under this category and their respective functionalities.

###### Cloud Container High Level Diagram

<img src="container.png" width="100%">

###### Kubernetes Container

Kubernetes Containers are lightweight, standalone, and executable packages that include everything needed to run a piece of software: the application code, runtime, libraries, and dependencies. In Kubernetes, these containers run within pods, which are the smallest deployable units in a Kubernetes cluster.

**Example**: AWS EKS Container,Azure AKS Container  

###### Serverless Container

Serverless container service on Azure that provides an integrated experience for deploying and managing containers without managing infrastructure directly.

**Example**: Azure ACI Containers , Azure Container Apps Container

###### Container Service Container

Container service containers refers to containers managed by AWS ECS. These containers leverage AWS's infrastructure to handle tasks such as provisioning resources, monitoring, and scaling based on configured rules.

**Example**: AWS ECS Containers



###### Container Registry
A Container Registry is a storage and distribution system for container images, providing a centralized and secure way to store, manage, and share container images.

**Example**: AWS Container Registry (ECR), Azure Container Registry (ACR)

###### Cloud Relationship
The below diagram represents relationship associated with Cloud container entity.

<img src="cloud_container_rel.png" width="75%">