[comment]: <> (Knowledge Graph)
[comment]: <> (Summary)
[comment]: <> (#entity-inventory/summary,#home)

###### Knowledge Graph
Knowledge Graph is a one stop solution for all the cyber analytical requirements where you populate a Knowledge Graph, create relationships between entities and use this data as a product to drive all the analytics. An inventory could be considered a knowledge base. It is meant to provide a single source of truth. It can facilitate bringing together knowledge of all assets into a single centralized location, to save having to go and consult many different records to provide a complete understanding of all assets.

###### What is an Entity?
An entity is an object that has an existence, which is independent of the changes of its attributes or features. An entity is some unit of data that can be classified and have stated relationships to other entities. Examples for entities are Person, Host, Identity, Vulnerability, Findings, Software, Tickets, etc.

###### Who Can Benefit from Keeping an Up-to-Date Knowledge Graph?
Organizations want to stop spinning the chair; they don’t want to have to go to multiple systems to manually build a picture of their environment. These separate systems often refer to the same entity differently, making it hard to correlate them. These separate systems are often limited in the perspective they have, and thus provide only limited context. These systems sometimes have more than one degree of separation between them, making it extremely time consuming to trace a path to an item of interest; a record of people might not link directly to a record of devices. At best it may be possible to pivot between an intermediary overlapping system, such as an enterprise directory. Inventory sources are often out of date and inaccurate. Telemetry sources aren’t fully integrated with inventory sources; one source might say a person exists, but the actions and behaviors might be stored somewhere else, and that person might have been dormant for a while. It quickly becomes unwieldy having to look in multiple systems for multiple entities. An inventory is a first step in providing a single location to catalogue all entities/assets in an organization.

Why does an organization need to know what they have, where it is, what it can access etc.? Some examples include:
- Compliance
  - To standards: Cyber hygiene is a foundational maturity requirement for all Cybersecurity frameworks.
  - To policy: Policies cannot be assessed without accurate and current knowledge of the entities the policies are controlling.
  - Licensing: Licenses are often per-seat (head, person); therefore, it is important to know if a license is required, and if it is being utilized.
  - Regulations: Like standards, basic hygiene factors are a foundation of many regulatory requirements. Knowing your user, what they can - and what they have - accessed are common requirements.
- IT Operations
  - Maintenance and support: ensuring support is still provided, systems aren’t to be deprecated etc.
  - Risk and Exposure management
  - Allocation and extent of privileges.
  - Quality and accuracy of identity and access control systems.
  - Security posture across devices
    - Patch levels
    - Compliance status
  - Types of devices: corporate vs BYOD.
  - Toxic combinations of cyber hygiene and entitlements: To identify areas of greatest potential impact. For example, and unpatched device with EDR disabled connecting over an insecure connection to a highly privileged system using an unmonitored/controlled local server account.

###### Key Features of Knowledge Graph
Entity Extraction

It is the process of extracting and enriching entities with as much information as possible from various sources. Example: A Person entity can be extracted from HR records, VPN records, event logs, etc. And can be enriched with information like joining date, termination date, address, location, login time, login location etc.

Steps in analysis:
- Whenever a new data source comes in, we analyze the data and understand the distinct entities that is part of that.
- Update the attributes and mappings for the entity.
- Define the Confidence Criteria.
- Define the update policy - Persist/Update/Reset and Update

Each of these entities will have its own database that will be filled with information specific to them. Each uniquely identified entity will be assigned an entity id to refer to. Every entity has a distinct class that describes what it is (for example, Host, Person, or Identity) and will have a type that aids in categorizing it. Example: for a Host entity, the type can be server, workstation, mobile, network device etc. For each entity, there will be a data model or schema. Refer section 8 on how to create a data model is explained. Information obtained from multiple sources will be mapped to this schema. Entity schema or data model consists of three types of attributes:
- Common attributes
- Entity specific attributes
- Source specific attributes

<img src="Summary.png" width="100%">

###### Entity Resolution
Entity resolution between different sources refers to the process of resolving conflicts that arise when entities extracted from different sources have multiple meanings or interpretations. For example, when extracting information about a person from different sources, there may be multiple people with the same name, and it can be difficult to determine which person the information pertains to. Resolution is a crucial step in the entity extraction process as it helps to ensure that the extracted entities are accurate and reliable.

###### Relationships
A knowledge graph, also known as a semantic network, represents a network of real-world entities—i.e. objects, events, situations, or concepts—and illustrates the relationship between them. A knowledge graph is made up of three main components: nodes, edges, and labels.

***Nodes***: nodes represent entities or objects in the real world. They can be anything from physical objects like people, places, or things, to abstract concepts or events. Each node typically has a unique identifier and may have additional attributes or properties associated with it.

***Edges***: edges represent the relationships or connections between nodes in the knowledge graph. They define how different entities are related to each other. For example, an edge may indicate that a Person "works at" a company or that a movie "stars" an actor. Edges can have labels or types that describe the nature of the relationship.

By organizing information in a knowledge graph, data can be put into context, allowing for better data integration, unification, analysis, and sharing. Knowledge graphs enable the discovery of new insights, as they capture not only the individual entities but also the complex interconnections between them. They provide a structured representation of knowledge that can be queried, analyzed, and used to power various applications, such as recommendation systems, question answering, and knowledge discovery.  

In the context of PAI SDS analytics, the Knowledge Graph serves as a knowledge base that captures all distinct entities and their relationships as an entity-relationship graph. This inventory allows for a comprehensive understanding of the entities involved in the analytics process and facilitates the analysis and interpretation of data within the PAI SDS system.

<img src="Summary2.png" width="75%">

Relationship describes connections between entities. It can have properties which further describe the relationship. This helps to get source level relationship information. It is not always mandatory to define the relationship between every entity. Entities can exist of their own. It is possible for an entity to have a relationship to itself. It must have a unique name to define or classify what type of relationship it is. For every relationship, there should be an inverse relationship. Here source and targets will be interchanged with a change in the relationship name.



For each release of EI we should target a pre-defined set of data sources, entities and relationships. If the client data does not have any data source or attributes needed to build the entities or relationships, the solution should be capable of handling that by inserting 'NULL' wherever not possible. Also, client teams should be given the capability to add or override some of the existing entities, attributes, and relationships via configurations.

###### Daily Snapshot
A snapshot is the state of a system at a particular point in time. From Knowledge Graph point of view, a snapshot will be having the latest attribute values for all entities on that particular day. So, whenever the user selects a date for the inventory, they can see how the inventory was on that particular date.

The user can exactly track these changes making use of this Inventory snapshot feature.

By maintaining daily snapshots of inventory, organizations can track changes in inventory over time and see the status of the assets at any specific point in time that helps to troubleshoot or do root cause analysis. From a compliance perspective it is good to keep track of daily snapshots of inventory for quick reference and accountability.

1. Retrieve the status of any entity on any particular date: If an end user filters for any date, Knowledge Graph should be able to show the attributes of all entities on that particular day.

2. Tracking changes: An end user should be able to see any changes that happened to the attributes of an entity. E.g.: On day1 the operating system was Windows 11 later it changed to Windows 12. Basically, we need to have a link between the PIDs generated on multiple dates.

3. UI requirement: When I click on the detailed view of an entity, I should be able to see any changes that happened to that entity.

<img src="daily_snapshot.png" width="75%">

###### Knowledge Graph Dashboard

Welcome to the Knowledge Graph Dashboard! This dashboard is designed to provide a comprehensive overview of entities within your inventory, along with detailed summaries and listings for each entity.

###### Entity Summary

The entity summary section offers a condensed view of key information about each entity in your inventory.

###### Entity Listing Details

For more in-depth information about individual entities, the "Entity Listing Details" section provides a comprehensive breakdown. 

###### Feedback and Support

We're constantly striving to improve the functionality and usability of this dashboard. If you have any feedback, suggestions, or encounter any issues, please don't hesitate to reach out to our support team.

Thank you for using the Knowledge Graph Dashboard!

