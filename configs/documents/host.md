[comment]: <> (Knowledge Graph)
[comment]: <> (Host)
[comment]: <> (#entity-inventory/host)

###### Introduction
Host entities represent a diverse array of computing resources and devices within an infrastructure environment. These resources serve as foundational elements for supporting various applications and services. Hosts encompass a wide range of types, including Endpoints, Servers, Mobile devices, Other specialized hardware, Cloud Compute instances, and Network devices.

###### Host High Level Diagram

<img src="HostEntityUpgraded.png" width="60%">

###### Hypervisor
A hypervisor is a software, firmware, or hardware component that creates and manages virtual machines (VMs) on a host system. It allows multiple operating systems to run concurrently on a single physical machine by abstracting the hardware and allocating resources to the VMs as needed.

###### Server
Servers are dedicated computing devices or instances that provide services or resources to other devices or applications within a network. They handle requests, process data, and facilitate communication between clients and services.

###### Mobile
Mobile devices comprise smartphones, tablets, and other handheld computing devices that are designed for portability and connectivity. They often run specialized operating systems optimized for mobile use cases.

###### Workstation
Workstation refer to computing devices such as laptops, desktops, smartphones, and tablets that serve as points of access to a network or system. These devices often interact directly with users and can be both physical and virtual.

###### Network
Network devices play a critical role in facilitating communication and data exchange between different computing resources within a network infrastructure. These devices include routers, switches, firewalls, and other networking hardware.

###### Printer
Peripheral device that produces a hard copy (physical output) of digital documents, images, or graphics. It operates as a networked device that can be accessed by multiple computers or users within a network.

###### Host Relationship
The below diagram represents relationship between Host with other entities.

<img src="HostRelationship.png" width="100%">