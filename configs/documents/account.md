[comment]: <> (Knowledge Graph)
[comment]: <> (Account)
[comment]: <> (#entity-inventory/account)

###### Introduction

An account provides access to resources within the scope of a specific system or service, with permissions and entitlements. It is associated with a particular context, such as the system, process, or resource it grants access to, and is authenticated using credentials or tokens. 
An account can be authenticated by an "external" identity, meaning an identity managed by a different system than the one in which the account exists. 

While an account is "accessed" by a specific identity, the username or ID of the account may differ from that of the identity used for authentication. An account also contains information about its associated privileges.

###### Account High-Level Diagram

<img src="Account_High_level.png" width="100%">


###### Account Relationship
The relationship between Account and Identity is represented as follows.

<img src="Account_Relationship.png" width="50%">
