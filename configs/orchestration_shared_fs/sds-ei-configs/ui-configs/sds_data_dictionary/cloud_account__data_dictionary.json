{"caption": "Cloud Account", "entity_classification": "Entity", "description": "A Cloud Account is a user's gateway to accessing and managing cloud computing services or any resources offered by a cloud provider.", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique entity id generated for the entity", "group": "common", "type": "string", "category": "General Information", "internally_generated": true}, "display_label": {"caption": "Display Label", "description": "The derived and \"best known\" identifier or name, based on the attribute that best uniquely identifies it.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Cloud Account, Host, Person, Vulnerability etc.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "type": {"caption": "Type", "description": "Type refers to the categorization of the cloud account or subscription that is being used within a cloud platform. It describes the cloud platform (AWS, Azure, etc.) and the structure in which cloud resources are managed.\n\nType is categorized into Azure Subscription, AWS Account etc.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "origin": {"caption": "Origin", "description": "Data source in which the entity was extracted (found or derived) Eg. MS Azure ", "group": "common", "type": "string", "category": "General Information", "ui_visibility": true, "data_structure": "list"}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity is extracted.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested.\nIt is the actual api name from which the data is ingested.\nFor example AWS Organizations, MS Azure Subscriptions", "group": "common", "examples": "", "category": "General Information", "ui_visibility": true, "type": "string", "data_structure": "list", "internally_generated": true}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data.\nThis will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "first_seen_date": {"caption": "First Seen", "description": "The initial observation date of the entity as inferred from available data sources.\nThis is the earliest date across all data sources which are contributing to first seen date from each data sources", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\\nDefaults to First Found.\\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\nThis will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information", "internally_generated": true}, "last_active_date": {"caption": "Last Active", "description": "The latest date on which the entity was active. This is determined and directly correlated with the API provided - Account Status or State fields.", "group": "common", "type": "timestamp", "ui_visibility": true, "category": "General Information"}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. If the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. ", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "General Information", "internally_generated": true}, "description": {"caption": "Description", "description": "A detailed description of the entity", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "location_country": {"caption": "Location Country", "description": "Country in which the data centers supporting the respective cloud services(AWS and Azure) are located", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "location_city": {"caption": "Location City", "description": "Region in which the data centers supporting the respective cloud services(AWS and Azure) are located", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "department": {"caption": "Department", "description": "The name of the department within the business unit.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.\n\nIt is determined based on number of sources that gets resolved for each entity.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "internally_generated": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "The key fields that are considering for updating the last updated date of an entity", "group": "common", "type": "string", "ui_visibility": false, "data_structure": "struct", "internally_generated": true}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive", "group": "common", "type": "integer", "ui_visibility": false, "range_selection": true}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "description": "Indicates the number of days after which the cloud entity become inactive.", "group": "common", "type": "integer", "ui_visibility": false}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "category": "General Information", "type": "string"}, "account_id": {"caption": "Cloud Account ID", "description": "Unique identifier associated with the account in a cloud provider.In AWS account ID is a 12-digit number unique to each AWS account and in Azure account ID is known as the subscription ID represented as 32-character hexadecimal string.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "account_name": {"caption": "Account Name", "description": "Name of the account. The account name helps identify and organize resources but is distinct from more technical identifiers like account IDs or tenant IDs", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud provider to which the account belongs. Eg. AWS, MS Azure", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "account_status": {"caption": "Account Status", "description": "The current status of the account, whether it is active or not. It helps determine what actions or access are permitted for the account at that time. Active means the account is in good standing, and users have full access to the resources and services associated with the account. Inactive refers to an account which is non-operative in nature.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Account Status and Lifecycle"}, "aws_account_arn": {"caption": "AWS Arn", "description": "Amazon Resource Names (ARNs) uniquely identify AWS resources. ARN can be used to identify a resource unambiguously across all of AWS", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Cloud Provider-Specific Identifiers"}, "aws_account_email": {"caption": "AWS Account Email", "description": "The email address associated with the AWS account", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Cloud Provider-Specific Identifiers"}, "aws_account_status": {"caption": "AWS Account Status", "description": "The status of the AWS account in the organization. Possible values are \"ACTIVE\", \"PENDING_CLOSURE\" and \"SUSPENDED\".", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Account Status and Lifecycle"}, "aws_account_joined_method": {"caption": "AWS Account Joined Method", "description": "The method by which the account joined the organization. Possible values are \"CREATED\", \"INVITED\".", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Account Status and Lifecycle"}, "aws_account_joined_timestamp": {"caption": "AWS Account Joined Date", "description": "The date the account became a part of the organization.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_subscription_authorization_source": {"caption": "AWS Account Authorization Source", "description": "The authorization source of the request. Valid values are one or more combinations of Legacy, RoleBased, Bypassed, Direct and Management. For example, 'Legacy, RoleBased'.", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Cloud Provider-Specific Identifiers"}, "azure_subscription_managed_by_tenants": {"caption": "Azure Managed By Tenants", "description": "An array containing the tenants managing the subscription.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "struct"}, "azure_subscription_state": {"caption": "Azure Subscription State", "description": "The subscription state. Possible values are \"ENABLED\", \"WARNED\", \"PAST DUE\", \"DISABLED\", and \"DELETED\".", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Account Status and Lifecycle"}, "azure_subscription_policies": {"caption": "Azure Subscription Policies", "description": "The subscription policies that govern the behavior, limits, and management of an Azure subscription.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "struct"}, "azure_subscription_location_placement_id": {"caption": "Azure Subscription Location Placement ID", "description": "The subscription location placement ID. The ID indicates which regions are visible for a subscription. For example, a subscription with a location placement Id of Public_2014-09-01 has access to Azure public regions.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_subscription_quota_id": {"caption": "Azure Subscription Quota ID", "description": "The ID of the quota that helps manage resource limits and usage within that subscription", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Cloud Provider-Specific Identifiers"}, "azure_subscription_spending_limit": {"caption": "Azure Subscription Spending Limit", "description": "The subscription spending limit. Values can be On, Off and CurrentPeriodOff", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Cloud Provider-Specific Identifiers"}, "azure_subscription_tags": {"caption": "Azure Subscription Tags", "description": "The tags attached to the subscription. Tags are essentially key-value pairs that help you organize and identify resources based on settings relevant to your organization.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "struct"}, "azure_subscription_tenantId": {"caption": "Azure Tenant ID", "description": "The subscription tenant ID. It is derived from the field tenantId in Azure Subscription source.", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Cloud Provider-Specific Identifiers"}, "associated_cloud_compute_resource_count": {"caption": "Count of Cloud Compute Resource", "description": "Number of cloud compute resource associated with cloud account.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_cloud_container_resource_count": {"caption": "Count of Cloud Container Resource", "description": "Number of cloud container resource associated with cloud account.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_cloud_storage_resource_count": {"caption": "Count of Cloud Storage Resource", "description": "Number of cloud storage resource associated with cloud account.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_compliance_standard_count": {"caption": "Count of Compliance Standard", "description": "Number of Compliance Standard associated with cloud account.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_security_control_count": {"caption": "Count of Security Control", "description": "Number of Security Control associated with cloud account.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_assessment_count": {"caption": "Count of Assessment", "description": "Number of compliance assessments.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_open_finding_count": {"caption": "Count of Open Findings", "description": "Number of open findings related to cloud account.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_finding_count": {"caption": "Count of Findings", "description": "Number of findings related to cloud account.", "group": "enrichment", "type": "integer", "range_selection": true}}, "dashboard_identifier": "EI"}