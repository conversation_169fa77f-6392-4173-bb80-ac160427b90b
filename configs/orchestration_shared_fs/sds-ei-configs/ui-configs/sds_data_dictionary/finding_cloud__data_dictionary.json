{"caption": "Finding Cloud", "entity_classification": "Supporting Entity", "description": "A finding is a specific piece of information that indicates a potential security issue or anomaly within an organization's IT infrastructure, systems, applications, or processes.", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.", "group": "common", "type": "string", "enable_hiding": true, "category": "General Information", "internally_generated": true}, "display_label": {"caption": "Display Label", "description": "The derived and \"best known\" identifier or name, based on the attribute that best uniquely identifies it. Finding Title field is used and if in case its not available then the ID of the Finding is used Eg. AWS Config should be enabled", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "category": "General Information"}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Finding, Host, Person, Vulnerability etc.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "category": "General Information", "internally_generated": true}, "type": {"caption": "Type", "description": "The specific type of the Entity. Eg. Cloud", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "category": "General Information"}, "origin": {"caption": "Origin", "description": "Data source in which the entity was extracted (found or derived). Eg. AWS, MS Azure", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "category": "General Information", "data_structure": "list"}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity has been extracted.", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested. Eg. AWS SH Findings", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "category": "General Information", "data_structure": "list", "internally_generated": true}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data.\nThis will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "ui_visibility": true, "enable_hiding": true, "category": "General Information", "internally_generated": true}, "first_seen_date": {"caption": "First Seen", "description": "The initial observation date of the entity as inferred from available data sources.", "group": "common", "type": "timestamp", "ui_visibility": true, "enable_hiding": true, "category": "General Information"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\\nDefaults to First Found.\\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "type": "timestamp", "ui_visibility": true, "enable_hiding": false, "category": "General Information", "internally_generated": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\nThis will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "ui_visibility": true, "enable_hiding": true, "category": "General Information", "internally_generated": true}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which entity was active as inferred from available data sources.\nThis date is determined by considering the maximum value of dates contributed by each data source for the entity in question.\nThis includes data such as Finding Last seen date, Status changes, or any other indicators of the entity's recent engagement or interaction.", "group": "common", "type": "timestamp", "category": "General Information", "ui_visibility": true, "enable_hiding": false}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \\n\\nIf the time since the last inventory update is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. \\nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "category": "General Information"}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "enable_hiding": false, "category": "General Information", "internally_generated": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "enable_hiding": false, "category": "General Information", "internally_generated": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "enable_hiding": false, "category": "General Information", "internally_generated": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "enable_hiding": true, "category": "General Information", "internally_generated": true}, "description": {"caption": "Description", "description": "A detailed description of the entity. This includes details such as root cause for the finding, how to remediate etc.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "category": "General Information"}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": true, "category": "General Information"}, "location_country": {"caption": "Location Country", "description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "category": "General Information"}, "location_city": {"caption": "Location City", "description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "category": "General Information"}, "department": {"caption": "Department", "description": "The name of the department within the business unit.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": true, "category": "General Information"}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.\\nIt is determined based on number of sources that gets resolved for each entity.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "enable_hiding": true, "internally_generated": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considered for updating the last updated date of an entity", "group": "common", "type": "string", "ui_visibility": false, "data_structure": "struct", "enable_hiding": true, "internally_generated": true}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "type": "integer", "ui_visibility": false, "range_selection": true, "enable_hiding": true}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "description": "Indicates the number of days after which the cloud entity become inactive", "group": "common", "type": "integer", "ui_visibility": false, "enable_hiding": true}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "enable_hiding": true, "type": "string", "category": "General Information"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud provider to which the finding belongs. Eg. AWS, Azure", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Information"}, "account_id": {"caption": "Cloud Account ID", "description": "Unique identifier associated with the account in a cloud provider.In AWS account ID is a 12-digit number unique to each AWS account and in Azure account ID is known as the subscription ID represented as 32-character hexadecimal string. Each cloud resource is associated with an account ID which is either available directly in the source or extracted from the resource ID.Examples are **********, 011c41b3-ba33-448e-86d2-34252xxx.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Information"}, "finding_id": {"caption": "ID", "description": "Unique Identifier of the finding. This includes the account and resource hierarchy under which a finding is generated and is kept as the primary key for the Entity.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "finding_title": {"caption": "Finding Title", "description": "Name of the finding. Derived from fields like Title, Display name etc. from data feeds. For eg. AWS Config should be enabled", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "assessment_title": {"caption": "Assessment", "description": "An assessment title is a clear identifier used to evaluate an organization's security measures, focusing on exposure, effectiveness, implementation, and compliance.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "finding_type": {"caption": "Finding Type", "description": "Classification of the security finding, based on the cloud platform generated the same. For eg. Azure Security Assessment,Azure Security Center Alerts", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "finding_sub_type": {"caption": "Finding Sub Type", "description": "Sub Type of the finding. For AWS this can be in the format of namespace/category/classifier that classify a finding. For Azure its the unique identifier for the detection logic For eg. Software and Configuration Checks, VM_SuspiciousScreenSaver", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "status": {"caption": "Status", "description": "The normalized status of the investigation into the finding across all vendors is categorized as either Open or Closed. Statuses such as Healthy, Dismissed, Resolved, Suppressed, and Not Applicable are grouped under Closed. Also, if the affected resource is no longer active or has been terminated, the status is set to Closed (with the Finding Activity status becoming Inactive). For active findings, any other status, such as New or Unhealthy, is classified as Open.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "workflow_status": {"caption": "Workflow Status", "description": "Workflow status tracks the progress of investigation into a finding. The possible values are 'Resolved, 'Unresolved' and 'Suppressed'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "finding_severity": {"caption": "Severity", "description": "The normalized severity corresponding to the finding across all vendors. Logical grouping is Informational, Low, Medium, High, Critical and Other.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "remediation": {"caption": "Remediation", "description": "Efficiently address identified issues through targeted remediation strategies, ensuring swift resolution and optimization of processes.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Remediation and Compliance"}, "techniques": {"caption": "Techniques", "description": "Refers to specific techniques or methods utilized by attackers or adversaries to exploit vulnerabilities, compromise security, and carry out malicious activities within the assessed environment. These techniques are typically classified based on well-known attack frameworks such as the MITRE ATT&CK framework.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Risk and Threat Intelligence"}, "threats": {"caption": "Threats", "description": "Refers to the specific threats or risks that are associated with the security findings or issues identified during the assessment. These threats describe the potential consequences or impacts of the security vulnerabilities or weaknesses detected in the assessed environment. Each finding may be linked to one or more threats, which helps security teams understand the potential risk posed by the identified issues and prioritize their remediation efforts accordingly.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Risk and Threat Intelligence"}, "tactics": {"caption": "Tactics", "description": "Refers to the tactics employed by attackers or adversaries to exploit vulnerabilities and compromise security within the assessed environment. These tactics are often categorized based on common attack patterns and strategies outlined by frameworks such as the MITRE ATT&CK framework.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Risk and Threat Intelligence"}, "categories": {"caption": "Categories", "description": "refers to the different categories or classifications of security issues or findings identified during the assessment. These categories help to organize and prioritize the remediation efforts based on the nature and severity of the security issues detected. Each finding or security issue may be assigned to one or more categories, such as \"Vulnerability Management,\" \"Identity and Access Management,\" \"Data Protection,\" \"Network Security,\" etc.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Risk and Threat Intelligence"}, "vendor_status": {"caption": "Vendor Status", "description": "The status of the investigation into the finding specific to each vendor. This indicates Non-transformed (raw) values from each vendor. For eg. Healthy, Dismissed, Resolved, Suppressed ", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Status Tracking and Resolution"}, "vendor_severity": {"caption": "<PERSON><PERSON><PERSON>", "description": "The severity label of a finding. This indicates Non-transformed (raw) values from each vendor. Eg. Low, Medium, High ", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Status Tracking and Resolution"}, "finding_detection_source": {"caption": "Detection Source", "description": "Specifies the source or tool that detected the security issue. Derived from the productName field in data feed. Eg. Config, Security Hub", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Finding Identification"}, "affected_resource_id": {"caption": "Affected Resource ID", "description": "The resource or asset that is affected by the finding. The resource affected could also be the cloud Account in which the finding is generated.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources"}, "affected_asset_display_label": {"caption": "Associated Entities Display Label", "description": "The resource or asset that is affected by the finding. The resource affected could also be the cloud Account in which the finding is generated.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources"}, "affected_asset": {"caption": "Affected Asset", "description": "The resource or asset that is affected by the finding. The resource affected could also be the cloud Account in which the finding is generated.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources", "data_structure": "list"}, "affected_resource_type": {"caption": "Affected Vendor Resource Type", "description": "The vendor specific resource type that is affected by the Finding. For eg.AwsEc2Volume, virtualMachines", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources"}, "affected_resource_type_normalized": {"caption": "Affected Resource Type", "description": "Normalized names for Affected resource Type. Resource type name coming from each source is compared with existing types in EI and categorized. For eg. AWS EC2 Volume is classified as Volume, AWS Account is classified as Cloud Account", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources"}, "finding_resolved_date": {"caption": "Resolved Date", "description": "Date on which the finding was resolved. Only applicable to Findings whose status is marked as closed.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true, "category": "Status Tracking and Resolution"}, "associated_standard": {"caption": "Associated Standard", "description": "The pre-configured security and compliance standards associated with a finding. This indicates the Non-transformed (raw) values. For eg. aws-foundational-security-best-practices/v/1.0.0, nist-800-53/v/5.0.0 ", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Remediation and Compliance"}, "compliance_status": {"caption": "Compliance Status", "description": "Compliance status refers to whether a resource meets specific security standards or regulatory frameworks or security controls. For eg. Passed, Failed", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Remediation and Compliance"}, "policy_definition_id": {"caption": "Policy Definition ID", "description": "", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "finding_latest_open_date": {"caption": "Latest Open Date", "description": "The most recent date when the finding's status was set to Open, regardless of whether it is the initial opening or a re-opening after being closed.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true, "category": "Status Tracking and Resolution"}, "aws_finding_first_seen_time": {"caption": "AWS SH First Seen Date", "description": "The Date at which the finding was first observed. Derived from Firstobservedat in the data feed.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "aws_finding_last_seen_time": {"caption": "AWS SH Last Seen Date", "description": "The Date at which the finding was last observed. Derived from Lastobservedat in the data feed.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "assessment_id": {"caption": "Assessment ID", "description": "Unique code of each assessment. Derived from Name field in the data feed.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "scope": {"caption": "<PERSON><PERSON>", "description": "Specifies the scope of the Assessment used to evaluate success and failure.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "exposure_category": {"caption": "Exposure Category", "description": "Type of exposure category the assessment belongs to. It can have values like Software Vulnerability, Control Gap and Threat Detection.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "azure_assessment_cause": {"caption": "Azure Assessment Cause", "description": "Cause of the assessment. Programmatic code for the cause of the assessment status is derived from properties.status.cause in the data feed. Eg. There are no Azure App Service plans available in the subscription.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_alert_product_component_name": {"caption": "Azure Alert Product Component Name", "description": "The name of Azure Security Center pricing tier which powering this alert. This is derived from properties.productComponentName in the data feed. Eg. KeyVault", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_policy_initiative_name": {"caption": "Azure Policy Initiative Name", "description": "Azure Policy Initiative that encapsulates a set of governance controls and compliance requirements, facilitating streamlined management and enforcement of policies across Azure resources.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_policy_definition_id": {"caption": "Azure Policy Definition ID", "description": "Unique identifier assigned to each Azure Policy Definition, enabling precise identification and management of specific governance rules and compliance standards within the Azure environment.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_assessment_type": {"caption": "Azure Assessment Type", "description": "A defined category or classification used to specify the type of assessment being conducted within the Azure environment, providing clarity on the focus and purpose of the evaluation.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_user_impact": {"caption": "Azure User Impact", "description": "Refers to the potential effect or consequence that a security finding or issue may have on users or stakeholders within the assessed environment. This attribute provides information on how the identified security vulnerabilities or weaknesses could impact the usability, functionality, accessibility, or overall experience for users interacting with the affected systems or resources.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_implementation_effort": {"caption": "Azure Implementation Effort", "description": "Refers to the potential effect or consequence that a security finding or issue may have on users or stakeholders within the assessed environment. This attribute provides information on how the identified security vulnerabilities or weaknesses could impact the usability, functionality, accessibility, or overall experience for users interacting with the affected systems or resources.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_alert_first_seen_time": {"caption": "Azure Alert First Seen Date", "description": "The Date of the first event or activity included in the alert. Derived from properties.startTimeUtc in the data feed.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_alert_last_seen_time": {"caption": "Azure Al<PERSON> Last Seen Date", "description": "The Date of the last event or activity included in the alert. Derived from properties.endTimeUtc in the data feed.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_alert_intent": {"caption": "Azure <PERSON><PERSON>", "description": "The kill chain related intent behind the alert. Derived from properties.intent in the data feed. For eg. Exfiltration,CredentialAccess", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_alert_name": {"caption": "Azure Alert Name", "description": "Name of the alert. Derived from name field in the data feed.", "group": "source_specific", "type": "string", "ui_visibility": true}, "associated_assessment_count": {"caption": "Count of Assessment", "description": "Number assessment associated with findings.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "associated_cloud_storage_resource_count": {"caption": "Count of Cloud Storage Resource with Open Findings", "description": "Number cloud storage resource associated with open findings.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "associated_cloud_account_with_open_finding_count": {"caption": "Count of Cloud Account with Open Findings", "description": "Number cloud account associated with open findings.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "associated_cloud_account_count": {"caption": "Count of Cloud Account", "description": "Number cloud account associated with findings.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "associated_cloud_compute_resource_count": {"caption": "Count of Cloud Compute Resource with Open Findings", "description": "Number of cloud compute resource associated with open findings.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "associated_cloud_container_resource_count": {"caption": "Count of Cloud Container Resource with Open Findings", "description": "Number of cloud container resources associated with open findings.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}}, "dashboard_identifier": "EI"}