{"caption": "Application", "entity_classification": "Entity", "description": "Software program designed to perform specific tasks or provide certain functionalities on electronic devices such as computers, smartphones, or tablets. It serves as an intermediary between users and computer systems, facilitating interaction, data processing, and problem-solving.", "navigator_constraints": "use 'display_label' for application name filtering (names in UPPERCASE)", "navigator_enabled": true, "navigator_description": "\n# Application Table Summary\n\n## Overview\nThe Application table serves as a central repository for tracking and managing all software applications across an organization's environment. It provides a unified view of systems, tools, software, databases, and applications with comprehensive details about their configuration, operational status, and security posture.\n\n## Data Categories\n\n### Identity & Classification\n- **Application Names & Identifiers**: Various name formats and unique identifiers (p_id, display_label)\n- **Type Classification**: Different classifications based on functionality (Software, Tool, Application, System, Database)\n- **Category**: Classifications based on origin, development approach, and licensing model (Opensource, Off The Shelf, Off The Shelf Customised)\n\n### Vendor & Version Information\n- **Vendor Details**: Information about the application providers and vendors\n- **Version Information**: Version numbers and release details\n- **Lifecycle Status**: Application lifecycle stages (Mainstream, Retired)\n\n### Operational Context\n- **Internet Exposure**: Whether the application is internet-facing\n- **Deployment Scope**: Internal vs. external accessibility\n- **Operational Status**: Current state of the application (Active, Disabled)\n\n### Security Posture\n- **Risk Categorization**: Internal classification of risk (CAT 1-4)\n- **Criticality Assessment**: Derived criticality indicators\n- **Sensitivity Status**: Whether the application contains sensitive information\n- **Vulnerability Metrics**: Count of vulnerabilities (total and open) associated with the application\n\n### Activity & Lifecycle\n- **Temporal Information**: First seen, last found, and last active dates\n- **Activity Status**: Current operational state (Active/Inactive)\n- **Retirement Details**: When applicable, retirement dates for decommissioned applications\n- **Lifecycle Metrics**: Lifetime duration and observed timeline\n\n### Relationship Mapping\n- **Host Relationships**: Number of hosts running the application\n- **Vulnerability Context**: Associated vulnerability findings (total and open)\n\n### Data Provenance\n- **Source Attribution**: Origin systems contributing data (MS Defender, Mega)\n- **Corroboration Information**: Whether data points are unique or corroborated across sources\n- **Source Count**: Number of data sources from which the entity has been extracted\n\n### Business Context\n- **Organizational Placement**: Business unit and department assignments\n\n## Key Features\n\n1. **Comprehensive Application Catalog**: Consolidates data about all software applications regardless of type or purpose\n\n2. **Security Risk Assessment**: Provides categorization of applications based on risk levels and criticality\n\n3. **Operational Status Tracking**: Monitors the active status and lifecycle stage of applications\n\n4. **Vulnerability Context**: Tracks vulnerability findings associated with applications\n\n5. **Deployment Scope Identification**: Distinguishes between internal and internet-facing applications\n\n6. **Temporal Tracking**: Maintains historical timeline of application lifecycle and activity\n\n7. **Source Verification**: Tracks data origin and corroboration across multiple sources\n\n8. **Vendor and Version Management**: Catalogs vendor information and version details for software asset management\n\nThe Application table functions as a critical component of the security analytics platform, providing essential software context needed for vulnerability management, security risk assessment, and application lifecycle management.\nIMPORTANT: Query: Show applications with critical vulnerability, filter for host will be '' because there is no filter related to application in the query.\nIMPORTANT:If the query asks for anyting related to any software,that filter should go to Application.For example:query:Show all openssl softwares,filter for application will be 'openssl'.", "navigator_graph_node_description": "The Application entity  represents software systems within an organization's technology landscape. It captures essential attributes including unique identifiers (p_id), display labels, classification types (Software, Tool, Application, System, Database), and data source origins.The schema tracks temporal aspects through timestamps (first_seen_date, last_found_date), activity status, and lifecycle stages (Mainstream, Retired). Application-specific attributes include app_name, vendor information, version details, and categorization (Opensource, Off The Shelf).Security aspects are covered through risk categorization (CAT1-4), internet exposure flags, criticality indicators, and sensitive information markers. Enrichment attributes establish relationships with hosts and vulnerability findings, providing context for security assessments.This schema enables comprehensive application inventory management, risk evaluation, and security monitoring across the organization's software ecosystem.", "navigator_entity_description": "\nThe Application Entity represents software systems, tools, databases, and applications within an organization's technology landscape. It serves as a comprehensive inventory of all software assets, providing visibility into the application ecosystem to support security posture, risk assessment, compliance tracking, and lifecycle management.\n\n## Key Attributes and Characteristics\n\n### Identification and Classification\n- **Unique identifiers** (p_id) and display labels for each application entity\n- **Type classification** categories including System, Tool, Application, Software, and Database\n- **Origin information** tracking data sources from which the application was discovered (e.g., MS Defender, Mega)\n- **Application name and vendor details** providing specific identification information\n\n### Lifecycle Management\n- **Temporal tracking** through timestamps for first seen, first found, last found, and last active dates\n- **Activity status indicators** showing whether applications are Active or Inactive\n- **Lifecycle stage classification** (Mainstream, Retired) with retirement dates when applicable\n- **Observed lifetime** measuring the duration an application has been present in data sources\n\n### Risk and Security Profiling\n- **Risk categorization** (CAT 1-4) indicating different risk levels associated with applications\n- **Internet exposure status** flagging whether applications are internet-facing\n- **Derived criticality markers** highlighting applications of heightened importance\n- **Sensitive information indicators** noting applications that process or store sensitive data\n- **Vulnerability association** tracking the number of vulnerabilities and open findings\n\n### Categorization and Context\n- **Application categories** classifying by origin, development approach, and licensing model:\n  - Opensource\n  - Off The Shelf\n  - Off The Shelf Customized\n- **Version information** tracking specific software versions and updates\n- **Department association** linking applications to business functions and organizational structure\n\n### Relationship Management\n- **Host relationships** tracking the number of hosts running each application\n- **Vulnerability exposure** monitoring vulnerability findings associated with applications\n- **Origin contribution type** distinguishing between unique and corroborated data sources\n\n## Purpose and Importance\n\nThe Application Entity serves multiple critical functions within an organization:\n\n1. **Asset Inventory Management**: Provides a comprehensive catalog of all software assets for visibility and governance\n2. **Security Risk Assessment**: Enables evaluation of application security posture through vulnerability tracking and risk categorization\n3. **Compliance Monitoring**: Supports regulatory adherence by identifying applications handling sensitive information\n4. **Lifecycle Oversight**: Facilitates software lifecycle management from deployment to retirement\n5. **Vulnerability Management**: Links applications to security vulnerabilities for targeted remediation planning\n6. **Technology Portfolio Analysis**: Offers insights into application diversity, redundancy, and deployment patterns\n7. **Software Supply Chain Visibility**: Tracks vendor relationships and software origins for supply chain security\n8. **Resource Optimization**: Identifies inactive applications for potential decommissioning to reduce overhead\n\nThe Application Entity forms a cornerstone of technology asset management, enabling organizations to maintain awareness of their software landscape, manage associated risks effectively, and make informed decisions about application deployment, security, and retirement strategies.\nIMPORTANT:ANY SOFTWARE NAME IN A QUERY REFERS TO THE APPLICATION ENTITY/TABLE", "navigator_examples": ["User Query: 'Show vulnerable applications' Output: 'application'"], "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.", "group": "common", "type": "string", "category": "General Information", "navigator_attribute_description": "Unique identifier generated for each entity by the Knowledge Graph.", "internally_generated": true}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.\nFor Application Entity App Name is the attribute that contribute to display label.", "group": "common", "type": "string", "category": "General Information", "navigator_attribute_description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "navigator_attribute_sample_data_values": ["CHROME FOR MAC", "LIBPCAP0.8 FOR LINUX", "SOURCE VIEWER", "APACHE HTTPD", "VIS TIMELINE", "FINE FREE FILE", "MONGOSE", "DOC PATH", "ELFINDER", "LIBJURT JAVA FOR LINUX"], "navigator_sample_values_enabled": true}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Host, Person, Vulnerability etc.", "group": "common", "type": "string", "category": "General Information", "internally_generated": true}, "type": {"caption": "Type", "description": "The classification of an application based on its functionality and role within a system.\nApplication entity encompass types such as Software,Tool,Application,System,Database.", "group": "common", "type": "string", "category": "General Information", "navigator_attribute_description": "The classification of an application based on its functionality and role within a system.\nApplication entity encompass types such as Software,Tool,Application,System,Database.", "navigator_attribute_distinct_values": ["System", "Tool", "Application", "Software", "Database"], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "origin": {"caption": "Origin", "description": "Data source(s) from which the entity has been extracted.\nFor Application entity examples are MS Defender,Mega.", "group": "common", "type": "string", "data_structure": "list", "category": "General Information", "navigator_attribute_description": "Data source(s) from which the entity has been extracted.\nFor Application entity examples are MS Defender,Mega.", "navigator_attribute_distinct_values": [["Mega", "MS Defender"], ["Mega"], ["MS Defender"]], "navigator_distinct_values_enabled": true}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity has been extracted.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "navigator_attribute_description": "Number of data sources from which the entity has been extracted.", "internally_generated": true}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested.\nFrom Application entity examples are Mega,defender software inventory etc.", "group": "common", "ui_visibility": true, "type": "string", "category": "General Information", "data_structure": "list", "internally_generated": true}, "first_found_date": {"caption": "First Found", "description": "The date at which the entity was first observed in the ingested data.This will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "navigator_attribute_description": "The date at which the entity was first observed in the ingested data.", "navigator_sample_format": 1722641805001, "category": "General Information", "internally_generated": true}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources.\nBy default first seen date date is calculated based on the minimum value between the last active date and the first found date of the entity.\nIf the data sources provides information regarding first seen activity of the entity for example software created date in application, they take precedence over the default logic.", "group": "common", "type": "timestamp", "navigator_attribute_description": "Initial observation date of the entity as inferred from available data sources.", "navigator_sample_format": 1719782857000, "category": "General Information"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\nDefaults to First Found.\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "type": "timestamp", "navigator_attribute_description": "Most recent date on which any update happened on an entity as inferred from the data.", "navigator_sample_format": 1721346491001, "category": "General Information", "internally_generated": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\nThis will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "navigator_attribute_description": "The date at which the entity was last observed in the ingested data.", "navigator_sample_format": 1720912858001, "category": "General Information", "internally_generated": true}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which entity was active as inferred from available data sources.\nThis date is determined by considering the maximum value of dates contributed by each data source for the entity in question.\nThis includes data such as activity logs, event timestamps, or any other indicators of the entity's recent engagement or interaction.", "group": "common", "type": "timestamp", "category": "General Information", "navigator_attribute_description": "Latest date on which entity was active as inferred from available data sources.", "navigator_sample_format": 1722638650001}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity.\nIf the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive.", "group": "common", "type": "string", "category": "General Information", "navigator_attribute_description": "Specifies the current status of the entity.", "navigator_attribute_distinct_values": ["Inactive", "Active"], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "group": "common", "type": "integer", "range_selection": true, "navigator_attribute_description": "The duration of time the entity has been active", "category": "General Information", "internally_generated": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "navigator_attribute_description": "Number of days since the entity was last active as inferred from ingested data sources.", "internally_generated": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "navigator_attribute_description": "Number of days over which the entity was present in one or more ingested data sources.", "internally_generated": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "navigator_attribute_description": "Number of days since the entity was last discovered in the ingested data.", "internally_generated": true}, "description": {"caption": "Description", "description": "Detailed explanation of the application.", "group": "common", "ui_visibility": true, "category": "General Information", "type": "string"}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "ui_visibility": true, "category": "General Information", "type": "string"}, "location_country": {"caption": "Location Country", "description": "Specifies the country where the asset is located.\nFor example 'South Africa'.", "group": "common", "ui_visibility": true, "type": "string", "category": "General Information"}, "location_city": {"caption": "Location City", "description": "Specifies the city where the asset is located.\nFor example 'Sydney'.", "group": "common", "ui_visibility": true, "type": "string", "category": "General Information"}, "department": {"caption": "Department", "description": "Name of the department within the business unit.\nFor example 'Product Management'.", "group": "common", "examples": "", "ui_visibility": true, "type": "string", "category": "General Information", "navigator_attribute_description": "Name of the department within the business unit.\nFor example 'Product Management'."}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.\nIt is determined based on number of sources that gets resolved for each entity.", "group": "common", "type": "integer", "range_selection": true, "internally_generated": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considering for updating the last updated date of an entity.", "group": "common", "type": "string", "data_structure": "struct", "ui_visibility": false, "internally_generated": true}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.\nFor example 180 days.", "group": "common", "ui_visibility": false, "type": "integer"}, "running_on_host_count": {"caption": "Count of Host Hosting Application", "description": "Number of Hosts hosting the Application.", "group": "enrichment", "type": "integer", "range_selection": true, "navigator_attribute_description": "Number of Hosts hosting the Application.", "navigator_deep_thinking_numerical": true}, "has_vulnerability_finding_count": {"caption": "Count of Vulnerability Findings", "description": "Number of vulnerability findings associated with application.", "group": "enrichment", "type": "integer", "range_selection": true, "navigator_attribute_description": "Number of vulnerability findings associated with application."}, "has_open_vulnerability_finding_count": {"caption": "Count of Open Vulnerability Findings", "description": "Number of open vulnerability findings associated with application.", "group": "enrichment", "type": "integer", "range_selection": true, "navigator_attribute_description": "Number of open vulnerability findings associated with application."}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "category": "General Information", "type": "string", "navigator_attribute_description": "Describes whether origin is unique or corroborated.", "navigator_attribute_distinct_values": ["Corroborated", "Unique"], "navigator_distinct_values_enabled": true}, "app_name": {"caption": "Application Name", "description": "The name or title of the application, representing its identity and providing users with a recognizable label.\nFor example 'chrome'.", "group": "entity_specific", "type": "string", "candidate_key": true, "data_structure": "list", "category": "General Information", "navigator_attribute_description": "The name or title of the application, representing its identity and providing users with a recognizable label.\nFor example 'chrome'.", "navigator_attribute_sample_data_values": [["delta industrial automation pmsoft"], ["selenium portal"], ["libpython3.10 minimal for linux"], ["vuelidate"], ["server center"], ["libflac8 for linux"], ["socket.io"], ["commons text"], ["view planner"], ["tpeditor"]], "navigator_sample_values_enabled": true}, "app_vendor": {"caption": "Application Vendor", "description": "The name or identifier of the vendor or provider associated with the entity application.\nFor example 'McAfee Australia'.", "group": "entity_specific", "type": "string", "category": "General Information", "navigator_attribute_description": "The name or identifier of the vendor or provider associated with the entity application.\nFor example 'McAfee Australia'.", "navigator_attribute_sample_data_values": ["skype", "criticalmanufacturing", "dbd-mysql_project", "qualys", "mapbox", "iobit", "gamespy", "express-restify-mongoose_project", "kitfox", "swagger"], "navigator_sample_values_enabled": true}, "app_version": {"caption": "Application Version", "description": "The information about the changes, updates, and improvements made in a specific version of software or an application.\nFor example '0.9.3-2ubuntu2.3'.", "group": "entity_specific", "type": "string", "category": "General Information", "navigator_attribute_description": "The information about the changes, updates, and improvements made in a specific version of software or an application.\nFor example '0.9.3-2ubuntu2.3'.", "navigator_attribute_sample_data_values": ["5.53.979", "2.47", "4.39.532", "4.09.528", "2.78.266", "8.61.858", "7.65.113", "3.09.752", "1.56", "2.89.825"], "navigator_sample_values_enabled": true}, "category": {"caption": "License", "description": "Categories to help classify applications based on their origin, development approach, and licensing model.\nFor example Off the Shelf-Customised, In house Development , Opensource.\n\nOff the Shelf-Customised- Refers to commercial software products that are developed by third-party vendors and available for purchase or subscription. These applications are designed to be broadly applicable but can be customized to meet specific needs of an organization.\n\nIn-house Development- Refers to software applications that are developed internally by an organization's own development team. These applications are built from scratch to meet the unique requirements and specifications of the organization.\n\nOpen Source- Software with source code that anyone can inspect, modify, and enhance. These applications are often developed collaboratively and are available for free under licenses that allow users to use, modify, and distribute the software.", "group": "entity_specific", "type": "string", "category": "Categorization", "navigator_attribute_description": "Categories to help classify applications based on their origin, development approach, and licensing model.", "navigator_attribute_distinct_values": ["Opensource", "Off The Shelf Customised", "Off The Shelf"], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "risk_category": {"caption": "Risk Category", "description": "A specific categorisation or classification of the application, potentially used for internal organizational purposes or alignment with a particular framework or system.\nFor example CAT 1,CAT 2,CAT 3,CAT 4.", "group": "entity_specific", "type": "string", "category": "Categorization", "navigator_attribute_description": "A specific categorisation or classification of the application, potentially used for internal organizational purposes or alignment with a particular framework or system.\nCAT 1 is the highest risk category, and CAT 4 is the lowest.", "navigator_attribute_distinct_values": ["cat 3", "cat 4", "cat 1", "cat1", "cat2", "cat 2", "cat3"], "navigator_distinct_values_enabled": true}, "criticality": {"caption": "Mega Application Criticality", "description": "An assessment of the importance or critical nature of the Mega Application.\nIt is boolean value indicator.", "group": "entity_specific", "type": "string", "ui_visibility": false}, "operational_status": {"caption": "Operational Status", "description": "The current operational state of an entity to detemine whether the entity is active or disabled respective to MEGA.\nIt is based on the application lifecycle value.\nIf lifecycle is not retired then application is active else disabled.\nPossible values are Active,Disabled.", "group": "entity_specific", "type": "string", "category": "Lifecycle and Status", "navigator_attribute_description": "The current operational state of an entity to detemine whether the entity is active or disabled respective to MEGA.", "navigator_attribute_distinct_values": ["Active", "Disabled"], "navigator_distinct_values_enabled": true}, "lifecycle": {"caption": "Application Lifecycle", "description": "Various stages and phases that a software application undergoes from its conception to retirement.\nUnderstanding the lifecycle of an application helps organizations manage their software assets effectively, ensuring that applications are maintained, supported, and eventually retired in a controlled manner.\nExamples are \n\nMainstream - During this phase, the application is actively used and supported. It receives regular updates, including bug fixes, performance improvements, and new features.,\nRetired- The application is no longer in use and is officially decommissioned. This phase involves data migration, system shutdown, and archival of necessary data.", "group": "entity_specific", "type": "string", "category": "Lifecycle and Status", "navigator_attribute_description": "Various stages and phases that a software application undergoes from its conception to retirement.", "navigator_attribute_distinct_values": ["Retired", "Mainstream"], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "derived_criticality": {"caption": "Application Criticality", "description": "Defines the critical nature of application.\nIt is based on risk category.\nReturns true for CAT1 and CAT2 applications. Else False.", "group": "entity_specific", "type": "string", "category": "Risk Information", "navigator_attribute_description": "Defines the critical nature of application.\nReturns true for CAT1 and CAT2 applications. Else false.", "navigator_attribute_distinct_values": ["false", "true"], "navigator_distinct_values_enabled": true}, "internet_facing": {"caption": "Internet Facing", "description": "Defines the accessibility and deployment scope of the application.\nIt is boolean value indicator.\nTrue indicates that the application is external facing or internet facing and else false.", "group": "entity_specific", "type": "string", "category": "Risk Information", "navigator_attribute_description": "Defines the accessibility and deployment scope of the application.\nIt is boolean value indicator.\ntrue indicates that the application is external facing or internet facing and else false.", "navigator_attribute_distinct_values": [false, true], "navigator_distinct_values_enabled": true}, "retired_date": {"caption": "Retired Date", "description": "The date on application, was officially retired or decommissioned.\nIt is based on the application lifecycle value.\nIf the value of lifecycle is retired then that corresponding date is taken as retired date of the application.", "group": "entity_specific", "type": "timestamp", "category": "Lifecycle and Status", "navigator_attribute_description": "The date on application, was officially retired or decommissioned.", "navigator_sample_format": 1719914190000}, "sensitive_information": {"caption": "Sensitive Information", "description": "A boolean indicator or categorization field denoting whether the entity contains sensitive information.\nIf the value denotes true then,sensitive information is present and if it is false then no sensitive information is present.", "group": "entity_specific", "type": "string", "category": "Risk Information", "navigator_attribute_description": "A boolean indicator or categorization field denoting whether the entity contains sensitive information.", "navigator_attribute_distinct_values": [false, true], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "app_first_seen": {"caption": "App First Seen", "description": "", "group": "source_specific", "type": "string", "ui_visibility": false}}, "dashboard_identifier": "EI"}