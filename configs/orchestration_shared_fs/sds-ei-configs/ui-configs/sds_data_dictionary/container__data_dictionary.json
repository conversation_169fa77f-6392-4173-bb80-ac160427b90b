{"caption": "Container", "entity_classification": "Entity", "description": "Container entity is a portable unit of software that packages up code and all its dependencies which helps the application runs quickly and reliably from one computing environment to another.", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.\nIt is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": true, "category": "General Information", "internally_generated": true}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.\nFor Container Entity resource name and its primary key are the attributes that contribute to display label.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "category": "General Information"}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself.\nExamples include Host, Person, Container etc.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "category": "General Information", "internally_generated": true}, "type": {"caption": "Type", "description": "The specific category to which the container resources are classified.\nPossible distinct values include Container Registry,Serverless Container,Container Service Container and Kubernetes Container.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "category": "General Information"}, "origin": {"caption": "Origin", "description": "Data source(s) from which the entity has been extracted.\nFor example AWS,Azure,Wiz etc", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "category": "General Information", "data_structure": "list"}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity has been extracted.", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested.\nIt is the actual api name from which the data is ingested.\nSome of the examples for data feed in Container entity are AWS SH Findings,MS Azure Resource Details etc.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "category": "General Information", "type": "string", "data_structure": "list", "internally_generated": true}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data.\nThis will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "ui_visibility": true, "enable_hiding": true, "category": "General Information", "internally_generated": true}, "first_seen_date": {"caption": "First Seen", "description": "The initial observation date of the entity as inferred from available data sources.\nBy default first seen date date is calculated based on the minimum value between the last active date and the first found date of the entity.\nIf the data sources provides information regarding first seen activity of the entity for example AWS resource created date in Container, they take precedence over the default logic.", "group": "common", "type": "timestamp", "ui_visibility": true, "enable_hiding": true, "category": "General Information"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\\nDefaults to First Found.\\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "type": "timestamp", "ui_visibility": true, "enable_hiding": false, "category": "General Information", "internally_generated": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\nThis will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "ui_visibility": true, "enable_hiding": true, "category": "General Information", "internally_generated": true}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which entity was active as inferred from available data sources.\nThis date is determined by considering the maximum value of dates contributed by each data source for the entity in question.\nThis includes data such as activity logs, event timestamps, or any other indicators of the entity's recent engagement or interaction.", "group": "common", "type": "timestamp", "category": "General Information", "ui_visibility": true, "enable_hiding": false}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \nIf the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. \nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": false, "category": "General Information"}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "enable_hiding": false, "category": "General Information", "internally_generated": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "enable_hiding": false, "category": "General Information", "internally_generated": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "enable_hiding": true, "category": "General Information", "internally_generated": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "enable_hiding": true, "category": "General Information", "internally_generated": true}, "description": {"caption": "Description", "description": "Detailed explanation of the Container.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": true, "category": "General Information"}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": true, "category": "General Information"}, "location_country": {"caption": "Location Country", "description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": false, "type": "string", "category": "General Information"}, "location_city": {"caption": "Location City", "description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": false, "type": "string", "category": "General Information"}, "department": {"caption": "Department", "description": "The name of the department within the business unit.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": true, "category": "General Information"}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.\nIt is determined based on number of sources that gets resolved for each entity.", "group": "common", "type": "integer", "ui_visibility": true, "range_selection": true, "enable_hiding": true, "internally_generated": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "The key fields that are considering for updating the last updated date of an entity.", "group": "common", "type": "string", "ui_visibility": false, "data_structure": "struct", "enable_hiding": true, "internally_generated": true}, "infrastructure_type": {"caption": "Deployment Type", "description": "Indicates the deployment environment of a device, specifying whether it is hosted on a cloud platform (e.g., AWS, Azure, Google Cloud) or located on-premise within an organization's data center. Examples include 'Cloud' and 'On-Premise'.\n", "type": "string", "group": "enrichment", "category": "Asset Identification", "navigator_attribute_description": "Indicates if a device is hosted on a cloud platform (e.g., AWS, Azure, Google Cloud) or on-premise in a local data center.", "navigator_attribute_distinct_values": ["Cloud", "On-Premise"], "navigator_distinct_values_enabled": true}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "type": "integer", "ui_visibility": false, "enable_hiding": true, "range_selection": true}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "description": "Indicates the number of days after which the cloud entity become inactive.", "group": "common", "type": "integer", "ui_visibility": false, "enable_hiding": true}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "enable_hiding": true, "type": "string", "category": "General Information"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Indicates the name of cloud service provider within which the cloud asset has been hosted.\nDistinct cloud providers present are AWS and Azure.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "account_id": {"caption": "Cloud Account ID", "description": "Unique identifier associated with an account in a cloud provider.\nThis ID is used to distinguish one account from another within the cloud provider's system.\nFor example '***********'.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "region": {"caption": "Cloud Region", "description": "Geographic location where a cloud provider establishes data centers and infrastructure to offer its services.\nThese regions are essentially physical locations with clusters of servers, storage systems, networking equipment, and other resources that power the cloud platform.\nFor example 'eu-west-1', 'ca-central-1'.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "resource_id": {"caption": "Cloud Resource ID", "description": "A unique ID assigned to a specific resource by the respective cloud provider.\nFor example '0b2b9e0b-8036-460a-9f4e-xxxxxxxxx'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "candidate_key": true, "data_structure": "list", "category": "Cloud Resource Identification"}, "resource_name": {"caption": "Cloud Resource Name", "description": "Human-readable unique name assigned to each cloud resource.\nResource names typically adheres to specific naming constraints based on the resource type, cloud provider etc and might include alphanumeric characters, hyphens, and underscores.\nExamples are aws-athena-query-results-soln-aaa-xxx,vm-pfsense-xx-yy.", "group": "entity_specific", "type": "string", "ui_visibility": true, "candidate_key": true, "data_structure": "list", "category": "Cloud Resource Identification"}, "native_type": {"caption": "Cloud Native Type", "description": "It denotes the specific cloud service designated by the Cloud Provider, typically identified by a prefix indicating the provider (such as AWS or Azure), followed by the service name as documented officially.\nNative types are either hardcoded or standardized using the type field sourced from available data.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource Identification"}, "operational_state": {"caption": "Cloud Last Known Operational State", "description": "Last known operational status of the resource. The possible distinct values are 'Active' and 'Inactive'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource State and Security"}, "environment": {"caption": "Environment", "description": "Metadata tag to categorize resources based on the environment they are deployed to.\nIt helps organizations manage and organize their resources more efficiently by providing a way to identify resources based on their deployment environment, such as development, testing, staging, or production.\nThese fields are extracted from inside the tags.\nExamples are devops,qaregress etc", "group": "entity_specific", "type": "string", "ui_visibility": true}, "zone_availability": {"caption": "Cloud Zone Availability", "description": "Specifies whether the resource is implemented in a single or multiple availability zones within the region. The possible distinct values are 'Single', 'Multiple','Regional' and 'Not Applicable'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource Identification"}, "encryption_status": {"caption": "Encryption Status", "description": "Encryption status refers to whether data associated with a particular resource is currently secured through encryption. The possible distinct values are 'True' and 'False'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource State and Security"}, "private_ip": {"caption": "Private IP", "description": "The set of private IP addresses that are currently assigned to the cloud resource.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Cloud Resource Identification"}, "properties": {"caption": "Properties", "description": "Set of attributes that describe specific details or configurations associated with a cloud resource.", "group": "entity_specific", "type": "string", "ui_visibility": false, "data_structure": "struct"}, "billing_tag": {"caption": "Billing Tag", "description": "Custom tag field to classify the resources based on the billing criteria.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "active_operational_date": {"caption": "Active Operational Date", "description": "Date at which the operational state of the resource was found active.", "group": "entity_specific", "type": "timestamp", "category": "Cloud Resource State and Security"}, "is_accessible_from_internet": {"caption": "Internet Exposure", "description": "Indicates whether this resource is accessible from at least one internet address.\nPossible values are true or false.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource State and Security"}, "has_high_privileges": {"caption": "Has High Privileges", "description": "This property indicates whether this VM has high privilege.High permissions are derived from workflow disruption - permissions that allow the deletion of resources that might disrupt the workflow.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource State and Security"}, "has_admin_privileges": {"caption": "<PERSON> <PERSON><PERSON> Privileges", "description": "This property indicates whether this VM is has admin privilege.Admin permissions are defined as permissions that can allow an attacker persistence in the environment. These can be IAM permissions to provision, create, delete, or update identities or wild card permissions on the subscription/account/project level.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource State and Security"}, "open_to_all_internet": {"caption": "Open To All Internet", "description": "Indicates whether this resource is accessible from the public internet.\nPossible values are true or false", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource State and Security"}, "vulnerability_last_observed_date": {"caption": "Vulnerability Last Observed", "description": "The time at which the vulnerability was last observed for a resource.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true}, "aci_cluster_name": {"caption": "ACI Cluster Name", "description": "The identifier for a cluster in Azure Container Instances (ACI), which is a service that allows you to run containers without having to manage the underlying infrastructure.\nThis name is used to reference a specific ACI environment where containers are hosted, orchestrated, and scaled automatically.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Container Infrastructure and Identification"}, "container_hostname": {"caption": "Container Hostname", "description": "The hostname associated with the container deployed with in the Kubernetes Cluster.\nThis hostname is used for internal networking purposes, allowing containers to communicate with each other or other services within the same environment.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Container Infrastructure and Identification"}, "container_cpu": {"caption": "Container CPU", "description": "The CPU allocated to this container instance.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Container Resource Allocation"}, "container_ecs_task_arn": {"caption": "Container ECS Task ARN", "description": "The Container ECS Task ARN is a globally unique identifier assigned to a specific task running on Amazon ECS. This ARN helps identify and manage the container instance associated with a particular ECS task, which is part of the Amazon ECS orchestration system.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Container Infrastructure and Identification"}, "is_container_serverless": {"caption": "Is Container Serverless", "description": "Refers to whether this system is designed to operate in serverless environments, where the infrastructure management is abstracted away. The possible values are 'True' and 'False'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Container Security and Privileges"}, "is_container_privileged": {"caption": "Is Container Privileged", "description": "When this parameter is true, the container is given elevated privileges on the host container instance (similar to the root user). The possible values are 'True' and 'False'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Container Security and Privileges"}, "is_container_tty_enabled": {"caption": "Is Container TTY Enabled", "description": "A pseudo-terminal for a container refers to a mechanism that enables terminal-like communication between the host system and a containerized application. Refers to whether TTY (teletype terminal) is enabled for a container, allowing for interactive command-line sessions.It allows interactive command-line sessions and facilitates the input and output streams between the container and the host. The possible values are 'True' and 'False'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Container Security and Privileges"}, "is_container_root": {"caption": "Container Runs as Root", "description": "A container running as root means that the processes within the container are executed with elevated privileges, typically associated with the root user. This configuration could pose security risks, as it grants the container extensive permissions within the system. It's generally recommended to avoid running containers as the root user whenever possible to enhance security and minimize potential vulnerabilities. The possible values are 'True' and 'False'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Container Security and Privileges"}, "container_memory": {"caption": "Container Memory", "description": "The container memory represents the memory limit or memory reservation assigned to a container during its runtime. This value ensures that the container has enough RAM to run the application and its associated processes effectively. ", "group": "entity_specific", "type": "double", "ui_visibility": true, "category": "Container Resource Allocation"}, "container_application": {"caption": "Container Application", "description": "The container application is the specific software, service, or set of processes deployed within a container. This could be anything from a web server, database, or microservice to a full-fledged application stack. ", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Container Infrastructure and Identification"}, "container_volume_name": {"caption": "Container Volume Name", "description": "The volume mounts available to the container instance.Volumes provide a way for containers to store and share data beyond the container's lifecycle, allowing data to be retained even when containers are stopped or removed.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Container Resource Allocation"}, "container_port_protocol": {"caption": "Container Port Protocol", "description": "Refers to the protocol details involves protocols like HTTP, HTTPS, TCP, or UDP for inter-container communication or communication with external services.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Container Resource Allocation"}, "container_environment_variables": {"caption": "Container Environment Variables", "description": "Container Environment Variables refer to configurable parameters or values that can be set within the environment of a containerized application hosted on the cloud platform. These variables are used to store key information such as configuration settings, connection strings, or other dynamic parameters that the application may need during runtime.", "group": "entity_specific", "type": "string", "ui_visibility": false}, "container_standard_input": {"caption": "Container Standard Input", "description": "Whether this container should allocate a buffer for stdin in the container runtime. If this is not set, reads from stdin in the container will always result in EOF(End Of File). Default is false. The possible values are 'True' and 'False'.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "container_standard_input_once": {"caption": "Container Standard Input Once", "description": "Whether the container runtime should close the stdin(standard input) channel after it has been opened by a single attach. When stdin is true the stdin stream will remain open across multiple attach sessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the first client attaches to stdin, and then remains open and accepts data until the client disconnects, at which time stdin is closed and remains closed until the container is restarted. If this flag is false, a container processes that reads from stdin will never receive an EOF. Default is false. The possible values are 'True' and 'False'.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "container_privilege_escalation": {"caption": "Container Privilege Escalation", "description": "Indicates whether a container is allowed to gain additional privileges during execution. Privilege escalation occurs when a process running inside a container can acquire higher-level permissions, such as root or admin rights, which could potentially allow the container to perform sensitive or unauthorized actions on the host system or other containers. This bool directly controls if the no_new_privs flag will be set on the container process. The possible values are 'True' and 'False'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Container Security and Privileges"}, "kubernetes_flavor": {"caption": "Kubernetes Flavor", "description": "The flavor of kubernetes used as per Wiz.Distinct values are AKS and EKS.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Cloud Resource Identification"}, "container_read_only_root_filesystem": {"caption": "Container Read Only Root FS", "description": "When this parameter is true, the container is given read-only access to its root file system. The possible values are 'True' and 'False'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Container Security and Privileges"}, "associated_container_service_count": {"caption": "Count of Container Services having Containers", "description": "Number of Container Services having Containers.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "associated_container_group_count": {"caption": "Count of Container Groups having Containers", "description": "Number of Container Groups having Containers.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "associated_cloud_account_count": {"caption": "Count of Cloud Account", "description": "Number of cloud accounts associated with container.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "azure_resource_created_date": {"caption": "Azure Resource Created Date", "description": "Refers to the timestamp when the Azure resource was initially created.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_system_data": {"caption": "Azure System Data", "description": "Metadata associated with a Azure resource that provides information about the resource's lifecycle, status etc.", "group": "source_specific", "type": "string", "ui_visibility": false, "data_structure": "struct"}, "azure_tags": {"caption": "Azure Tags", "description": "Metadata elements associated with Azure resources, represented as key-value pairs.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "struct"}, "azure_resource_last_modified_date": {"caption": "Azure Resource Modified", "description": "Resource last modified time.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "azure_region": {"caption": "Azure Region", "description": "Specific geographical area where Azure data centers are located.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_availability_zone": {"caption": "Azure Availability Zone", "description": "Specific geographical area where Azure data centers are located within the region.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_admin_user_enabled": {"caption": "Azure ACR Admin User Enabled", "description": "Azure Container Registry allows an admin user to be enabled. This user has full access to all repositories within the registry. It's recommended to disable the admin account when not needed for security reasons. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_quarantine_policy_status": {"caption": "Azure ACR Quarantine Policy Status", "description": "Quarantine policies in container registries are typically used to identify and isolate images that might be considered suspicious or pose security risks. These policies help prevent potentially harmful images from being deployed or used within an environment until they have been reviewed or validated. The possible values are 'Enabled' and 'Disabled'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_trust_policy_status": {"caption": "Azure ACR Trust Policy Status", "description": "Azure Container Registry (ACR) supports content trust, which allows you to use digital signatures to verify the authenticity and integrity of container images. This feature helps ensure that the images you pull from a registry are the ones you expect. The possible values are 'Enabled' and 'Disabled'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_retention_policy_status": {"caption": "Azure ACR Retention Policy Status", "description": "Azure Container Registry allows you to configure retention policies to automatically delete images from your container registry based on certain criteria, such as image age or the number of images retained. This feature helps manage storage costs and keeps the registry tidy. The possible values are 'Enabled' and 'Disabled'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_export_policy_status": {"caption": "Azure ACR Export Policy Status", "description": "Disabling exports improves security by ensuring data in a registry is accessed solely via the dataplane ('docker pull'). Data cannot be moved out of the registry via 'acr import' or via 'acr transfer'. In order to disable exports, public network access must be disabled. The possible values are 'Enabled' and 'Disabled'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_ad_auth_as_arm_policy": {"caption": "Azure ACR ARM Token Status", "description": "Disable Azure Active Directory ARM audience tokens for authentication to your registry. Only Azure Container Registry (ACR) audience tokens will be used for authentication. This will ensure only tokens meant for usage on the registry can be used for authentication. Disabling ARM audience tokens does not affect admin user's or scoped access tokens' authentication. The possible values are 'Enabled' and 'Disabled'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_soft_delete_policy": {"caption": "Azure ACR Soft Delete Policy", "description": "It allows you to mark some records as deleted without actual erasure from the database. The possible values are 'Enabled' and 'Disabled'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_encryption_status": {"caption": "Azure ACR Encryption Status", "description": "Encryption status of the Azure container registry. The possible values are 'Enabled' and 'Disabled'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_data_endpoint_enabled": {"caption": "Azure ACR Data Endpoint Enabled", "description": "Azure Container Registry introduces dedicated data endpoints. The feature enables tightly scoped client firewall rules to specific registries, minimizing data ex-filtration concerns. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_network_rule_bypass_options": {"caption": "Azure ACR Network Rule Bypass Options", "description": "In Azure Container Registry (ACR), network rules can be configured to control access to the registry based on IP addresses. If you need to allow specific IP addresses to bypass network rules, you can do so by configuring \"Network Rule Bypass.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_zone_redundency": {"caption": "Azure ACR Zone Redundancy", "description": "Container Registry can be configured to be Zone Redundant or not. When the zoneRedundancy property for a Container Registry is set to 'Disabled', it means the registry is not Zone Redundant. Enforcing this policy helps ensure that your Container Registry is appropriately configured for zone resilience, reducing the risk of downtime during zone outages. The possible values are 'Enabled' and 'Disabled'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_acr_anonymous_pull_enabled": {"caption": "Azure ACR Anonymous Pull Enabled", "description": "Disable anonymous pull for your registry so that data is not accessible by unauthenticated user. Disabling local authentication methods like admin user, repository scoped access tokens and anonymous pull improves security by ensuring that container registries exclusively require Azure Active Directory identities for authentication. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_container_image": {"caption": "Azure Container Image", "description": "The name of the image used to create the container instance.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_container_port": {"caption": "Azure Container Port", "description": "The port number exposed within the container group.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "azure_container_state": {"caption": "Azure Container State", "description": "The state of the container instance. The possible values are 'Stopped', 'Terminated' and 'Running'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_container_gpu": {"caption": "Azure Container GPU", "description": "The SKU of the GPU resource.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_container_gpu_count": {"caption": "Azure Container GPU Count", "description": "The GPU request of this container instance.", "group": "source_specific", "type": "Integer", "ui_visibility": true, "range_selection": true}, "azure_container_host_port": {"caption": "Azure Container Host Port", "description": "The port number on the host that's used with the network binding.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_container_runs_as_root": {"caption": "Azure Container Runs as Root", "description": "Azure Container Runs as Root indicates that the processes within an AKS container are executed with elevated privileges associated with the root user. This configuration may pose security risks, as it grants the container extensive permissions within the AWS environment. It is generally advisable to avoid running containers as the root user to enhance security and minimize potential vulnerabilities in the Azure infrastructure. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_container_privileged": {"caption": "Azure Container is Privileged", "description": "When this parameter is true, the container is given elevated privileges on the host container instance (similar to the root user). The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "azure_aks_finish_date": {"caption": "Azure AKS Finish Date", "description": "", "group": "source_specific", "type": "timestamp", "ui_visibility": false}, "azure_aci_finish_date": {"caption": "Azure ACI Finish Date", "description": "", "group": "source_specific", "type": "string", "ui_visibility": false}, "azure_container_is_tty_enabled": {"caption": "Azure Container is TTY Enabled", "description": "A pseudo-terminal for a AWS container refers to a mechanism that enables terminal-like communication between the host system and a containerized application. It allows interactive command-line sessions and facilitates the input and output streams between the container and the host. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_tags": {"caption": "AWS Tags", "description": "Metadata elements associated with resources, represented as key-value pairs.", "group": "source_specific", "type": "string", "ui_visibility": false, "data_structure": "struct"}, "aws_tag": {"caption": "AWS Tag", "description": "Metadata elements associated with resources, represented as key-value pairs.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_resource_created_date": {"caption": "AWS Resource Created Date", "description": "Refers to the timestamp when the resource was initially created.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "aws_resource_configuration_change_date": {"caption": "AWS Resource Configuration Change Date", "description": "The time when the recording of configuration changes was initiated for the resource.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "aws_region": {"caption": "AWS Region", "description": "Specific geographical area where AWS data centers are located.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_availability_zone": {"caption": "AWS Availability Zone", "description": "Availability zones are separated groups of datacenters within an AWS region.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_eks_finish_date": {"caption": "AWS EKS Finish Date", "description": "", "group": "source_specific", "type": "timestamp", "ui_visibility": false}, "aws_ecr_encryption_type": {"caption": "AWS ECR Encryption Type", "description": "Type of encryption used to encrypt the contents of ecr at rest.Possible values are AES256 or KMS.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ecr_scan_on_push": {"caption": "AWS ECR Scan On Push", "description": "If set to true, ECR triggers an image scan automatically when a new image is pushed to the repository so that it can check for any vulnerabilities associated with the image.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ecr_repository_name": {"caption": "AWS ECR Repository Name", "description": "Name of the container image repository that is created to store the container images.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_ecr_image_tag_mutability": {"caption": "AWS ECR Image Tag Immutability", "description": "Determines whether the tags associated with images in a repository can be modified after the image has been pushed to the repository.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_image": {"caption": "AWS Container Image", "description": "An AWS container image is a lightweight, standalone, and executable package that encapsulates software, its dependencies, and configuration settings required to run an application.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_status": {"caption": "AWS Container Status", "description": "The last known status of the AWS container. Examples are 'stopped','activating','pending', 'provisioning' etc.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_health_status": {"caption": "AWS Container Health Status", "description": "The health status of the container. If health checks aren't configured for this container in its task definition, then it reports the health status as UNKNOWN.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_runtime_id": {"caption": "AWS Container Runtime ID", "description": "The ID of the Docker container.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_gpu_id": {"caption": "AWS Container GPU ID", "description": "The IDs of each GPU assigned to the container.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_bind_ip": {"caption": "AWS Container Bind IP", "description": "The IP address that the container is bound to on the container instance.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_port": {"caption": "AWS Container Port", "description": "The port number on the container that's used with the network binding.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "aws_container_host_port": {"caption": "AWS Container Host Port", "description": "The port number on the host that's used with the network binding.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_privileged": {"caption": "AWS Container is Privileged", "description": "When this parameter is true, the container is given elevated privileges on the host container instance (similar to the root user). The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_is_tty_enabled": {"caption": "AWS Container is TTY Enabled", "description": "A pseudo-terminal for a AWS container refers to a mechanism that enables terminal-like communication between the host system and a containerized application. It allows interactive command-line sessions and facilitates the input and output streams between the container and the host. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_network_interface_attc_id": {"caption": "AWS Container Network Interface Attachment ID", "description": "The AWS Container Network Interface Attachment Id is a unique identifier associated with the attachment of a Container Network Interface (CNI) to an Amazon Elastic Compute Cloud (EC2) instance. It serves as a reference point for managing and identifying the specific network attachment configuration for containers running on the EC2 instance within an AWS environment.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_launch_type": {"caption": "AWS Container Launch Type", "description": "The AWS Container Launch Type refers to the specific infrastructure type on which your containerized applications run in Amazon Web Services (AWS). When using AWS services like Amazon Elastic Container Service (ECS), you choose a launch type that determines the underlying compute infrastructure for your containers. The possible values are 'Fargate' and 'EC2'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "aws_container_runs_as_root": {"caption": "AWS Container Runs as Root", "description": "AWS Container Runs as Root indicates that the processes within an Amazon Web Services (AWS) container are executed with elevated privileges associated with the root user. This configuration may pose security risks, as it grants the container extensive permissions within the AWS environment. It is generally advisable to avoid running containers as the root user to enhance security and minimize potential vulnerabilities in the AWS infrastructure. The possible values are 'True' and 'False'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "vulnerability_first_observed": {"caption": "Vulnerability First Observed", "description": "The time at which the vulnerability was first observed for a resource.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true}, "wiz_id": {"caption": "Wiz ID", "description": "Unique ID assigned by Wiz.", "group": "source_specific", "type": "string", "ui_visibility": true}, "wiz_operational_state": {"caption": "Wiz Operational State", "description": "Reflects the operational state of the resource, possible values are Active,Inactive and Error.", "group": "source_specific", "type": "string", "ui_visibility": true}, "wiz_modified_date": {"caption": "Wiz Modified Date", "description": "Modified date of wiz.", "group": "source_specific", "type": "timestamp", "ui_visibility": true}, "wiz_is_default_security_context": {"caption": "Is Default Security Context As Per Wiz", "description": "A Security Context defines privilege and access control settings for a Pod or a Container (e.g., privileged mode, read/write permissions to filesystem, etc.). If the flag is set, the Pod or Container security context wasn't altered by the user, so it fallbacks on the default configuration for the given environment.", "group": "source_specific", "type": "string", "ui_visibility": true}, "wiz_active_services": {"caption": "Wiz Active Services", "description": "Refers to network ports that are in use or actively engaged by a process, service, or application.", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "list"}, "has_vulnerability_finding_count": {"caption": "Count of Vulnerability Findings", "description": "Number of vulnerability findings associated with container.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "has_open_vulnerability_finding_count": {"caption": "Count of Open Vulnerability Findings", "description": "Number of open vulnerability findings associated with container.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "associated_open_finding_count": {"caption": "Count of Open Findings", "description": "Number of open findings associated with container resource.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}}, "dashboard_identifier": "EI"}