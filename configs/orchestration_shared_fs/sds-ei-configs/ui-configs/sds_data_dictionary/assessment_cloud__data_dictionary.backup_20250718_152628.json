{"caption": "Assessment Cloud", "entity_classification": "Supporting Entity", "description": "Control assessment involves evaluating the effectiveness and implementation of security controls within an organization.", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.", "group": "common", "type": "string", "enable_hiding": true, "category": "General Information", "internally_generated": true}, "display_label": {"caption": "Display Label", "description": "The derived and \"best known\" identifier or name, based on the attribute that best uniquely identifies it. For assessment entity title, assessmentId will contribute to display label.", "group": "common", "type": "string", "enable_hiding": false, "category": "General Information"}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Host,Compliance Standard, Security Control, Assessment etc.", "group": "common", "type": "string", "enable_hiding": false, "category": "General Information", "internally_generated": true}, "type": {"caption": "Type", "description": "The type field categorizes compliance standards into distinct classifications, specifically focusing on regulatory frameworks associated with cloud service providers. Possible distinct values includes AWS Compliance Assessment, Azure Regulatory Compliance Assessment.", "group": "common", "type": "string", "enable_hiding": false, "category": "General Information"}, "origin": {"caption": "Origin", "description": "Data source(s) from which the entity has been extracted. For example AWS, Qualys etc.", "group": "common", "type": "string", "enable_hiding": false, "category": "General Information", "data_structure": "list"}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity is extracted.", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested. It is the actual api name from which the data is ingested. Some of the examples for data feed in Host entity are Qualys Host List, MS Azure AD Devices etc.", "group": "common", "examples": "", "category": "General Information", "ui_visibility": true, "enable_hiding": true, "type": "string", "data_structure": "list", "internally_generated": true}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data. This will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "enable_hiding": true, "category": "General Information", "internally_generated": true}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources. By default first seen date date is calculated based on the minimum value between the last active date and the first found date of the entity. If the data sources provides information regarding first seen activity of the entity for example AD created date in Host, they take precedence over the default logic.", "group": "common", "type": "timestamp", "enable_hiding": true, "category": "General Information"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data. Defaults to First Found. If any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "type": "timestamp", "enable_hiding": false, "category": "General Information", "internally_generated": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data. This will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "enable_hiding": true, "category": "General Information", "internally_generated": true}, "last_active_date": {"caption": "Last Active", "description": "The latest date on which the entity was active.", "group": "common", "type": "timestamp", "category": "General Information", "enable_hiding": false}, "activity_status": {"caption": "Activity Status", "description": "Latest date on which entity was active as inferred from available data sources. This date is determined by considering the maximum value of dates contributed by each data source for the entity in question. This includes data such as activity logs, event timestamps, or any other indicators of the entity's recent engagement or interaction.", "group": "common", "type": "string", "enable_hiding": false, "category": "General Information"}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "group": "common", "type": "integer", "range_selection": true, "enable_hiding": false, "category": "General Information", "internally_generated": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "group": "common", "type": "integer", "range_selection": true, "enable_hiding": false, "category": "General Information", "internally_generated": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "group": "common", "type": "integer", "range_selection": true, "enable_hiding": true, "category": "General Information", "internally_generated": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data", "group": "common", "type": "integer", "range_selection": true, "enable_hiding": true, "category": "General Information", "internally_generated": true}, "description": {"caption": "Description", "description": "Detailed explanation of the entity. For example it includes details about purpose of the assessment.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": true, "category": "General Information"}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": true, "category": "General Information"}, "location_country": {"caption": "Location Country", "description": "Country derived from the cloud region reported by cloud vendors normalized to ISO standard. For example 'South Africa'.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "category": "General Information"}, "location_city": {"caption": "Location City", "description": "City derived from the cloud region reported by cloud vendors normalized to ISO standard. For example 'Sydney'.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "category": "General Information"}, "department": {"caption": "Department", "description": "Tag used to identify or categorize the resource based on its association with a specific business department within an organization.", "group": "common", "type": "string", "ui_visibility": true, "enable_hiding": true, "category": "General Information"}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity. It is determined based on number of sources that gets resolved for each entity.", "group": "common", "type": "integer", "range_selection": true, "enable_hiding": true, "internally_generated": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considered for updating the last updated date of an entity", "group": "common", "type": "string", "ui_visibility": false, "data_structure": "struct", "enable_hiding": true, "internally_generated": true}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the cloud entity become inactive. For example 1 day.", "group": "common", "type": "integer", "ui_visibility": false, "enable_hiding": true}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "category": "General Information", "enable_hiding": true, "type": "string"}, "assessment_id": {"caption": "Assessment ID", "description": "Unique Identifier of the assessment. For example, In azure: 18bf29b3-a844-e170-2826-4e95d0ba4dc9, In AWS: securityhub-access-keys-rotated-fbbbbcb9. ", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "title": {"caption": "Title", "description": "Title refers to the name or description of a specific assessment. It summarizes the security issue or best practice that the assessment has identified, helping users quickly understand what aspect of their environment needs attention. For example, a title might be \"Enable Multi-Factor Authentication\" or \"Unencrypted Data Storage Detected,\" indicating the security concern or action to be taken.", "group": "enrichment", "type": "string", "ui_visibility": true, "category": "General Information"}, "tenant_id": {"caption": "Organization ID", "description": "The unique identifier of the Azure Active Directory instance or the unique identifier (ID) of an organization in AWS. For example in azure: 'd9b72e31-456c-4f8f-a367-e7f01234abcd' and in aws: 'o-abc123xyz'. ", "group": "entity_specific", "type": "string", "ui_visibility": true}, "associated_standards": {"caption": "Associated Standard", "description": "Complance Standards to which the assessment is associated with or belong to. For example 'MICROSOFT CLOUD SECURITY BENCHMARK'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Compliance and Security"}, "associated_controls": {"caption": "Associated Control", "description": "Security Controls to which the assessment is associated with or belong to. For example 'DP.5'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Compliance and Security"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud service provider to which the assessment belongs. Eg. AWS, Azure", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "assessment_severity": {"caption": "Assessment Severity", "description": "Level of impact or seriousness of an assessment. The possible values are Critical, High, Low, Medium, Informational.", "group": "enrichment", "type": "string", "ui_visibility": true, "category": "General Information"}, "status": {"caption": "Status", "description": "Operational status of an assessment. The possible values are 'Enabled' and 'Disabled'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "scope": {"caption": "<PERSON><PERSON>", "description": "Specifies the scope of the Assessment used to evaluate success and failure.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "policy_definition_id": {"caption": "Policy Definition ID", "description": "", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "affected_resource_type": {"caption": "Affected Resource Type", "description": "", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "associated_framework": {"caption": "Associated Framework", "description": "Framework to which this assessment is associated.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Affected Resources", "data_structure": "list"}, "exposure_category": {"caption": "Exposure Category", "description": "Type of exposure category the assessment belongs to. It can have values like Software Vulnerability, Control Gap and Threat Detection.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "assessment_weightage": {"caption": "Assessment Weightage", "description": "", "group": "entity_specific", "type": "string", "ui_visibility": true}, "associated_security_control_count": {"caption": "Count of Security Control", "description": "Number of security control associated with assessment.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "associated_resources_count": {"caption": "Associated Resources Count", "description": "Number of resources associated with assessment.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "associated_cloud_account_count_with_assessment": {"caption": "Associated Cloud Account Count", "description": "Number of Cloud Account associated with assessment.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}}, "dashboard_identifier": "EI"}