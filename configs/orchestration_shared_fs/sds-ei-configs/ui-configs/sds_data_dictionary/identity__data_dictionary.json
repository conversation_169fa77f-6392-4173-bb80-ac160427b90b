{"caption": "Identity", "entity_classification": "Entity", "description": "The Inventory Dictionary defines attributes and includes references to the events and objects in which they are used.", "navigator_enabled": true, "navigator_description": "\n# Identity Table Summary\n\n## Overview\nThe Identity table serves as the central repository for all authentication entities within the cybersecurity environment, tracking both human and non-human identities across various systems. It provides comprehensive visibility into authentication methods, access patterns, security configurations, and risk factors associated with identities that access organizational resources.\n\n## Data Categories\n\n### Identity Classification & Attribution\n- **Identity Types**: Categorization as Human or Non-Human identities\n- **Identity Formats**: Various formats including User Principal Name, SAM Account Name, AAD Device ID, External Email\n- **Provider Details**: Identity source systems (Azure AD, Active Directory, AWS, Others)\n- **Internal Services**: Associated services like IAM Center and Cloudtrail\n- **Ownership Classification**: Corporate vs. External identity categorization\n\n### Authentication Configuration\n- **MFA Status**: Multi-factor authentication enablement and registration\n- **Authentication Methods**: Registered authentication methods (mobile phone, authenticator app, Windows Hello)\n- **Default MFA Method**: Primary second-factor authentication type\n- **Password Settings**: Password expiration configuration, requirements, last change date\n- **SSPR Status**: Self-service password reset registration\n\n### Cross-Geography Access\n- **location_accessed_flag**: Tracking of logins from multiple countries within one day\n\n### Access Patterns & Behavior\n- **Login Information**: Last login dates, password usage, and sign-in attempts\n- **Location Data**: Last logged-in location and multiple-location access patterns\n\n- **Operational Status**: Current functioning state (Active, Disabled)\n- **Activity Timeline**: First seen, last found, last active, and lifetime metrics\n\n### Identifiers & Reference Data\n- **Primary Identifiers**: AAD IDs, Employee IDs, User Principal Names\n- **Email Addresses**: Associated email identifiers\n- **Display Labels**: Human-readable identity references\n- **Identity Primary Keys**: Combined identity and provider references\n- **Unique Identifiers**: System-generated p_ids for internal reference\n\n### Risk Assessment\n- **Inherent Risk Scoring**: Composite risk score based on multiple security factors\n- **MFA Risk Evaluation**: Risk assessment based on multi-factor authentication status\n- **Machine Identity Risk**: Special scoring for non-human identities\n- **Multi-Login Risk**: Assessment of risks from geographic access patterns\n- **Password Configuration Risk**: Evaluation of password policy implementation\n- **Ownership Risk**: Risk scores based on identity governance and control\n\n### Relationship Mapping\n- **Account Associations**: Count of linked accounts\n- **Service Connections**: Associated internal services\n- **Data Source Integration**: Original systems contributing identity data\n\n### Organizational Context\n- **Location Information**: Country and city data\n- **Business Unit**: Organizational placement\n- **Department**: Functional grouping within the organization\n\n### Data Provenance\n- **Origin Systems**: Source platforms (Azure AD, Active Directory, AWS)\n- **Data Source Details**: Specific API endpoints and data feeds\n- **Origin Contribution Type**: Whether data is unique or corroborated\n- **Fragment Counts**: Number of partial records consolidated for each identity\n\n## Key Features\n\n1. **Comprehensive Identity Catalog**: Unified repository of all authentication entities regardless of type or source\n\n2. **Authentication Security Assessment**: Detailed tracking of MFA enablement, methods, and password policies\n\n3. **Behavioral Analysis**: Monitoring of access patterns including geographic anomalies and multi-location access\n\n4. **Risk-Based Evaluation**: Multi-dimensional risk scoring across various security factors\n\n5. **Temporal Tracking**: Complete timeline from creation through latest activity\n\n6. **Cross-Platform Integration**: Consolidation of identity data from cloud, on-premise, and third-party systems\n\n7. **Identity Governance Insights**: Visibility into corporate vs. external identities and their security configurations\n\n8. **Relationship Context**: Mapping to accounts and services within the environment\n\nThis Identity table forms a critical component of the security analytics platform, providing the authentication context needed for comprehensive security risk assessment, access governance, and threat detection focusing on identity-based attacks.\n", "navigator_graph_node_description": "The Identity entity represents a digital persona within an organization's IT infrastructure, focusing on authentication, authorization, and access management. It includes both human and non-human identities (e.g., service accounts, applications, devices).Key attributes include unique digital identifiers (User Principal Name, AAD Object ID), authentication details (MFA status, sign-in activity), and lifecycle tracking (account creation, last active timestamp).It integrates with identity providers like Azure AD, Active Directory, and AWS IAM to enable risk assessment and security monitoring. Identity management is distinct from personnel tracking, focusing solely on system access and security controls.", "navigator_entity_description": "\\nAn Identity in cybersecurity represents a digital entity that interacts with an organization's IT infrastructure, focusing exclusively on system-level access, authentication, and authorization. \\nIt applies to both human and non-human entities (e.g., users, service accounts, applications, and devices) and plays a critical role in access control and security monitoring.\\n\\nIdentity strictly pertains to system authentication and authorization processes, and DOES NOT include employment details, organizational affiliations, or asset ownership.\\n\\nKey Attributes Defining an Identity:\\n1. Digital Identification:\\nAn Identity is uniquely recognized in digital systems by authentication-related attributes, such as:\\n\\nExamples: aad_object_id, sam_account_name, oauth_client_id, User Principal Name (UPN), AAD Device ID.\\nNot Included: HR identifiers such as employee ID, job title, department, or business unit.\\n2. Classification:\\nIdentities are categorized based on system-level properties, such as:\\n\\nType: Human vs. Non-Human (e.g., service accounts, applications, IoT devices).\\nFormat: Cloud-based, on-premises, federated identity, machine accounts.\\nNot Included: Employment status, organizational roles, or reporting structures.\\n3. Authentication & Authorization:\\nAttributes related to verifying and controlling system access, including:\\n\\nAuthentication: Multi-factor authentication (MFA) status, sign-in activity (e.g., successful/failed logins).\\nAuthorization: Access roles, privileges, sign-in frequency, access across locations.\\nNot Included: Employee job roles, HR-related access policies, or employment history.\\n4. Lifecycle Tracking:\\nTracking an identity\\u2019s presence within systems, such as:\\n\\nIncluded: Account creation date, last active timestamp, operational status, expiration policies.\\nNot Included: Hire/termination dates, job transfers, or HR system records.\\n5. Risk Assessment & Security Monitoring:\\nSecurity aspects related to identity usage and authentication, such as:\\n\\nIncluded: Risk scores (e.g., MFA risk score), anomaly detection, exposure levels, unauthorized access attempts.\\nNot Included: Department-based risk profiling, employment risk analysis, or business unit compliance.\\n6. Integration with Identity Providers:\\nIdentity spans across various authentication platforms, including:\\n\\nExamples: Azure AD (AAD ID), Active Directory (Distinguished Name), AWS IAM (Role ARN).\\nNot Included: HR systems, personnel management platforms, or employee data repositories.\\nCommon Ways Identity Is Referred To:\\nBy non-technical users: \\'login,\\' \\'username,\\' \\'computer account.\\'\\nBy IT staff: \\'IAM entity,\\' \\'machine identity,\\' \\'service account.\\'\\nBy cybersecurity professionals: \\'authentication principal,\\' \\'privileged account.\\'\\nPlatform-specific terms: \\'Azure AD user,\\' \\'AWS IAM role.\\'\\nKey Differentiation from the Person Entity:\\nIdentity focuses solely on system access and authentication, while Person relates to employment details, organizational roles, and asset ownership.\\n\\nQueries relevant to Identity must center around access control and authentication, NOT employment or organizational affiliations.\\n\\nQueries Relevant to Identity (Examples):\\n\\'Disabled identities in MS Active Directory with recent activity.\\'\\n\\'Identities without MFA enrollment.\\'\\n\\'List of privileged accounts in Azure AD.\\'\\n\\'Service accounts that have accessed multiple systems recently.\\'\\n\\nKey Exclusions from Identity Entity:\\nTo ensure accurate classification, the following aspects should NOT be considered part of the Identity entity:\\n\\nEmployment details: Employee ID, job title, department, reporting manager.\\nOrganizational hierarchy: Business units, cost centers, reporting structures.\\nAsset ownership: Devices or resources assigned to individuals.\\nLocation or company-specific attributes: Physical location, company affiliation.\\nUser status in HR systems: Employment termination, contract types, department membership.\\n\\nAdditionally the table contains\\n**has_account_count**:Number of accounts linked to the identity\\n", "navigator_examples": ["User Query: 'Show active accounts in active directory' Output: 'identity'", "User Query: 'Find all active identities' Output: 'identity'"], "extends": "", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.", "examples": ["0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e", "112bfd47e969ccaad185b1f087dd9c8b6e23bf6f882ccb59343af3476159e8cc"], "group": "common", "enable_hiding": true, "type": "string", "internally_generated": true, "category": "General Information", "navigator_attribute_description": "Unique identifier for each Identity, generated using the primary key, origin, class, and attribute name."}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "examples": ["<EMAIL>", "P-111"], "group": "common", "enable_hiding": false, "type": "string", "category": "General Information", "navigator_attribute_description": "The display_label is the best-known identifier that uniquely represents an attribute.", "navigator_attribute_sample_data_values": ["ACNA\\SERVER-CFL967", "SERVER-DC-912238", "ACNA\\WORK-HZX184", "WORK-KIA111", "ACNA\\WORK-KCV500", "ACNA\\WORK-MVY529", "ACNA\\WORK-GHF895", "ACNA\\WORK-IDW402", "REBECCA MAYER", "ACNA\\SERVER-GTW620"], "navigator_sample_values_enabled": true}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Host, Person, Vulnerability etc.", "examples": ["Host", "Person", "Identity", "Vulnerability"], "group": "common", "enable_hiding": false, "internally_generated": true, "category": "General Information", "type": "string"}, "type": {"caption": "Type", "description": "The specific type of the Identity entity classified based on whether the identity is used by a human or not. For Identity entity, type includes Human and Non-Human.", "group": "common", "examples": ["Human", "Non-Human"], "enable_hiding": false, "type": "string", "category": "General Information", "navigator_attribute_description": "The type of Identity entity is classified as either Human or Non-Human.", "navigator_attribute_distinct_values": ["Non-Human", "Human"], "navigator_examples": ["Human", "Non-Human"], "navigator_deep_thinking_categorical": true, "navigator_examples_enabled": true, "navigator_distinct_values_enabled": true}, "origin": {"caption": "Origin", "description": "Data source(s) in which the entity was present.Eg:MS Active Directory,MS Azure AD,AWS", "examples": ["MS Intune", "Defender"], "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "category": "General Information", "navigator_attribute_description": "Origin refers to the data sources where the Identity was present, such as MS Active Directory, MS Azure AD, and AWS.", "navigator_attribute_distinct_values": [["MS Azure AD"], ["MS Active Directory"], ["MS Azure AD", "AWS Cloudtrail"], ["MS Azure AD", "AWS IAM Center"], ["MS Azure AD", "AWS Cloudtrail", "AWS IAM Center"], ["AWS IAM Users"]], "navigator_distinct_values_enabled": true}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity has been extracted.", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "internally_generated": true, "category": "General Information", "navigator_attribute_description": "Count of origin refers to the number of data sources, such as databases or APIs, from which the Identity is extracted."}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "data_structure": "list", "internally_generated": true, "category": "General Information", "navigator_attribute_description": "The data_source_subset_name refers to the API feed used for data ingestion.", "navigator_attribute_sample_data_values": [["MS Azure AD Devices"], ["MS Azure AD Users", "MS Azure AD User Registration"], ["MS Active Directory"], ["MS Azure AD Users"], ["MS Azure AD Users", "MS Azure AD Sign-in Logs", "AWS Cloudtrail ConsoleLogin", "AWS IAM Center"]], "navigator_sample_values_enabled": true}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data.\nThis will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "examples": "*************", "enable_hiding": true, "category": "General Information", "type": "timestamp", "internally_generated": true, "navigator_attribute_description": "First found date indicates when the Identity was initially discovered in the ingested data, marking the earliest observation during the inventory run."}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources.", "group": "common", "examples": "*************", "enable_hiding": true, "category": "General Information", "type": "timestamp", "navigator_attribute_description": "The first_seen_date is the initial observation date of the Identity, determined from available data sources."}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\nDefaults to First Found.\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "examples": "*************", "group": "common", "type": "timestamp", "derived_field": true, "enable_hiding": false, "internally_generated": true, "category": "General Information", "navigator_attribute_description": "The last_updated_date is the most recent date an Identity was updated based on changes to its attributes, defaulting to the first found date."}, "has_account_count": {"caption": "Count of Accounts", "description": "Number of Accounts associated with Identity.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "navigator_attribute_description": "Number of accounts linked to the identity.", "navigator_deep_thinking_numerical": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\nThis will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "examples": "*************", "enable_hiding": true, "type": "timestamp", "category": "General Information", "internally_generated": true, "navigator_attribute_description": "The last_found_date indicates the most recent observation of the Identity in the ingested data during the inventory run.", "navigator_deep_thinking_numerical": true}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which an activity has been observed for the entity.", "examples": "*************", "group": "common", "type": "timestamp", "category": "General Information", "enable_hiding": false, "derived_field": true, "navigator_attribute_description": "The last_active_date indicates the most recent date an activity was observed for the Identity."}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. The logic is as follows: If the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. When there is no information available to determine the activity of an identity, its status is recorded as No Data.For Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days.", "group": "common", "type": "string", "enable_hiding": false, "derived_field": true, "category": "General Information", "navigator_attribute_description": "The activity_status indicates if an Identity is active or inactive based on the last inventory update and last active date, with a 2-day inactivity period for Cloud sources and 180 days for others.", "navigator_attribute_distinct_values": ["Active", "Inactive"], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "examples": "5", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "internally_generated": true, "category": "General Information", "navigator_attribute_description": "The lifetime is the total number of days from the First Seen Date to the Last Active Date of the Identity."}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "examples": "10", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "internally_generated": true, "category": "General Information", "navigator_attribute_description": "Number of days since the Identity was last active, based on the difference between the last inventory update date and the inferred last active date."}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "examples": "10", "group": "common", "enable_hiding": true, "type": "integer", "internally_generated": true, "range_selection": true, "category": "General Information", "navigator_attribute_description": "Observed lifetime is the number of days an Identity appears in data sources, calculated from its first to last appearance."}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data", "examples": "10", "group": "common", "enable_hiding": true, "type": "integer", "internally_generated": true, "range_selection": true, "category": "General Information", "navigator_attribute_description": "Recency measures the number of days since the Identity was last discovered, calculated from the difference between the last inventory update date and the last found date."}, "description": {"caption": "Description", "description": "Description of the entity.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "category": "General Information", "type": "string", "navigator_attribute_description": "Short description of the Identity."}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "category": "General Information", "type": "string", "navigator_attribute_description": "A business unit is a department or team in an organization that handles specific functions, products, or markets."}, "location_country": {"caption": "Location Country", "description": "IDP provider configured country location of the identity", "group": "common", "ui_visibility": true, "enable_hiding": true, "category": "General Information", "type": "string", "navigator_attribute_description": "Country where the IDP provider is configured for identity location.", "navigator_attribute_distinct_values": ["United States of America", "India", "United Kingdom of Great Britain and Northern Ireland"], "navigator_examples": ["United Kingdom of Great Britain and Northern Ireland", "India", "United States of America"], "navigator_examples_enabled": true, "navigator_distinct_values_enabled": true}, "location_city": {"caption": "Location City", "description": "IDP provider configured city location of the identity", "group": "common", "ui_visibility": true, "enable_hiding": true, "category": "General Information", "type": "string", "navigator_attribute_description": "City location of the IDP provider for identity configuration.", "navigator_attribute_distinct_values": ["Cardiff", "Cochin", "London", "Glasgow", "Manchester", "GJA", "Belfast"], "navigator_examples": ["Cardiff", "Manchester", "Belfast", "Glasgow", "Cochin", "London", "GJA"], "navigator_examples_enabled": true, "navigator_distinct_values_enabled": true}, "department": {"caption": "Department", "description": "Name of the department within the business unit.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "category": "General Information", "type": "string", "navigator_attribute_description": "Department name within the business unit."}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.\nIt is determined based on number of sources that gets resolved for each entity.", "group": "common", "enable_hiding": true, "type": "integer", "internally_generated": true, "range_selection": true, "navigator_attribute_description": "Fragments refer to the count of partial records or pieces of evidence for an Identity, determined by the number of resolved sources."}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considering for updating the last updated date of an entity.", "examples": "{identity_provider,identity_type,account_name,account_type,status,ad_distinguished_name,ad_when_created_date,ad_account_expires_date,ad_last_password_change_date,ad_sam_account_name_with_domain,user_principal_name,iga_account_id,ad_last_login_date,ad_password_policy,ad_last_sync_date,azure_on_premises_sync_enabled_status,azure_enrollment_type,ad_security_identifier,ad_sam_account_type,ad_member_of,manager}", "group": "common", "type": "string", "ui_visibility": false, "enable_hiding": true, "internally_generated": true, "data_structure": "struct"}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "ui_visibility": false, "enable_hiding": true, "type": "integer"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "category": "General Information", "enable_hiding": true, "type": "string", "navigator_attribute_description": "Origin can be either unique or corroborated.", "navigator_attribute_distinct_values": ["Corroborated", "Unique"], "navigator_distinct_values_enabled": true}, "identity_format": {"caption": "Identity Format", "description": "Specifies the format that contributes to identity. Values include AAD Device ID,External Email,User Principal Name and SAM Account Name.", "examples": ["User", "Service"], "group": "entity_specific", "type": "string", "data_structure": "list", "category": "General Information", "navigator_attribute_description": "Identity format includes AAD Device ID, External Email, User Principal Name, and SAM Account Name.", "navigator_attribute_distinct_values": [["AAD Device ID"], ["External Email"], ["User Principal Name", "External Email"], ["SAM Account Name"], ["User Principal Name"]], "navigator_distinct_values_enabled": true}, "ownership": {"caption": "Ownership", "description": "Specifies the ownership category of the entity and classified into Corp or External.\nIdentities owned and managed within the organization are classified as Corp and the rest are marked as external ", "examples": ["Corp", "External"], "group": "entity_specific", "type": "string", "category": "General Information", "navigator_attribute_description": "Ownership is classified as Corp for entities owned and managed within the organization, and External for others.", "navigator_attribute_distinct_values": ["Corp", "External"], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "identity_provider": {"caption": "Identity Provider", "description": "Information regarding the provider of identity.Example Azure AD.", "examples": ["Azure AD"], "group": "entity_specific", "type": "string", "category": "General Information", "navigator_attribute_description": "Identity provider refers to the entity that manages user identities, such as Azure AD.", "navigator_attribute_distinct_values": ["AWS", "Other", "Active Directory", "Azure AD"], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "is_mfa_enabled": {"caption": "Is MFA Enabled", "description": "Indicates whether the identity has MFA registered or not", "examples": ["True"], "group": "entity_specific", "type": "string", "category": "Authentication Details", "navigator_attribute_description": "Indicates if the identity has multi-factor authentication (MFA) registered.", "navigator_attribute_distinct_values": [false, true], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "aad_id": {"caption": "AAD ID", "description": "Azure AD ID of the identity.", "examples": ["0a9a511d-7f2b-4e78-a346-a47d80c0e982", "ed7abac2-3603-4c41-946f-7a50cce8b2fa"], "group": "entity_specific", "type": "string", "navigator_attribute_description": "Azure AD ID representing the identity.", "navigator_attribute_sample_data_values": [["1e5bc9d7e616273ddd839473c342473e72bd0cc7"], ["user_8e8b70d2-ab32-4c37-ayzb-b1c12137876r"], ["1e5bc9d7e585249ddd808449c311449e72bd0cc7"]], "navigator_sample_values_enabled": true}, "email_id": {"caption": "Email ID", "description": "Email ID also known as an email address, is a unique identifier assigned to an individual for sending and receiving electronic messages over the internet.\nAn email ID is a combination of a unique username and a domain name, separated by the '@' symbol.\nFor example '<EMAIL>'.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Email ID is a unique identifier for sending and receiving electronic messages, formatted as '<EMAIL>', such as '<EMAIL>'.", "navigator_attribute_sample_data_values": [["<EMAIL>"], ["<EMAIL>"], ["<EMAIL>"]], "navigator_sample_values_enabled": true}, "employee_id": {"caption": "Employee ID", "description": "Employee ID collected from multiple sources.\nIt is a unique identifier assigned to person within an organization for tracking and administrative purposes.\nSuccessFactor,MS Active Directory,Saviynt,ServiceNow ITSM,Azure AD,Bamboo HR are the data sources contributing.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Employee ID is a unique identifier for tracking individuals in an organization, sourced from SuccessFactors, MS Active Directory, Saviynt, ServiceNow ITSM, Azure AD, and Bamboo HR.", "navigator_attribute_sample_data_values": [["44430630"], ["43707552"], ["43648500"]], "navigator_sample_values_enabled": true}, "identity_display_name": {"caption": "Identity Display Name", "description": "Display name for the Identity entity.", "group": "entity_specific", "type": "string", "ui_visibility": false}, "identity_primary_key": {"caption": "Identity with IDP", "description": "Identity combined with the identity provider information", "group": "entity_specific", "type": "string", "data_structure": "list", "candidate_key": true, "navigator_attribute_description": "Identity combined with identity provider information, such as a user ID from a social media account.", "navigator_attribute_sample_data_values": [["6ee8b70d3-fcc36-4cb37-4785360-ac"], ["acna\\work-vkb353:active directory"], ["6ee8b70d3-fcc36-4cb37-7610988-ac"], ["<EMAIL>:azure ad"]], "navigator_sample_values_enabled": true}, "company_email_domains": {"caption": "Organization Email Domains", "description": "Specifies the list of email domains owned by the Organization", "ui_visibility": false, "examples": ["domain.com", "domain.ai"], "group": "entity_specific", "type": "string", "data_structure": "list"}, "user_principal_name": {"caption": "User Principal Name", "description": "Sign in name used by the users when they sign in (log in) to their accounts.", "examples": ["<EMAIL>"], "group": "entity_specific", "type": "string", "candidate_key": true, "navigator_attribute_description": "User principal name is the sign-in name for users logging into their accounts.", "navigator_attribute_sample_data_values": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "navigator_sample_values_enabled": true}, "login_last_date": {"caption": "Last Login", "description": "Last date at which a login activity has happened.", "examples": ["*************", "*************"], "group": "entity_specific", "type": "timestamp", "category": "Login and Access History", "navigator_attribute_description": "Last date of login activity."}, "last_password_change_date": {"caption": "Last Password Change", "description": "Latest date at which the password is updated.", "examples": ["*************", "*************"], "group": "entity_specific", "type": "timestamp", "category": "Login and Access History", "navigator_attribute_description": "The last_password_change_date indicates the most recent date the password was updated."}, "operational_status": {"caption": "Operational Status", "description": "Operational Status of the entity derived from the source.", "examples": ["Active", "Disabled"], "group": "entity_specific", "type": "string", "category": "Login and Access History", "navigator_attribute_description": "Operational status indicates the current functioning state of the entity based on the source.", "navigator_attribute_distinct_values": ["Active", "Disabled"], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "account_never_expire": {"caption": "Account Never Expire", "description": "Indicates whether the account is configured to never expire.", "examples": ["True"], "group": "entity_specific", "type": "string", "category": "Authentication Details", "navigator_attribute_description": "Indicates if the account is set to never expire.", "navigator_attribute_distinct_values": [false, true], "navigator_distinct_values_enabled": true}, "password_not_required": {"caption": "Password Not Required", "description": "Indicates whether the account is configured to have blank password.", "examples": ["True"], "group": "entity_specific", "type": "string", "category": "Authentication Details", "navigator_attribute_description": "Indicates if the account can have a blank password.", "navigator_attribute_distinct_values": [true, false], "navigator_distinct_values_enabled": true}, "password_never_expire": {"caption": "Password Never Expire", "description": "Indicates whether password for the account is configured to never expire.", "examples": ["True"], "group": "entity_specific", "type": "string", "navigator_attribute_description": "Indicates if the account's password is set to never expire.", "navigator_attribute_distinct_values": [false, true], "navigator_distinct_values_enabled": true}, "password_last_used": {"caption": "Last Password Used Date", "description": "Latest date at which the password is used.", "examples": ["*************", "*************"], "group": "entity_specific", "type": "timestamp", "navigator_attribute_description": "The most recent date the password was used."}, "last_logged_in_location": {"caption": "Successful Login Location", "description": "Country from which latest successful login attempt was made.", "group": "entity_specific", "ui_visibility": true, "enable_hiding": true, "type": "string", "category": "Login and Access History", "navigator_attribute_description": "Country of the most recent successful login attempt.", "navigator_attribute_distinct_values": ["Turkey", "Sweden", "Australia", "Qatar", "United States of America", "Colombia", "India", "United Kingdom of Great Britain and Northern Ireland"], "navigator_distinct_values_enabled": true}, "internal_service": {"caption": "Internal Service", "description": "Internal service within the vendor for which identity is used such as IAM Center, Cloudtrail.", "examples": ["IAM Users"], "group": "entity_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Internal services like IAM Center and Cloudtrail use identity management.", "navigator_attribute_distinct_values": [["IAM Users"], ["Cloudtrail", "IAM Center"], ["IAM Center"], ["Cloudtrail"]], "navigator_distinct_values_enabled": true}, "last_signin_attempt": {"caption": "Last Signin Attempt", "description": "The last sign in attempt found in the sign in logs", "examples": ["*************"], "group": "entity_specific", "type": "timestamp", "category": "Login and Access History", "navigator_attribute_description": "The last sign-in attempt recorded in the logs."}, "location_accessed_flag": {"caption": "Multiple Location Access Flag", "description": "Indicates whether the location of sign-in is from more than 1 country on the latest day", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Login and Access History", "navigator_attribute_description": "Indicates if the sign-in location on the latest day is from multiple countries.", "navigator_attribute_distinct_values": [true, false], "navigator_deep_thinking_categorical": true, "navigator_distinct_values_enabled": true}, "location_accessed_in_one_day": {"caption": "Locations Accessed In One Day", "description": "Multiple locations accessed in one day using the identity", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "category": "Login and Access History", "navigator_attribute_description": "Accessed multiple locations in a single day using the identity.", "navigator_attribute_sample_data_values": [["IN", "SE"], ["TR"], ["IN", "DE"]], "navigator_sample_values_enabled": true}, "is_sspr_registered": {"caption": "Is SSPR Registered", "description": "Indicates whether the user has completed the registration process for Self Service Password Reset.", "group": "entity_specific", "type": "string", "category": "Authentication Details", "navigator_attribute_description": "Indicates if the user is registered for Self Service Password Reset (SSPR).", "navigator_attribute_distinct_values": [true, false], "navigator_distinct_values_enabled": true}, "auth_methods_registered": {"caption": "Authentication Methods Registered", "description": "Collection of authentication methods registered for the identity.", "group": "entity_specific", "type": "string", "data_structure": "list", "category": "Authentication Details", "navigator_attribute_description": "Registered authentication methods for the identity include examples like password, biometrics, and two-factor authentication.", "navigator_attribute_distinct_values": [["softwareOneTimePasscode"], ["mobilePhone", "microsoftAuthenticatorPush", "softwareOneTimePasscode"], ["windowsHelloForBusiness", "microsoftAuthenticatorPush", "softwareOneTimePasscode"], ["windowsHelloForBusiness", "mobilePhone", "microsoftAuthenticatorPush", "softwareOneTimePasscode"], ["mobilePhone"], ["microsoftAuthenticatorPush", "softwareOneTimePasscode"]], "navigator_distinct_values_enabled": true}, "default_mfa_method": {"caption": "Default MFA Method", "description": "The method the user selected as the default second-factor for performing multi-factor authentication.", "group": "entity_specific", "type": "string", "category": "Authentication Details", "navigator_attribute_description": "The default_mfa_method is the user-selected second-factor for multi-factor authentication.", "navigator_attribute_distinct_values": ["mobilePhone", "softwareOneTimePasscode", "microsoftAuthenticatorPush", "none"], "navigator_distinct_values_enabled": true}, "ad_last_sync_date": {"caption": "AD Last Sync Date", "description": "Latest date on which the account has synced with AD.", "examples": "*************", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The ad_last_sync_date indicates the most recent synchronization date of the account with Active Directory (AD)."}, "aad_created_date": {"caption": "AAD Created", "description": "Date when Azure AD object was created.", "examples": ["*************", "*************"], "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date of creation for Azure AD objects."}, "ad_distinguished_name": {"caption": "AD Distinguished Name", "description": "Name that uniquely identifies an entry in the AD.", "examples": "CN=802.1X (Wired) 301 Developers,OU=CorporateOld,OU=Groups,OU=Stage,OU=Locations,OU=_Corp,DC=corp,DC=safe,DC=com", "group": "source_specific", "type": "string", "navigator_attribute_description": "A name that uniquely identifies an entry in Active Directory (AD).", "navigator_attribute_sample_data_values": ["WORK-DMI235,OU=Organisation,DC=acna,DC=corp,DC=com", "WORK-SYJ357239,OU=HR,OU=Organisation,DC=acna,DC=corp,DC=com"], "navigator_sample_values_enabled": true}, "ad_member_of": {"caption": "AD Member Of", "description": "A multi-valued attribute that contains groups of which the user is a direct member, except for the primary group.", "examples": "CN=VPN,OU=DistributionUnit,OU=SharedMailbox,DC=dir;CN=ActiveDirectory,OU=DistributionUnit,OU=SharedMailbox,DC=dir;CN=InternetAccess,OU=DistributionUnit,OU=SharedMailbox,DC=dir", "group": "source_specific", "type": "string", "navigator_attribute_description": "A multi-valued attribute indicating the groups, excluding the primary group, of which the user is a direct member."}, "ad_domain": {"caption": "AD Domain", "description": "AD Domain of the Identity.", "examples": ["corp", "lyman"], "group": "source_specific", "type": "string", "navigator_attribute_description": "The AD domain identifies the network identity, such as \"example.com\".", "navigator_attribute_distinct_values": ["acna", "prevalent"], "navigator_distinct_values_enabled": true}, "ad_sam_account_name_with_domain": {"caption": "AD SAM Account Name With Domain", "description": "Logon name used by AD account.", "examples": ["corp\\7e2368c8-5e11-456c-989e-50352f1c48e0", "lyman\\MTL-JONDEJ1LA-MOB$", "PROD\\TOR-PROD-PSM-03$"], "group": "source_specific", "type": "string", "navigator_attribute_description": "Logon name for AD accounts, <NAME_EMAIL>.", "navigator_attribute_sample_data_values": ["acna\\WORK-CET394", "acna\\WORK-FJA324", "acna\\WORK-UJU319159"], "navigator_sample_values_enabled": true}, "ad_sam_account_type": {"caption": "AD SAM Account Type", "description": "A SAM Account Type is a single valued indexed attribute that uniquely defines user objects in AD.", "examples": "*********", "group": "source_specific", "type": "string", "navigator_attribute_description": "A SAM Account Type uniquely identifies user objects in Active Directory.", "navigator_attribute_distinct_values": ["MACHINE_ACCOUNT", "*********", "GROUP_OBJECT", "NON_SECURITY_GROUP_OBJECT", "NORMAL_USER_ACCOUNT", "*********"], "navigator_distinct_values_enabled": true}, "ad_created_date": {"caption": "AD Created", "description": "Date when an object, such as a user account or a group, was created within Active Directory (AD).", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when an object like a user account or group was created in Active Directory."}, "aws_account_created_date": {"caption": "AWS Created Date", "description": "Date when AWS object was created.", "examples": ["*************", "*************"], "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when an AWS account was created."}, "authentication_factors": {"caption": "Authentication Factors", "description": "Lists the various authentication factors that are enabled for the Identity.", "group": "enrichment", "type": "string", "data_structure": "list", "navigator_attribute_description": "Lists the enabled authentication factors for the Identity, such as passwords, biometrics, and security tokens.", "navigator_attribute_distinct_values": [[], ["MFA Enabled"]], "navigator_distinct_values_enabled": true}, "multi_login_score": {"caption": "Identity Multiple Login Risk Score", "description": "Risk Score derived for the Identity based on the check whether there has been multi country login for the identity on a single day.\n This potentially states the presence of a remote threat actor", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}, "mfa_score": {"caption": "Identity MFA Risk Score", "description": "Risk Score derived for the Identity based on the enablement of MFA at the Identity Provider.\nThe non-availability of this control can enable easy breach of account with only single factor", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}, "ownership_score": {"caption": "Identity Ownership Risk Score", "description": "Risk Score derived for the Identity based the ownership.\nExternal IDP increases the risk of compliance, integrity and controls in place for the identity.", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}, "password_never_expire_score": {"caption": "Identity Password Expiry Risk Score", "description": "Risk Score derived for the Identity based on whether the password is set to expire or not.\nIF the password is set not to expire, it is considered to be risk", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}, "machine_score": {"caption": "Identity Type Risk Score", "description": "Risk Score derived for the Identity based on whether identity is a machine identity or not.\nIf the identity is considered a machine identity we may have to increase the risk of that identity considering the extra risks that comes with the machine identity, such as: no MFA configuration, no detections or preventions for impersonation.", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}, "identity_inherent_score": {"caption": "Identity Inherent Risk Score", "description": "Inherent Risk Score derived for the Identity based on risk scores associated to Identity as below.\nMultiple Login:Checks whether there has been multi country login for the identity on a single day.This potentially states the presence of a remote threat actor.\nMFA:Checks on the enablement of MFA at the Identity Provider.The non-availability of this control can enable easy breach of account with only single factor.\nOwnership:External IDP increases the risk of compliance, integrity and controls in place for the identity.\nPassword Expiry:Checks whether the password is set to expire or not.If the password is set not to expire, it is considered to be risk.\nType:If the identity is considered a machine identity we may have to increase the risk of that identity considering the extra risks that comes with the machine identity, such as: no MFA configuration, no detections or preventions for impersonation.", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}}, "dashboard_identifier": "EI"}