{"caption": "Compliance Standard", "entity_classification": "Entity", "description": "Compliance standards are sets of guidelines and regulations that organizations must adhere to in order to meet specific security, privacy, and regulatory requirements.", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Knowledge Graph.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data", "group": "common", "type": "string", "category": "General Information", "internally_generated": true}, "display_label": {"caption": "Display Label", "description": "The derived and \"best known\" identifier or name, based on the attribute that best uniquely identifies it.", "group": "common", "type": "string", "category": "General Information"}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Host,Compliance Standard, Cloud Compute etc.", "group": "common", "type": "string", "category": "General Information", "internally_generated": true}, "type": {"caption": "Type", "description": "The type field categorizes compliance standards into distinct classifications, specifically focusing on regulatory frameworks associated with cloud service providers.\n\nPossible distinct values includes MS Azure Regulatory Compliance Standard, AWS Compliance Standard.", "group": "common", "type": "string", "category": "General Information"}, "origin": {"caption": "Origin", "description": "Data source(s) from which the entity has been extracted. For example AWS, Qualys etc.", "group": "common", "type": "string", "category": "General Information", "data_structure": "list"}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity has been extracted.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested. It is the actual api name from which the data is ingested. Some of the examples for data feed in Host entity are Qualys Host List, MS Azure AD Devices etc.", "group": "common", "examples": "", "ui_visibility": true, "category": "General Information", "type": "string", "data_structure": "list", "internally_generated": true}, "first_found_date": {"caption": "First Found", "description": "The date at which the entity was first observed in the ingested data.This will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "category": "General Information", "internally_generated": true}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources. By default first seen date date is calculated based on the minimum value between the last active date and the first found date of the entity.\\nIf the data sources provides information regarding first seen activity of the entity for example AD created date in Host, they take precedence over the default logic.", "group": "common", "type": "timestamp", "category": "General Information"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data. Defaults to First Found. If any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "type": "timestamp", "category": "General Information", "internally_generated": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data. This will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "type": "timestamp", "category": "General Information", "internally_generated": true}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which entity was active as inferred from available data sources. This date is determined by considering the maximum value of dates contributed by each data source for the entity in question. This includes data such as activity logs, event timestamps, or any other indicators of the entity's recent engagement or interaction.", "group": "common", "type": "timestamp", "category": "General Information"}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. If the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. For Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days. ", "group": "common", "type": "string", "category": "General Information"}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data", "group": "common", "type": "integer", "range_selection": true, "category": "General Information", "internally_generated": true}, "description": {"caption": "Description", "description": "Detailed explanation of the entity. For example it includes details about the purpose of the entity etc.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "location_country": {"caption": "Location Country", "description": "Specifies the country where the entity is located. For example 'South Africa'.", "group": "common", "examples": "", "ui_visibility": true, "type": "string", "category": "General Information"}, "location_city": {"caption": "Location City", "description": "Specifies the city where the entity is located. For example 'Sydney'.", "group": "common", "examples": "", "ui_visibility": true, "type": "string", "category": "General Information"}, "department": {"caption": "Department", "description": "Tag used to identify or categorize the resource based on its association with a specific business department within an organization", "group": "common", "type": "string", "ui_visibility": true, "category": "General Information"}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity. It is determined based on number of sources that gets resolved for each entity.", "group": "common", "type": "integer", "range_selection": true, "internally_generated": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considered for updating the last updated date of an entity", "group": "common", "type": "string", "ui_visibility": false, "data_structure": "struct", "internally_generated": true}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the cloud entity become inactive. For example 1 day.", "group": "common", "type": "integer", "ui_visibility": false}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "type": "string", "category": "General Information"}, "id": {"caption": "ID", "description": "Unique Identifier of the Compliance Standards. For example AWS FOUNDATIONAL SECURITY BEST PRACTICES V 1.0.0, NIST 800 53 V 5.0.0, MICROSOFT CLOUD SECURITY BENCHMARK etc.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "status": {"caption": "Status", "description": "The aggregated state is based on the standard's supported control states. If any of the security controls are activated, the compliance standard is enabled. Possible values include 'Enabled' and 'Disabled'.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Compliance Status"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud service provider to which the Compliance Standard belongs or enabled. Eg. AWS, Azure", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "associated_security_control_count": {"caption": "Count of Security Control", "description": "Number of security control associated with compliance standard.", "group": "enrichment", "type": "integer", "range_selection": true}, "associated_cloud_account_count_with_standard": {"caption": "Associated Cloud Account Count", "description": "Count of cloud accounts where specific compliance standard is enabled.", "group": "enrichment", "type": "integer", "range_selection": true}}, "dashboard_identifier": "EI"}