{"relationship_attributes": {"caption": "string", "is_enrichment": "boolean", "width": "integer", "description": "string", "internally_generated": "boolean", "data_structure": "string", "range_selection": "boolean", "ui_visibility": "boolean", "type": "string", "step_interval": "float", "min": "float", "max": "float", "detailed_view_hide": "boolean"}, "entity_attributes": {"caption": "string", "description": "string", "is_enrichment": "boolean", "internally_generated": "boolean", "enable_hiding": "boolean", "range_selection": "boolean", "ui_visibility": "boolean", "data_structure": "string", "type": "string", "examples": "string", "step_interval": "float", "min": "float", "max": "float", "width": "integer"}, "attributes": {"candidate_key": "boolean", "caption": "string", "internally_generated": "boolean", "data_structure": "string", "description": "string", "enable_hiding": "boolean", "examples": "string", "group": "string", "has_line_break": "boolean", "max": "float", "min": "float", "range_selection": "boolean", "step_interval": "float", "type": "string", "ui_visibility": "boolean", "width": "integer"}}