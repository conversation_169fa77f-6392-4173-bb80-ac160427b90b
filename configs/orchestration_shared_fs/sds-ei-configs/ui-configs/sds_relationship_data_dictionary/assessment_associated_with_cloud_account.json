{"name": "Assessment Associated With Cloud Account", "caption": "Assessment Associated With Cloud Account", "detail_view_caption": "Assessment Associated With Cloud Account", "sorting_columns": [{"field": "display_label", "type": "string", "desc": true}], "isInverse": false, "inverse_relationship_name": "Cloud Account Associated With Assessment", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud provider to which the account belongs. Eg. AWS, MS Azure", "type": "string", "visualization_enabled": true}, "account_status": {"caption": "Account Status", "description": "The current status of the account, whether it is active or not. It helps determine what actions or access are permitted for the account at that time. Active means the account is in good standing, and users have full access to the resources and services associated with the account. Inactive refers to an account which is non-operative in nature.", "type": "string", "visualization_enabled": true}}, "relationship_attributes": {"rel_account_id": {"caption": "Cloud Account ID", "description": "Unique Identifier of the account or subscription.", "type": "string"}, "relationship_origin": {"caption": "Origin", "description": "Sources in which the cloud account to Assessment relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the cloud account connected to Assessment.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Latest time at which the cloud account to Assessment relation is inferred from data.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the cloud account belongs to Assessment.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}}, "target_entity": "Cloud Account", "dashboard_identifier": "EI", "source_entity": "Assessment"}