{"name": "Person Has Identity", "caption": "Person Has Identity", "navigator_enabled": true, "sorting_columns": [{"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "isInverse": false, "inverse_relationship_name": "Identity Associated With Person", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "identity_provider": {"caption": "Identity Provider", "description": "Identity Provider of the identity.", "type": "string", "visualization_enabled": true}, "type": {"caption": "Identity Type", "description": "Type of identity like Person, Non-Person, etc.", "type": "string", "data_structure": "list", "visualization_enabled": true}, "ownership": {"caption": "Ownership", "description": "Specifies the ownership category of the entity.", "type": "string"}, "user_principal_name": {"caption": "User Principal Name", "description": "Sign in name used by the users when they sign in (log in) to their accounts.", "type": "string"}, "activity_status": {"caption": "Activity Status", "description": "Indicates whether the entity is active within the configured inactivity period.", "type": "string", "visualization_enabled": true}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list", "navigator_enabled": true}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the identity was first observed for the person.", "type": "timestamp", "navigator_enabled": true}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Time at which the identity was last observed for the person.", "type": "timestamp", "navigator_enabled": true}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the identity was seen for the person.", "type": "integer", "range_selection": true, "navigator_enabled": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true, "navigator_enabled": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}}, "target_entity": "Identity", "dashboard_identifier": "EI", "source_entity": "Person"}