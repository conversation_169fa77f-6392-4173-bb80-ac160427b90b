{"name": "Assessment Associated With Finding", "caption": "Assessment Associated With Finding", "detail_view_caption": "Assessment Associated With Finding", "sorting_columns": [{"field": "display_label", "type": "string", "desc": true}], "isInverse": true, "inverse_relationship_name": "Finding Associated With Assessment", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}}, "relationship_attributes": {"rel_finding_id": {"caption": "Finding ID", "description": "ID of the finding.", "type": "string"}, "rel_finding_status": {"caption": "Finding Status", "description": "Status of the finding. For eg. Open", "type": "string"}, "rel_affected_resource_id": {"caption": "Affected Resource ID", "description": "ID of the resource that is affected.", "type": "string"}, "relationship_origin": {"caption": "Origin", "description": "Sources in which the assessment to finding relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the assessment connected to finding.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Latest time at which the assessment to finding relation is inferred from data.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the assessment belongs to finding.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}}, "target_entity": "Finding", "dashboard_identifier": "EI", "source_entity": "Assessment"}