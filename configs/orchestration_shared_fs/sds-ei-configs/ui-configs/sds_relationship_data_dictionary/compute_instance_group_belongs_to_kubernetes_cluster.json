{"name": "Compute Instance Group Belongs To Kubernetes Cluster", "caption": "Compute Instance Group Belongs To Kubernetes Cluster", "sorting_columns": [{"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "relationshipCheck": [{"field": "type", "value": ["Compute Instance Group"]}, {"field": "native_type", "value": ["AWS AutoScaling Group"]}, {"field": "data_source_subset_name", "value": ["AWS Resource Details"]}], "isInverse": false, "inverse_relationship_name": "Kubernetes Cluster Has Compute Instance Group", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "resource_id": {"caption": "Cloud Resource ID", "description": "Unique identifier assigned to a specific resource.", "type": "string", "data_structure": "list"}, "type": {"caption": "Type", "description": "The specific type of the Entity.", "type": "string", "data_structure": "list", "visualization_enabled": true}, "native_type": {"caption": "Cloud Native Type", "description": "It specifies the exact cloud service named by the Cloud Provider.", "type": "string"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud provider to which the resource belongs.", "type": "string", "visualization_enabled": true}, "activity_status": {"caption": "Activity Status", "description": "Indicates whether the entity is active within the configured inactivity period.", "type": "string", "visualization_enabled": true}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the compute instance group to kubernetes cluster relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the compute instance group connected to kubernetes cluster.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Latest time at which the compute instance group to kubernetes cluster relation is inferred from data.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the compute instance group belongs to kubernetes cluster.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}}, "target_entity": "Cloud Compute", "dashboard_identifier": "EI", "source_entity": "Cloud Compute"}