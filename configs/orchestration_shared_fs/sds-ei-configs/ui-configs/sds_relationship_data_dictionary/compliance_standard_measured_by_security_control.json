{"name": "Compliance Standard Measured By Security Control", "caption": "Compliance Standard Measured By Security Control", "detail_view_caption": "Compliance Standard Measured By Security Control", "sorting_columns": [{"field": "control_id", "type": "string", "desc": true}], "isInverse": true, "inverse_relationship_name": "Security Control Measuring Compliance Standard", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}}, "relationship_attributes": {"control_id": {"caption": "Control ID", "description": "Unique Identifier of the security control", "type": "string"}, "relationship_origin": {"caption": "Origin", "description": "Sources in which the security control to compliance standards relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the security control connected to compliance standards.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Latest time at which the security control to compliance standards relation is inferred from data.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the security control belongs to compliance standards.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}}, "target_entity": "Security Control", "dashboard_identifier": "EI", "source_entity": "Compliance Standard"}