{"name": "Cloud Account Associated With Assessment", "caption": "Cloud Account Associated With Assessment", "detail_view_caption": "Cloud Account Associated With Assessment", "sorting_columns": [{"field": "display_label", "type": "string", "desc": true}], "isInverse": true, "inverse_relationship_name": "Assessment Associated With Cloud Account", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "assessment_severity": {"caption": "Assessment Severity", "description": "Level of impact or seriousness of an assessment. The possible values are Critical, High, Low, Medium, Informational.", "type": "string", "visualization_enabled": true}, "status": {"caption": "Status", "description": "Operational status of an assessment. The possible values are 'Enabled' and 'Disabled'.", "type": "string", "visualization_enabled": true}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the cloud account to cloud storage resource relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the cloud account connected to cloud storage resource.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Latest time at which the cloud account to cloud storage resource relation is inferred from data.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the cloud account belongs to cloud storage resource.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}}, "target_entity": "Assessment", "dashboard_identifier": "EI", "source_entity": "Cloud Account"}