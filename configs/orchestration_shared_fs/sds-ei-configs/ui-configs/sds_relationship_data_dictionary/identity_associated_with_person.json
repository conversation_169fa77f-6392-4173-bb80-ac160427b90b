{"name": "Identity Associated With Person", "caption": "Identity Associated With Person", "sorting_columns": [{"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "isInverse": true, "inverse_relationship_name": "Person Has Identity", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "job_title": {"caption": "Job Title", "description": "The Designation details of the employee.", "type": "string", "visualization_enabled": true}, "employee_status": {"caption": "Employee Status", "description": "Employee status of person defines whether the employee is active or terminated from a company,based on the status value.\nSources contributing to this include SuccessFactor,Bamboo HR .\nFor example 'Terminated'.", "type": "string", "visualization_enabled": true}, "business_unit": {"caption": "Business Unit", "description": "A business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "type": "string", "visualization_enabled": true}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the person was first observed with the identity", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Time at which the person was last observed with the identity.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which person was observed with the identity.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "source_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}}, "target_entity": "Person", "dashboard_identifier": "EI", "source_entity": "Identity"}