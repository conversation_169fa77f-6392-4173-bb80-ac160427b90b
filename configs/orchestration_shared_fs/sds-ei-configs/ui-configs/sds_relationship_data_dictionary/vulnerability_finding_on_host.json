{"name": "Vulnerability Finding On Host", "caption": "Vulnerability Finding On Host", "navigator_enabled": true, "detail_view_caption": "Open Vulnerability Finding On Host", "sorting_columns": [{"field": "current_status", "type": "string", "desc": true}, {"field": "recency_relationship", "type": "integer", "desc": false}, {"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "isInverse": false, "inverse_relationship_name": "Host Has Vulnerability Finding", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "type": {"caption": "Type", "description": "The specific type of the entity.", "type": "string", "data_structure": "list", "visualization_enabled": true}, "os_family": {"caption": "OS Family", "description": "Classifies the OS into its corresponding platforms such as Windows, Linux, macOS, Android, iOS.\nIt refers to a group of operating systems that share a common core or foundation.\nFor example 'Windows'.", "type": "string", "visualization_enabled": true}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "type": "string", "visualization_enabled": true}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list", "navigator_enabled": true}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the vulnerability was first seen on host as inferred from data source.", "type": "timestamp", "navigator_enabled": true}, "vulnerability_fixed_date": {"caption": "Vulnerability Fixed Date", "description": "The date when a vulnerability was fixed.", "type": "timestamp", "detailed_view_hide": true, "navigator_enabled": true}, "vulnerability_latest_open_date": {"caption": "Vulnerability latest Open Date", "description": "The date when the vulnerability was first discovered or reopened.", "type": "timestamp", "navigator_enabled": true}, "vulnerability_breach_duration": {"caption": "Vulnerability Breach Duration", "description": "Calculates the positive difference between sla_duration and a severity-based SLA threshold, defaulting to 0 if the threshold exceeds the duration.", "type": "integer", "range_selection": true, "is_enrichment": true, "enable_hiding": true, "navigator_enabled": true}, "vulnerability_patch_sla_breach_status": {"caption": "Vulnerability Patch SLA Breach Status", "description": "Indicates whether the vulnerability patch SLA has been breached (true if breach duration is greater than 0, else false).", "type": "string"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Time at which the vulnerability was last seen on host as inferred from data source.", "type": "timestamp", "navigator_enabled": true}, "lifetime_relationship": {"caption": "Duration", "description": "How long the vulnerability was present on the host.", "type": "integer", "range_selection": true, "navigator_enabled": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the vulnerability last observed on the host.", "type": "integer", "range_selection": true, "navigator_enabled": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "current_status": {"caption": "Vulnerability Finding Status", "description": "Current Status of the vulnerability on host.", "navigator_description": "Current status of vulnerability on host - 'Open' (unresolved, requires attention) or 'Closed' (resolved/patched)", "type": "string", "navigator_distinct_values_enabled": true, "navigator_attribute_distinct_values": ["Open", "Closed"], "navigator_enabled": true}, "initial_status": {"caption": "Initial Status", "description": "Initial Status of the vulnerability on host.", "type": "string", "detailed_view_hide": true, "navigator_distinct_values_enabled": true, "navigator_attribute_distinct_values": ["Open"], "navigator_enabled": true}, "software_vendor": {"caption": "Software Vendor", "description": "Software vendor is a company or individual that develops, sells, or licenses software products or services. These vendors can range from large multinational corporations to smaller boutique firms. They provide a variety of software solutions.", "type": "string", "detailed_view_hide": true}, "software_name": {"caption": "Software Name", "description": "A software name is a unique identifier or label given to a particular software application. It serves as a way to identify, reference, and distinguish the software from others.", "type": "string", "detailed_view_hide": true}, "software_version": {"caption": "Software Version", "description": "A software version is a unique identifier that indicates a specific release or iteration of a software application. It helps track changes, updates, and bug fixes made to the software over time.", "type": "string", "detailed_view_hide": true}, "software_full_name": {"caption": "Software", "description": "Concatenated form of Software Vendor, Software Name and Software Version", "type": "string"}, "software_product": {"caption": "Software Product", "description": "Concatenated form of Software Vendor and Software Name", "type": "string"}, "path_details": {"caption": "Path Details", "description": "Specify the exact path or location within the software/system where the vulnerability exists. This might include file paths, URL endpoints, or specific components/modules.", "type": "string", "data_structure": "list", "navigator_enabled": true}, "rel_archival_flag": {"caption": "Relationship Archival Flag", "description": "The Archival Flag indicates whether the relationship is set to be archived or not.", "type": "string", "is_enrichment": true, "detailed_view_hide": true, "dashboard_identifier": {"EI": {}, "Vulnerability": {}}}, "sla_duration": {"caption": "Finding Age", "description": "Duration in days between cve first seen and cve last seen on an Asset", "type": "integer", "range_selection": true, "is_enrichment": true, "enable_hiding": true, "navigator_enabled": true}}, "target_entity": "Host", "dashboard_identifier": "EI", "source_entity": "Vulnerability"}