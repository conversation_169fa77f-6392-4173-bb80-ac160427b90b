{"name": "Storage Account Has <PERSON>", "caption": "Storage Account Has <PERSON>", "detail_view_caption": "Storage Account Has <PERSON>", "sorting_columns": [{"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "relationshipCheck": [{"field": "type", "value": ["Storage Account"]}], "isInverse": true, "inverse_relationship_name": "Bucket Belongs To Storage Account", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "resource_id": {"caption": "Cloud Resource ID", "description": "Unique identifier assigned to a specific resource.", "type": "string", "data_structure": "list"}, "type": {"caption": "Type", "description": "The specific type of the Entity.", "type": "string", "data_structure": "list", "visualization_enabled": true}, "native_type": {"caption": "Cloud Native Type", "description": "It specifies the exact cloud service named by the Cloud Provider.", "type": "string"}, "cloud_provider": {"caption": "Cloud Provider", "description": "Type of cloud provider to which the resource belongs.", "type": "string", "visualization_enabled": true}, "activity_status": {"caption": "Activity Status", "description": "Indicates whether the entity is active within the configured inactivity period.", "type": "string", "visualization_enabled": true}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the storage account to bucket relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the storage account connected to bucket.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Latest time at which the storage account to bucket relation is inferred from data.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the storage account has bucket.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "source_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}}, "target_entity": "Cloud Storage", "dashboard_identifier": "EI", "source_entity": "Cloud Storage"}