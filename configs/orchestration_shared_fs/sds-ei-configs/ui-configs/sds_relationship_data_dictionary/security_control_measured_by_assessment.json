{"name": "Security Control Measured By Assessment", "caption": "Security Control Measured By Assessment", "detail_view_caption": "Security Control Measured By Assessment", "sorting_columns": [{"field": "id", "type": "string", "desc": true}, {"field": "assessment_title", "type": "string", "desc": true}], "isInverse": true, "inverse_relationship_name": "Assessment Measuring Security Control", "entity_attributes": {"id": {"caption": "Assessment ID", "description": "Unique Identifier of the assessment.", "type": "string"}, "associated_standards": {"caption": "Associated Standards", "description": "Security Standards to which the control is associated with.", "type": "string"}}, "relationship_attributes": {"assessment_title": {"caption": "Assessment", "description": "Title of the assessment.", "type": "string"}, "relationship_origin": {"caption": "Origin", "description": "Sources in which the security control to assessment relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the security control connected to assessment.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Latest time at which the security control to assessment relation is inferred from data.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the security control belongs to assessment.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}}, "target_entity": "Assessment", "dashboard_identifier": "EI", "source_entity": "Security Control"}