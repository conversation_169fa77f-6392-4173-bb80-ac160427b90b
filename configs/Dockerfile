###testing
ARG PYTHON_BASE_IMAGE="prevalentai/python:4-1-0-3.10.4-bookworm-12.10-20250428-slim"
FROM ${PYTHON_BASE_IMAGE}
USER root

WORKDIR "/opt/airflow"

RUN mkdir /opt/airflow/ei_configs
COPY ./scripts /opt/airflow/ei_configs/scripts
COPY ./documents /opt/airflow/ei_configs/documents
COPY ./orchestration_variables /opt/airflow/ei_configs/orchestration_variables
COPY ./orchestration_shared_fs /opt/airflow/ei_configs/orchestration_shared_fs
COPY ./spark_job_configs /opt/airflow/ei_configs/spark_job_configs
COPY ./config_item_type /opt/airflow/ei_configs/config_item_type
RUN python -u /opt/airflow/ei_configs/scripts/mark_internally_generated_attributes.py /opt/airflow/ei_configs
RUN python -u /opt/airflow/ei_configs/scripts/collect_properties_group.py /opt/airflow/ei_configs/spark_job_configs
#COPY ./druid_indexing_configs /opt/airflow/ei_configs/druid_indexing_configs


