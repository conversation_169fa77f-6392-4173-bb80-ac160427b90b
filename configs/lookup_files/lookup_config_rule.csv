"id","title","severity"
securityhub-subnet-auto-assign-public-ip-disabled-645335e8,Ensure subnet auto-assign public IP is disabled,Medium
securityhub-rds-resources-protected-by-backup-plan-abff738c,Ensure RDS resources are protected by backup plan,Medium
securityhub-opensearch-https-required-468a40d3,Ensure OpenSearch domains require HTTPS,High
securityhub-iam-customer-policy-blocked-kms-actions-50cd2a78,Ensure IAM customer policies do not allow blocked KMS actions,High
securityhub-opensearch-encrypted-at-rest-9036406a,Ensure OpenSearch domains are encrypted at rest,High
iam-password-policy,Ensure IAM password policy is configured,Medium
securityhub-macie-status-check-7820b01d,Ensure Amazon Macie is enabled,Medium
securityhub-elasticache-repl-grp-redis-auth-enabled-a3323457,Ensure ElastiCache replication groups have Redis AUTH enabled,High
ebs-snapshot-public-restorable-check,Ensure EBS snapshots are not publicly restorable,Medium
securityhub-elasticache-repl-grp-encrypted-at-rest-5c44afc6,Ensure ElastiCache replication groups are encrypted at rest,High
securityhub-eip-attached-4c2603ea,Ensure Elastic IPs (EIPs) are attached to Amazon EC2 instances,Medium
rds-multi-az-support,Ensure RDS instances have multi-AZ support enabled,Medium
securityhub-ec2-launch-template-public-ip-disabled-cd3c8d7d,Ensure EC2 launch templates do not have public IPs enabled,Medium
securityhub-service-vpc-endpoint-enabled-299e38e0,Ensure SecurityHub service VPC endpoint is enabled,Medium
ec2-ebs-encryption-by-default,Ensure EC2 EBS encryption by default,Medium
securityhub-alb-desync-mode-check-e0b5bb7b,Ensure ALB desync mode is set to defensive,Medium
securityhub-neptune-cluster-copy-tags-to-snapshot-enabled-fa40bb13,Ensure Neptune cluster copy tags to snapshot is enabled,Medium
iam-policy-no-statements-with-admin-access,Ensure IAM policies do not have statements with admin access,High
securityhub-rds-instance-event-notifications-configured-b933e367,Ensure RDS instance event notifications are configured,Medium
securityhub-redshift-cluster-public-access-check-3caca250,Ensure Redshift cluster does not allow public access,High
securityhub-mfa-enabled-for-iam-console-access-ff9a2ad0,Ensure MFA is enabled for IAM console access,High
securityhub-rds-multi-az-support-8e8dc81b,Ensure RDS instances have multi-AZ support enabled,Medium
securityhub-netfw-logging-enabled-7423fa2f,Ensure network firewall logging is enabled,Medium
lambda-inside-vpc,Ensure Lambda functions are inside a VPC,Medium
securityhub-api-gwv2-access-logs-enabled-5f019ed2,Ensure API Gateway V2 has access logs enabled,Medium
cw-loggroup-retention-period-check,Ensure CloudWatch log group retention period is set,Medium
securityhub-s3-bucket-level-public-access-prohibited-c58e291d,Ensure S3 bucket level public access is prohibited,High
securityhub-sns-topic-message-delivery-notification-enabled-c25c668d,Ensure SNS topic message delivery notification is enabled,Medium
securityhub-cw-loggroup-retention-period-check-b9999028,Ensure CloudWatch log group retention period is set,Medium
securityhub-netfw-deletion-protection-enabled-10e745de,Ensure network firewall deletion protection is enabled,Medium
autoscaling-group-elb-healthcheck-required,Ensure Auto Scaling groups use ELB health checks,Medium
securityhub-s3-bucket-blacklisted-actions-prohibited-7fe17836,Ensure S3 bucket blacklisted actions are prohibited,High
securityhub-dms-auto-minor-version-upgrade-check-4d04975f,Ensure DMS instances have auto minor version upgrade enabled,Medium
securityhub-neptune-cluster-encrypted-fed3a3f2,Ensure Neptune clusters are encrypted,High
securityhub-sagemaker-notebook-instance-root-access-check-e352a403,Ensure SageMaker notebook instances do not have root access,Medium
s3-bucket-public-write-prohibited,Ensure S3 bucket public write is prohibited,High
securityhub-ecs-no-environment-secrets-b1f3aae1,Ensure ECS tasks do not use environment variables for secrets,High
securityhub-elastic-beanstalk-logs-to-cloudwatch-6ba48ccc,Ensure Elastic Beanstalk logs are sent to CloudWatch,Medium
restricted-ssh,Ensure SSH access is restricted,High
emr-kerberos-enabled,Ensure EMR clusters have Kerberos enabled,Medium
securityhub-rds-cluster-encrypted-at-rest-0c863356,Ensure RDS clusters are encrypted at rest,High
securityhub-vpc-sg-open-only-to-authorized-ports-5a6bb22a,Ensure VPC security groups are open only to authorized ports,Medium
redshift-cluster-public-access-check,Ensure Redshift cluster does not allow public access,High
securityhub-secretsmanager-rotation-enabled-check-809f8b92,Ensure Secrets Manager secrets have rotation enabled,Medium
rds-cluster-multi-az-enabled,Ensure RDS clusters have multi-AZ enabled,Medium
securityhub-opensearch-logs-to-cloudwatch-a9d3be2f,Ensure OpenSearch logs are sent to CloudWatch,Medium
secretsmanager-secret-unused,Ensure Secrets Manager secrets are not unused,Medium
securityhub-redshift-cluster-maintenancesettings-check-6520be91,Ensure Redshift clusters have maintenance settings configured,Medium
ebs-in-backup-plan,Ensure EBS volumes are in a backup plan,Medium
securityhub-waf-regional-rulegroup-not-empty-c7f8507f,Ensure WAF regional rule group is not empty,Medium
api-gw-associated-with-waf,Ensure API Gateway is associated with WAF,Medium
alb-http-to-https-redirection-check,Ensure ALB HTTP to HTTPS redirection is configured,Medium
ec2-instances-in-vpc,Ensure EC2 instances are within a VPC,Medium
ec2-imdsv2-check,Ensure EC2 instances use instance metadata service version 2 (IMDSv2),High
securityhub-neptune-cluster-snapshot-encrypted-426dfdd7,Ensure Neptune cluster snapshots are encrypted,High
securityhub-rds-storage-encrypted-6ff18ef1,Ensure RDS storage is encrypted,High
securityhub-backup-recovery-point-encrypted-9f018e8c,Ensure backup recovery points are encrypted,High
securityhub-lambda-function-public-access-prohibited-26331b91,Ensure Lambda function public access is prohibited,High
securityhub-redshift-enhanced-vpc-routing-enabled-211402a9,Ensure Amazon Redshift clusters have enhanced VPC routing enabled,Medium
securityhub-iam-policy-no-statements-with-full-access-9c7f17b8,Ensure IAM policies do not have statements with full access,High
securityhub-elasticache-subnet-group-check-1b2f1a65,Ensure ElastiCache clusters are deployed within a VPC,Medium
ec2-volume-inuse-check,Ensure EC2 volumes are in use,Medium
redshift-require-tls-ssl,Ensure Redshift clusters require TLS/SSL,High
securityhub-docdb-cluster-backup-retention-check-b4e6eccf,Ensure DocumentDB clusters have backup retention enabled,Medium
securityhub-elasticsearch-in-vpc-only-c057e00f,Ensure Elasticsearch domains are within a VPC,High
codepipeline-deployment-count-check,Ensure CodePipeline deployment count is within specified limits,Medium
securityhub-s3-version-lifecycle-policy-check-8a6cbc5f,Ensure S3 buckets have versioning and lifecycle policies enabled,Medium
securityhub-elasticsearch-data-node-fault-tolerance-12dccb03,Ensure Elasticsearch data nodes have fault tolerance,Medium
securityhub-redshift-backup-enabled-77a35e9d,Ensure Amazon Redshift clusters have automated backups enabled,Medium
s3-bucket-server-side-encryption-enabled,Ensure S3 bucket server-side encryption is enabled,Medium
elasticache-redis-cluster-automatic-backup-check,Ensure ElastiCache Redis clusters have automatic backup enabled,Medium
s3-default-encryption-kms,Ensure S3 buckets have default encryption using KMS,Medium
securityhub-opensearch-data-node-fault-tolerance-b08884a5,Ensure OpenSearch data nodes have fault tolerance,Medium
cloud-trail-cloud-watch-logs-enabled,Ensure CloudTrail is integrated with CloudWatch Logs,Medium
lambda-concurrency-check,Ensure Lambda functions have appropriate concurrency limits,Medium
securityhub-dynamodb-pitr-enabled-8c0d4ddd,Ensure DynamoDB tables have point-in-time recovery (PITR) enabled,Medium
elb-acm-certificate-required,Ensure ELB uses ACM certificates,Medium
securityhub-api-gw-execution-logging-enabled-ba21105c,Ensure API Gateway execution logging is enabled,Medium
securityhub-autoscaling-launch-template-c3687956,Ensure Auto Scaling launch templates have security group,Medium
securityhub-neptune-cluster-cloudwatch-log-export-enabled-bdf1aebe,Ensure Neptune cluster has CloudWatch log export enabled,Medium
aurora-mysql-backtracking-enabled,Ensure Aurora MySQL backtracking is enabled,Medium
root-account-mfa-enabled,Ensure root account mfa is enabled,Critical
securityhub-dms-replication-task-sourcedb-logging-b282b3da,Ensure DMS replication task source database logging is enabled,Medium
sns-encrypted-kms,Ensure SNS topics are encrypted with KMS,Medium
securityhub-cmk-backing-key-rotation-enabled-43ae4225,Ensure CMK backing key rotation is enabled,Medium
ec2-instance-profile-attached,Ensure EC2 instances have an instance profile attached,Medium
rds-cluster-iam-authentication-enabled,Ensure RDS clusters have IAM authentication enabled,Medium
vpc-flow-logs-enabled,Ensure VPC flow logs are enabled for all VPCs,Medium
securityhub-macie-auto-sensitive-data-discovery-check-78a651ae,Ensure SecurityHub Macie auto sensitive data discovery check,Medium
securityhub-ecs-task-definition-log-configuration-047d138f,Ensure ECS task definitions have log configuration,Medium
securityhub-rds-pg-event-notifications-configured-8d8ec68a,Ensure RDS PostgreSQL event notifications are configured,Medium
elasticsearch-node-to-node-encryption-check,Ensure Elasticsearch node-to-node encryption is enabled,High
securityhub-opensearch-https-required-30e13e4c,Ensure OpenSearch domains require HTTPS,High
beanstalk-enhanced-health-reporting-enabled,Ensure Elastic Beanstalk environments have enhanced health reporting enabled,Medium
securityhub-ecs-fargate-latest-platform-version-b24add36,Ensure ECS Fargate tasks are using the latest platform version,Medium
s3-bucket-default-lock-enabled,Ensure S3 bucket default lock is enabled,Medium
securityhub-efs-encrypted-check-60eaf28c,Ensure EFS file systems are encrypted,Medium
securityhub-autoscaling-multiple-instance-types-62c0a866,Ensure Auto Scaling groups use multiple instance types,Medium
securityhub-vpc-flow-logs-enabled-e40e4a4a,Ensure VPC flow logs are enabled,Medium
ec2-instance-multiple-eni-check,Ensure EC2 instances do not have multiple ENIs attached,Medium
cmk-backing-key-rotation-enabled,Ensure CMK backing key rotation is enabled,Medium
securityhub-rds-snapshots-public-prohibited-cc951076,Ensure RDS snapshots are not publicly accessible,High
securityhub-cloud-trail-cloud-watch-logs-enabled-17f8b0cd,Ensure CloudTrail is integrated with CloudWatch Logs,High
securityhub-aurora-mysql-backtracking-enabled-45bf00ef,Ensure Aurora MySQL backtracking is enabled,Medium
securityhub-ecr-private-lifecycle-policy-configured-0857e5ed,Ensure ECR private repositories have lifecycle policy configured,Medium
wafv2-logging-enabled,Ensure WAFv2 logging is enabled,Medium
api-gw-cache-enabled-and-encrypted,Ensure API Gateway cache is enabled and encrypted,Medium
securityhub-elb-cross-zone-load-balancing-enabled-0040c5cc,Ensure ELB cross-zone load balancing is enabled,Medium
securityhub-opensearch-in-vpc-only-3f0206ce,Ensure OpenSearch domains are within a VPC,High
rds-logging-enabled,Ensure RDS logging is enabled,Medium
ec2-instance-detailed-monitoring-enabled,Ensure EC2 instance detailed monitoring is enabled,Medium
vpc-sg-open-only-to-authorized-ports,Ensure VPC security groups are open only to authorized ports,Medium
securityhub-opensearch-access-control-enabled-76a225f4,Ensure OpenSearch access control is enabled,High
s3-account-level-public-access-blocks-periodic,Ensure S3 account-level public access blocks are enabled periodically,Medium
securityhub-lambda-vpc-multi-az-check-80df4190,Ensure Lambda functions are deployed in VPCs across multiple AZs,Medium
securityhub-s3-bucket-default-lock-enabled-964ca84c,Ensure S3 bucket default lock is enabled,Medium
securityhub-elasticsearch-logs-to-cloudwatch-********,Ensure Elasticsearch logs are sent to CloudWatch,Medium
eks-endpoint-no-public-access,Ensure EKS endpoint has no public access,High
cloudtrail-s3-dataevents-enabled,Ensure CloudTrail is configured to log S3 data events,Medium
codepipeline-region-fanout-check,Ensure CodePipeline region fanout is checked,Medium
securityhub-s3-bucket-acl-prohibited-48c11736,Ensure S3 bucket ACLs are prohibited,High
rds-snapshots-public-prohibited,Ensure RDS snapshots are not publicly accessible,High
securityhub-efs-access-point-enforce-root-directory-efa90b68,Ensure EFS access points enforce root directory,Medium
securityhub-autoscaling-launch-config-hop-limit-43d6623b,Ensure AutoScaling launch configurations have a hop limit of 1,Medium
securityhub-ec2-stopped-instance-94e9386e,Ensure EC2 instances are not stopped,Medium
guardduty-non-archived-findings,Ensure GuardDuty non-archived findings are addressed,Medium
securityhub-elasticsearch-node-to-node-encryption-check-0959a80e,Ensure Elasticsearch node-to-node encryption is enabled,High
securityhub-dms-replication-not-public-558f97f2,Ensure DMS replication instances are not publicly accessible,Medium
securityhub-s3-bucket-public-write-prohibited-6a66afbc,Ensure S3 bucket public write is prohibited,High
cloudwatch-log-group-encrypted,Ensure CloudWatch log group is encrypted,Medium
sagemaker-notebook-instance-kms-key-configured,Ensure SageMaker notebook instance KMS key is configured,Medium
dynamodb-table-encryption-enabled,Ensure DynamoDB tables have encryption enabled,Medium
securityhub-elasticsearch-encrypted-at-rest-6b0b79e7,Ensure Elasticsearch domains are encrypted at rest,High
securityhub-elb-predefined-security-policy-ssl-check-49a7cb05,Ensure ELB predefined security policy SSL check,Medium
eks-secrets-encrypted,Ensure EKS secrets are encrypted,High
securityhub-efs-in-backup-plan-3b1d4c2d,Ensure EFS is in backup plan,Medium
securityhub-docdb-cluster-deletion-protection-enabled-137b28be,Ensure Amazon DocumentDB clusters have deletion protection enabled,Medium
alb-waf-enabled,ensure ALB WAF is enabled,Medium
securityhub-security-account-information-provided-59b42cd9,Ensure SecurityHub account information is provided,Medium
cloud-trail-encryption-enabled,Ensure CloudTrail encryption is enabled,High
securityhub-kms-cmk-not-scheduled-for-deletion-2-8d5bcde4,Ensure KMS CMK is not scheduled for deletion,High
secretsmanager-using-cmk,Ensure Secrets Manager uses CMK,Medium
securityhub-iam-password-policy-recommended-defaults-4c06da96,Ensure IAM password policy follows recommended defaults,Medium
rds-in-backup-plan,Ensure RDS instances are in a backup plan,Medium
securityhub-ec2-client-vpn-connection-log-enabled-c77a6fa5,Ensure EC2 client VPN connection logging is enabled,Medium
securityhub-iam-user-unused-credentials-check-45-21ebd0d8,Ensure IAM user credentials are disabled if unused for 45 days,Medium
ecs-task-definition-user-for-host-mode-check,Ensure ECS task definitions do not use root user for host mode,Medium
securityhub-waf-regional-rule-not-empty-81648af1,Ensure WAF regional rule is not empty,Medium
db-instance-backup-enabled,Ensure DB instance backup is enabled,Medium
securityhub-eks-cluster-supported-version-65999a97,Ensure EKS clusters are running a supported version,Medium
securityhub-wafv2-rulegroup-logging-enabled-224c884d,Ensure WAFv2 rule group logging is enabled,Medium
securityhub-vpc-vpn-2-tunnels-up-c23120af,Ensure VPC VPN connections have 2 tunnels up,Medium
ec2-security-group-attached-to-eni,Ensure EC2 security group is attached to ENI,Medium
securityhub-netfw-policy-default-action-fragment-packets-fadf847e,Ensure SecurityHub network firewall policy default action is set for fragment packets,Medium
securityhub-rds-instance-copy-tags-to-snapshots-enabled-fd1b8624,Ensure RDS instances have copy tags to snapshots enabled,Medium
rds-enhanced-monitoring-enabled,Ensure RDS instances have enhanced monitoring enabled,Medium
dynamodb-table-encrypted-kms,Ensure DynamoDB tables are encrypted with KMS,Medium
securityhub-multi-region-cloud-trail-enabled-7bb049df,Ensure multi-region CloudTrail is enabled,High
securityhub-lambda-function-settings-check-645ca914,Ensure Lambda function settings adhere to security best practices,Medium
securityhub-sagemaker-notebook-no-direct-internet-access-a1227138,Ensure SageMaker notebook instances have no direct internet access,Medium
lambda-dlq-check,Ensure Lambda functions have a dead-letter queue configured,Medium
iam-user-mfa-enabled,Ensure IAM users have MFA enabled,Medium
securityhub-s3-lifecycle-policy-check-a499a395,Ensure S3 buckets have lifecycle policies configured,Medium
securityhub-neptune-cluster-iam-database-authentication-760facf1,Ensure Neptune clusters use IAM database authentication,Medium
securityhub-mq-rabbit-deployment-mode-ee06bd45,Ensure Amazon MQ RabbitMQ brokers are deployed in a supported deployment mode,Medium
securityhub-sagemaker-notebook-instance-inside-vpc-a6181393,Ensure SageMaker notebook instance is inside VPC,Medium
securityhub-ebs-resources-protected-by-backup-plan-7be22313,Ensure EBS resources are protected by a backup plan,Medium
securityhub-lambda-inside-vpc-56e6549c,Ensure Lambda functions are inside a VPC,Medium
elasticsearch-in-vpc-only,Ensure Elasticsearch domains are within a VPC,Medium
securityhub-clb-desync-mode-check-d2fa9d07,Ensure CLB desync mode is set to defensive,Medium
securityhub-rds-instance-iam-authentication-enabled-6133f376,Ensure RDS instances have IAM authentication enabled,Medium
iam-no-inline-policy-check,Ensure IAM users do not have inline policies,Medium
securityhub-dynamodb-resources-protected-by-backup-plan-db964c50,Ensure DynamoDB resources are protected by a backup plan,Medium
securityhub-enabled,Ensure SecurityHub is enabled,Medium
securityhub-rds-cluster-event-notifications-configured-6514ad59,Ensure RDS cluster event notifications are configured,Medium
securityhub-redshift-cluster-audit-logging-enabled-febaf020,Ensure Redshift cluster audit logging is enabled,Medium
securityhub-appsync-logging-enabled-8085f60e,Ensure AppSync logging is enabled,Medium
securityhub-vpc-network-acl-unused-check-2acff834,Ensure VPC network ACLs are not unused,Medium
elb-cross-zone-load-balancing-enabled,Ensure ELB cross-zone load balancing is enabled,Medium
cloudtrail-security-trail-enabled,Ensure CloudTrail security trail is enabled,Medium
securityhub-api-gwv2-authorization-type-configured-56bb26db,Ensure API Gateway V2 has authorization type configured,Medium
securityhub-elasticache-repl-grp-encrypted-in-transit-45af9264,Ensure ElastiCache replication groups are encrypted in transit,High
securityhub-rds-aurora-mysql-audit-logging-enabled-78e03d95,Ensure RDS Aurora MySQL audit logging is enabled,Medium
no-unrestricted-route-to-igw,Ensure no unrestricted route to IGW,High
securityhub-efs-access-point-enforce-user-identity-6bef81ab,Ensure EFS access points enforce user identity,Medium
securityhub-acm-certificate-rsa-check-9617cbb5,Ensure ACM certificates use RSA keys,Medium
securityhub-api-gw-xray-enabled-3a2fd9e9,Ensure API Gateway has X-Ray tracing enabled,Medium
securityhub-alb-http-drop-invalid-header-enabled-5a1160e3,Ensure ALB drops invalid HTTP headers,Medium
securityhub-s3-bucket-mfa-delete-enabled-b54ce472,Ensure S3 bucket MFA delete is enabled,High
securityhub-rds-deployed-in-vpc-46454114,Ensure RDS instances are deployed in a VPC,High
dms-replication-not-public,Ensure DMS replication instances are not publicly accessible,Medium
securityhub-ec2-instance-managed-by-ssm-f94216f0,Ensure EC2 instance is managed by SSM,Medium
securityhub-eks-cluster-log-enabled-b2772b80,Ensure EKS cluster logging is enabled,Medium
securityhub-clb-multiple-az-87bf0687,Ensure CLB is configured to span multiple AZs,Medium
rds-automatic-minor-version-upgrade-enabled,Ensure RDS instances have automatic minor version upgrade enabled,Medium
securityhub-api-gw-ssl-enabled-95c11d9e,Ensure API Gateway has SSL enabled,High
securityhub-ssm-document-not-public-0aac55bf,Ensure SSM documents are not public,Medium
securityhub-dms-endpoint-ssl-configured-41635424,Ensure DMS endpoints are configured with SSL,High
securityhub-neptune-cluster-snapshot-public-prohibited-f455b7ef,Ensure Neptune cluster snapshots are not publicly accessible,High
securityhub-codebuild-project-logging-enabled-981c455c,Ensure CodeBuild project logging is enabled,Medium
securityhub-dynamodb-table-deletion-protection-enabled-bf30459f,Ensure DynamoDB table deletion protection is enabled,Medium
securityhub-rds-cluster-copy-tags-to-snapshots-enabled-297f1fb7,Ensure RDS cluster copy tags to snapshots is enabled,Medium
securityhub-redshift-default-db-name-check-e6d35734,Ensure Amazon Redshift clusters do not use the default database name,Medium
securityhub-ecr-private-tag-immutability-enabled-f9c4c278,Ensure ECR private tag immutability is enabled,Medium
securityhub-ecs-container-insights-enabled-cb0d48fa,Ensure ECS container insights are enabled,Medium
securityhub-neptune-cluster-multi-az-enabled-970c1dac,Ensure Neptune clusters are multi-AZ enabled,Medium
securityhub-codebuild-project-envvar-awscred-check-9bb4d8c6,Ensure CodeBuild project environment variables do not contain AWS credentials,High
dynamodb-in-backup-plan,Ensure DynamoDB tables are in a backup plan,Medium
securityhub-elasticache-auto-minor-version-upgrade-check-c6e97bc9,Ensure ElastiCache clusters have automatic minor version upgrade enabled,Medium
securityhub-redshift-require-tls-ssl-f699997f,Ensure Amazon Redshift clusters require TLS/SSL,High
securityhub-iam-root-access-key-check-e807ef66,Ensure IAM root access key is not used,High
securityhub-nacl-no-unrestricted-ssh-rdp-c4104c73,Ensure network ACLs do not allow unrestricted SSH and RDP access,High
subnet-auto-assign-public-ip-disabled,Ensure subnet auto-assign public IP is disabled,Medium
securityhub-neptune-cluster-deletion-protection-enabled-f36503f6,Ensure Neptune cluster deletion protection is enabled,High
securityhub-eks-cluster-supported-version-d020be48,Ensure EKS clusters are running a supported version,Medium
vpc-network-acl-unused-check,Ensure VPC network ACLs are used,Low
efs-in-backup-plan,Ensure EFS is in backup plan,Medium
securityhub-s3-bucket-ssl-requests-only-d8662560,Ensure S3 bucket allows SSL requests only,High
securityhub-elastic-beanstalk-managed-updates-enabled-cb22458a,Ensure Elastic Beanstalk managed updates are enabled,Medium
secretsmanager-secret-periodic-rotation,Ensure Secrets Manager secrets are periodically rotated,Medium
securityhub-api-gw-cache-encrypted-3ad3b8ea,Ensure API Gateway cache is encrypted,Medium
securityhub-rds-no-default-ports-2c7cc1af,Ensure RDS instances do not use default ports,Medium
ec2-managedinstance-association-compliance-status-check,Ensure EC2 managed instance association compliance status check,Medium
rds-instance-public-access-check,Ensure RDS instances are not publicly accessible,High
securityhub-netfw-policy-default-action-full-packets-60d8d282,Ensure SecurityHub network firewall policy default action is to drop full packets,Medium
securityhub-redshift-cluster-kms-enabled-a244b8c7,Ensure Redshift clusters have KMS encryption enabled,High
securityhub-ec2-managedinstance-patch-compliance-6ad4b25b,Ensure EC2 managed instances are compliant with patching requirements,Medium
securityhub-root-account-mfa-enabled-c0adb2fc,Ensure root account has MFA enabled,High
securityhub-ecs-task-definition-user-for-host-mode-check-769e4c4c,Ensure ECS task definitions do not use host mode networking,Medium
elb-deletion-protection-enabled,Ensure ELB deletion protection is enabled,Medium
securityhub-emr-block-public-access-00dea2d0,Ensure EMR clusters block public access,High
efs-encrypted-check,Ensure EFS file systems are encrypted,Medium
securityhub-rds-cluster-deletion-protection-enabled-a1f0ba6b,Ensure RDS cluster deletion protection is enabled,Medium
securityhub-vpc-default-security-group-closed-133e9df3,Ensure VPC default security group is closed,Medium
securityhub-ecs-service-assign-public-ip-disabled-2d3bdf29,Ensure ECS service does not assign public IP,Medium
securityhub-iam-support-policy-in-use-7324ba29,Ensure IAM support policy is not in use,Medium
iam-user-group-membership-check,Ensure IAM user is a member of at least one group,Medium
securityhub-netfw-multi-az-enabled-********,Ensure SecurityHub network firewall is enabled in multiple AZs,Medium
securityhub-access-keys-rotated-fbbbbcb9,Ensure access keys are rotated every 90 days,Medium
elastic-beanstalk-managed-updates-enabled,Ensure Elastic Beanstalk managed updates are enabled,Medium
root-account-hardware-mfa-enabled,Ensure root account has hardware MFA enabled,Critical
s3-bucket-replication-enabled,Ensure S3 bucket replication is enabled,Medium
s3-bucket-level-public-access-prohibited,Ensure S3 bucket level public access is prohibited,High
securityhub-rds-instance-public-access-check-4affaad2,Ensure RDS instances do not have public access,High
securityhub-custom-eventbus-policy-attached-6c870601,Ensure SecurityHub custom event bus policy is attached,Medium
securityhub-ec2-instance-multiple-eni-check-b9d9c9da,Ensure EC2 instances do not have multiple ENIs attached,Medium
securityhub-neptune-cluster-backup-retention-check-d84974b9,Ensure Neptune cluster backup retention is configured,Medium
encrypted-volumes,ensure EBS volumes are encrypted,High
securityhub-eks-cluster-supported-version-aaaeeb17,Ensure EKS clusters are running a supported Kubernetes version,Medium
securityhub-eks-endpoint-no-public-access-1739bb62,Ensure EKS endpoint has no public access,High
sagemaker-endpoint-configuration-kms-key-configured,Ensure SageMaker endpoint configuration has KMS key configured,Medium
securityhub-elb-connection-draining-enabled-72348d6a,Ensure ELB connection draining is enabled,Medium
iam-root-access-key-check,Ensure IAM root access key is not used,Critical
elbv2-acm-certificate-required,Ensure ELBv2 uses ACM certificates,Medium
securityhub-secretsmanager-scheduled-rotation-success-check-8711f107,Ensure Secrets Manager scheduled rotation is successful,Medium
account-part-of-organizations,Ensure account is part of AWS Organizations,Medium
s3-bucket-ssl-requests-only,Ensure S3 bucket requests use SSL,Medium
securityhub-msk-enhanced-monitoring-enabled-c10a8447,Ensure MSK clusters have enhanced monitoring enabled,Medium
securityhub-rds-automatic-minor-version-upgrade-enabled-5a69b4a6,Ensure RDS instances have automatic minor version upgrade enabled,Medium
multi-region-cloudtrail-enabled,Ensure multi-region CloudTrail is enabled,High
securityhub-netfw-policy-rule-group-associated-6ed8e9d4,Ensure network firewall policy is associated with rule group,Medium
securityhub-kinesis-stream-encrypted-c440a431,Ensure Kinesis streams are encrypted,High
redshift-cluster-kms-enabled,Ensure Redshift clusters have KMS encryption enabled,Medium
sagemaker-notebook-no-direct-internet-access,Ensure SageMaker notebook instances have no direct internet access,Medium
securityhub-sns-encrypted-kms-2f632782,Ensure SNS topics are encrypted with KMS,Medium
securityhub-s3-bucket-public-read-prohibited-68470b82,Ensure S3 bucket public read access is prohibited,High
securityhub-acm-certificate-expiration-check-71a2e9a8,Ensure ACM certificates are not expired,Medium
securityhub-iam-user-no-policies-check-3c48d59e,Ensure IAM users have no policies attached,Medium
securityhub-dax-encryption-enabled-be0e981a,Ensure DAX clusters have encryption enabled,High
securityhub-iam-user-unused-credentials-check-a42c2a3f,Ensure IAM user credentials are not unused,Medium
securityhub-guardduty-enabled-centralized-5d1a3b33,Ensure SecurityHub and GuardDuty are enabled and centralized,High
securityhub-iam-inline-policy-blocked-kms-actions-6a5412ac,Ensure IAM inline policies do not allow blocked KMS actions,High
api-gw-ssl-enabled,Ensure API Gateway has SSL enabled,High
cloud-trail-log-file-validation-enabled,Ensure CloudTrail log file validation is enabled,Medium
securityhub-autoscaling-launchconfig-requires-imdsv2-47c394e1,Ensure AutoScaling launch configurations require IMDSv2,High
securityhub-docdb-cluster-snapshot-public-prohibited-f91a55dd,Ensure Amazon DocumentDB cluster snapshots are not publicly accessible,High
securityhub-mq-active-deployment-mode-659900d2,Ensure Amazon MQ brokers are deployed in active/standby mode,Medium
securityhub-ecs-containers-nonprivileged-853bb7d8,Ensure ECS containers are not running as privileged,High
securityhub-dynamodb-autoscaling-enabled-b90c1244,Ensure DynamoDB auto-scaling is enabled,Medium
kms-cmk-not-scheduled-for-deletion,Ensure KMS CMK is not scheduled for deletion,Medium
securityhub-wafv2-webacl-not-empty-0052bd09,Ensure WAFv2 WebACL is not empty,Medium
eip-attached,Ensure EIP is attached to EC2 instance,Medium
securityhub-rds-enhanced-monitoring-enabled-ce73c5c2,Ensure RDS instances have enhanced monitoring enabled,Medium
securityhub-elasticsearch-https-required-e868b83d,Ensure Elasticsearch domains require HTTPS,High
securityhub-s3-bucket-replication-enabled-a0afa5b6,Ensure S3 bucket replication is enabled,Medium
securityhub-db-instance-backup-enabled-5869813a,Ensure RDS instances have backups enabled,Medium
securityhub-autoscaling-multiple-az-5cca3c97,Ensure Auto Scaling groups span multiple AZs,Medium
securityhub-vpc-sg-open-only-to-authorized-ports-dee76c43,Ensure VPC security groups are open only to authorized ports,Medium
securityhub-docdb-cluster-encrypted-5c5b98bf,Ensure Amazon DocumentDB clusters are encrypted,High
securityhub-redshift-default-admin-check-177531f1,Ensure Amazon Redshift clusters do not use the default admin username,Medium
securityhub-cloudformation-stack-notification-check-6c6da78b,Ensure CloudFormation stack notifications are enabled,Medium
securityhub-global-endpoint-event-replication-enabled-209f5d9d,Ensure SecurityHub global endpoint event replication is enabled,Medium
s3-bucket-versioning-enabled,Ensure S3 bucket versioning is enabled,Medium
securityhub-cloudwatch-alarm-action-check-a8429a01,Ensure Security Hub has CloudWatch alarm actions enabled,Medium
securityhub-alb-waf-enabled-feb639ee,Ensure ALB has WAF enabled,Medium
securityhub-s3-default-encryption-kms-1ecff5f2,Ensure S3 buckets have default encryption with KMS,High
securityhub-ec2-imdsv2-check-56b1e20a,Ensure EC2 instances use instance metadata service version 2 (IMDSv2),High
securityhub-dms-replication-task-targetdb-logging-55245838,Ensure DMS replication task target database logging is enabled,Medium
securityhub-ebs-snapshot-public-restorable-check-d690d6a4,Ensure EBS snapshots are not publicly restorable,High
securityhub-beanstalk-enhanced-health-reporting-enabled-50a21dae,Ensure Elastic Beanstalk enhanced health reporting is enabled,Medium
api-gw-execution-logging-enabled,Ensure API Gateway execution logging is enabled,Medium
s3-bucket-policy-grantee-check,Ensure S3 bucket policies do not allow public access,High
s3-bucket-public-read-prohibited,Ensure S3 bucket does not allow public read access,High
secretsmanager-rotation-enabled-check,Ensure Secrets Manager secrets have rotation enabled,Medium
lambda-function-public-access-prohibited,Ensure Lambda function public access is prohibited,High
securityhub-msk-in-cluster-node-require-tls-e04ff3ca,Ensure MSK in-cluster nodes require TLS,High
ec2-instance-managed-by-systems-manager,Ensure EC2 instance is managed by Systems Manager,Medium
securityhub-ecs-task-definition-pid-mode-check-d99ddde4,Ensure ECS task definitions do not share the host's process ID namespace,Medium
emr-master-no-public-ip,Ensure EMR master nodes do not have public IP addresses,Medium
securityhub-secretsmanager-secret-periodic-rotation-91269e3e,Ensure Secrets Manager secrets have periodic rotation enabled,Medium
iam-policy-no-statements-with-full-access,Ensure IAM policies do not have statements with full access,High
securityhub-netfw-stateless-rule-group-not-empty-85d67f1d,Ensure SecurityHub network firewall stateless rule group is not empty,Medium
s3-account-level-public-access-blocks,Ensure S3 account-level public access blocks,Medium
securityhub-api-gw-associated-with-waf-2251395a,Ensure API Gateway is associated with WAF,Medium
securityhub-autoscaling-launch-config-public-ip-disabled-5f2193de,Ensure Auto Scaling launch configurations do not have public IP addresses,Medium
securityhub-sqs-queue-encrypted-1f8a9e93,Ensure SQS queues are encrypted,Medium
securityhub-wafv2-logging-enabled-7161b0d0,Ensure AWS WAFv2 logging is enabled,Medium
rds-instance-deletion-protection-enabled,Ensure RDS instances have deletion protection enabled,Medium
ec2-instance-no-public-ip,Ensure EC2 instances do not have public IP addresses,Medium
securityhub-step-functions-state-machine-logging-enabled-273508a7,Ensure Step Functions state machine logging is enabled,Medium
internet-gateway-authorized-vpc-only,Ensure internet gateway is authorized for VPC only,Medium
dynamodb-throughput-limit-check,Ensure DynamoDB tables do not exceed throughput limits,Medium
securityhub-acm-pca-root-ca-disabled-ddaea556,Ensure ACM PCA root CA is disabled,High
alb-http-drop-invalid-header-enabled,Ensure ALB drops invalid HTTP headers,Medium
securityhub-rds-instance-default-admin-check-7aa9768a,Ensure RDS instances do not use the default admin username,Medium
secretsmanager-scheduled-rotation-success-check,Ensure Secrets Manager scheduled rotation is successful,Medium
securityhub-s3-bucket-logging-enabled-ce4ccdc8,Ensure S3 bucket logging is enabled,Medium
securityhub-iam-policy-no-statements-with-admin-access-a431dfba,Ensure IAM policies do not have statements with admin access,High
securityhub-s3-bucket-cross-region-replication-enabled-2954ad8f,Ensure S3 bucket cross-region replication is enabled,Medium
securityhub-elasticsearch-audit-logging-enabled-1d57e7b5,Ensure Elasticsearch audit logging is enabled,Medium
mfa-enabled-for-iam-console-access,Ensure MFA enabled for IAM console access,Medium
securityhub-rds-logging-enabled-64948d66,Ensure RDS logging is enabled,Medium
securityhub-ec2-instance-no-public-ip-f34aa552,Ensure EC2 instance has no public IP,High
securityhub-alb-http-to-https-redirection-check-0fbcabdc,Ensure ALB HTTP to HTTPS redirection is configured,Medium
securityhub-account-part-of-organizations-47434ce7,Ensure SecurityHub account is part of AWS Organizations,Medium
securityhub-cloud-trail-log-file-validation-enabled-c089674c,Ensure CloudTrail log file validation is enabled,Medium
securityhub-elb-tls-https-listeners-only-65f11e81,Ensure ELB uses TLS for HTTPS listeners only,High
s3-bucket-logging-enabled,Ensure S3 bucket logging is enabled,Medium
securityhub-cloud-trail-encryption-enabled-21dd4ba9,Ensure CloudTrail logs are encrypted at rest,High
ebs-optimized-instance,Ensure EC2 instances are EBS-optimized,Medium
redshift-backup-enabled,Ensure Redshift clusters have automated backups enabled,Medium
securityhub-docdb-cluster-audit-logging-enabled-52bd4055,Ensure Amazon DocumentDB clusters have audit logging enabled,Medium
dax-encryption-enabled,Ensure DAX encryption is enabled,Medium
api-gw-xray-enabled,Ensure API Gateway has X-Ray tracing enabled,Medium
securityhub-opensearch-audit-logging-enabled-bfca7400,Ensure OpenSearch audit logging is enabled,Medium
securityhub-iam-password-policy-prevent-reuse-check-aba713bb,Ensure IAM password policy prevents password reuse,Medium
guardduty-enabled-centralized,Ensure GuardDuty is enabled and centralized,High
rds-cluster-deletion-protection-enabled,Ensure RDS cluster deletion protection is enabled,Medium
securityhub-ecs-containers-readonly-access-7f8b9617,Ensure ECS containers have read-only access,Medium
securityhub-rds-cluster-iam-authentication-enabled-d3b175e7,Ensure RDS cluster IAM authentication is enabled,Medium
elasticsearch-logs-to-cloudwatch,Ensure Elasticsearch logs are sent to CloudWatch,Medium
securityhub-elb-logging-enabled-02f148be,Ensure ELB logging is enabled,Medium
securityhub-rds-instance-deletion-protection-enabled-7cc151c0,Ensure RDS instance deletion protection is enabled,Medium
securityhub-ec2-ebs-encryption-by-default-899cc9e9,Ensure EC2 EBS encryption by default is enabled,High
securityhub-opensearch-node-to-node-encryption-check-f00b5030,Ensure OpenSearch node-to-node encryption is enabled,High
securityhub-iam-user-mfa-enabled-6a756bf6,Ensure IAM users have MFA enabled,High
securityhub-codebuild-project-s3-logs-encrypted-583d41bb,Ensure CodeBuild project S3 logs are encrypted,High
ssm-document-not-public,Ensure SSM documents are not public,Medium
autoscaling-launch-config-public-ip-disabled,Ensure Auto Scaling launch configurations do not assign public IPs,Medium
iam-user-no-policies-check,Ensure IAM users have no policies attached,Medium
cloudformation-stack-notification-check,Ensure CloudFormation stack has SNS topic configured for notifications,Low
securityhub-waf-regional-webacl-not-empty-0533147e,Ensure WAF regional WebACL is not empty,Medium
securityhub-cloudwatch-alarm-action-enabled-check-6db8479f,Ensure CloudWatch alarm actions are enabled,Medium
securityhub-rds-cluster-auto-minor-version-upgrade-enable-02b6b2ae,Ensure RDS cluster auto minor version upgrade is enabled,Medium
rds-snapshot-encrypted,Ensure RDS snapshots are encrypted,Medium
securityhub-codebuild-project-environment-privileged-check-411dbabb,Ensure CodeBuild project environment is not privileged,High
elasticsearch-encrypted-at-rest,Ensure Elasticsearch domains have encryption at rest enabled,Medium
vpc-default-security-group-closed,Ensure VPC default security group is closed,Medium
securityhub-emr-master-no-public-ip-ed818625,Ensure EMR master nodes do not have public IP addresses,High
securityhub-rds-snapshot-encrypted-a23b7e02,Ensure RDS snapshots are encrypted,High
securityhub-elb-acm-certificate-required-8827d280,Ensure ELB uses ACM certificate,High
securityhub-encrypted-volumes-0465d6a7,Ensure EBS volumes are encrypted,High
securityhub-elb-deletion-protection-enabled-044771b9,Ensure ELB deletion protection is enabled,Medium
vpc-vpn-2-tunnels-up,Ensure VPC VPN connections have 2 tunnels up,Medium
securityhub-elasticsearch-primary-node-fault-tolerance-7f89671a,Ensure Elasticsearch primary node fault tolerance,High
securityhub-rds-sg-event-notifications-configured-14739a5e,Ensure RDS security group event notifications are configured,Medium
securityhub-dax-encryption-enabled-884eb4fd,Ensure Amazon DAX clusters have encryption at rest enabled,High
securityhub-ecr-private-image-scanning-enabled-ecb42dcd,Ensure ECR private image scanning is enabled,Medium
securityhub-s3-access-point-public-access-blocks-e818a9b2,Ensure S3 access points have public access blocks enabled,High
securityhub-codebuild-project-source-repo-url-check-8187685e,Ensure CodeBuild project source repository URL is valid,Medium
ec2-stopped-instance,Ensure EC2 instances are not stopped,Medium
securityhub-secretsmanager-secret-unused-200f9e3d,Ensure Secrets Manager secrets are rotated or deleted if unused,Medium
securityhub-restricted-ssh-e7eab365,Ensure SSH access is restricted,High
securityhub-iam-password-policy-minimum-length-check-23d6c014,Ensure IAM password policy minimum length is set,Medium
securityhub-autoscaling-group-elb-healthcheck-required-c0f3956b,Ensure Auto Scaling groups use ELB health checks,Medium
securityhub-s3-account-level-public-access-blocks-periodic-6c2b9770,Ensure S3 account-level public access blocks are enabled,Medium
rds-storage-encrypted,Ensure RDS storage is encrypted,Medium
securityhub-fsx-openzfs-copy-tags-enabled-6b455e4e,Ensure FSx OpenZFS copy tags are enabled,Medium
rds-instance-iam-authentication-enabled,Ensure RDS instances have IAM authentication enabled,Medium
securityhub-elbv2-multiple-az-a7714cde,Ensure ELBv2 load balancers are configured with multiple availability zones,Medium
securityhub-rds-cluster-default-admin-check-df127618,Ensure RDS clusters do not use the default admin username,Medium
securityhub-rds-cluster-multi-az-enabled-dc2ce241,Ensure RDS clusters have multi-AZ enabled,High
securityhub-ec2-managedinstance-association-compliance-status-check-dd6dd5d1,Ensure EC2 managed instances association compliance status,Medium
ec2-managedinstance-patch-compliance-status-check,Ensure EC2 managed instances are patch compliant,Medium
securityhub-opensearch-update-check-4dc8f1c0,Ensure OpenSearch Service domains are updated to the latest version,Medium
securityhub-athena-workgroup-encrypted-at-rest-08e09110,Ensure Athena workgroup is encrypted at rest,Medium
cloudtrail-enabled,Ensure CloudTrail is enabled,High
securityhub-appsync-authorization-check-363900f2,Ensure AppSync APIs have authorization configured,Medium
securityhub-elasticache-redis-cluster-automatic-backup-check-fc4288a5,Ensure ElastiCache Redis clusters have automatic backups enabled,Medium
securityhub-vpc-sg-restricted-common-ports-f7febf4d,Ensure VPC security groups restrict common ports,Medium
redshift-enhanced-vpc-routing-enabled,Ensure Redshift enhanced VPC routing is enabled,Medium
dynamodb-pitr-enabled,Ensure DynamoDB tables have point-in-time recovery (PITR) enabled,Medium
securityhub-s3-event-notifications-enabled-472349a9,Ensure S3 event notifications are enabled,Medium
securityhub-root-account-hardware-mfa-enabled-ae603bcc,Ensure root account has hardware MFA enabled,High
acm-certificate-expiration-check,Ensure ACM certificates are not expiring within a specified time period,Medium
securityhub-elasticache-repl-grp-auto-failover-enabled-f2cca36f,Ensure ElastiCache replication groups have auto-failover enabled,Medium
dynamodb-autoscaling-enabled,Ensure DynamoDB auto-scaling is enabled,Medium
elb-logging-enabled,Ensure ELB logging is enabled,Medium
securityhub-multi-region-cloud-trail-enabled-734732d0,Ensure multi-region CloudTrail is enabled,High
securityhub-s3-bucket-versioning-enabled-1fb7e24b,Ensure S3 bucket versioning is enabled,Medium
