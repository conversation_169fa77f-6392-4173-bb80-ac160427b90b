{"transformSpec": {"type": "AttributeWriteBack", "postSchemas": "<%EI_POST_SCHEMA_NAMES%>", "tableInfo": {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__enrich", "tableType": "entity", "commonColumns": ["p_id", "updated_at", "updated_at_ts"], "uniqCol": "p_id"}}, "outputTableInfo": {"partitionColumns": ["class"], "outputTableName": "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei__assessment__publish"}, "entity": {"name": "Assessment"}}