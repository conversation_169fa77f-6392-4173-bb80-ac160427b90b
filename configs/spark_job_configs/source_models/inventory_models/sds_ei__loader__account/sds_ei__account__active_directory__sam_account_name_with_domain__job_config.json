{"primaryKey": "temp_primary_key_sam_account_name", "filterBy": "(sAMAccountType = '*********' OR sAMAccountType='NORMAL_USER_ACCOUNT') AND (not(REGEXP_LIKE(lower(DistinguishedName),'(?i)service[ ]?account|sccm|scom')))", "origin": "'MS Active Directory'", "temporaryProperties": [{"colName": "domain_temp", "colExpr": "regexp_extract(distinguishedName,'DC=([^,]++)')"}, {"colName": "sam_account_name_temp", "colExpr": "RTRIM('$',sAMAccountName)"}, {"colName": "sam_account_name_with_domain_temp", "colExpr": "CASE WHEN sam_account_name_temp IS NULL OR domain_temp IS NULL THEN NULL ELSE CONCAT_WS('\\\\',domain_temp,sam_account_name_temp) END"}, {"colName": "temp_primary_key_sam_account_name", "colExpr": "CONCAT_WS(':',sam_account_name_with_domain_temp,'Active Directory')"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,ad_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(last_password_change_date,login_last_date,ad_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "operational_status", "colExpr": "CASE WHEN userAccountControl is NULL THEN NULL WHEN LOWER(userAccountControl) LIKE '%disable%' THEN 'Disabled' ELSE 'Active' END"}, {"colName": "account_name", "colExpr": "LOWER(sam_account_name_with_domain_temp)"}, {"colName": "ownership_of_identity", "colExpr": "'Corp'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "source_of_identity", "colExpr": "'Active Directory'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "service", "colExpr": "'Active Directory'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_display_name", "colExpr": "temp_primary_key_sam_account_name"}, {"colName": "privilege_account", "colExpr": "CASE WHEN lower(sam_account_name_temp) = 'administrator' OR lower(memberof) LIKE '%domain admins%' THEN True ELSE False END"}, {"colName": "entitlement_value", "colExpr": "split(memberOf,',')"}, {"colName": "account_never_expire", "colExpr": "CASE WHEN accountExpires = 0 THEN True ELSE False END"}, {"colName": "password_not_required", "colExpr": "CAST(null AS BOOLEAN)"}, {"colName": "password_never_expire", "colExpr": "CAST(null AS BOOLEAN)"}, {"colName": "login_last_date", "colExpr": "CASE WHEN (lastLogon != '[]' AND lastLogon  != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastLogon))) ELSE NULL END"}, {"colName": "last_password_change_date", "colExpr": "CASE WHEN (pwdLastSet != '[]' AND pwdLastSet != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(pwdLastSet))) ELSE NULL END"}, {"colName": "failed_logon_count", "colExpr": "cast(badPwdCount as bigint)"}, {"colName": "last_lock_out_time", "colExpr": "CASE WHEN (lockoutTime != '[]' AND lockoutTime != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lockoutTime))) ELSE NULL END"}, {"colName": "last_lock_out_flag", "colExpr": "case when updated_at-last_lock_out_time<******** then True else False end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bad_password_configured_time", "colExpr": "5", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bad_password_count_flag", "colExpr": "case when badPwdCount=bad_password_configured_time then True else False end", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "ad_created_date", "colExpr": "CASE WHEN (whenCreated != '[]' AND whenCreated != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(whenCreated))) ELSE NULL END"}, {"colName": "ad_sam_account_type", "colExpr": "sAMAccountType"}], "dataSource": {"name": "MS Active Directory", "feedName": "MS Active Directory", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__active_directory"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__account__active_directory__sam_account_name_with_domain", "entity": {"name": "Account"}}