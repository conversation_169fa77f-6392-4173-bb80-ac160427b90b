{"primaryKey": "lower(containerArn)", "origin": "'AWS ECS Task Container'", "commonProperties": [{"colName": "type", "colExpr": "'Container Service Container'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "private_ip", "colExpr": "Coalesce(networkInterfaces.privateIpv4Address,networkInterfaces.ipv6Address)"}, {"colName": "image", "colExpr": "aws_container_image", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_container_serverless", "colExpr": "CASE WHEN LOWER(aws_container_launch_type)='fargate' then 'true' else 'false' end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(aws_container_status) IN ('running','deactivating') THEN 'Active' WHEN LOWER(aws_container_status) IN ('stopped','activating','pending', 'provisioning','deprovisioning','stopping', 'deleted') THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS ECS Container'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id,':([0-9]+):')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "container_memory", "colExpr": "cast(memory as double)"}, {"colName": "container_port_protocol", "colExpr": "networkBindings.protocol"}, {"colName": "ecs_cluster_name", "colExpr": "clusterName"}, {"colName": "container_ecs_task_arn", "colExpr": "taskArn"}, {"colName": "container_cpu", "colExpr": "cpu"}], "sourceSpecificProperties": [{"colName": "aws_ecs_cluster_arn", "colExpr": "clusterArn"}, {"colName": "aws_container_image", "colExpr": "image"}, {"colName": "aws_container_status", "colExpr": "INITCAP(lastStatus)"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN lower(aws_container_status) IN ('active', 'running') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_container_health_status", "colExpr": "CASE WHEN lower(healthStatus) = 'unknown' then NULL ELSE INITCAP(healthStatus) END"}, {"colName": "aws_container_runtime_id", "colExpr": "runtimeId"}, {"colName": "aws_container_gpu_id", "colExpr": "gpuIds"}, {"colName": "aws_container_bind_ip", "colExpr": "networkBindings.bindIp"}, {"colName": "aws_container_port", "colExpr": "cast(networkBindings.containerPort as array<string>)"}, {"colName": "aws_container_host_port", "colExpr": "cast(networkBindings.hostPort as array<string>)[0]"}, {"colName": "aws_container_network_interface_attc_id", "colExpr": "networkInterfaces.attachmentId"}, {"colName": "aws_container_launch_type", "colExpr": "launchType"}, {"colName": "aws_region", "colExpr": "regexp_extract(containerArn,'arn:aws:ecs:(.*):(.+):', 1)"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.aws_region = e.region", "sourcePreTransform": [{"colName": "aws_region", "colExpr": "REGEXP_EXTRACT(containerArn,'arn:aws:eks:([^:]+):.*')"}]}], "dataSource": {"name": "AWS", "feedName": "ECS Task Container", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__ecs_task_containers"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container__aws_ecs_task_container__containerarn", "entity": {"name": "Cloud Container"}}