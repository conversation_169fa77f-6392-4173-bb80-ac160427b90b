{"primaryKey": "lower(regexp_extract(clusterId, '/subscriptions/([^/]+)/', 1))", "origin": "'MS Azure ACI Container'", "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "Microsoft Azure", "feedName": "ACI Container", "srdm": "<%SRDM_SCHEMA_NAME%>.azure__aci_containers"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__azure_aci_container__account_id", "entity": {"name": "Cloud Account"}}