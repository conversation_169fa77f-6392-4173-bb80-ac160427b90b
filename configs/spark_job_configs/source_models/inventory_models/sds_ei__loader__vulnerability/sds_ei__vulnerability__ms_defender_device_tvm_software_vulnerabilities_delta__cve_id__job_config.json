{"primaryKey": "cveId", "origin": "'MS Defender'", "temporaryProperties": [{"colName": "first_seen_timestamp_epoch", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(firstSeenTimestamp)))"}], "commonProperties": [{"colName": "type", "colExpr": "'Vulnerability'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,vulnerability_first_observed_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "CASE WHEN (lastSeenTimestamp!='' AND (lastSeenTimestamp IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp)))ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}], "entitySpecificProperties": [{"colName": "cve_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v31_score", "colExpr": "cvssScore"}, {"colName": "v31_severity", "colExpr": "INITCAP(vulnerabilitySeverityLevel)"}, {"colName": "vendor_severity", "colExpr": "vulnerabilitySeverityLevel"}, {"colName": "patch_available", "colExpr": "cast(securityUpdateAvailable as string)"}, {"colName": "exploit_available", "colExpr": "CASE WHEN exploitabilityLevel LIKE 'NoExploit' THEN false ELSE true END"}, {"colName": "ms_recommended_update", "colExpr": "recommendedSecurityUpdate"}, {"colName": "ms_recommended_update_id", "colExpr": "recommendedSecurityUpdateId"}, {"colName": "vulnerability_first_observed_date", "colExpr": "CASE WHEN (firstSeenTimestamp!='' AND (firstSeenTimestamp IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(firstSeenTimestamp)))ELSE NULL END", "fieldsSpec": {"aggregateFunction": "min"}}], "sourceSpecificProperties": [{"colName": "vendor_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "found_in_organisation", "colExpr": "true", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device Software Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_software_vuln_delta"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__ms_defender_device_tvm_software_vulnerabilities_delta__cve_id", "entity": {"name": "Vulnerability"}}