{"primaryKey": "name", "origin": "'Wiz'", "temporaryProperties": [{"colName": "first_seen_timestamp_epoch", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(firstDetectedAt)))"}, {"colName": "temp_status", "colExpr": "collect_set(status) OVER (PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_vendor_severity", "colExpr": "collect_set(vendorSeverity) OVER (PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}], "commonProperties": [{"colName": "type", "colExpr": "'Vulnerability'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "vulnerability_first_observed_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "CASE WHEN UPPER(status)='OPEN' THEN max(UNIX_MILLIS(TIMESTAMP(to_timestamp(lastDetectedAt)))) OVER (PARTITION BY primary_key,status ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) END"}, {"colName": "description", "colExpr": "regexp_replace(CVEDescription, '<[^>]++>', ' ')"}], "entitySpecificProperties": [{"colName": "cve_id", "colExpr": "name"}, {"colName": "exploit_available", "colExpr": "hasExploit"}, {"colName": "vendor_severity", "colExpr": "CASE WHEN ARRAY_CONTAINS(TRANSFORM(temp_vendor_severity, x -> LOWER(x)), 'critical') THEN 'Critical' WHEN ARRAY_CONTAINS(TRANSFORM(temp_vendor_severity, x -> LOWER(x)), 'high') THEN  'High' WHEN ARRAY_CONTAINS(TRANSFORM(temp_vendor_severity, x -> LOWER(x)), 'medium') THEN  'Medium' ELSE 'Low' END"}, {"colName": "recommendation", "colExpr": "collect_set(regexp_replace(remediation, '<[^>]++>', ' ')) OVER (PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "vulnerability_first_observed_date", "colExpr": "min(UNIX_MILLIS(TIMESTAMP(to_timestamp(firstDetectedAt)))) over (partition by primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}], "sourceSpecificProperties": [{"colName": "found_in_organisation", "colExpr": "true"}, {"colName": "has_cisa_kev_exploit", "colExpr": "hasCisaKevExploit"}], "dataSource": {"name": "Wiz", "feedName": "Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.wiz__vulnerability_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__wiz_vulnerability_findings__name", "entity": {"name": "Vulnerability"}}