{"primaryKey": "lower(clusterId)", "origin": "'MS Azure ACI Container'", "commonProperties": [{"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "'Container Groups'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "native_type", "colExpr": "'Azure Container Group'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "ACI Container", "srdm": "<%SRDM_SCHEMA_NAME%>.azure__aci_containers"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cluster__ms_azure_aci_container__cluster_id", "entity": {"name": "Cluster"}}