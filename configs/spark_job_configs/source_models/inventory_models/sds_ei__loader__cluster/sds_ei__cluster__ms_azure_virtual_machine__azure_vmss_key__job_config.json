{"primaryKey": "REGEXP_extract(id, '/virtualMachineScaleSets/([^/]+)', 1)", "origin": "'MS Azure Virtual Machine'", "commonProperties": [{"colName": "type", "colExpr": "'Compute Instance Group'"}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "azure_tags.BusinessUnit", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "azure_tags.Department", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "cloud_provider", "colExpr": "'Azure'"}, {"colName": "billing_tag", "colExpr": "azure_tags.Billing", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties"}, {"colName": "environment", "colExpr": "lower(azure_tags.Environment)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')"}, {"colName": "native_type", "colExpr": "'Azure Virtual Machine Scaleset'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "azure_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "organisational_unit", "colExpr": "azure_tags.OU", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "azure_tags.Project", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "azure_vmss_key", "colExpr": "lower(primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_tags", "colExpr": "tags"}, {"colName": "azure_aks_node_pool_names", "colExpr": "azure_tags.aks__managed__poolName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_resource_last_modified_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(azure_system_data.lastModifiedAt)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_region", "colExpr": "location"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Virtual Machine", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__virtual_machine"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cluster__ms_azure_virtual_machine__azure_vmss_key", "entity": {"name": "Cluster"}}