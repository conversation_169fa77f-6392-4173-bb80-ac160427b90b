{"primaryKey": "lower(temp_resource_id)", "filterBy": "lower(regexp_extract(temp_resource_id,'\\\\\\\\/providers\\\\\\\\/([^\\\\\\\\/]+\\\\\\\\/[^\\\\\\\\/]+)\\\\\\\\/[^\\\\\\\\/]+')) IN ('microsoft.compute/virtualmachinescalesets','microsoft.containerservice/managedclusters','microsoft.containerinstance/containergroups')", "origin": "'MS Azure Security Center Alerts'", "temporaryProperties": [{"colName": "temp_resource_identifiers", "colExpr": "explode_outer(properties.resourceIdentifiers)", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_resource_id", "colExpr": "temp_resource_identifiers.azureResourceId"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN regexp_extract(resource_id, '/([^/]+)/(?:[^/]+)$') LIKE '%virtualmachinescalesets%' THEN 'Compute Instance Group' WHEN regexp_extract(resource_id, '/([^/]+)/(?:[^/]+)$') LIKE '%virtualmachines%' THEN 'Virtual Machine' WHEN regexp_extract(resource_id, '\\/([^\\/]+)\\/([^\\/]+)$') LIKE '%managedclusters%' THEN 'Kubernetes Cluster' WHEN regexp_extract(resource_id, '\\/([^\\/]+)\\/([^\\/]+)$') LIKE '%containergroups%' THEN 'Container Group' ELSE regexp_extract(resource_id, '\\/([^\\/]+)\\/([^\\/]+)$')END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(regexp_extract(resource_id,'\\/([^\\/]+)$'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%virtualmachinescalesets%' THEN 'Azure Virtual Machine Scaleset' WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%virtualmachines%' THEN 'Azure Virtual Machine' WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%managedclusters%' THEN 'Azure Managed Cluster' WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%containergroups%' THEN 'Azure Container Group' ELSE (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) END", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Center Alerts", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_center_alerts"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cluster__ms_azure_security_alerts__azure_resource_id", "entity": {"name": "Cluster"}}