{"primaryKey": "ARRAY_EXCEPT(TRANSFORM(tags , x -> (CASE WHEN x.key = 'aws:autoscaling:groupName' THEN x.value END)), array(NULL))[0]", "filterBy": "lower(resourceType) in ('aws::ec2::instance')", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "'Compute Instance Group'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "aws_resource_configuration_change_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "billing_tag", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'Billing' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'Environment' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "accountId"}, {"colName": "native_type", "colExpr": "'AWS AutoScaling Group'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ec2fleet_id", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'aws:ec2:fleet-id' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "scaling_group_name", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'aws:autoscaling:groupName' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "kubernetes_node_group_name", "colExpr": "COALESCE(ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'eks:nodegroup-name' THEN x.value END)), array(NULL))[0],ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'aws:eks:nodegroup-name' THEN x.value END)), array(NULL))[0])"}, {"colName": "kubernetes_cluster_name", "colExpr": "COALESCE(ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'eks:cluster-name' THEN x.value END)), array(NULL))[0],ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'aws:eks:cluster-name' THEN x.value END)), array(NULL))[0])"}], "sourceSpecificProperties": [{"colName": "aws_autoscaling_group_key", "colExpr": "lower(primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_resource_configuration_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configurationItemCaptureTime)))"}, {"colName": "aws_tags", "colExpr": "tags"}, {"colName": "aws_tag", "colExpr": "concat_ws(', ', transform(tags, d -> concat(d.key,' : ',d.value)))"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.awsRegion = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cluster__aws_resource_details__aws_autoscaling_group_key", "entity": {"name": "Cluster"}}