{"primaryKey": "lower(clusterArn)", "origin": "'AWS ECS Task Container'", "commonProperties": [{"colName": "type", "colExpr": "'Container Service'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "native_type", "colExpr": "'AWS ECS Cluster'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "ECS Task Container", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__ecs_task_containers"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cluster__aws_ecs_task_container__cluster_arn", "entity": {"name": "Cluster"}}