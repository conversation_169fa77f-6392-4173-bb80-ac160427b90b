{"primaryKey": "lower(id)", "filterBy": "lower(type) IN ('microsoft.compute/virtualmachinescalesets','microsoft.containerservice/managedclusters','microsoft.containerinstance/containergroups')", "origin": "'MS Azure Resource Details'", "temporaryProperties": [{"colName": "temp_type", "colExpr": "type"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN SUBSTRING_INDEX(lower(type), '/', -1) LIKE '%scalesets%' THEN 'Compute Instance Group' WHEN SUBSTRING_INDEX(lower(type), '/', -1) LIKE '%managedclusters%'  THEN 'Kubernetes Cluster' WHEN SUBSTRING_INDEX(lower(type), '/', -1) LIKE '%containergroups%' THEN 'Container Groups' ELSE SUBSTRING_INDEX(lower(type), '/', -1) END"}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,azure_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(azure_resource_last_modified_date,azure_aci_finish_date,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date, case when lower(type)='compute instance group' then last_found_date end)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "azure_tags.BusinessUnit", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "azure_tags.Department", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "purchase_plan_name", "colExpr": "azure_plan", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "billing_tag", "colExpr": "azure_tags.Billing", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "lower(azure_tags.Environment)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "kubernetes_version", "colExpr": "properties.currentKubernetesVersion", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "accessibility", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(azure_aci_instance_state) LIKE '%running%' OR LOWER(azure_aks_power_state) LIKE '%running%' THEN 'Active' WHEN LOWER(azure_aci_instance_state) LIKE '%stopped%' OR LOWER(azure_aks_power_state) LIKE '%stopped%' THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "provisioning_state", "colExpr": "properties.provisioningState", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "scaling_instance_count", "colExpr": "azure_vmss_vm_count", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "scaling_instance_type", "colExpr": "azure_vmss_vm_size", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN SUBSTRING_INDEX(lower(temp_type), '/', -1) LIKE '%scalesets%' THEN 'Azure Virtual Machine Scaleset' WHEN SUBSTRING_INDEX(lower(temp_type), '/', -1) LIKE '%managedclusters%' THEN 'Azure Managed Cluster' WHEN SUBSTRING_INDEX(lower(temp_type), '/', -1) LIKE '%containergroups%' THEN 'Azure Container Group' ELSE SUBSTRING_INDEX(lower(temp_type), '/', -1) END"}, {"colName": "zone_availability", "colExpr": "CASE WHEN azure_availability_zone = 1 THEN 'Single' WHEN azure_availability_zone > 1 THEN 'Multiple' WHEN azure_availability_zone IS NULL THEN 'Not Applicable' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "azure_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "kubernetes_node_group_name", "colExpr": "Coalesce(array_distinct(properties.agentPoolProfiles.name), array(azure_tags.aks__managed__poolName))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "scaling_group_name", "colExpr": "case when lower(temp_type) in ('microsoft.compute\\/virtualmachinescalesets\\/virtualmachines') then REGEXP_extract(id, '\\/virtualMachineScaleSets\\/([^\\/]+)', 1) else null end"}, {"colName": "kubernetes_node_pool_vm_count", "colExpr": "aggregate(cast(properties.agentPoolProfiles.count as array<integer>),0,(acc, x) -> acc + x)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aci_container_services_active_containers", "colExpr": "array_distinct(properties.containers.name)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "organisational_unit", "colExpr": "azure_tags.OU", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "azure_tags.Project", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "azure_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(coalesce(properties.timeCreated,azure_system_data.createdAt,properties.virtualMachineProfile.timeCreated,cast(properties.containers.properties.instanceView.currentState.startTime as array<string>)[0]))))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_vmss_vm_count", "colExpr": "sku.capacity"}, {"colName": "azure_vmss_vm_size", "colExpr": "case when temp_type='microsoft.compute/virtualmachinescalesets' then array(sku.name) else null end"}, {"colName": "azure_vmss_key", "colExpr": "case when lower(type)= 'compute instance group' then lower(resource_name) else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_system_data", "colExpr": "systemData"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN LOWER(properties.instanceView.state) LIKE '%running%' OR LOWER(properties.powerState.code) LIKE '%running%' OR LOWER(case when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%running%' then 'Running'  when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string)))like '%creating%' then 'Running' when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%starting%' then 'Running' when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%stopping%' then 'Stopped'when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%deallocating%' then 'Stopped' when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%stopped%' then 'Stopped' when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%deallocated%' then 'Stopped' else null end) LIKE '%running%' THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "azure_tags", "colExpr": "tags"}, {"colName": "azure_public_network_access", "colExpr": "INITCAP(properties.publicNetworkAccess)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_power_state", "colExpr": "properties.powerState.code", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_dns_prefix", "colExpr": "properties.dnsPrefix", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_private_fqdn", "colExpr": "properties.privateFQDN", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_node_pool_profiles", "colExpr": "properties.agentPoolProfiles", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_node_pool_vm_sizes", "colExpr": "array_distinct(properties.agentPoolProfiles.vmSize)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_node_pool_type", "colExpr": "array_distinct(properties.agentPoolProfiles.type)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_node_pool_node_image_version", "colExpr": "array_distinct(properties.agentPoolProfiles.nodeImageVersion)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_node_resource_group", "colExpr": "properties.nodeResourceGroup", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_enable_rbac", "colExpr": "properties.enableRBAC", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_max_node_pools", "colExpr": "properties.maxAgentPools", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_node_pool_count", "colExpr": "CASE WHEN kubernetes_node_group_name!= array(null) THEN SIZE(kubernetes_node_group_name) ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_node_pool_maximum_vm_count", "colExpr": "aggregate(cast(properties.agentPoolProfiles.maxCount as array<integer>),0,(acc, x) -> acc + x)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_node_pool_maximum_pod_count", "colExpr": "aggregate(cast(properties.agentPoolProfiles.maxPods as array<integer>),0,(acc, x) -> acc + x)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_image_cleaner_enabled_status", "colExpr": "properties.securityProfile.imageCleaner.enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_auto_upgrade_type", "colExpr": "properties.autoUpgradeProfile.upgradeChannel", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_storage_snapshot_controller_enabled_status", "colExpr": "properties.storageProfile.snapshotController.enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_defender_status", "colExpr": "cast(properties.securityProfile.defender.securityMonitoring.enabled as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_workload_identity_status", "colExpr": "properties.securityProfile.workloadIdentity.enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_support_plan", "colExpr": "properties.supportPlan", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_aad_profile_admin_group_object_ids", "colExpr": "properties.aadProfile.adminGroupObjectIDs", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_azure_rbac_status", "colExpr": "properties.aadProfile.enableAzureRBAC", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_key_vault_secrets_status", "colExpr": "properties.addonProfiles.azureKeyvaultSecretsProvider.enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_azure_policy_status", "colExpr": "properties.addonProfiles.azurepolicy.enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_oms_agent_status", "colExpr": "properties.addonProfiles.omsagent.enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aks_monitor_enabled", "colExpr": "properties.azureMonitorProfile.metrics.enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aci_instance_state", "colExpr": "properties.instanceView.state", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aci_restart_policy", "colExpr": "properties.restartPolicy", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aci_container_services_active_containers_count", "colExpr": "case when array_distinct(properties.containers.name) is not null then size(array_distinct(properties.containers.name)) else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_aci_finish_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(cast(properties.instanceView.currentState.finishTime as array<string>)[0])))"}, {"colName": "azure_resource_last_modified_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(azure_system_data.lastModifiedAt)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_plan", "colExpr": "plan.name"}, {"colName": "azure_availability_zone", "colExpr": "CASE WHEN zones IS NULL OR size(zones) = 0 THEN NULL ELSE size(zones) END"}, {"colName": "azure_region", "colExpr": "location"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cluster__ms_azure_resource_details__id", "entity": {"name": "Cluster"}}