{"primaryKey": "lower(temp_resource_id)", "filterBy": "lower(temp_resource_type) IN ('awsec2ec2fleet','awsecscluster','awsecsservice','awsekscluster','awslambdafunction','awsautoscalingautoscalinggroup')", "origin": "'AWS SH Findings'", "temporaryProperties": [{"colName": "temp_resource", "colExpr": "explode_outer(Resources)", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_resource_id", "colExpr": "temp_resource.Id"}, {"colName": "temp_resource_type", "colExpr": "temp_resource.Type"}, {"colName": "temp_resource_details", "colExpr": "temp_resource.Details"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN lower(temp_resource_type) LIKE '%awsec2ec2fleet%' OR lower(temp_resource_type) LIKE '%awsautoscalingautoscalinggroup%' THEN 'Compute Instance Group' WHEN lower(temp_resource_type) LIKE '%awsecscluster%'  THEN 'Container Service' WHEN  lower(temp_resource_type) LIKE '%awsekscluster%'  THEN 'Kubernetes Cluster' WHEN  lower(temp_resource_type) LIKE '%awsecsservice%' THEN 'Compute Instance Group' WHEN lower(temp_resource_type) LIKE '%awslambdafunction%' THEN 'Serverless'  ELSE lower(temp_resource_type)END"}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN lower(temp_resource_type) LIKE '%awsec2ec2fleet%' THEN 'AWS EC2 Fleet' WHEN lower(temp_resource_type) LIKE '%awsecscluster%' THEN 'AWS ECS Cluster' WHEN lower(temp_resource_type) LIKE '%awsecsservice%' THEN 'AWS ECS Service' WHEN lower(temp_resource_type) LIKE '%awsekscluster%' THEN 'AWS EKS Cluster' WHEN lower(temp_resource_type) LIKE '%awslambdafunction%' THEN 'AWS Lambda Function' WHEN lower(temp_resource_type) LIKE '%awsautoscalingautoscalinggroup%' THEN 'AWS AutoScaling Group' ELSE lower(temp_resource_type) END"}, {"colName": "environment", "colExpr": "Resources.Tags.Environment[0]"}, {"colName": "account_id", "colExpr": "CAST(AwsAccountId as string)"}], "sourceSpecificProperties": [{"colName": "aws_region", "colExpr": "Region"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.Region = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_sh_findings__resource_id", "entity": {"name": "Cloud Compute"}}