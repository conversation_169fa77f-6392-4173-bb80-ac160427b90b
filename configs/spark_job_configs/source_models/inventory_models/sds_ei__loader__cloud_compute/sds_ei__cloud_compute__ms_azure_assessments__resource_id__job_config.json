{"primaryKey": "lower(properties.resourceDetails.Id)", "filterBy": "lower(type) IN ('microsoft.security/assessments') and (lower(regexp_extract(properties.resourceDetails.Id,'\\\\/providers\\\\/([^\\\\/]+\\\\/[^\\\\/]+)\\\\/[^\\\\/]+$')) IN ('microsoft.compute/virtualmachinescalesets','microsoft.containerservice/managedclusters','microsoft.containerinstance/containergroups'))", "origin": "'MS Azure Security Resources'", "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN regexp_extract(resource_id, '/([^/]+)/(?:[^/]+)$') LIKE '%virtualmachinescalesets%' THEN 'Compute Instance Group' WHEN regexp_extract(resource_id, '/([^/]+)/(?:[^/]+)$') LIKE '%virtualmachines%' THEN 'Virtual Machine' WHEN regexp_extract(resource_id, '\\/([^\\/]+)\\/([^\\/]+)$') LIKE '%managedclusters%' THEN 'Kubernetes Cluster' WHEN regexp_extract(resource_id, '\\/([^\\/]+)\\/([^\\/]+)$') LIKE '%containergroups%' THEN 'Container Group' ELSE regexp_extract(resource_id, '\\/([^\\/]+)\\/([^\\/]+)$')END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(regexp_extract(resource_id,'\\/([^\\/]+)$'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%virtualmachinescalesets%' THEN 'Azure Virtual Machine Scaleset' WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%virtualmachines%' THEN 'Azure Virtual Machine' WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%managedclusters%' THEN 'Azure Managed Cluster' WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%containergroups%' THEN 'Azure Container Group' ELSE (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "regexp_extract(id, '/subscriptions/([^/]+)/', 1)"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_azure_assessments__resource_id", "entity": {"name": "Cloud Compute"}}