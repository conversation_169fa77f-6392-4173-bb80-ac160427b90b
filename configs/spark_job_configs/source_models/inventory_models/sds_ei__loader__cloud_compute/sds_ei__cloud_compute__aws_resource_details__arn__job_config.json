{"primaryKey": "lower(arn)", "filterBy": "resourceType IN ('AWS::EC2::EC2Fleet','AWS::ECS::Cluster','AWS::ECS::Service','AWS::EKS::Cluster','AWS::Lambda::Function','AWS::AutoScaling::AutoScalingGroup','AWS::SageMaker::NotebookInstance','AWS::SageMaker::Model')", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%ec2::ec2fleet%' OR SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%autoscaling::autoscalinggroup%' THEN 'Compute Instance Group' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%ecs::cluster%' THEN  'Container Service' WHEN  SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%eks::cluster%'  THEN 'Kubernetes Cluster' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%ecs::service%' THEN 'Compute Instance Group' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%lambda::function%' THEN 'Serverless' WHEN SUBSTRING_INDEX(LOWER(resourceType), '::', -2) LIKE '%sagemaker::model%' THEN 'AI Model' WHEN SUBSTRING_INDEX(LOWER(resourceType), '::', -2) LIKE '%sagemaker::notebookinstance%' THEN 'Data Workload' ELSE SUBSTRING_INDEX(lower(resourceType), '::', -2) END"}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date,aws_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aws_lambda_last_modified_date,aws_resource_configuration_change_date,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date,case when lower(type)='compute instance group' or lower(type)='kubernetes cluster' then last_found_date end)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(coalesce(resourceName,resourceId))"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties", "colExpr": "configuration"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "billing_tag", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'Billing' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'Environment' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "kubernetes_version", "colExpr": "properties.Version", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "private_dns_name", "colExpr": "properties.privateDnsName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "public_dns_name", "colExpr": "properties.publicDnsName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "accountId"}, {"colName": "accessibility", "colExpr": "CAST(NULL as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(aws_operational_state) IN ('active', 'running') THEN 'Active' WHEN LOWER(aws_operational_state) IN ('stopped', 'pending', 'shutting-down', 'stopping', 'failed', 'inactive', 'delete_in_progress', 'terminated') THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "scaling_instance_count", "colExpr": "aws_autoscaling_instance_count", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "scaling_instance_type", "colExpr": "aws_autoscaling_instance_types", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%ec2::ec2fleet%' THEN 'AWS EC2 Fleet' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%ecs::cluster%' THEN 'AWS ECS Cluster' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%ecs::service%' THEN 'AWS ECS Service' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%eks::cluster%' THEN 'AWS EKS Cluster' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%lambda::function%' THEN 'AWS Lambda Function' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%autoscaling::autoscalinggroup%' THEN 'AWS AutoScaling Group' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%sagemaker::model%' THEN 'AWS SageMaker Model' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%sagemaker::notebookinstance%' THEN 'AWS SageMaker Notebook Instance' ELSE SUBSTRING_INDEX(lower(resourceType), '::', -2) END"}, {"colName": "zone_availability", "colExpr": "CASE WHEN aws_availability_zone = 'Multiple Availability Zones' THEN 'Multiple'  WHEN aws_availability_zone= 'Not Applicable'  THEN 'Not Applicable' WHEN aws_availability_zone= 'Regional'  THEN 'Regional' WHEN aws_availability_zone IS NULL THEN NULL ELSE 'Single' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "kubernetes_cluster_name", "colExpr": "COALESCE(ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'eks:cluster-name' THEN x.value END)), array(NULL))[0],ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'aws:eks:cluster-name' THEN x.value END)), array(NULL))[0])", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "scaling_instance_ids", "colExpr": "properties.instances.instanceId", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ecs_cluster_name", "colExpr": "properties.ClusterName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ec2fleet_id", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'aws:ec2:fleet-id' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "scaling_group_name", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'aws:autoscaling:groupName' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "kubernetes_node_group_name", "colExpr": "COALESCE(ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'eks:nodegroup-name' THEN x.value END)), array(NULL))[0] ,ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'aws:eks:nodegroup-name' THEN x.value END)), array(NULL))[0])", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "aws_tags.Project", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "instance_imdsv2_status", "colExpr": "INITCAP(properties.metadataOptions.httpTokens)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aws_operational_state", "colExpr": "lower(coalesce(properties.state.name,properties.state.value))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "active_operational_date", "colExpr": "CASE WHEN lower(coalesce(configuration.state.name,configuration.state.value)) IN ('active', 'running') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_tags", "colExpr": "tags"}, {"colName": "aws_tag", "colExpr": "concat_ws(', ', transform(tags, d -> concat(d.key,' : ',d.value)))"}, {"colName": "aws_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(resourceCreationTime)))"}, {"colName": "aws_resource_configuration_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configurationItemCaptureTime)))"}, {"colName": "aws_lambda_memory_size", "colExpr": "floor(properties.memorySize)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_lambda_runtime", "colExpr": "properties.runtime", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_lambda_last_modified_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.lastModified)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_lambda_code_size", "colExpr": "floor(properties.codeSize)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_eks_endpoint_private_access", "colExpr": "properties.ResourcesVpcConfig.EndpointPrivateAccess", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_eks_endpoint_public_access", "colExpr": "properties.ResourcesVpcConfig.EndpointPublicAccess", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_eks_network_ip_family", "colExpr": "properties.KubernetesNetworkConfig.IpFamily", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_eks_cluster_security_group_id", "colExpr": "properties.ClusterSecurityGroupId", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ecr_encryption_type", "colExpr": "properties.EncryptionConfiguration.EncryptionType", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ecr_scan_on_push", "colExpr": "properties.ImageScanningConfiguration.ScanOnPush", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ecr_repository_name", "colExpr": "properties.RepositoryName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ecr_image_tag_mutability", "colExpr": "properties.ImageTagMutability", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ecs_service_name", "colExpr": "properties.ServiceName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ecs_platform_version", "colExpr": "properties.PlatformVersion", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ecs_scheduling_strategy", "colExpr": "properties.SchedulingStrategy", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ecs_launch_type", "colExpr": "properties.LaunchType", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ecs_assign_public_ip", "colExpr": "properties.NetworkConfiguration.AwsvpcConfiguration.AssignPublicIp", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_autoscaling_group_key", "colExpr": "case when lower(type)= 'compute instance group' then lower(resource_name) else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_eks_cluster_key", "colExpr": "case when lower(type)= 'kubernetes cluster' then lower(concat(resource_name,aws_region)) else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_autoscaling_instance_max_size", "colExpr": "floor(properties.maxSize)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_autoscaling_instance_types", "colExpr": "array_distinct(properties.instances.instanceType)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_autoscaling_instance_count", "colExpr": "case when scaling_instance_ids is null then null else size(scaling_instance_ids) end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_autoscaling_instance_min_size", "colExpr": "floor(properties.minSize)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ec2fleet_valid_from", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.ValidFrom)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ec2fleet_valid_until", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.ValidUntil)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ec2fleet_terminate_instances", "colExpr": "properties.TerminateInstancesWithExpiration", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ec2fleet_replace_unhealthy_instances", "colExpr": "properties.ReplaceUnhealthyInstances", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ec2fleet_override_instance_type", "colExpr": "array_distinct(TRANSFORM(properties.LaunchTemplateConfigs.Overrides[0], x -> x.InstanceType))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ec2fleet_total_target_capacity", "colExpr": "floor(properties.TargetCapacitySpecification.TotalTargetCapacity)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ec2fleet_on_demand_target_capacity", "colExpr": "floor(properties.TargetCapacitySpecification.OnDemandTargetCapacity)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ec2fleet_spot_target_capacity", "colExpr": "floor(properties.TargetCapacitySpecification.SpotTargetCapacity)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ec2fleet_default_target_capacity_type", "colExpr": "properties.TargetCapacitySpecification.DefaultTargetCapacityType", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ec2fleet_type", "colExpr": "lower(properties.Type)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_autoscaling_override_mixed_instance_type", "colExpr": "array_distinct(properties.mixedInstancesPolicy.launchTemplate.overrides.instanceType)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_configuration_id", "colExpr": "properties.Id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_notebook_instance_storage_size", "colExpr": "properties.VolumeSizeInGB", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_default_code_repository", "colExpr": "properties.DefaultCodeRepository", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_direct_internet_access_status", "colExpr": "properties.DirectInternetAccess", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_availability_zone", "colExpr": "availabilityZone"}, {"colName": "aws_region", "colExpr": "awsRegion"}, {"colName": "aws_sagemaker_role_arn", "colExpr": "properties.RoleArn", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_root_access_status", "colExpr": "properties.RootAccess", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_lifecycle_config_name", "colExpr": "properties.LifecycleConfigName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_model_name", "colExpr": "properties.ModelName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_network_isolation_status", "colExpr": "properties.EnableNetworkIsolation", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_execution_role_arn", "colExpr": "properties.ExecutionRoleArn", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_model_primary_container_image", "colExpr": "properties.PrimaryContainer.Image", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_model_primary_container_mode", "colExpr": "properties.PrimaryContainer.Mode", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_model_data_url", "colExpr": "properties.PrimaryContainer.ModelDataUrl", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_model_environment_config", "colExpr": "properties.PrimaryContainer.Environment", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_model_available_gpu", "colExpr": "properties.PrimaryContainer.Environment.SM_NUM_GPUS", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_model_cache_root", "colExpr": "properties.PrimaryContainer.Environment.MODEL_CACHE_ROOT", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_sagemaker_model_datasource_s3_uri", "colExpr": "properties.PrimaryContainer.ModelDataSource.S3DataSource.S3Uri", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.awsRegion = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_resource_details__arn", "entity": {"name": "Cloud Compute"}}