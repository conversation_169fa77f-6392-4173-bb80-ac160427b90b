{"primaryKey": "vulnerableAsset.id", "filterBy": "lower(vulnerableAsset.type) IN ('container')", "origin": "'Wiz Vulnerability Findings'", "temporaryProperties": [{"colName": "first_seen_timestamp_epoch", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(firstDetectedAt)))"}], "commonProperties": [{"colName": "last_active_date", "colExpr": "vulnerability_last_observed_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date,vulnerability_first_observed)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "vulnerableAsset.name"}, {"colName": "vm_product", "colExpr": "'Wiz'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "wiz_onboarding_status", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "wiz_onboarding_status", "colExpr": "true", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vulnerability_first_observed", "colExpr": "first_seen_timestamp_epoch"}, {"colName": "vulnerability_last_observed_date", "colExpr": "case when lower(status)='open' then UNIX_MILLIS(TIMESTAMP(to_timestamp(lastDetectedAt))) else cast(null as long) end"}, {"colName": "wiz_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Wiz", "feedName": "Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.wiz__vulnerability_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__container__wiz_vulnerability_findings__vulnerableassetid", "entity": {"name": "Container"}}