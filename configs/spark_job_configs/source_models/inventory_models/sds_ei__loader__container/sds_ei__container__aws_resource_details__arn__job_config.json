{"primaryKey": "lower(arn)", "filterBy": "resourceType IN ('AWS::ECR::Repository')", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "'Container Registry'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date,aws_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "aws_resource_configuration_change_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(coalesce(resourceName,resourceId))"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties", "colExpr": "configuration"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "billing_tag", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'Billing' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'Environment' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "accountId"}, {"colName": "native_type", "colExpr": "'AWS ECR Repository'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "encryption_status", "colExpr": "case when aws_ecr_encryption_type IS NOT NULL THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "CASE WHEN aws_availability_zone = 'Multiple Availability Zones' THEN 'Multiple'  WHEN aws_availability_zone= 'Not Applicable'  THEN 'Not Applicable' WHEN aws_availability_zone= 'Regional'  THEN 'Regional' WHEN aws_availability_zone IS NULL THEN NULL ELSE 'Single' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aws_tags", "colExpr": "tags"}, {"colName": "aws_region", "colExpr": "awsRegion"}, {"colName": "aws_availability_zone", "colExpr": "availabilityZone"}, {"colName": "aws_tag", "colExpr": "concat_ws(', ', transform(tags, d -> concat(d.key,' : ',d.value)))"}, {"colName": "aws_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(resourceCreationTime)))"}, {"colName": "aws_resource_configuration_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configurationItemCaptureTime)))"}, {"colName": "aws_ecr_encryption_type", "colExpr": "configuration.EncryptionConfiguration.EncryptionType"}, {"colName": "aws_ecr_scan_on_push", "colExpr": "configuration.ImageScanningConfiguration.ScanOnPush"}, {"colName": "aws_ecr_repository_name", "colExpr": "properties.RepositoryName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ecr_image_tag_mutability", "colExpr": "properties.ImageTagMutability", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.awsRegion = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__container__aws_resource_details__arn", "entity": {"name": "Container"}}