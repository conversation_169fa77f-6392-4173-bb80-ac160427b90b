{"primaryKey": "CASE WHEN lower(properties.resourceDetails.ResourceType)  = 'microsoft.containerregistry/registries' THEN lower(properties.resourceDetails.Id) WHEN  lower(properties.resourceDetails.ResourceType)  = 'container'  THEN lower(properties.additionalData.ContainerId) END", "filterBy": "lower(type) IN ('microsoft.security/assessments') AND lower(properties.resourceDetails.ResourceType) IN ('microsoft.containerregistry/registries', 'container')", "origin": "'MS Azure Security Resources'", "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN  lower(properties.resourceDetails.ResourceType)  = 'microsoft.containerregistry/registries' THEN  'Container Registry'  WHEN  lower(properties.resourceDetails.ResourceType)  = 'container'  THEN 'Kubernetes Container' END"}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "properties.resourceDetails.ResourceName"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN lower(properties.resourceDetails.ResourceType)  = 'microsoft.containerregistry/registries' THEN  'Azure Container Registry'  WHEN  lower(regexp_extract(properties.resourceDetails.Id, '([^\\/]+)\\/(?:[^\\/]+)\\/(?:[^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%managedclusters%' THEN 'Azure AKS Container' WHEN lower(regexp_extract(properties.resourceDetails.Id, '([^\\/]+)\\/(?:[^\\/]+)\\/(?:[^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%containergroups%' THEN 'Azure ACI Container' END"}, {"colName": "account_id", "colExpr": "regexp_extract(id, '/subscriptions/([^/]+)/', 1)"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__container__ms_azure_assessments__resource_id", "entity": {"name": "Container"}}