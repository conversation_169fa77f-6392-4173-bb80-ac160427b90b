{"primaryKey": "lower(id)", "filterBy": "lower(type) IN ('microsoft.containerregistry/registries')", "origin": "'MS Azure Resource Details'", "temporaryProperties": [{"colName": "temp_type", "colExpr": "type"}], "commonProperties": [{"colName": "type", "colExpr": "'Container Registry'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,azure_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "azure_resource_last_modified_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "azure_tags.BusinessUnit", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "azure_tags.Department", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "billing_tag", "colExpr": "azure_tags.Billing", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "lower(azure_tags.Environment)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Container Registry'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "encryption_status", "colExpr": "CASE  WHEN  lower(azure_acr_encryption_status) = 'enabled' THEN true WHEN lower(azure_acr_encryption_status) = 'disabled' THEN false ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "CASE WHEN azure_availability_zone = 1 THEN 'Single' WHEN azure_availability_zone > 1 THEN 'Multiple' WHEN azure_availability_zone IS NULL THEN 'Not Applicable' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "azure_region", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "azure_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(coalesce(properties.timeCreated,azure_system_data.createdAt))))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_tags", "colExpr": "tags"}, {"colName": "azure_acr_admin_user_enabled", "colExpr": "properties.adminUserEnabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_acr_quarantine_policy_status", "colExpr": "INITCAP(properties.policies.quarantinePolicy.status)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_acr_trust_policy_status", "colExpr": "INITCAP(properties.policies.trustPolicy.status)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_acr_retention_policy_status", "colExpr": "INITCAP(properties.policies.retentionPolicy.status)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_acr_export_policy_status", "colExpr": "INITCAP(properties.policies.exportPolicy.status)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_acr_ad_auth_as_arm_policy", "colExpr": "INITCAP(properties.policies.azureADAuthenticationAsArmPolicy.status)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_acr_soft_delete_policy", "colExpr": "INITCAP(properties.policies.softDeletePolicy.status)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_acr_encryption_status", "colExpr": "INITCAP(properties.encryption.status)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_acr_data_endpoint_enabled", "colExpr": "properties.dataEndpointEnabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_acr_network_rule_bypass_options", "colExpr": "properties.networkRuleBypassOptions", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_acr_zone_redundency", "colExpr": "INITCAP(properties.zoneRedundancy)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_acr_anonymous_pull_enabled", "colExpr": "properties.anonymousPullEnabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_resource_last_modified_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(azure_system_data.lastModifiedAt)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_availability_zone", "colExpr": "CASE WHEN zones IS NULL OR size(zones) = 0 THEN NULL ELSE size(zones) END"}, {"colName": "azure_region", "colExpr": "location"}, {"colName": "azure_system_data", "colExpr": "systemData"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__container__ms_azure_resource_details__id", "entity": {"name": "Container"}}