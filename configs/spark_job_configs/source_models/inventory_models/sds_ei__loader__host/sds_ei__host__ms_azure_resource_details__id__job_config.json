{"primaryKey": "lower(id)", "filterBy": "lower(type) IN ('microsoft.compute/virtualmachines','microsoft.compute/virtualmachinescalesets/virtualmachines')", "origin": "'MS Azure Resource Details'", "temporaryProperties": [{"colName": "temp_type", "colExpr": "type"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,azure_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(name)"}, {"colName": "os", "colExpr": "INITCAP(Coalesce(properties.osType,properties.storageProfile.osDisk.osType, properties.virtualMachineProfile.storageProfile.osDisk.osType, array_distinct(properties.agentPoolProfiles.osType)[0]))"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "lower(properties.vmId)"}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(azure_vm_power_state) LIKE '%running%' THEN 'Active' WHEN  LOWER(azure_vm_power_state) LIKE '%stopped%' THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "image", "colExpr": "azure_vm_image_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "provisioning_state", "colExpr": "properties.provisioningState"}, {"colName": "cloud_instance_type", "colExpr": "properties.hardwareProfile.vmSize"}, {"colName": "cloud_instance_lifecycle", "colExpr": "lower(azure_vm_lifecycle)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "CASE WHEN size(zones) = 1 THEN 'Single' WHEN size(zones) > 1 THEN 'Multiple' ELSE null END"}, {"colName": "region", "colExpr": "location"}, {"colName": "cloud_resource_type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_ephemeral", "colExpr": "case when LOWER(Coalesce(properties.priority, properties.virtualMachineProfile.priority)) IN ('low', 'spot') THEN True else False end"}], "sourceSpecificProperties": [{"colName": "azure_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(coalesce(properties.timeCreated,systemData.createdAt))))"}, {"colName": "azure_vm_lifecycle", "colExpr": "coalesce(properties.priority, properties.virtualMachineProfile.priority)"}, {"colName": "azure_vm_os_name", "colExpr": "properties.extended.instanceView.osName"}, {"colName": "azure_vm_os_version", "colExpr": "properties.extended.instanceView.osVersion"}, {"colName": "azure_vm_image_id", "colExpr": "coalesce(properties.storageProfile.imageReference.id, properties.virtualMachineProfile.storageProfile.imageReference.id,TO_JSON(array_distinct(properties.agentPoolProfiles.nodeImageVersion)))"}, {"colName": "azure_vm_power_state", "colExpr": "case when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%running%' then 'Running'  when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string)))like '%creating%' then 'Running' when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%starting%' then 'Running' when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%stopping%' then 'Stopped'when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%deallocating%' then 'Stopped' when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%stopped%' then 'Stopped' when COALESCE(lower(properties.extended.instanceView.powerState.code),lower(cast(properties.instanceView.statuses.code as string))) like '%deallocated%' then 'Stopped' else null end"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN LOWER(azure_vm_power_state) LIKE '%running%' THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "azure_tags", "colExpr": "tags"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_resource_details__id", "entity": {"name": "Host"}}