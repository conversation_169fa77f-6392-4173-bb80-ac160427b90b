{"primaryKey": "SessionHostAzureVmId", "filterBy": "SessionHostAzureVmId != '<>'", "origin": "'MS Azure VDI'", "commonProperties": [{"colName": "last_active_date", "colExpr": "Greatest(last_found_date,azure_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,azure_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(dns_name,'(^[^.]++)'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "CASE WHEN (CASE WHEN SessionHostOSDescription='<>' THEN NULL ELSE SessionHostOSDescription END) IS NULL THEN NULL WHEN LOWER(CASE WHEN SessionHostOSDescription='<>' THEN NULL ELSE SessionHostOSDescription END) LIKE '%unknown%' \n THEN null WHEN LOWER(CASE WHEN SessionHostOSDescription='<>' THEN NULL ELSE SessionHostOSDescription END) LIKE '%other%' THEN 'Other' ELSE CONCAT_WS(' ',CASE WHEN SessionHostOSDescription='<>' THEN NULL ELSE SessionHostOSDescription END,\n CASE WHEN NOT regexp_like((CASE WHEN SessionHostOSVersion='<>' THEN NULL ELSE SessionHostOSVersion END),'(?i)unknown|other') THEN (CASE WHEN SessionHostOSVersion='<>' THEN NULL ELSE SessionHostOSVersion END) END) END"}, {"colName": "ip", "colExpr": "case when SessionHostIPAddress ='<>' then Null else regexp_replace(SessionHostIPAddress,'â‰¤|â‰¥','') end"}, {"colName": "dns_name", "colExpr": "trim('â‰¤â‰¥',CASE WHEN SessionHostName='<>' THEN NULL ELSE SessionHostName END)"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "case when lower(azure_operational_state)='started' OR lower(azure_operational_state)='connected' OR lower(azure_operational_state)='completed' THEN 'Active' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "lower(primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_user", "colExpr": "case when lower(State) IN ('connected', 'started') then UserName end"}], "sourceSpecificProperties": [{"colName": "azure_operational_state", "colExpr": "State"}, {"colName": "azure_resource_created_date", "colExpr": "case when lower(State) IN ('connected', 'started') then event_timestamp_epoch end", "fieldsSpec": {"aggregateFunction": "min"}}], "dataSource": {"name": "Microsoft Azure", "feedName": "VDI", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__vdi"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_vdi__session_host_azure_vm_id", "entity": {"name": "Host"}}