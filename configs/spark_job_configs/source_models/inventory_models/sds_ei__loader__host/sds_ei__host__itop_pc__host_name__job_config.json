{"primaryKey": "name", "origin": "'iTOP PC'", "temporaryProperties": [{"colName": "temp_type", "colExpr": "type"}, {"colName": "temp_purchase_date", "colExpr": "COALESCE(purchasedate,purchase_date)"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(itop_pc_purchase_date,itop_pc_move_to_production_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(last_found_date,itop_pc_move_to_production_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "name"}, {"colName": "os", "colExpr": "CASE WHEN (itop_pc_os_version IS NULL OR itop_pc_os_build IS NULL) THEN NULL ELSE  CONCAT(itop_pc_os_version,' ',itop_pc_os_build) END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_version", "colExpr": "itop_pc_os_version", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_build", "colExpr": "itop_pc_os_build", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_manufacturer", "colExpr": "itop_pc_brand", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_model", "colExpr": "itop_pc_model", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_serial_number", "colExpr": "itop_pc_device_serial_number", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_chassis_type", "colExpr": "itop_pc_type", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "itop_pc_display_name", "colExpr": "COALESCE(name,friendlyname)"}, {"colName": "itop_pc_move_to_production_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(TO_DATE(move2production,'yyyy-MM-dd')))"}, {"colName": "itop_pc_end_of_warranty_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(TO_DATE(end_of_warranty,'yyyy-MM-dd')))"}, {"colName": "itop_pc_status", "colExpr": "status"}, {"colName": "itop_pc_org_unit", "colExpr": "COALESCE(org_unit,organization_name)"}, {"colName": "itop_pc_org_id", "colExpr": "org_id"}, {"colName": "itop_pc_business_criticality", "colExpr": "COALESCE(business_criticity,BusinessCriticity)"}, {"colName": "itop_pc_device_serial_number", "colExpr": "COALESCE(device_serialnumber,serialnumber)"}, {"colName": "itop_pc_location", "colExpr": "COALESCE(location,location_name)"}, {"colName": "itop_pc_obsolete_status", "colExpr": "obsolescence_flag"}, {"colName": "itop_pc_obsolete_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(TO_DATE(obsolescence_date,'yyyy-MM-dd')))"}, {"colName": "itop_pc_brand", "colExpr": "brand_name"}, {"colName": "itop_pc_model", "colExpr": "model_name"}, {"colName": "itop_pc_cpu", "colExpr": "cpu"}, {"colName": "itop_pc_ram", "colExpr": "ram"}, {"colName": "itop_pc_type", "colExpr": "COALESCE(temp_type,pc_type)"}, {"colName": "itop_pc_os_family", "colExpr": "COALESCE(osfamily_name,osfamily)"}, {"colName": "itop_pc_os_version", "colExpr": "COALESCE(osversion_name,osversion)"}, {"colName": "itop_pc_os_build", "colExpr": "osbuild"}, {"colName": "itop_pc_asset_number", "colExpr": "asset_number"}, {"colName": "itop_pc_purchase_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(TO_DATE(temp_purchase_date,'yyyy-MM-dd')))"}, {"colName": "itop_class", "colExpr": "finalclass"}], "dataSource": {"name": "iTOP", "feedName": "PC", "srdm": "<%SRDM_SCHEMA_NAME%>.itop__pc"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__itop_pc__host_name", "entity": {"name": "Host"}}