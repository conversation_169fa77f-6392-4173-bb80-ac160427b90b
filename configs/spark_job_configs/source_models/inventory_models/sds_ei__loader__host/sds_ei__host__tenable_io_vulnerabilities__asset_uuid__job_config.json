{"primaryKey": "asset.uuid", "origin": "'Tenable.io Vulnerabilities'", "temporaryProperties": [{"colName": "temp_primary_key", "colExpr": "asset.uuid"}], "commonProperties": [{"colName": "last_active_date", "colExpr": "tenable_io_last_authenticated_scan_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "CASE WHEN asset.hostname IS NULL OR asset.hostname= '' or asset.fqdn NOT RLIKE '^\\\\d{1,3}(\\\\.\\\\d{1,3}){3}$' THEN LOWER(REGEXP_EXTRACT(asset.fqdn, '(^([^.]+))', 1)) when asset.hostname NOT RLIKE '^\\\\d{1,3}(\\\\.\\\\d{1,3}){3}$' then upper(REGEXP_EXTRACT(asset.hostname, '(^([^.]+))', 1)) END"}, {"colName": "os", "colExpr": "asset.operating_system[0]"}, {"colName": "netbios", "colExpr": "asset.netbios_name"}, {"colName": "dns_name", "colExpr": "CASE WHEN asset.fqdn RLIKE \"^(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}$\" THEN NULL ELSE asset.fqdn END"}, {"colName": "vm_product", "colExpr": "CASE WHEN tenable_io_onboarding_status is true THEN 'Tenable.io' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "tenable_io_onboarding_status", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_last_scan_date", "colExpr": "tenable_io_last_authenticated_scan_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mac_address", "colExpr": "asset.mac_address"}, {"colName": "vm_tracking_method", "colExpr": "transform( collect_set(source) over (partition by temp_primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING), x -> CASE WHEN lower(trim(x)) = 'agent' THEN 'Nessus Agent'  WHEN lower(trim(x)) = 'nnm' THEN 'Nessus Network Monitor' WHEN lower(trim(x)) = 'nessus' THEN 'Nessus Scan'  ELSE NULL END )"}, {"colName": "ip", "colExpr": "tenable_io_ipv4_addresses", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "case when asset.device_type like '%aws-ec2-instance%'  then 'AWS EC2 INSTANCE' when asset.device_type like '%azure-instance%' then 'Azure Virtual Machine' when asset.device_type like '%gcp-instance%'  then 'GCP Compute Engine' END"}], "sourceSpecificProperties": [{"colName": "tenable_io_last_authenticated_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(asset.last_authenticated_results)))"}, {"colName": "tenable_io_system_types", "colExpr": "TRANSFORM(array(asset.device_type), x -> INITCAP(REPLACE(x, '-', ' ')))"}, {"colName": "tenable_io_id", "colExpr": "asset.uuid"}, {"colName": "tenable_io_ipv4_addresses", "colExpr": "asset.ipv4"}, {"colName": "tenable_io_ipv6_addresses", "colExpr": "asset.ipv6"}, {"colName": "tenable_io_onboarding_status", "colExpr": "TRUE", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "tenable_io_agent_uuid", "colExpr": "asset.agent_uuid"}], "dataSource": {"name": "Tenable.io", "feedName": "Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.tenable_io_Vulnerabilities"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_io_vulnerabilities__asset_uuid", "entity": {"name": "Host"}}