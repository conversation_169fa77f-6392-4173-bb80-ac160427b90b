{"primaryKey": "id", "filterBy": "lower(type) != 'container'", "origin": "'Wiz Cloud Resource'", "temporaryProperties": [{"colName": "temp_type", "colExpr": "type"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,wiz_onboarding_date,aws_resource_created_date,azure_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(wiz_onboarding_date,aws_resource_created_date,azure_resource_created_date,wiz_last_scan_date,wiz_modified_date,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "graphEntity.properties.tags.Department"}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(name)"}, {"colName": "cloud_resource_name", "colExpr": "lower(name)"}, {"colName": "accessibility", "colExpr": "case when graphEntity.typedProperties.accessibleFrom.internet=True or graphEntity.typedProperties.openToAllInternet =True then 'External' when graphEntity.typedProperties.accessibleFrom.internet=False and graphEntity.typedProperties.openToAllInternet =False then 'Internal' else Null end"}, {"colName": "os", "colExpr": "graphEntity.properties.operatingSystem"}, {"colName": "cloud_provider", "colExpr": "case when graphEntity.properties.cloudPlatform IS NULL THEN 'No Data' when lower(graphEntity.properties.cloudPlatform)='kubernetes' and LOWER(graphEntity.properties.kubernetes_kubernetesFlavor)='aks' THEN 'Azure'  when lower(graphEntity.properties.cloudPlatform)='kubernetes' and LOWER(graphEntity.properties.kubernetes_kubernetesFlavor)='eks' THEN 'AWS' else graphEntity.properties.cloudPlatform end"}, {"colName": "account_id", "colExpr": "subscriptionExternalId"}, {"colName": "cloud_account_name", "colExpr": "subscriptionName"}, {"colName": "resource_id", "colExpr": "case when lower(graphEntity.properties.cloudPlatform)='aws' then graphEntity.providerUniqueId when lower(graphEntity.properties.cloudPlatform)='azure' then graphEntity.properties.externalId when lower(graphEntity.properties.cloudPlatform) = 'kubernetes' then coalesce(graphEntity.properties.providerUniqueId, graphEntity.properties.externalId) else null end"}, {"colName": "cloud_resource_type", "colExpr": "case when lower(temp_type) = 'virtual_machine' THEN 'Virtual Machine' END"}, {"colName": "region", "colExpr": "graphEntity.properties.region"}, {"colName": "cloud_instance_id", "colExpr": "case when lower(temp_type) = 'virtual_machine' and lower(graphEntity.properties.cloudPlatform)='aws' then graphEntity.properties.externalId when lower(temp_type) = 'virtual_machine' and lower(graphEntity.properties.cloudPlatform)='azure' then graphEntity.properties.providerUniqueId else null end"}, {"colName": "native_type", "colExpr": "CASE WHEN lower(graphEntity.properties.nativeType) LIKE '%ec2%'  AND lower(graphEntity.properties.cloudPlatform) LIKE '%aws%'  THEN 'AWS EC2 Instance'  WHEN lower(graphEntity.properties.nativeType) LIKE '%virtualmachine%'  AND lower(graphEntity.properties.cloudPlatform) LIKE '%azure%' THEN 'Azure Virtual Machine'  WHEN lower(graphEntity.properties.nativeType) LIKE '%containerapps%' THEN 'Azure Container Apps Container'  WHEN lower(graphEntity.properties.nativeType) LIKE  '%containerinstance%' THEN 'Azure ACI Container'  WHEN lower(graphEntity.properties.nativeType) LIKE '%lightsail%' THEN 'AWS Lightsail Instance' WHEN lower(graphEntity.properties.nativeType) LIKE '%container%' and lower(graphEntity.properties.cloudPlatform) LIKE '%kubernetes%'  and  lower(graphentity.properties.kubernetes_kubernetesFlavor) LIKE '%eks%' THEN 'AWS EKS Container'   WHEN lower(graphEntity.properties.nativeType) LIKE '%container%' and lower(graphEntity.properties.cloudPlatform) LIKE '%kubernetes%'  and  lower(graphentity.properties.kubernetes_kubernetesFlavor) LIKE '%aks%' THEN 'Azure AKS Container' WHEN lower(graphEntity.properties.nativeType)  LIKE '%hostedcontainer%' THEN 'Hosted Container' ELSE  graphEntity.properties.nativeType END"}, {"colName": "image", "colExpr": "graphEntity.properties.image"}, {"colName": "is_container_host", "colExpr": "graphEntity.properties.isContainerHost"}, {"colName": "is_ephemeral", "colExpr": "graphEntity.properties.isEphemeral"}, {"colName": "vm_product", "colExpr": "'Wiz'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "wiz_onboarding_status", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN lower(wiz_operational_state) = 'active' THEN 'Active'  WHEN lower(wiz_operational_state) = 'inactive' OR lower(wiz_operational_state) = 'error' THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_accessible_from_internet", "colExpr": "graphEntity.typedProperties.accessibleFrom.internet"}, {"colName": "open_to_all_internet", "colExpr": "graphEntity.typedProperties.openToAllInternet"}, {"colName": "billing_tag", "colExpr": "graphEntity.properties.tags.billing"}, {"colName": "environment", "colExpr": "lower(graphEntity.properties._environments)"}, {"colName": "has_admin_privileges", "colExpr": "graphEntity.typedProperties.hasAdminPrivileges"}, {"colName": "has_high_privileges", "colExpr": "graphEntity.typedProperties.hasHighPrivileges"}, {"colName": "has_sensitive_data", "colExpr": "graphEntity.typedProperties.hasSensitiveData"}, {"colName": "scaling_group_name", "colExpr": "REGEXP_extract(lower(graphEntity.properties.externalId), '/virtualmachinescalesets/([^/]+)', 1)"}], "sourceSpecificProperties": [{"colName": "aws_resource_created_date", "colExpr": "case when lower(graphEntity.properties.cloudPlatform)='kubernetes' and LOWER(graphEntity.properties.kubernetes_kubernetesFlavor)='eks' THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.properties.creationDate)))  when graphEntity.properties.cloudPlatform='AWS' then UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.properties.creationDate)))  else cast(null as long) end"}, {"colName": "azure_resource_created_date", "colExpr": "case when lower(graphEntity.properties.cloudPlatform)='kubernetes' and LOWER(graphEntity.properties.kubernetes_kubernetesFlavor)='aks' THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.properties.creationDate)))  when graphEntity.properties.cloudPlatform='Azure' then UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.properties.creationDate))) else cast(null as long) end"}, {"colName": "wiz_onboarding_status", "colExpr": "true", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "wiz_onboarding_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.firstSeen)))"}, {"colName": "wiz_last_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.lastSeen)))"}, {"colName": "wiz_operational_state", "colExpr": "graphEntity.properties.status"}, {"colName": "wiz_modified_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.properties.updatedAt)))"}, {"colName": "wiz_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "active_operational_date", "colExpr": "CASE WHEN lower(wiz_operational_state) IN ('active') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "azure_vm_disable_password_authentication", "colExpr": "CASE WHEN lower(graphEntity.properties.cloudPlatform) LIKE '%azure%' THEN  lower(graphEntity.properties.passwordAuthDisabled) ELSE NULL END"}, {"colName": "wiz_is_cloud_managed", "colExpr": "graphEntity.properties.isManaged"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.graphEntity.properties.region = e.region"}], "dataSource": {"name": "Wiz", "feedName": "Cloud Resource", "srdm": "<%SRDM_SCHEMA_NAME%>.wiz__cloud_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__wiz_cloud_resource__id", "entity": {"name": "Host"}}