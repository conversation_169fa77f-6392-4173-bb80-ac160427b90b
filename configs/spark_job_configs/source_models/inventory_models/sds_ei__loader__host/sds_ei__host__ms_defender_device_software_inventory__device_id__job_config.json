{"primaryKey": "deviceId", "origin": "'MS Defender Device Software Inventory'", "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(deviceName,'^((?>(?:[0-9]++[.]){3}[0-9]++|[^.]++))([.][^\\\\r\\\\n]*+)?$'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "osPlatform"}, {"colName": "dns_name", "colExpr": "deviceName"}, {"colName": "cloud_resource_name", "colExpr": "lower(deviceName)"}], "sourceSpecificProperties": [{"colName": "defender_id", "colExpr": "deviceId"}, {"colName": "defender_detection_method", "colExpr": "'Defender Agent'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device Software Inventory", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_software"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_software_inventory__device_id", "entity": {"name": "Host"}}