{"primaryKey": "lower(arn)", "filterBy": "resourceType='AWS::EC2::Instance'", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,aws_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aws_instance_launch_date,aws_instance_attach_time,aws_resource_created_date,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date,aws_resource_configuration_change_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "coalesce(configuration.publicDnsName,configuration.privateDnsName)"}, {"colName": "ip", "colExpr": "CONCAT_WS(',',configuration.publicIpAddress,configuration.privateIpAddress)"}, {"colName": "dns_name", "colExpr": "CASE WHEN configuration.publicDnsName RLIKE '^([a-zA-Z0-9][-a-zA-Z0-9]*\\\\.)+[a-zA-Z]{2,}$' THEN configuration.publicDnsName WHEN configuration.privateDnsName RLIKE '^([a-zA-Z0-9][-a-zA-Z0-9]*\\\\.)+[a-zA-Z]{2,}$' THEN configuration.privateDnsName ELSE NULL END"}, {"colName": "os", "colExpr": "COALESCE(INITCAP(configuration.platform),CASE WHEN configuration.platformDetails = 'Linux/UNIX' THEN 'Linux' ELSE initcap(configuration.platformDetails) END)"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "accountId"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "resourceId"}, {"colName": "instance_name", "colExpr": "CASE WHEN native_type = 'AWS EC2 Instance' THEN ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'Name' THEN x.value END)), array(NULL))[0] ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EC2 Instance'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "awsRegion"}, {"colName": "cloud_instance_type", "colExpr": "configuration.instanceType"}, {"colName": "zone_availability", "colExpr": "CASE WHEN availabilityZone IN ('Regional', 'Multiple Availability Zones') THEN 'Multiple' WHEN availabilityZone= 'Not Applicable'  THEN 'Not Applicable' WHEN availabilityZone IS NULL THEN NULL ELSE 'Single' END"}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(aws_operational_state) IN ('active', 'running') THEN 'Active' WHEN LOWER(aws_operational_state) IN ('stopped', 'pending', 'shutting-down', 'stopping', 'failed', 'inactive', 'delete_in_progress', 'terminated') THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_ephemeral", "colExpr": "case when array_contains(aws_tags.key,'aws:ec2spot:fleet-request-id') or array_contains(aws_tags.key,'aws:autoscaling:groupName') or array_contains(aws_tags.key,'spotinst:aws:ec2:group:id') or array_contains(aws_tags.key,'aws:ec2:fleet-id') or array_contains(aws_tags.key,'gitlab_autoscaler_token') or array_contains(aws_tags.key,'aws:elasticmapreduce:job-flow-id') or array_contains(aws_tags.key,'spotinst:aws:ec2:group:createdBy:spotinst') then True Else False end", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aws_resource_created_date", "colExpr": "case when resourceCreationTime IS NOT NULL then UNIX_MILLIS(TIMESTAMP(to_timestamp(resourceCreationTime))) END "}, {"colName": "aws_instance_launch_date", "colExpr": "CASE WHEN configuration.launchTime IS NOT NULL then UNIX_MILLIS(TIMESTAMP(to_timestamp(configuration.launchTime))) END"}, {"colName": "aws_instance_attach_time", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(array_max(NetworkInterfaces.Attachment.AttachTime))))"}, {"colName": "aws_operational_state", "colExpr": "lower(coalesce(configuration.state.name,configuration.state.value))"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN LOWER(aws_operational_state) IN ('active', 'running') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_tags", "colExpr": "tags"}, {"colName": "aws_instance_monitoring_state", "colExpr": "INITCAP(configuration.monitoring.state)"}, {"colName": "aws_instance_has_iam_role", "colExpr": "case when configuration.iamInstanceProfile.arn is not null then true else false end"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.awsRegion = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_resource_details__arn", "entity": {"name": "Host"}}