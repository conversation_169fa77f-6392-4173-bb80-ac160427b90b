{"primaryKey": "workstation_name", "filterBy": "workstation_name!='-' AND event_code='4624'", "origin": "'WinEvents 4624'", "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(dns_name,'^([^.]++)'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "dns_name", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "'Windows'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_family", "colExpr": "'Windows'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_user", "colExpr": "case when object_account_name not like '%$' AND lower(object_account_name) not like '%system%' then object_account_name end"}, {"colName": "login_last_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "win_event_id", "colExpr": "'4624'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "WinEvents", "feedName": "4624", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__windows_security_logs"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__winevents_4624__workstation_name", "entity": {"name": "Host"}}