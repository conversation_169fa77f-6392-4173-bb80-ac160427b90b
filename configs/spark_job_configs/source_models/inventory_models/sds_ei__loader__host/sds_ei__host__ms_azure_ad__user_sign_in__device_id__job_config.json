{"primaryKey": "deviceDetail.deviceId", "origin": "'MS Azure AD Sign-in Logs'", "filterBy": "deviceDetail.deviceId != '' and lower(deviceDetail.deviceId) not like '%removed%'", "commonProperties": [{"colExpr": "greatest(login_last_date,aad_last_signin_attempt)", "colName": "last_active_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "upper(coalesce(host_name,aad_device_id,primary_key))", "colName": "display_label", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "LEAST(first_found_date,aad_last_signin_attempt)", "colName": "first_seen_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colExpr": "case when status.errorCode = 0 then event_timestamp_epoch ELSE NULL end", "colName": "login_last_date", "fieldsSpec": {"aggregateFunction": "max"}}, {"colExpr": "case when status.errorCode = 0 then upper(userDisplayName) else null end", "colName": "login_last_user"}, {"colExpr": "UPPER(deviceDetail.displayName)", "colName": "host_name"}, {"colExpr": "collect_set(ipAddress) OVER (PARTITION BY deviceDetail.deviceId ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)", "colName": "ip"}, {"colExpr": "deviceDetail.operatingSystem", "colName": "os"}], "sourceSpecificProperties": [{"colExpr": "deviceDetail.deviceId", "colName": "aad_device_id"}, {"colExpr": "CASE WHEN (createdDateTime!='' AND (createdDateTime IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(createdDateTime))) ELSE NULL END", "colName": "aad_last_signin_attempt"}, {"colExpr": "Case when conditionalAccessStatus like 'notApplied' then 'Not Applied' else initcap(conditionalAccessStatus) END", "colName": "aad_conditional_access_status"}, {"colExpr": "deviceDetail.isCompliant", "colName": "aad_compliance_status"}, {"colExpr": "deviceDetail.isManaged", "colName": "aad_management_status"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country"]}, "joinCondition": "s.location.countryOrRegion = e.region"}], "dataSource": {"name": "MS Azure AD", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_user_sign_in", "feedName": "Sign-in Logs"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad__user_sign_in__device_id", "entity": {"name": "Host"}}