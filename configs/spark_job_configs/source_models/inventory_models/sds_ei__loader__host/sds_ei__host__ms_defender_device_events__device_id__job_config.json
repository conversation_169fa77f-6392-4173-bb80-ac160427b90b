{"primaryKey": "DeviceId", "origin": "'MS Defender Device Events'", "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(DeviceName,'^((?>(?:[0-9]++[.]){3}[0-9]++|[^.]++))([.][^\\\\r\\\\n]++)?$'))"}, {"colName": "dns_name", "colExpr": "DeviceName"}, {"colName": "cloud_resource_name", "colExpr": "lower(DeviceName)"}, {"colName": "login_last_user", "colExpr": "CASE WHEN REGEXP_EXTRACT(AdditionalFields,'User\\\"\\\\s*:\\\"(?<user>[^\\\\r\\\\n\\\"]*)\\\"',1)='' THEN NULL ELSE REGEXP_EXTRACT(AdditionalFields,'User\\\"\\\\s*:\\\"(?<user>[^\\\\r\\\\n\\\"]*)\\\"',1) END"}, {"colName": "av_status", "colExpr": "case when CAST(defender_action_type AS STRING) like '%AntivirusScanCompleted%' then true else false end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_last_scan_date", "colExpr": "case when CAST(ActionType AS STRING) like '%AntivirusScanCompleted%' then event_timestamp_epoch end", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "edr_threat_count", "colExpr": "defender_threat_count", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "threat_count", "colExpr": "edr_threat_count", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_scan_sla_breach_status", "colExpr": "CASE WHEN datediff( from_unixtime(updated_at / 1000, 'yyyy-MM-dd'), from_unixtime(av_last_scan_date / 1000, 'yyyy-MM-dd') ) <= av_scan_sla_duration THEN false ELSE true END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_scan_sla_breach_duration", "colExpr": "datediff( from_unixtime(updated_at / 1000, 'yyyy-MM-dd'), from_unixtime(av_last_scan_date / 1000, 'yyyy-MM-dd') )", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "defender_action_type", "colExpr": "collect_set(ActionType) OVER (partition by primary_key  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "defender_id", "colExpr": "DeviceId"}, {"colName": "defender_threat_name", "colExpr": "collect_set(CASE WHEN ((CAST(CASE WHEN REGEXP_EXTRACT(AdditionalFields,'WasRemediated\\\"\\\\s*:(?<wasRemediated>[^\\\\r\\\\n\\\",]*)',1)='' THEN NULL ELSE REGEXP_EXTRACT(AdditionalFields,'WasRemediated\\\"\\\\s*:(?<wasRemediated>[^\\\\r\\\\n\\\",]*)',1) END as Boolean) )\n )=false OR ((CAST(CASE WHEN REGEXP_EXTRACT(AdditionalFields,'WasRemediated\\\"\\\\s*:(?<wasRemediated>[^\\\\r\\\\n\\\",]*)',1)='' THEN NULL ELSE REGEXP_EXTRACT(AdditionalFields,'WasRemediated\\\"\\\\s*:(?<wasRemediated>[^\\\\r\\\\n\\\",]*)',1) END as Boolean) ) ) is null THEN (CASE WHEN REGEXP_EXTRACT(AdditionalFields,'ThreatName\\\"\\\\s*:\\\"(?<threatName>[^\\\\r\\\\n\\\"]*)\\\"',1)='' THEN NULL ELSE REGEXP_EXTRACT(AdditionalFields,'ThreatName\\\"\\\\s*:\\\"(?<threatName>[^\\\\r\\\\n\\\"]*)\\\"',1) END)  END) OVER (partition by primary_key  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "defender_detection_method", "colExpr": "'Defender Agent'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)<0 THEN 0 ELSE size(defender_threat_name) END", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device Events", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_events"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_events__device_id", "entity": {"name": "Host"}}