{"primaryKey": "lower(managedBy)", "filterBy": "lower(type) IN ('microsoft.compute/disks')", "origin": "'MS Azure Resource Details'", "commonProperties": [{"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "REGEXP_EXTRACT(id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')"}, {"colName": "native_type", "colExpr": "'Azure Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "host_name", "colExpr": "REGEXP_extract(managedBy, '\\/virtualMachines\\/([^\\/]+)', 1)"}, {"colName": "region", "colExpr": "location"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_resource_details__managed_by", "entity": {"name": "Host"}}