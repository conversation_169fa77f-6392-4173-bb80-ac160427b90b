{"primaryKey": "deviceId", "origin": "'MS Defender Device TVM Software Vulnerability Delta'", "temporaryProperties": [{"colName": "first_seen_timestamp_epoch", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(firstSeenTimestamp)))"}, {"colName": "temp_primary_key", "colExpr": "CONCAT_WS('',softwareName,softwareVersion)"}, {"colName": "vul_status", "colExpr": "case when lower(status) like '%new%' then 'Open' when lower(status) like '%updated%' then 'Open' when lower(status) like '%fixed%' then 'Closed' else null end"}, {"colName": "vulnerability_status", "colExpr": "case when array_contains(collect_set(vul_status) over (partition by deviceId,temp_primary_key rows between unbounded preceding and unbounded following),'Open') then true else false end"}], "commonProperties": [{"colName": "last_active_date", "colExpr": "vulnerability_last_observed_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(dns_name,'^((?>(?:[0-9]++[.]){3}[0-9]++|[^.]++))([.][^\\\\r\\\\n]*+)?$'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "CASE WHEN osPlatform IS NULL THEN NULL WHEN LOWER(osPlatform) LIKE '%unknown%' THEN NULL WHEN LOWER(osPlatform) LIKE '%other%' THEN 'Other' ELSE CONCAT_WS(' ',osPlatform,CASE WHEN NOT regexp_like(osVersion,'(?i)unknown|other') THEN osVersion END,CASE WHEN NOT regexp_like(osArchitecture,'(?i)unknown|other') THEN osArchitecture END) END"}, {"colName": "dns_name", "colExpr": "deviceName"}, {"colName": "cloud_resource_name", "colExpr": "lower(deviceName)"}, {"colName": "vulnerability_last_observed_date", "colExpr": "CASE WHEN (lastSeenTimestamp!='' AND (lastSeenTimestamp IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp))) ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "vm_last_scan_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "defender_id", "colExpr": "deviceId"}, {"colName": "defender_detection_method", "colExpr": "'Defender Agent'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device Software Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_software_vuln_delta"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_tvm_software_vulnerabilities_delta__device_id", "entity": {"name": "Host"}}