{"primaryKey": "computer_name", "filterBy": "computer_name!= '-' and event_code= '4725' AND coalesce(object_account_name,'dummy')!='defaultuser0' AND RTRIM('$',coalesce(subject_account_name,'dummy')) != RTRIM('$',coalesce(object_account_domain,'dummy2'))", "origin": "'WinEvents 4725'", "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(dns_name,'^([^.]++)'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "dns_name", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "'Windows'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_family", "colExpr": "'Windows'", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "win_event_id", "colExpr": "'4725'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "WinEvents", "feedName": "4725", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__windows_security_logs"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__winevents_4725__computer_name", "entity": {"name": "Host"}}