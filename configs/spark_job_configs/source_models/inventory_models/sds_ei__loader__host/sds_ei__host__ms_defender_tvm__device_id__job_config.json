{"primaryKey": "DeviceId", "origin": "'MS Defender Device TVM'", "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(dns_name,'^((?>(?:[0-9]++[.]){3}[0-9]++|[^.]++))([.][^\\\\r\\\\n]*+)?$'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "OSPlatform"}, {"colName": "dns_name", "colExpr": "DeviceName"}, {"colName": "cloud_resource_name", "colExpr": "lower(DeviceName)"}, {"colName": "av_signature_update_date", "colExpr": "CASE WHEN lower(ConfigurationId) like '%scid-2011%' and  cast(IsApplicable as INT) =1 AND cast(IsCompliant as INT) =1 then event_timestamp_epoch WHEN lower(ConfigurationId) like '%scid-5095%' and  cast(IsApplicable as INT) =1 AND cast(IsCompliant as INT) =1 then event_timestamp_epoch WHEN lower(ConfigurationId) like '%scid-6095%' and  cast(IsApplicable as INT) =1 AND cast(IsCompliant as INT) =1 then event_timestamp_epoch ELSE NULL END"}, {"colName": "av_block_malicious_code_status", "colExpr": "CASE WHEN lower(ConfigurationId) like '%scid-2012%' AND  cast(IsApplicable as INT) =1  then ( case when  cast(IsCompliant as INT) =1  then  true else false end) when lower(ConfigurationId) like '%scid-5090%'  AND cast(IsApplicable as INT) =1  then (case when cast(IsCompliant as INT)=1 then true else false end )  when lower(ConfigurationId) like '%scid-6090%'  AND cast(IsApplicable as INT) =1    then( case when cast(IsCompliant as INT)=1 then true else false end)  ELSE null END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "av_signature_update_sla_breach_status", "colExpr": "CASE WHEN datediff(from_unixtime(updated_at/1000,'yyyy-MM-dd'),from_unixtime(av_signature_update_date/1000,'yyyy-MM-dd'))<= av_signature_update_sla_duration then false else true END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_signature_update_sla_breach_duration", "colExpr": "datediff(from_unixtime(updated_at/1000,'yyyy-MM-dd'),from_unixtime(av_signature_update_date/1000,'yyyy-MM-dd'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "fw_status", "colExpr": "case when CAST(IsApplicable AS INT)=1  and lower(ConfigurationId ) like '%scid-2070%'  then (case  when CAST(IsCompliant AS INT) =1 then true else false end) when CAST(IsApplicable AS INT)=1 and lower(ConfigurationId) like '%scid-5007%' then (case when CAST(IsCompliant AS INT) =1 then true else false end)else null end", "fieldsSpec": {"persistNonNullValue": false}}], "sourceSpecificProperties": [{"colName": "defender_id", "colExpr": "DeviceId"}, {"colName": "defender_detection_method", "colExpr": "'Defender Agent'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device TVM Secure Config", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_tvm_secure_config"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_tvm__device_id", "entity": {"name": "Host"}}