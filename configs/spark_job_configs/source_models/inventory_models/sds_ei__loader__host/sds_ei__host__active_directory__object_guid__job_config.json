{"primaryKey": "ObjectGUID", "filterBy": "sAMAccountType = '*********' OR LOWER(sAMAccountType) LIKE '%machine_account%'", "origin": "'MS Active Directory'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,ad_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(login_last_date, ad_created_date,ad_account_disabled_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(cn,'^([^.]++)'))"}, {"colName": "os", "colExpr": "CASE WHEN operatingSystem IS NULL THEN NULL\nWHEN LOWER(operatingSystem) LIKE '%unknown%' THEN NULL\nWHEN LOWER(operatingSystem) LIKE '%other%' THEN 'Other'\nELSE CONCAT_WS(' ',operatingSystem,CASE WHEN NOT regexp_like(operatingSystemVersion,'(?i)unknown|other') THEN operatingSystemVersion END)\nEND"}, {"colName": "dns_name", "colExpr": "CASE WHEN dNSHostName!='[]' THEN dNSHostName ELSE NULL END"}, {"colName": "login_last_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(LastLogonDate, 'M/d/yyyy h:mm:ss a')))"}], "sourceSpecificProperties": [{"colName": "ad_sam_account_name", "colExpr": "sAMAccountName"}, {"colName": "ad_sam_account_type", "colExpr": "sAMAccountType"}, {"colName": "account_enabled_status", "colExpr": "lower(TRIM(Enabled))"}, {"colName": "earliest_ad_account_disabled_date", "colExpr": "CASE WHEN account_enabled_status = 'false' THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "min"}}, {"colName": "ad_account_disabled_date", "colExpr": "CASE WHEN account_enabled_status = 'false' and last_updated_attrs.account_enabled_status.last_changed.updated_at is not null then last_updated_attrs.account_enabled_status.last_changed.updated_at WHEN account_enabled_status = 'false' THEN earliest_ad_account_disabled_date ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ad_distinguished_name", "colExpr": "<PERSON><PERSON><PERSON>"}, {"colName": "ad_uac", "colExpr": "CAST(userAccountControl AS STRING)"}, {"colName": "ad_object_guid", "colExpr": "objectGUID"}, {"colName": "ad_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(whenCreated, 'M/d/yyyy h:mm:ss a')))"}, {"colName": "ad_operational_status", "colExpr": "CASE WHEN lower(TRIM(Enabled)) = 'false' THEN 'Disabled' WHEN lower(TRIM(Enabled)) = 'true' THEN 'Active' ELSE NULL END"}], "dataSource": {"name": "MS Active Directory", "feedName": "MS Active Directory", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__active_directory"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid", "entity": {"name": "Host"}}