{"primaryKey": "lower(case when configuration.multiAttachEnabled=false then configuration.attachments[0].instanceId else null end)", "filterBy": "lower(resourceType) in ('aws::ec2::volume')", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "accountId"}, {"colName": "native_type", "colExpr": "'AWS EC2 Instance'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "lower(primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.awsRegion = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_resource_details__instance_id", "entity": {"name": "Host"}}