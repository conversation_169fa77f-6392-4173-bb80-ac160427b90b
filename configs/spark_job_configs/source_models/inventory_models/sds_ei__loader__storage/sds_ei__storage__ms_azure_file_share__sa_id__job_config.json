{"primaryKey": "lower(REGEXP_extract(id,'/subscriptions/.*?/providers/Microsoft.Storage/storageAccounts/.*?(?=/fileServices)', 0))", "origin": "'MS Azure File Share'", "dataSource": {"name": "Microsoft Azure", "feedName": "File Share", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__file_share"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__storage__ms_azure_file_share__sa_id", "entity": {"name": "Storage"}}