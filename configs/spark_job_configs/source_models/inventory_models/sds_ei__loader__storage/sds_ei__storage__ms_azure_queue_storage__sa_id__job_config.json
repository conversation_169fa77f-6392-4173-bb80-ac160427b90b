{"primaryKey": "lower(REGEXP_extract(id,'/subscriptions/.*?/providers/Microsoft.Storage/storageAccounts/.*?(?=/queueServices)', 0))", "origin": "'MS Azure Queue Storage'", "dataSource": {"name": "Microsoft Azure", "feedName": "Queue Storage", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__queue_storage"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__storage__ms_azure_queue_storage__sa_id", "entity": {"name": "Storage"}}