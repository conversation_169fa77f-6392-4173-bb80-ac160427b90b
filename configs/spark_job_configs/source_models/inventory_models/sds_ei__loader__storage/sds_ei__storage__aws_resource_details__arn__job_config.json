{"primaryKey": "lower(arn)", "filterBy": "resourceType IN ('AWS::EC2::Volume', 'AWS::EFS::FileSystem', 'AWS::S3::Bucket')", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%ec2::volume%' THEN 'Volume' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%s3::bucket%' THEN 'Bucket' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%efs::filesystem%' THEN 'File System Service'  end "}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,aws_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(case when operational_state like '%Active%' then last_found_date ELSE NULL END, aws_resource_configuration_change_date, aws_ebs_attach_date, case when lower(type)='file system service' or lower(type)='bucket' then last_found_date end,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(coalesce(resourceName,resourceId))"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties", "colExpr": "configuration"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "billing_tag", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'Billing' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'Environment' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.key = 'Project' THEN lower(x.value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "accountId"}, {"colName": "volume_name", "colExpr": "case when lower(type)='volume' then ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'Name' THEN x.value END)), array(NULL))[0] else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bucket_name", "colExpr": "case when lower(type)='bucket' then ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'Name' THEN x.value END)), array(NULL))[0] else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "file_system_service_name", "colExpr": "case when lower(type)='file system service' then ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'Name' THEN x.value END)), array(NULL))[0] else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "aws_instance_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "case when lower(aws_ebs_state) in ('in-use', 'available',  'creating') then 'Active' when lower(aws_ebs_state) in ('deleting', 'deleted', 'error') then 'Inactive' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%ec2::volume%' THEN 'AWS EC2 Volume (EBS)' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%s3::bucket%' THEN 'AWS S3 Bucket' WHEN SUBSTRING_INDEX(lower(resourceType), '::', -2) LIKE '%efs::filesystem%' THEN 'AWS Elastic File System (EFS)'  end "}, {"colName": "zone_availability", "colExpr": "CASE WHEN aws_availability_zone = 'Multiple Availability Zones' THEN 'Multiple'  WHEN aws_availability_zone= 'Not Applicable'  THEN 'Not Applicable' WHEN aws_availability_zone= 'Regional'  THEN 'Regional' WHEN aws_availability_zone IS NULL THEN NULL ELSE 'Single' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "kubernetes_cluster_name", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'KubernetesCluster' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_pvc_namespace", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'kubernetes.io/created-for/pvc/namespace' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_pvc_name", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'kubernetes.io/created-for/pvc/name' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_pv_name", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'kubernetes.io/created-for/pv/name' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_size", "colExpr": "aws_ebs_size", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_iops", "colExpr": "properties.iops", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_type", "colExpr": "properties.volumeType", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_throughput", "colExpr": "properties.throughput", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "encryption_at_rest", "colExpr": "case when type='Bucket' then (case when aws_s3_server_side_encryption is not null then True else false end) else (case when properties.encrypted=true then True else false end) end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "encryption_in_transit", "colExpr": "case when resourcetype='AWS::S3::Bucket' then (case when cast(supplementaryConfiguration as string) like '%\"aws:SecureTransport\":\"true\"%' and cast(supplementaryConfiguration as string) like '%\"Effect\":\"Allow\"%' or cast(supplementaryConfiguration.BucketPolicy.policyText as string) like  '%\"aws:SecureTransport\":\"false\"%' and cast(supplementaryConfiguration.BucketPolicy.policyText as string) like '%\"Effect\":\"Deny\"%'or cast(supplementaryConfiguration.BucketPolicy.policyText as string) like '%\"Sid\":\"AllowSSLRequestsOnly\"% ' or cast(supplementaryConfiguration.BucketPolicy.policyText as string) like '%\"Sid\":\"AllowCloudFrontServicePrincipal\"%' then true else false end) else null end"}], "sourceSpecificProperties": [{"colName": "aws_resource_created_date", "colExpr": "coalesce(UNIX_MILLIS(TIMESTAMP(to_timestamp(resourceCreationTime))), UNIX_MILLIS(TIMESTAMP(to_timestamp(configuration.createTime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(configuration.creationDate))))"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN lower(coalesce(configuration.state.name,configuration.state.value)) IN ('active', 'running') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_tags", "colExpr": "tags"}, {"colName": "aws_tag", "colExpr": "concat_ws(', ', transform(tags, d -> concat(d.key,' : ',d.value)))"}, {"colName": "aws_ebs_attach_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.attachments[0].attachTime)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ebs_attachment_state", "colExpr": "properties.attachments.state", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_resource_configuration_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configurationItemCaptureTime)))"}, {"colName": "aws_ebs_associated_resource", "colExpr": "properties.attachments.associatedResource", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ebs_state", "colExpr": "INITCAP(properties.state.value)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ebs_size", "colExpr": "cast(properties.size as int)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_kms_key_id", "colExpr": "properties.kmsKeyId", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ebs_snapshot_id", "colExpr": "properties.snapshotId", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_instance_id", "colExpr": "case when aws_ebs_multi_attach_enabled=false then properties.attachments[0].instanceId else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_multi_attached_instances", "colExpr": "case when aws_ebs_multi_attach_enabled=true then properties.attachments.instanceId else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ebs_multi_attach_enabled", "colExpr": "properties.multiAttachEnabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ebs_fast_restored", "colExpr": "properties.fastRestored", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_ebs_delete_on_termination", "colExpr": "properties.attachments.deleteOnTermination[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_availability_zone", "colExpr": "availabilityZone"}, {"colName": "aws_region", "colExpr": "awsRegion"}, {"colName": "aws_ebs_csi_name", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.key = 'CSIVolumeName' THEN x.value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_efs_throughput_mode", "colExpr": "properties.ThroughputMode", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_efs_performance_mode", "colExpr": "properties.PerformanceMode", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_efs_transition_to_ia", "colExpr": "properties.LifecyclePolicies.TransitionToIA[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_efs_backup_policy_status", "colExpr": "INITCAP(properties.BackupPolicy.Status)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_owner_id", "colExpr": "properties.owner.id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "supplementary_configuration", "colExpr": "supplementaryConfiguration"}, {"colName": "aws_s3_access_control_list", "colExpr": "supplementary_configuration.AccessControlList", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_block_public_acls", "colExpr": "supplementary_configuration.PublicAccessBlockConfiguration.blockPublicAcls", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_ignore_public_acls", "colExpr": "supplementary_configuration.PublicAccessBlockConfiguration.ignorePublicAcls", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_block_public_policy", "colExpr": "supplementary_configuration.PublicAccessBlockConfiguration.blockPublicPolicy", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_restrict_public_buckets", "colExpr": "supplementary_configuration.PublicAccessBlockConfiguration.restrictPublicBuckets", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_log_bucket_name", "colExpr": "supplementary_configuration.BucketLoggingConfiguration.destinationBucketName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_bucket_policy_text", "colExpr": "supplementary_configuration.BucketPolicy.policyText", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_is_mfa_delete_enabled", "colExpr": "supplementary_configuration.BucketVersioningConfiguration.isMfaDeleteEnabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_bucket_versioning_status", "colExpr": "CASE WHEN lower(supplementary_configuration.BucketVersioningConfiguration.status) IN ('off', 'disabled') THEN 'Disabled' WHEN lower(supplementary_configuration.BucketVersioningConfiguration.status) = 'enabled' THEN 'Enabled' ELSE supplementary_configuration.BucketVersioningConfiguration.status END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_server_side_encryption", "colExpr": "supplementary_configuration.ServerSideEncryptionConfiguration.rules.applyServerSideEncryptionByDefault.sseAlgorithm[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_server_side_encryption_kms_master_key", "colExpr": "supplementary_configuration.ServerSideEncryptionConfiguration.rules.applyServerSideEncryptionByDefault.kmsMasterKeyID[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_bucket_encryption_key_enabled", "colExpr": "supplementary_configuration.ServerSideEncryptionConfiguration.rules.bucketKeyEnabled[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_transfer_acceleration", "colExpr": "supplementary_configuration.BucketAccelerateConfiguration.status", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_requester_charged", "colExpr": "supplementary_configuration.BucketAccelerateConfiguration.isRequesterCharged", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_s3_storage_class", "colExpr": "supplementary_configuration.BucketLifecycleConfiguration.rules.transitions.storageClass", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.awsRegion = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__storage__aws_resource_details__arn", "entity": {"name": "Storage"}}