{"primaryKey": "lower(id)", "origin": "'MS Azure File Share'", "commonProperties": [{"colName": "type", "colExpr": "'File System Service'"}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "azure_tags.BusinessUnit", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "azure_tags.Department", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "lease_status", "colExpr": "INITCAP(properties.leaseStatus)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure File Share'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "capacity", "colExpr": "azure_file_share_quota", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "azure_tags.Project", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "azure_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "file_system_service_name", "colExpr": "name"}, {"colName": "zone_availability", "colExpr": "CASE WHEN azure_availability_zone = 1 THEN 'Single' WHEN azure_availability_zone > 1 THEN 'Multiple' WHEN azure_availability_zone IS NULL THEN 'Not Applicable' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "encryption_in_transit", "colExpr": "azure_supports_http_traffic_only", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "encryption_at_rest", "colExpr": "case when azure_encryption_key_source is not null then true else false end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "billing_tag", "colExpr": "azure_tags.Billing", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "azure_tags.Environment", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "storage_account_access_tier", "colExpr": "SAaccessTier"}, {"colName": "storage_account_name", "colExpr": "REGEXP_extract(id,'/storageAccounts/(.*?)/fileServices', 1)"}], "sourceSpecificProperties": [{"colName": "azure_resource_group", "colExpr": "REGEXP_extract(id,'/resourceGroups/(.*?)/providers', 1)"}, {"colName": "azure_availability_zone", "colExpr": "CASE WHEN zones IS NULL OR size(zones) = 0  THEN NULL ELSE size(zones) END"}, {"colName": "azure_tags", "colExpr": "tags"}, {"colName": "azure_region", "colExpr": "location"}, {"colName": "azure_ou", "colExpr": "azure_tags.OU", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_resource_last_modified_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.lastModifiedTime)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_remaining_retention_days", "colExpr": "properties.remainingRetentionDays", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_lease_state", "colExpr": "INITCAP(properties.leaseState)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_deleted", "colExpr": "properties.deleted", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_storage_account_id", "colExpr": "REGEXP_extract(id,'/subscriptions/.*?/providers/Microsoft.Storage/storageAccounts/.*?(?=/fileServices)', 0)"}, {"colName": "azure_deleted_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.deletedTime)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_file_share_access_tier", "colExpr": "properties.accessTier", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_file_share_access_tier_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.accessTierChangeTime)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_file_share_quota", "colExpr": "properties.shareQuota", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_file_share_enabled_protocols", "colExpr": "properties.enabledProtocols", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_file_share_version", "colExpr": "properties.version", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_encryption_key_source", "colExpr": "encryptionkeySource"}, {"colName": "azure_supports_http_traffic_only", "colExpr": "supportsHttpsTrafficOnly"}, {"colName": "azure_large_file_shares_state", "colExpr": "largeFileSharesState"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "File Share", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__file_share"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__storage__ms_azure_file_share__id", "entity": {"name": "Storage"}}