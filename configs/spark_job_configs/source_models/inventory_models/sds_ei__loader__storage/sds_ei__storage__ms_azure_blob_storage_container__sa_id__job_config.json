{"primaryKey": "lower(REGEXP_extract(id,'/subscriptions/.*?/providers/Microsoft.Storage/storageAccounts/.*?(?=/blobServices)', 0))", "origin": "'MS Azure Blob Storage Container'", "dataSource": {"name": "Microsoft Azure", "feedName": "Blob Storage Container", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__blob_storage_container"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__storage__ms_azure_blob_storage_container__sa_id", "entity": {"name": "Storage"}}