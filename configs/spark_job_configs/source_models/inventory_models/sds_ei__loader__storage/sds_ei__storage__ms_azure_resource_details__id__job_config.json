{"primaryKey": "lower(id)", "filterBy": "lower(type) IN ('microsoft.compute/disks', 'microsoft.storage/storageaccounts')", "origin": "'MS Azure Resource Details'", "temporaryProperties": [{"colName": "temp_type", "colExpr": "type"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN SUBSTRING_INDEX(lower(type), '/', -1) LIKE '%storageaccounts%' THEN 'Storage Account' WHEN SUBSTRING_INDEX(lower(type), '/', -1) LIKE '%disks%' THEN 'Volume'  end"}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,azure_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "COALESCE(last_found_date,GREATEST(case when operational_state like '%Active%' then last_found_date ELSE NULL END, azure_resource_last_modified_date,azure_disk_last_ownership_update, case when type='Storage Account' then last_found_date end))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "azure_tags.BusinessUnit", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "azure_tags.Department", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "billing_tag", "colExpr": "azure_tags.Billing", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "lower(azure_tags.Environment)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "azure_vm_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_name", "colExpr": "azure_vm_name", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "case when lower(azure_disk_state) IN ('attached', 'unattached', 'activesas', 'activeupload') then 'Active' when lower(azure_disk_state) IN ('reserved', 'frozen', 'activesasfrozen', 'readytoupload') then 'Inactive' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "provisioning_state", "colExpr": "INITCAP(properties.provisioningState)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN SUBSTRING_INDEX(lower(temp_type), '/', -1) LIKE '%storageaccounts%' THEN 'Azure Storage Account' WHEN SUBSTRING_INDEX(lower(temp_type), '/', -1) LIKE '%disks%' THEN 'Azure Disk' end"}, {"colName": "encryption_at_rest", "colExpr": "case when type='Storage Account'  then  (case when azure_encryption_key_source is not null then true else false end) when lower(type)='volume' then (case when azure_disk_encryption_type is not null then true else false end) else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "encryption_in_transit", "colExpr": "azure_supports_http_traffic_only", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "CASE WHEN azure_availability_zone = 1 THEN 'Single' WHEN azure_availability_zone > 1 THEN 'Multiple' WHEN azure_availability_zone IS NULL THEN 'Not Applicable' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "azure_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_name", "colExpr": "case when lower(temp_type)='microsoft.compute/disks' then name else null end"}, {"colName": "storage_account_name", "colExpr": "case when lower(type)='storage account' then coalesce(azure_tags.Name,resource_name) else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_pvc_namespace", "colExpr": "azure_tags.kubernetes__io__created__for__pvc__namespace", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_pvc_name", "colExpr": "azure_tags.kubernetes__io__created__for__pvc__name", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_pv_name", "colExpr": "azure_tags.kubernetes__io__created__for__pv__name", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_size", "colExpr": "properties.diskSizeGB", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_iops", "colExpr": "properties.diskIOPSReadWrite", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_type", "colExpr": "case when lower(temp_type)='microsoft.compute/disks' then sku.name else null end"}, {"colName": "volume_throughput", "colExpr": "properties.diskMBpsReadWrite", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "azure_tags.Project", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "storage_account_access_tier", "colExpr": "properties.accessTier", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_sftp_enabled", "colExpr": "properties.isSftpEnabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_nfs_v3_enabled", "colExpr": "properties.isNfsV3Enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "infrastructure_encryption_applied", "colExpr": "properties.encryption.requireInfrastructureEncryption", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "allow_blob_public_access", "colExpr": "properties.allowBlobPublicAccess", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "public_network_access", "colExpr": "INITCAP(properties.publicNetworkAccess)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "azure_resource_created_date", "colExpr": "coalesce(UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.timeCreated))), UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.creationTime))))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_availability_zone", "colExpr": "CASE WHEN zones IS NULL OR size(zones) = 0  THEN NULL ELSE size(zones) END"}, {"colName": "azure_resource_last_modified_date", "colExpr": "greatest(azure_disk_last_ownership_update,azure_blob_encryption_enabled_date,azure_file_encryption_enabled_date,azure_geo_replication_last_sync, azure_storage_account_key1_creation_date,azure_storage_account_key2_creation_date )", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_region", "colExpr": "location"}, {"colName": "azure_resource_group", "colExpr": "lower(REGEXP_extract(id,'/resourceGroups/(.*?)/providers', 1))"}, {"colName": "azure_ou", "colExpr": "azure_tags.OU", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_tags", "colExpr": "tags"}, {"colName": "azure_network_policy_access", "colExpr": "properties.networkAccessPolicy", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_disk_encryption_type", "colExpr": "properties.encryption.type", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_disk_image_id", "colExpr": "properties.creationData.imageReference.id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_disk_create_option", "colExpr": "properties.creationData.createOption", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_disk_state", "colExpr": "INITCAP(properties.diskState)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_disk_last_ownership_update", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.LastOwnershipUpdateTime)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_disk_os_type", "colExpr": "properties.osType", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_disk_unique_id", "colExpr": "properties.uniqueId", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_disk_security_type", "colExpr": "properties.securityProfile.securityType", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_disk_tier", "colExpr": "properties.tier", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_disk_supports_hibernation", "colExpr": "properties.supportsHibernation", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_disk_controller_types", "colExpr": "properties.supportedCapabilities.diskControllerTypes", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_vm_id", "colExpr": "managedBy"}, {"colName": "azure_vmss_name", "colExpr": "REGEXP_extract(managedBy,'/virtualMachineScaleSets/([^/]+)', 1)"}, {"colName": "azure_vm_name", "colExpr": "REGEXP_extract(managedBy,'\\/virtualMachines\\/(.*?)($|\\/)', 1)"}, {"colName": "azure_encryption_key_source", "colExpr": "properties.encryption.keySource", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_blob_encryption_enabled", "colExpr": "properties.encryption.services.blob.enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_blob_encryption_enabled_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.encryption.services.blob.lastEnabledTime)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_blob_encryption_key_type", "colExpr": "properties.encryption.services.blob.keyType", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_file_encryption_enabled", "colExpr": "properties.encryption.services.file.enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_file_encryption_enabled_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.encryption.services.file.lastEnabledTime)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_file_encryption_key_type", "colExpr": "properties.encryption.services.file.keyType", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_supports_http_traffic_only", "colExpr": "properties.supportsHttpsTrafficOnly", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_minimum_tls_version", "colExpr": "properties.minimumTlsVersion", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_storage_account_primary_location", "colExpr": "properties.primaryLocation", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_storage_account_secondary_location", "colExpr": "properties.secondaryLocation", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_storage_account_key1_creation_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.keyCreationTime.key1)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_storage_account_key2_creation_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.keyCreationTime.key2)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_allow_cross_tenant_replication", "colExpr": "properties.allowCrossTenantReplication", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_default_o_auth", "colExpr": "properties.defaultToOAuthAuthentication", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_storage_account_allow_shared_key", "colExpr": "properties.allowSharedKeyAccess", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_is_hns_enabled", "colExpr": "properties.isHnsEnabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_large_file_shares_state", "colExpr": "properties.largeFileSharesState", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_identity_type", "colExpr": "identity.type"}, {"colName": "azure_storage_account_type", "colExpr": "case when lower(temp_type)='microsoft.storage/storageaccounts' then sku.name else null end"}, {"colName": "azure_storage_account_kind", "colExpr": "case when lower(temp_type)='microsoft.storage/storageaccounts' then kind else null end"}, {"colName": "azure_geo_replication_status", "colExpr": "properties.geoReplicationStats.status", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_geo_replication_fail_over", "colExpr": "properties.geoReplicationStats.canFailover", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_geo_replication_last_sync", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.geoReplicationStats.lastSyncTime)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_network_acls_bypass", "colExpr": "properties.networkAcls.bypass", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_network_acls_default_action", "colExpr": "properties.networkAcls.defaultAction", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_dns_endpoint_type", "colExpr": "properties.dnsEndpointType", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__storage__ms_azure_resource_details__id", "entity": {"name": "Storage"}}