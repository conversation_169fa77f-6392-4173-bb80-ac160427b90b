{"primaryKey": "lower(REGEXP_extract(id,'/subscriptions/.*?/providers/Microsoft.Storage/storageAccounts/.*?(?=/tableServices)', 0))", "origin": "'MS Azure Table Storage'", "dataSource": {"name": "Microsoft Azure", "feedName": "Table Storage", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__table_storage"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__storage__ms_azure_table_storage__sa_id", "entity": {"name": "Storage"}}