{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container__enrich"}, "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__container_belongs_to_container_group", "alias": "cnt_bl_cg1", "leftCol": "p_id", "rightCol": "source_p_id", "colEnrichments": [{"colName": "associated_container_group_count", "aggExpr": "cnt_bl_cg1.target_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__container_belongs_to_container_group", "alias": "cnt_bl_cg2", "leftCol": "p_id", "rightCol": "target_p_id", "colEnrichments": [{"colName": "container_has_container_group_count", "aggExpr": "cnt_bl_cg2.source_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__container_belongs_to_container_service", "alias": "cnt_bl_cs1", "leftCol": "p_id", "rightCol": "source_p_id", "colEnrichments": [{"colName": "associated_container_service_count", "aggExpr": "cnt_bl_cs1.target_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__container_belongs_to_container_service", "alias": "cnt_bl_cs2", "leftCol": "p_id", "rightCol": "target_p_id", "colEnrichments": [{"colName": "container_has_container_service_count", "aggExpr": "cnt_bl_cs2.source_p_id", "aggFunction": "count"}]}], "countEnriches": [{"colName": "has_vulnerability_finding_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_cloud_container"}, {"colName": "has_open_vulnerability_finding_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_cloud_container", "filter": "current_status='Open'"}, {"colName": "associated_cloud_account_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__cloud_container_resource_belongs_to_cloud_account"}], "entity": {"name": "Cloud Container"}}