{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__enrich"}, "countEnriches": [{"colName": "associated_cloud_compute_resource_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__cloud_compute_resource_belongs_to_cloud_account"}, {"colName": "associated_cloud_container_resource_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__cloud_container_resource_belongs_to_cloud_account"}, {"colName": "associated_cloud_storage_resource_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__cloud_storage_resource_belongs_to_cloud_account"}], "entity": {"name": "Cloud Account"}}