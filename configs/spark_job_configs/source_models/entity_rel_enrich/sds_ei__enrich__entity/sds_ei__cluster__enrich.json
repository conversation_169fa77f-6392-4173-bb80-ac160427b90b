{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__cluster", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__compute_instance_group_belongs_to_kubernetes_cluster", "alias": "cig_bl_kc1", "leftCol": "p_id", "rightCol": "source_p_id", "colEnrichments": [{"colName": "associated_kubernetes_cluster_count", "aggExpr": "cig_bl_kc1.target_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__compute_instance_group_belongs_to_kubernetes_cluster", "alias": "cig_bl_kc2", "leftCol": "p_id", "rightCol": "target_p_id", "colEnrichments": [{"colName": "compute_instance_group_has_kubernetes_count", "aggExpr": "cig_bl_kc2.source_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__compute_instance_group_belongs_to_mapreduce_cluster", "alias": "cig_bl_mpc1", "leftCol": "p_id", "rightCol": "source_p_id", "colEnrichments": [{"colName": "associated_mapreduce_cluster_count", "aggExpr": "cig_bl_mpc1.target_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__compute_instance_group_belongs_to_mapreduce_cluster", "alias": "cig_bl_mpc2", "leftCol": "p_id", "rightCol": "target_p_id", "colEnrichments": [{"colName": "compute_instance_group_has_mapreduce_count", "aggExpr": "cig_bl_mpc2.source_p_id", "aggFunction": "count"}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__cluster_resource_belongs_to_cloud_account", "alias": "cc_bl_ca", "leftCol": "p_id", "rightCol": "source_p_id", "colEnrichments": [{"colName": "associated_cloud_account_count", "aggExpr": "cc_bl_ca.target_p_id", "aggFunction": "count"}]}], "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__cluster__enrich"}, "entity": {"name": "Cluster"}}