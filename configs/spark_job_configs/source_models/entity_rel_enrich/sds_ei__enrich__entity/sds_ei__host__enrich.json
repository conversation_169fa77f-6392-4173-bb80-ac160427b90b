{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__host", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__host__enrich"}, "countEnriches": [{"colName": "has_vulnerability_finding_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host"}, {"colName": "has_open_vulnerability_finding_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host", "filter": "current_status='Open'"}, {"colName": "has_identity_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__host_has_identity"}, {"colName": "owned_person_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__person_owns_host"}, {"colName": "hosting_application_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__application_running_on_host"}], "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__person_owns_host", "alias": "person_owns_host", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__person", "alias": "person", "filter": "lower(employee_status)='active'", "colEnrichments": [{"colName": "active_owner_count", "aggExpr": "person_owns_host.source_p_id", "aggFunction": "count"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host", "alias": "vulnerability_finding_on_host", "filter": "lower(current_status)='open'", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability", "alias": "vulnerability", "filter": "<PERSON>OW<PERSON>(title) LIKE '%weak password vulnerability%' OR <PERSON><PERSON><PERSON>(title) LIKE '%default password vulnerability%' OR <PERSON><PERSON><PERSON>(title) LIKE '%default username-password found%' OR LOWER(title) LIKE '%default credentials%' OR <PERSON>OWER(title) LIKE '%default service credentials%' OR <PERSON>OW<PERSON>(title) <PERSON>IKE '%default password%' OR <PERSON><PERSON><PERSON>(title) <PERSON>IKE '%default password test%' OR <PERSON>OW<PERSON>(title) LIKE '%credential vulnerability%' OR <PERSON>OW<PERSON>(title) LIKE '%administration password vulnerability%' OR <PERSON>OWER(title) LIKE '%password vulnerability%' OR LOWER(title) LIKE '%password list vulnerability%' OR LOWER(title) LIKE '%administrator password vulnerability%' OR LOWER(title) LIKE '%remote access credential vulnerability%' OR LOWER(title) LIKE '%without password authentication%' OR LOWER(description) LIKE '%without password authentication%'", "colEnrichments": [{"colName": "password_vulnerability_count", "aggExpr": "vulnerability_finding_on_host.source_p_id", "aggFunction": "count"}]}]}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host", "alias": "vulnerability_finding_on_host", "filter": "lower(current_status)='open'", "inheritedPropertyEnrichment": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability", "alias": "vulnerability", "colEnrichments": [{"colName": "ssl_certificate_vulnerability_detected", "aggExpr": "case when LOWER(COALESCE(vulnerability.title, '')) LIKE '%ssl certificate%' then true else false end", "aggFunction": "max"}]}]}], "entity": {"name": "Host"}}