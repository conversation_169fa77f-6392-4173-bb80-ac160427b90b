{"entityClass": "Host", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(instance_name,fqdn,dns_name,host_name,aad_device_id,hardware_serial_number,ip,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "all"}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN 'No Data' WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "all"}}, {"colName": "inactivity_period", "colExpr": "30", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "CASE WHEN business_unit IS NULL THEN 'No Data' ELSE business_unit END", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "inter"}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "CASE WHEN lower(type) != 'no data' then type else (CASE WHEN (LOWER(internal_contributor) IN ('unknown', 'other', '') OR internal_contributor IS NULL OR TRIM(internal_contributor) = '')   AND (SIZE(host_name)=0) THEN 'No Data' WHEN LOWER(internal_contributor) RLIKE '(?i).*server.*' OR (LOWER(internal_contributor) RLIKE '.*ibm .*/400.*|.*db server|.*red hat.*enterprise.*linux.*|.*dell.*remote.*access.*|.*domain controller.*|.*hp-ux 11.*|.*hp tru64.*|.*aix.*|.*solaris.*|.*openvms.*|.*as/400.*|.*ubuntu / tiny core linux.*|.*oracle.*|.*datacenter.*|.*amazon linux.*|.*suse.*|.*red.?hat.*|.*i86pc.*|.*ilom.*|.*rhel.*|.*z/vse.*|.*qnap nas.*|.*euleros.*|.*hp.*ux.*|.*sql.*enterprise.*|.*teradata.*|.*sunos.*|.*[.]el.*|.*active directory.*|.*redhat.*|.*red hat.*|.*proliant.*|.*poweredge.*|.*idrac.*|.*nutanix.*|.*super micro.*|.*centos.*|.*netapp.*|.*hp onboard administrator.*|.*x86_64 linux/5.15.0-94-generic.*|.*ppc linux.*|.*i686 linux.*|.*unix/samba 3.6.3-.*|.*generic linux 2.6.18.*|.*rocky.*linux.*|.*freebsd(?!.*network).*|.*citrix.*virtual.*app.*|.*xenserver.*|.*bastion.*' AND LOWER(internal_contributor) NOT RLIKE '.*workstation.*') THEN 'Server' WHEN LOWER(internal_contributor) RLIKE '(?i).*r2.*windows.*|.*windows.*2003.*|.*windows.*2008.*|.*windows.*2012.*|.*windows.*2016.*|.*windows.*2022.*|.*nt.*windows.*|.*windows.*2000.*' AND LOWER(internal_contributor) LIKE '%server%' THEN 'Server' WHEN LOWER(internal_contributor) RLIKE '.*windows 2000 lan manager.*|.*linux 2.6.*|.*linux 2.4.*' THEN 'Server' WHEN LOWER(internal_contributor) RLIKE '(?i).*network device|.*freebsd.*jnpr.*network.*|.*cisco ios|.*fortinet.*|.*fortios.*|.*juniper.*|.*junos.*|.*sonicos.*|.*pulse secure.*|.*big.*ip.*|.*pulse connect.*|.*netscreen.*|.*netscaler.*|.*router.*|.*huawei ar151.*|.*ironport.*|.*.*wireless access point.*' OR LOWER(internal_contributor) RLIKE '.*biometric.*|.*switch.*|.*arista.*|.*check point gaia.*|.*aironet.*|.*cisco.*|.*pan-os.*|.*panos.*|.*palo alto.*|.*pfsense.*|.*forti.*|.*polycom.*|.*blue.*coat.*|.*mikrotik.*|.*asterisk.*|.*okilan.*|.*qnap.*|.*vxworks.*|.*extremexos.*|.*axis-.*|.*f5.*networks.*|.*openwrt.*|.*jnpr.*|.*citrix.*|.*simatic net.*|.*arista eos.*|.*brocade.*|.*fabric.*|.*timos.*|.*mcafee.*|.*alcatel.*|.*acme.*|.*cabletron.*|.*ciena.*|.*covaro.*|.*extreme networks.*|.*fibrenet.*|.*profinet.*|.*lightwave.*|.*microchip.*|.*net optics.*|.*nextep.*|.*nortel.*|.*oneaccess.*|.*optix.*|.*powertel.*|.*telstra.*|.*alteon.*|.*apc network.*|.*aruba.*|.*neterra.*|.*buffalo terastation.*|.*emcnetwork.*|.*network router.*|.*firewall.*|.*netgear.*' THEN 'Network Device' WHEN (LOWER(internal_contributor) RLIKE '.*ipad.*|.*ipod.*|.*iphone.*|.*android.*|.*windowsphone.*|.*tizen.*|.*tecno ch7n.*|.*lenovo tb-.*|.*sm-.*|.*moto.*|.*huawei.*vog-.*|.*eml-.*|.*ane-.*|.*mar-.*|.*vivo.*|.*tecno.*|.*swift.*|.*nokia.*|.*pixel.*|.*nexus.*|.*oneplus.*|.*redmi .*|.*asus_.*|.*phone.*|.*poco.*|.*realme.*|.*one-plus.*|.*tablet.*|.*appleios.*' OR lower(internal_contributor) LIKE 'ios%') AND (LOWER(internal_contributor) NOT RLIKE '.*windows.*') THEN 'Mobile' WHEN LOWER(internal_contributor) RLIKE '.*vmware.*|.*esx.*' THEN 'Hypervisor' WHEN LOWER(internal_contributor) RLIKE '.*xerox.*|.*printer.*|.*canon.*|.*hp.*laser.*|.*hp.*jetdirect.*|.*samsung x4220r.*|.*varioprint.*|.*sato network printing version.*|.*lexmark.*|.*lantronix.*|.*kyocera.*|.*hp ethernet.*|.*ricoh.*printer.*|.*epson.*printer.*' THEN 'Printer' WHEN LOWER(internal_contributor) RLIKE '.*windows 7.*|.*windows7.*|.*windows 8.*|.*windows8.*|.*windows 10.*|.*windows10.*|.*windows 11.*|.*windows11.*|.*windows xp.*|.*windows vista.*|.*desktop.*|.*workstation.*|.*chromeos.*|.*chrome os.*|.*mac.*' AND LOWER(internal_contributor) NOT RLIKE '.*2019.*|.*20.*' THEN 'Workstation' WHEN LOWER(internal_contributor) RLIKE '.*laptop.*|.*virtual host.*|.*vdi.*|.*mac.*|.*linux.*|.*win.*|.*win10.*|.*win7.*|.*windows.*|.*endpoint.*|.*debian.*|.*ubuntu.*|.*tablet.*|.*optiplex.*|.*book.*|.*hp elite.*|.*prodesk.*|.*pavilion.*|.*surface.*|.*compaq.*|.*latitude.*|.*travelmate.*|.*gaming.*|.*veriton.*|.*precision.*|.*presario.*|.*predator.*|.*inspiron.*|.*vostro.*|.*mini pc.*|.*extensa.*|.*proone.*|.*sff.*|.*tecra.*|.*thin.*|.*alienware.*|.*all-in-one pc.*|.*asus(?!_).*|.*acer.*|.*aspire.*|.*microtower.*|.*spectre.*|.*nitro.*|.*ideapad.*|.*bravo.*|.*rog.*|.*hp pro.*|.*dell xps 13.*|.*dell xps 15.*|.*lenovo legion.*|.*lenovo yoga.*|.*haiku.*|.*parrot.*|.*webos.*' OR (LOWER(internal_contributor) RLIKE '.*other.*' AND LOWER(CAST(host_name AS string)) RLIKE '.*vdi.*') OR LOWER(CAST(host_name AS string)) RLIKE '.*laptop.*|.*vdi.*|.*wvd.*|.*work.*' THEN 'Workstation' WHEN (LOWER(CAST(host_name AS string)) RLIKE '.*server.*|.*prd.*|.*app.*|.*dev.*|.*dbs.*' and lower(CAST(host_name AS string)) NOT RLIKE '.*work.*' and lower(internal_contributor) NOT RLIKE '.*ios.*|.*android.*|.*work.*') THEN 'Server' ELSE 'Other' END) END", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "inter"}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,azure_vm_os_name,azure_vm_os_version,defender_tags,qualys_tags,crowdstrike_tags,ad_distinguished_name,hardware_model,hardware_chassis_type,hardware_manufacturer,native_type,dns_name,host_name,crowdstrike_product_type_desc,itop_class,aad_device_category,aad_profile_type,cast(aad_system_label as string),tenable_io_system_type,cast(tenable_io_system_types as string),tenablesc_asset_groups)", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "inter"}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "'No Data'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os_family", "colExpr": "CASE WHEN lower(os_family) != 'no data' THEN os_family ELSE (CASE WHEN COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name) IS NULL OR regexp_like(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name), '(?i)unknown|^-$') OR TRIM(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) = '' OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) IN ('none', 'other')   THEN 'No Data'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*macos.*|.*mac.*|.*mac os.*|.*darwin.*|.*catalina.*|.*cheetah.*|.*puma.*|.*jaguar.*|.*panther.*|.*tiger.*|.*leopard.*|.*lion.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*mavericks.*|.*yosemite.*|.*capitan.*|.*sierra.*|.*mojave.*|.*big sur.*|.*monterey.*|.*sequoia.*|.*ventura.*|.*sonoma.*|.*macmdm.*'   THEN 'macOS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%nutanix%'   THEN 'AOS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%vxworks%' OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%ecos%' OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%simatic s7%'   THEN 'RTOS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*printer.*|.*hp.*laser.*|.*xerox.*versalink.*|.*xerox.*printer.*|.*ricoh.*printer.*|.*epson.*printer.*'   THEN 'Printer OS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*linux.*|.*ubuntu.*|.*centos.*|.*fedora.*|.*webos.*|.*chromeos.*|.*chromium.*|.*debian.*|.*redhat.*|.*red hat.*|.*suse.*|.*tizen.*|.*openwrt.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*embeddedos.*|.*panos.*|.*stratodesk notouch.*|.*rhel.*|.*data domain.*|.*avaya.*|.*sles.*'   THEN 'Linux'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*cisco.*|.*fortinet.*|.*juniper.*|.*sonicos.*|.*simatic net.*|.*panos.*|.*pan-os.*|.*arista eos.*|.*extremexos.*|.*pfsense.*|.*mikrotik.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*aironet.*|.*switch.*|.*qnap.*|.*check point gaia.*|.*check point.*|.*brocade.*|.*f5 networks.*|.*fabric.*|.*fortios.*|.*netapp.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*timos.*|.*mcafee.*|.*ips.*|.*f5.*|.*palo.*alto.*|.*ribbon.*|.*nec.*|.*ceragon.*|.*adva.*|.*rad.*|.*checkpoint.*|.*alcatel.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*huawei.*|.*ericsson.*|.*sandvine.*|.*genie.*|.*remote access.*|.*apple airport.*|.*hitachi nas.*|.*lantronix.*|.*acme.*|.*ciena.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*covaro.*|.*extreme networks.*|.*fibrenet.*|.*lightwave.*|.*microchip.*|.*net optics.*|.*nextep.*|.*nortel.*|.*oneaccess.*|.*optix.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*powertel.*|.*telstra.*|.*alteon.*|.*apc network.*|.*citrix.*|.*aruba.*|.*buffalo terastation.*|.*router.*|.*emcnetwork.*|.*proxysg.*|.*blue coat.*'   THEN 'Network OS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*solaris.*|.*sunos.*|.*hp.*ux.*|.*lights.*|.*ux.*|.*aix.*|.*unix.*|.*freebsd.*|.*netbsd.*|.*busybox.*'   THEN 'Unix'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*ios.*|.*ipados.*|.*ipad.*|.*ipod.*|.*iphone.*|.*tvos.*'   THEN 'iOS'   WHEN LOWER(TRIM(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name))) LIKE '%android%'   THEN 'Android'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*oracle.*|.*teradata.*|.*sql.*'   THEN 'Database'   WHEN LOWER(TRIM(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name))) LIKE '%vmware%' OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%esx%'   THEN 'Hypervisor OS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%windows%' OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%win%'   THEN 'Windows'   ELSE 'Other' END) END", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "inter"}}, {"colName": "asset_role", "colExpr": "CASE WHEN internal_contributor IS NULL AND host_name IS NULL THEN NULL WHEN LOWER(internal_contributor) RLIKE '.*database.*|.*db server.*|.*database server.*' OR (LOWER(host_name) RLIKE '.*dbs.*' AND LOWER(internal_contributor) <PERSON><PERSON>IKE '.*server.*') THEN 'Database' WHEN LOWER(host_name) RLIKE '.*prd.*|.*prod.*' AND <PERSON>OWER(internal_contributor) RLIKE '.*server.*' THEN 'Production Server' WHEN LOWER(internal_contributor) RLIKE '.*domain controller.*|.*domain_controller.*|.*domaincontroller.*|.*active directory.*|.*ad server.*|.*directory server.*' THEN 'Domain Controller' WHEN LOWER(internal_contributor) RLIKE '.*proxy.*|.*proxies.*' THEN 'Proxy' WHEN LOWER(internal_contributor) RLIKE '.*app.*server.*|.*application.*server.*|.*web application.*' OR (LOWER(host_name) RLIKE '.*app.*' AND LOWER(internal_contributor) RLIKE '.*server.*') THEN 'Application Server' WHEN LOWER(internal_contributor) RLIKE '.*load balancer.*|.*load.*balancer.*' THEN 'Load Balancer' WHEN LOWER(internal_contributor) RLIKE '.*web.*server.*|.*webserver.*|.*web_server.*' THEN 'Web Server' WHEN LOWER(internal_contributor) RLIKE '.*archive server.*' THEN 'Archive Server' WHEN LOWER(internal_contributor) RLIKE '.*citrix workspace.*' THEN 'Citrix Workspace' WHEN LOWER(internal_contributor) RLIKE '.*esxi.*' THEN 'ESXi' WHEN LOWER(internal_contributor) RLIKE '.*xen server.*' THEN 'Xen Server' WHEN LOWER(internal_contributor) RLIKE '.*aix.*server.*' THEN 'AIX Server' WHEN LOWER(internal_contributor) RLIKE '.*nicesystems.*' THEN 'Nice Systems' WHEN LOWER(internal_contributor) RLIKE '.*ivanti.*' THEN 'Ivanti' WHEN LOWER(internal_contributor) RLIKE '.*web portal.*' THEN 'Web Portal' WHEN LOWER(internal_contributor) RLIKE '.*logging system.*|.*centralize log.*|.*centralized log.*' THEN 'Logging System' WHEN LOWER(host_name) RLIKE '.*router.*' OR (LOWER(internal_contributor) RLIKE '.*router.*|.*asr.*|.*mx.*|.*ptx.*|.*ncs.*|.*cx.*|.*atn.*' AND lower(internal_contributor) NOT RLIKE '.*windows.*') THEN 'Router' WHEN LOWER(internal_contributor) RLIKE '.*security gateway.*' THEN 'Security Gateway' WHEN LOWER(internal_contributor) RLIKE '.*api gateway.*' THEN 'API Gateway' WHEN LOWER(internal_contributor) RLIKE '.*remote console manager.*' THEN 'Remote Console Manager' WHEN LOWER(internal_contributor) RLIKE '.*hypervisor.*|.*vmware.*|.*esx.*' THEN 'Hypervisor' WHEN LOWER(internal_contributor) RLIKE '.*jump host.*|.*jumphost.*' THEN 'Jump Host' WHEN LOWER(internal_contributor) RLIKE '.*firewall.*|.*firewall vpn.*|.*fortigate.*' THEN 'Firewall' WHEN LOWER(internal_contributor) RLIKE '.*wap.*' THEN 'Wireless Access Point' WHEN LOWER(internal_contributor) RLIKE '.*network switch.*|.*nexus.*' THEN 'Network Switch' WHEN LOWER(internal_contributor) RLIKE '.*enterprise storage.*' THEN 'Enterprise Storage' WHEN LOWER(internal_contributor) RLIKE '.*analytics.*|.*analytical.*' THEN 'Analytical System' WHEN LOWER(internal_contributor) RLIKE '.*backup.*|.*back up.*|.*veeam.*' THEN 'Backup' WHEN LOWER(internal_contributor) RLIKE '.*data platform.*' THEN 'Data Platform' WHEN LOWER(internal_contributor) RLIKE '.*it management system.*' THEN 'IT Management System' WHEN LOWER(internal_contributor) RLIKE '.*rnd software.*' THEN 'R&D Software' WHEN LOWER(internal_contributor) RLIKE '.*monitoring.*' THEN 'Monitoring' WHEN LOWER(internal_contributor) RLIKE '.*network traffic performance.*' THEN 'Network Performance' WHEN LOWER(internal_contributor) RLIKE '.*power management.*' THEN 'Power Management System' WHEN LOWER(internal_contributor) RLIKE '.*erp.*' THEN 'ERP System' WHEN LOWER(internal_contributor) RLIKE '.*mail.*server.*' THEN 'Mail Server' WHEN LOWER(internal_contributor) RLIKE '.*dns.*server.*|.*dns.*' THEN 'DNS Server' WHEN LOWER(internal_contributor) RLIKE '.*file.*server.*|.*fileserver.*' THEN 'File Server' WHEN LOWER(internal_contributor) RLIKE '.*printer.*|.*print server.*|.*xerox.*|.*printer.*|.*canon.*|.*hp.*laser.*|.*hp.*jetdirect.|.*samsung x4220r.*|.*varioprint.*|.*sato network printing version.*|.*lexmark.*|.*lantronix.*|.*kyocera.*|.*hp ethernet.*' THEN 'Printer' WHEN LOWER(internal_contributor) RLIKE '.*telephony.*' THEN 'Telecom System' WHEN LOWER(internal_contributor) RLIKE '.*general.*server.*|.*pci.*server.*|.*iso.*server.*|.*regular.*server.*' THEN 'General Server' WHEN LOWER(internal_contributor) RLIKE '.*vpn.*' THEN 'VPN' WHEN LOWER(internal_contributor) RLIKE '.*scanner.*|.*nessus.*' THEN 'Scanner' WHEN LOWER(internal_contributor) RLIKE '.*siem.*' THEN 'SIEM' WHEN LOWER(internal_contributor) RLIKE '.*dlp.*' THEN 'Data Loss Prevention' WHEN LOWER(internal_contributor) RLIKE '.*ftp.*' THEN 'File Transfer Protocol' WHEN LOWER(internal_contributor) RLIKE '.*smtp.*' THEN 'Simple Mail Transfer Protocol' WHEN LOWER(internal_contributor) RLIKE '.*webmail.*' THEN 'Web Mail' WHEN LOWER(internal_contributor) RLIKE '.*mdm.*|.*ios .*|.*android.*|.*phone.*' AND lower(internal_contributor) NOT RLIKE '.*windows.*' THEN 'Mobie Device Management' WHEN LOWER(internal_contributor) RLIKE '.*intranet.*' THEN 'Intranet' WHEN LOWER(internal_contributor) RLIKE '.*persistentvdi.*|.*vdi.*' OR lower(host_name) RLIKE '.*wvd.*' THEN 'Virtual Desktop' WHEN LOWER(internal_contributor) RLIKE '.*general.*purpose.*|.*desktop.*|.*laptop.*|.*tablet.*' and LOWER(internal_contributor) NOT LIKE '%server%' THEN 'General Purpose' ELSE 'Other' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "fqdn", "colExpr": "LOWER(case when lower(dns_name) like '%ip-%' then null WHEN regexp_like(dns_name,'^(?!:\\\\/\\\\/)(?=.{1,255}$)((.{1,63}[.]){1,127}(?![0-9]*$)[a-z0-9-A-Z]+[.]?)$') THEN dns_name END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "domain", "colExpr": "regexp_extract(fqdn,'^(?:[^.]++[.])((local|corp|[^.]++[^\\\\r\\\\n]++))$')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "host_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ip", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "dns_name", "colExpr": "CASE WHEN dns_name RLIKE '^([a-zA-Z0-9][-a-zA-Z0-9]*\\\\.)+[a-zA-Z]{2,}$' THEN dns_name ELSE NULL END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "netbios", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "accessibility", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_mac_address", "colExpr": "from_json(null, 'ARRAY<STRING>')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_version", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_architecture", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_build", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'No Data'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "provisioning_state", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "'No Data'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_manufacturer", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_model", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_serial_number", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_imei", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_user", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "host_last_reboot_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_product", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_onboarding_status", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_compliance_state", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_enrolled_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_last_sync_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_sync_duration", "colExpr": "datediff(from_unixtime(updated_at/1000),from_unixtime(mdm_last_sync_date/1000))", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "inter"}}, {"colName": "mdm_sync_delay_status", "colExpr": "case when mdm_sync_duration > mdm_sync_threshold then true else false end", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "inter"}}, {"colName": "mdm_sync_threshold", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_onboarding_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "cmdb_onboarding_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_product", "colExpr": "cast(null as string)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "edr_last_scan_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_cloud_reporting_sla_breach_status", "colExpr": "CASE WHEN edr_cloud_reporting_sla_duration<=edr_cloud_reporting_sla_threshold then true else false END", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "inter"}}, {"colName": "edr_cloud_reporting_sla_duration", "colExpr": "CASE WHEN datediff(from_unixtime(updated_at / 1000, 'yyyy-MM-dd'), from_unixtime(edr_last_scan_date/1000,'yyyy-MM-dd')) = -1 THEN 0 ELSE datediff(from_unixtime(updated_at / 1000, 'yyyy-MM-dd'), from_unixtime(edr_last_scan_date/1000,'yyyy-MM-dd')) END", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "inter"}}, {"colName": "edr_cloud_reporting_sla_threshold", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cmdb_last_sync_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cmdb_last_sync_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_last_scan_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_signature_update_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_block_malicious_code_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_signature_update_sla_breach_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_signature_update_sla_breach_duration", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_scan_sla_breach_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_scan_sla_breach_duration", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_signature_update_sla_duration", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_scan_sla_duration", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "fw_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "cast(null as string)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "vm_onboarding_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "vm_tracking_method", "colExpr": "cast(null as string)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "vm_last_scan_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vulnerability_last_observed_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_threat_count", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "threat_count", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "encryption_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "compliance_state", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_fim_policy_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "unapproved_location_login_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}], "fieldLevelSpec": [{"colName": "ip", "fieldsSpec": {"persistNonNullValue": false}}], "lastUpdateFields": ["type", "business_unit", "department", "description", "location_country", "host_name", "fqdn", "ip", "netbios", "accessibility", "os", "cloud_provider", "resource_id", "hardware_model", "hardware_serial_number", "login_last_date", "login_last_user", "edr_onboarding_status", "vm_onboarding_status", "mdm_product", "mdm_onboarding_status", "mdm_last_sync_date", "edr_last_scan_date", "vm_last_scan_date", "ad_distinguished_name", "ad_last_sync_date", "ad_operational_status", "aad_device_id", "aad_operational_status", "operational_state", "cloud_instance_lifecycle", "is_ephemeral", "mac_address", "account_enabled_status"], "entity": {"name": "Host"}}