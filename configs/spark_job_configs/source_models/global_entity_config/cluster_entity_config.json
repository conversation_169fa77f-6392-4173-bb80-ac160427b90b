{"entityClass": "Cluster", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(public_dns_name,private_dns_name,resource_name,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "all"}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "all"}}, {"colName": "inactivity_period", "colExpr": "180", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_score", "colExpr": "cast(null as int)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os_family", "colExpr": "CASE WHEN os IS NULL OR regexp_like(os,'(?i)unknown|^-$') THEN NULL WHEN LOWER(os) LIKE '%windows%' THEN 'Windows' WHEN LOWER(os) LIKE '%macos%' or LOWER(os) LIKE '%mac%' THEN 'macOS' WHEN lower(os) RLIKE '.*linux.*|.*ubuntu.*|.*centos.*|.*fedora.*|.*webos.*|.*chromeos.*|.*debian.*|.*redhat.*|.*tizen.*|.*panos.*|openwrt|.*embeddedos.*' THEN 'Linux' WHEN lower(os) RLIKE '.*cisco.*|.*fortinet.*|.*juniper.*|.*sonicos.*' THEN 'Network OS' WHEN lower(os) RLIKE '.*ios.*|.*ipados.*|.*ipad.*|.*iphone.*' THEN 'iOS' WHEN LOWER(TRIM(os)) LIKE '%android%' THEN 'Android' ELSE 'Other' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_threat_count", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_signature_update_sla_breach_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_signature_update_sla_breach_duration", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_scan_sla_breach_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_scan_sla_breach_duration", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_signature_update_sla_duration", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_scan_sla_duration", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "infrastructure_type", "colExpr": "CASE WHEN cloud_provider IS NOT NULL THEN 'Cloud' ELSE 'On-Premise' END", "fieldsSpec": {"computationPhase": "inter"}}], "lastUpdateFields": ["type", "business_unit", "department", "location_country", "purchase_plan_name", "os_family", "billing_tag", "environment", "edr_onboarding_status", "vm_onboarding_status", "operational_state", "accessibility", "os", "cloud_provider", "resource_id", "resource_name", "login_last_date", "login_last_user", "edr_last_scan_date", "vm_last_scan_date", "private_ip", "public_ip"], "entity": {"name": "Cluster"}}