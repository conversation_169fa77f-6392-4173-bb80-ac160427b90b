{"entityClass": "Vulnerability", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(cve_id, vendor_id, primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computationPhase": "all"}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "activity_status", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_score", "colExpr": "cast(null as int)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "normalized_severity", "colExpr": "coalesce(v31_severity, v30_severity, v2_severity)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vendor_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vulnerability_first_observed_date", "colExpr": "cast(null as bigint)"}, {"colName": "title", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cve_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v2_score", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v2_vector", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v2_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v2_exploitability", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v2_impact_score", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v30_score", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "temporal_cvss_score", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v30_vector", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v30_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v30_exploitability", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v30_impact_score", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v31_score", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v31_vector", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v31_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v31_exploitability", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "v31_impact_score", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "software_list", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "patch_available", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exploit_available", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "recommendation", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ms_recommended_update", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ms_recommended_update_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "published_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_modified_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cisa_exploit_add_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cisa_action_due_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cisa_required_action", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "epss", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "epss_percentile", "colExpr": "cast(null as double)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cwe", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cpe", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "lastUpdateFields": ["type", "first_seen_date", "business_unit", "department", "description", "title", "cve_id", "temporal_cvss_score", "v31_score", "v31_severity", "v31_impact_score", "patch_available", "exploit_available", "recommendation", "ms_recommended_update", "last_modified_date", "cwe", "normalized_severity", "nvd_status", "epss_percentile", "v40_severity", "v40_score", "epss"], "entity": {"name": "Vulnerability"}}