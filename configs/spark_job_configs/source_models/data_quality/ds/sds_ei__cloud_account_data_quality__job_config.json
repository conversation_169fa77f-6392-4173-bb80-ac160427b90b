{"pattern_analysis_fields": ["host_name", "fqdn", "dns_name", "netbios", "hardware_imei", "cloud_account_id", "os_family"], "temporal_analysis": [["first_seen_date", "first_found_date"], ["last_active_date", "last_found_date"], ["last_active_date", "updated_at"], ["last_active_date", "last_found_date", "updated_at"], ["first_seen_date", "first_found_date", "last_found_date", "updated_at"], ["first_seen_date", "first_found_date", "last_found_date", "updated_at"], ["last_found_date", "updated_at"], ["first_found_date", "last_found_date"]], "regex_pattern_analysis": {"display_label": ["^VM-TSR[0-9]{4}", "^WORK-[A-Z]{3}[0-9]{3}.ACNA.CORP.COM", "^SERVER-[A-Z]{3}[0-9]{3}.ACNA.CORP.COM", "^WORK-[A-Z]{3}[0-9]{6}.ACNA.CORP.COM", "1137[0-9]{5}", "SERVER-[A-Z]{3}[0-9]{6}.ACNA.CORP.COM", "LSERVER-[A-Z]{1}[0-9]{4}[A-Z]{1}.ACNA.CORP.COM"], "ip": ["(\"(10\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}|172\\.(1[6-9]|2\\d|3[0-1])\\.\\d{1,3}\\.\\d{1,3}|192\\.168\\.\\d{1,3}\\.\\d{1,3})\",?)"], "mdm_mac_address": ["([0-9A-F]{2}[:-]){5}([0-9A-F]{2})"], "cloud_resource_id__resolved": ["^/subscriptions/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/resourcegroups/", "^arn:aws:ec2"]}, "entity": {"name": "Cloud Account"}}