{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__compliance_standard__ms_azure", "name": "sds_ei__compliance_standard__ms_azure"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__compliance_standard__aws", "name": "sds_ei__compliance_standard__aws"}], "disambiguation": {"candidateKeys": ["id"], "confidenceMatrix": ["sds_ei__compliance_standard__ms_azure", "sds_ei__compliance_standard__aws"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}], "rollingUpFields": ["origin"], "aggregation": [{"field": "last_active_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "id", "colExpr": "upper(id[0])"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__compliance_standard"}, "entity": {"name": "Compliance Standard"}}