{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__ms_azure", "name": "sds_ei__security_control__ms_azure"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__aws", "name": "sds_ei__security_control__aws"}], "disambiguation": {"candidateKeys": [{"name": "id", "exceptionFilter": "cloud_provider = 'AWS' OR cloud_provider='Azure'"}], "confidenceMatrix": ["sds_ei__security_control__ms_azure", "sds_ei__security_control__aws"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "severity", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}], "rollingUpFields": ["origin", "associated_standards"], "aggregation": [{"field": "last_active_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "id", "colExpr": "upper(id[0])"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__security_control"}, "entity": {"name": "Security Control"}}