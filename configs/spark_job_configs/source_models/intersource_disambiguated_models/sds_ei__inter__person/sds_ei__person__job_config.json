{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__successfactors_hr__employee_id", "name": "sf"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__saviynt_iga", "name": "iga"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__active_directory__object_guid", "name": "ad"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__snow_itsm__sf_id_employee_id", "name": "snow"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_intunes__user_id", "name": "mdm"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad", "name": "msazure"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__aws_identitystore_listusers__user_id", "name": "aws_identitystore"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__bamboohr__employee_number", "name": "bamboohr"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__aws__iam_list_users__user_name", "name": "aws__iam_list_users"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__aws__iam_security_center_permission_set_assignment__user_name", "name": "sds_ei__person__aws__iam_security_center_permission_set_assignment__user_name"}], "disambiguation": {"candidateKeys": ["employee_id", "aad_user_id", "email_id", "aws_iam_user_name"], "confidenceMatrix": ["sf", "bamboohr", "iga", "ad", "snow", "mdm", "msazure", "aws_identitystore", "aws__iam_list_users", "sds_ei__person__aws__iam_security_center_permission_set_assignment__user_name"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "manager", "confidenceMatrix": ["sf", "bamboohr", "iga", "mdm", "snow", "ad"]}], "rollingUpFields": ["origin", "external_email_id"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "aggregation": [{"field": "first_seen_date", "function": "min"}, {"field": "last_active_date", "function": "max"}, {"field": "recruit_date", "function": "min"}, {"field": "last_known_termination_date", "function": "max"}, {"field": "contract_end_date", "function": "max"}, {"field": "login_last_date", "function": "max"}, {"field": "ad_last_password_change_date", "function": "max"}, {"field": "ad_last_sync_date", "function": "max"}, {"field": "ad_created_date", "function": "min"}, {"field": "termination_date", "function": "max"}]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__person", "filter": "display_label NOT RLIKE '[0-9@]'"}, "entity": {"name": "Person"}}