{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__container__ms_azure", "name": "sds_ei__container__ms_azure"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__container__aws", "name": "sds_ei__container__aws"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__container__wiz", "name": "sds_ei__container__wiz"}], "disambiguation": {"candidateKeys": ["resource_id"], "confidenceMatrix": ["sds_ei__container__ms_azure", "sds_ei__container__aws", "sds_ei__container__wiz"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [], "rollingUpFields": ["origin"], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "azure_resource_created_date", "function": "min"}, {"field": "aws_resource_created_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "operational_state", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "account_id", "colExpr": "ARRAY(account_id)"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__container"}, "entity": {"name": "Container"}}