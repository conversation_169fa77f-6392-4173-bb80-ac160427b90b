{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__finding__aws", "name": "sds_ei__finding__aws"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__finding__ms_azure", "name": "sds_ei__finding__ms_azure"}], "disambiguation": {"candidateKeys": ["finding_id"], "confidenceMatrix": ["sds_ei__finding__aws", "sds_ei__finding__ms_azure"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "finding_severity", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "compliance_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}], "rollingUpFields": ["origin"], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "aws_finding_first_seen_time", "function": "min"}, {"field": "aws_finding_last_seen_time", "function": "max"}, {"field": "azure_alert_first_seen_time", "function": "min"}, {"field": "azure_alert_last_seen_time", "function": "max"}], "valueConfidence": []}}, "derivedProperties": [{"colName": "finding_id", "colExpr": "finding_id__resolved"}, {"colName": "account_id", "colExpr": "ARRAY(account_id)"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__finding"}, "entity": {"name": "Finding"}}