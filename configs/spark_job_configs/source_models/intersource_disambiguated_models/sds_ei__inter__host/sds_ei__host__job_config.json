{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid", "name": "sds_ei__host__active_directory__object_guid"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender", "name": "sds_ei__host__ms_defender"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_intunes__device_id", "name": "sds_ei__host__ms_intunes"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys", "name": "sds_ei__host__qualys"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__globalprotect_vpn", "name": "sds_ei__host__globalprotect_vpn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__winevents", "name": "sds_ei__host__winevents"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad", "name": "sds_ei__host__ms_azure_ad"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure", "name": "sds_ei__host__ms_azure"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_sc", "name": "sds_ei__host__tenable_sc"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__crowdstrike", "name": "sds_ei__host__crowdstrike"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__itop", "name": "sds_ei__host__itop"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws", "name": "sds_ei__host__aws"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__wiz", "name": "sds_ei__host__wiz"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_io", "name": "sds_ei__host__tenable_io"}], "disambiguation": {"candidateKeys": [{"name": "aad_device_id", "exceptionFilter": "aad_device_id = '00000000-0000-0000-0000-000000000000'"}, {"name": "host_name", "exceptionFilter": "(lower(host_name) LIKE '%iphone%' OR lower(host_name) LIKE '%android%' OR lower(host_name) LIKE '%ipad%' OR lower(host_name) LIKE '%macbook%' OR host_name RLIKE '(?i)pro([^a-zA-Z0-9]|$)' OR lower(host_name) LIKE '%galaxy%' OR lower(host_name) LIKE '%samsung%') OR lower(host_name) IN ('wrk','user deleted for this device') OR (lower(os) LIKE '%android%' OR lower(os) LIKE '%appleios%' OR lower(os) LIKE '%tizen%')", "matchAttributesList": ["cloud_instance_id", "resource_id"]}, "fqdn", "hardware_serial_number", "resource_id", "cloud_instance_id", "netbios", "cloud_resource_name"], "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_defender", "sds_ei__host__ms_intunes", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__crowdstrike", "sds_ei__host__aws", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure", "sds_ei__host__tenable_sc", "sds_ei__host__itop", "sds_ei__host__wiz", "sds_ei__host__tenable_io"], "excludeValues": ["Unknown", "Other", "-", "No Data"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "login_last_user", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "defender_health_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "defender_exposure_level", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "type", "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_intunes", "sds_ei__host__ms_defender", "sds_ei__host__crowdstrike", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__aws", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure", "sds_ei__host__tenable_sc", "sds_ei__host__itop", "sds_ei__host__tenable_io"]}, {"field": "av_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "fw_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "av_block_malicious_code_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}], "rollingUpFields": ["origin", "defender_threat_name", "defender_action_type", "qualys_id", "defender_id", "qualys_asset_id", "qualys_detection_method", "vm_tracking_method", "vm_product", "defender_detection_method", "win_event_id", "ip", "mac_address", "edr_product", "asset_role"], "aggregation": [{"field": "login_last_date", "function": "max"}, {"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "mdm_enrolled_date", "function": "min"}, {"field": "vm_last_scan_date", "function": "max"}, {"field": "ad_last_sync_date", "function": "max"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "azure_resource_created_date", "function": "min"}, {"field": "ad_account_disabled_date", "function": "max"}, {"field": "defender_onboarding_date", "function": "min"}, {"field": "vulnerability_last_observed_date", "function": "max"}, {"field": "aws_instance_launch_date", "function": "min"}, {"field": "aws_resource_created_date", "function": "min"}, {"field": "tenable_io_last_authenticated_scan_date", "function": "max"}, {"field": "tenable_io_last_scan_date", "function": "max"}, {"field": "tenable_io_asset_updated_at", "function": "max"}, {"field": "tenable_io_asset_aws_terminated_date", "function": "max"}, {"field": "tenable_io_onboarding_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "vm_onboarding_status", "confidenceMatrix": ["true", "false"]}, {"field": "operational_state", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)>=0 THEN size(defender_threat_name) END"}, {"colName": "edr_threat_count", "colExpr": "defender_threat_count"}, {"colName": "asset_role", "colExpr": "case when size(asset_role)>1 and array_contains(asset_role,'Other') then array_except(asset_role,array('Other')) else asset_role end"}, {"colName": "edr_onboarding_status", "colExpr": "CASE WHEN edr_onboarding_status THEN true ELSE false END"}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN vm_onboarding_status THEN true ELSE false END"}, {"colName": "infrastructure_type", "colExpr": "CASE WHEN lower(cloud_provider) = 'no data' THEN 'On-Premise' ELSE 'Cloud' END"}, {"colName": "naming_convention_compliance_status", "colExpr": "cast(null as string)"}, {"colName": "asset_compliance_scope", "colExpr": "<PERSON><PERSON>y('No Data')"}, {"colName": "asset_is_inventoried", "colExpr": "arrays_overlap(origin,array('MS Active Directory','MS Azure AD','MS Intune','iTOP','ServiceNow ITSM','AWS','MS Azure'))"}, {"colName": "host_type_rating", "colExpr": "CASE WHEN type='Server' and infrastructure_type='On-Premise' THEN 1 WHEN type='Server' and infrastructure_type='Cloud' THEN 0.75 WHEN type IN ('Workstation') THEN 0.75 WHEN type='Mobile' THEN 0.5 WHEN type='Other' THEN 0.25 ELSE 0 END"}, {"colName": "is_edr_present", "colExpr": "CASE WHEN edr_onboarding_status THEN 1 ELSE 0 END"}, {"colName": "is_vuln_software_present", "colExpr": "CASE WHEN vm_onboarding_status THEN 1 ELSE 0 END"}, {"colName": "asset_security_posture", "colExpr": "ROUND(double((is_edr_present+is_vuln_software_present)/2),2)"}, {"colName": "host_meet_security_posture", "colExpr": "CASE WHEN asset_security_posture=1 THEN TRUE ELSE FALSE END"}, {"colName": "region", "colExpr": "CASE WHEN region is null then 'No Data' ELSE region END"}, {"colName": "location_country", "colExpr": "CASE WHEN location_country is null then 'No Data' ELSE location_country END"}, {"colName": "archival_flag", "colExpr": "CASE WHEN datediff(from_unixtime(updated_at/1000, 'yyyy-MM-dd'), coalesce(from_unixtime(last_active_date/1000, 'yyyy-MM-dd'), from_unixtime(last_updated_date/1000, 'yyyy-MM-dd'))) > CASE WHEN cloud_inactivity_period IS NOT NULL THEN 6 ELSE 70 END THEN true ELSE false END"}, {"colName": "vm_scan_sla_duration", "colExpr": "CAST (null as integer)"}, {"colName": "vm_scan_sla_breach_duration", "colExpr": "datediff(from_unixtime(updated_at/1000,'yyyy-MM-dd'),from_unixtime(vm_last_scan_date/1000,'yyyy-MM-dd'))"}, {"colName": "vm_scan_sla_breach_status", "colExpr": "CASE WHEN datediff(from_unixtime(updated_at/1000,'yyyy-MM-dd'),from_unixtime(vm_last_scan_date/1000,'yyyy-MM-dd'))<=vm_scan_sla_duration THEN false ELSE true END"}, {"colName": "account_id", "colExpr": "ARRAY(account_id)"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host"}, "entity": {"name": "Host"}}