{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__application__ms_defender", "name": "sds_ei__application__ms_defender"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__application__mega__software", "name": "sds_ei__application__mega__software"}], "disambiguation": {"candidateKeys": [{"name": "app_name"}], "confidenceMatrix": ["sds_ei__application__mega__software", "sds_ei__application__ms_defender"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": ["origin"], "fieldLevelConfidenceMatrix": [{"field": "type", "confidenceMatrix": ["sds_ei__application__mega__software", "sds_ei__application__ms_defender"]}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "retired_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__application"}, "entity": {"name": "Application"}}