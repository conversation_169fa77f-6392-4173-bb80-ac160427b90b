{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__open_data_nvd_vuln", "name": "nvd", "isEnrichSource": true, "removeFields": ["first_found_date", "last_found_date", "last_active_date", "first_seen_date", "vulnerability_first_observed_date", "recency", "observed_lifetime", "lifetime", "recent_activity"]}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__open_data_epss_data", "name": "epss", "isEnrichSource": true, "removeFields": ["first_found_date", "last_found_date", "last_active_date", "first_seen_date", "vulnerability_first_observed_date", "recency", "observed_lifetime", "lifetime", "recent_activity"]}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__tenable_sc_analysis__cve_pluginid", "name": "sds_ei__vulnerability__tenable_sc"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__wiz_vulnerability_findings__name", "name": "sds_ei__vulnerability__wiz_vulnerability_findings"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__ms_defender_device_tvm_software_vulnerabilities_delta__cve_id", "name": "defender_vulnerability"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__qualys", "name": "qualys_vulnerability"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__cisa_vulnrichment__cve_id", "name": "sds_ei__vulnerability__cisa_vulnrichment__cve_id", "isEnrichSource": true, "removeFields": ["first_found_date", "last_found_date", "last_active_date", "first_seen_date", "vulnerability_first_observed_date", "recency", "observed_lifetime", "lifetime", "recent_activity"]}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__tenable_io_vulnerabilities__cve_plugin_id", "name": "sds_ei__vulnerability__tenable_io_vulnerabilities__cve_plugin_id"}], "disambiguation": {"candidateKeys": ["cve_id"], "confidenceMatrix": ["nvd", "sds_ei__vulnerability__cisa_vulnrichment__cve_id", "defender_vulnerability", "qualys_vulnerability", "epss", "sds_ei__vulnerability__tenable_sc", "sds_ei__vulnerability__wiz_vulnerability_findings", "sds_ei__vulnerability__tenable_io_vulnerabilities__cve_plugin_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "vendor_severity", "confidenceMatrix": ["sds_ei__vulnerability__tenable_sc", "sds_ei__vulnerability__wiz_vulnerability_findings", "defender_vulnerability", "qualys_vulnerability", "sds_ei__vulnerability__tenable_io_vulnerabilities__cve_plugin_id"]}, {"field": "v30_score", "temporalConfidenceMatrix": ["last_found_date", "last_active_date"], "confidenceMatrix": ["nvd", "sds_ei__vulnerability__cisa_vulnrichment__cve_id", "defender_vulnerability", "qualys_vulnerability", "epss", "sds_ei__vulnerability__tenable_sc", "sds_ei__vulnerability__wiz_vulnerability_findings", "sds_ei__vulnerability__tenable_io_vulnerabilities__cve_plugin_id"]}, {"field": "v30_severity", "temporalConfidenceMatrix": ["last_found_date", "last_active_date"], "confidenceMatrix": ["nvd", "sds_ei__vulnerability__cisa_vulnrichment__cve_id", "defender_vulnerability", "qualys_vulnerability", "epss", "sds_ei__vulnerability__tenable_sc", "sds_ei__vulnerability__wiz_vulnerability_findings", "sds_ei__vulnerability__tenable_io_vulnerabilities__cve_plugin_id"]}, {"field": "v31_score", "temporalConfidenceMatrix": ["last_found_date", "last_active_date"], "confidenceMatrix": ["nvd", "sds_ei__vulnerability__cisa_vulnrichment__cve_id", "defender_vulnerability", "qualys_vulnerability", "epss", "sds_ei__vulnerability__tenable_sc", "sds_ei__vulnerability__wiz_vulnerability_findings", "sds_ei__vulnerability__tenable_io_vulnerabilities__cve_plugin_id"]}, {"field": "v31_severity", "temporalConfidenceMatrix": ["last_found_date", "last_active_date"], "confidenceMatrix": ["nvd", "sds_ei__vulnerability__cisa_vulnrichment__cve_id", "defender_vulnerability", "qualys_vulnerability", "epss", "sds_ei__vulnerability__tenable_sc", "sds_ei__vulnerability__wiz_vulnerability_findings", "sds_ei__vulnerability__tenable_io_vulnerabilities__cve_plugin_id"]}, {"field": "cwe", "temporalConfidenceMatrix": ["last_found_date", "last_active_date"], "confidenceMatrix": ["sds_ei__vulnerability__cisa_vulnrichment__cve_id", "nvd", "defender_vulnerability", "qualys_vulnerability", "epss", "sds_ei__vulnerability__tenable_sc", "sds_ei__vulnerability__wiz_vulnerability_findings", "sds_ei__vulnerability__tenable_io_vulnerabilities__cve_plugin_id"]}], "rollingUpFields": ["origin", "vendor_id"], "aggregation": [{"field": "last_modified_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "last_active_date", "function": "max"}, {"field": "vulnerability_first_observed_date", "function": "min"}], "valueConfidence": [{"field": "patch_available", "confidenceMatrix": ["true", "false"]}, {"field": "found_in_organisation", "confidenceMatrix": ["true", "false"]}, {"field": "qualys_pci_flag", "confidenceMatrix": ["1", "0"]}]}}, "derivedProperties": [{"colName": "vendor_severity_normalised", "colExpr": "lower( CASE WHEN vendor_severity = '5' THEN 'critical' WHEN vendor_severity = '4' THEN 'high' WHEN vendor_severity = '3' THEN 'medium' WHEN vendor_severity = '2' or vendor_severity = '1' THEN 'low' ELSE vendor_severity END)"}, {"colName": "vulnerability_severity", "colExpr": "CASE WHEN initcap(coalesce(v40_severity, v31_severity, v30_severity, v2_severity, vendor_severity_normalised)) is null or initcap(coalesce(v40_severity, v31_severity, v30_severity, v2_severity, vendor_severity_normalised))='None' THEN 'Low' ELSE initcap(coalesce(v40_severity, v31_severity, v30_severity, v2_severity, vendor_severity_normalised)) END"}, {"colName": "cvss_normalised", "colExpr": "coalesce( CASE WHEN v40_score != 0 THEN v40_score END, CASE WHEN v31_score != 0 THEN v31_score END, CASE WHEN v30_score != 0 THEN v30_score END, CASE WHEN v2_score != 0 THEN v2_score END)"}, {"colName": "exploitability", "colExpr": "CASE WHEN exploit_available THEN 'Exploitable' WHEN epss > 0.01 THEN 'Likely Exploitable' ELSE 'Exploitability Unknown' END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability", "filter": "case when origin_entity_enrich is not null then origin!=origin_entity_enrich else true end"}, "entity": {"name": "Vulnerability"}}