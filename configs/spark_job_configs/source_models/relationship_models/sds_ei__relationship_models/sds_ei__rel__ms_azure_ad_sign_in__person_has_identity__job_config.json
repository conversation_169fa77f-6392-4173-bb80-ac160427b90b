{"name": "Person Has Identity", "inverseRelationshipName": "Identity Associated With Person", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_user_sign_in", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__person__ms_azure_ad_sign_in__user_id"], "origin": "MS Azure AD", "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__identity__ms_azure_ad_sign_in__user_principal_name"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_ad_sign_in__person_has_identity", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__ms_azure_ad_sign_in__person_has_identity"}, "relationship": {"rel_name": "Person Has Identity", "name": "MS Azure AD", "feedName": "Sign-in Logs"}}