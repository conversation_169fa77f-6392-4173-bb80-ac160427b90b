{"name": "Volume Associates To Virtual Machine", "origin": "MS Azure", "inverseRelationshipName": "Virtual Machine Has Volume", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details", "origin": "MS Azure", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_storage__ms_azure_resource_details__id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__ms_azure_resource_details__managed_by"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_resource_details__volume_associates_to_virtual_machine", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__azure_resource_details__volume_associates_to_virtual_machine"}, "relationship": {"rel_name": "Volume Associates To Virtual Machine", "name": "Microsoft Azure", "feedName": "Resource Details"}}