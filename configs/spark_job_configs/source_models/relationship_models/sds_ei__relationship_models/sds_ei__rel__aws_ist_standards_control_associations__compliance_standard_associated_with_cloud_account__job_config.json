{"name": "Compliance Standard Associated With Cloud Account", "origin": "AWS", "inverseRelationshipName": "Cloud Account Associated With Compliance Standard", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__sh_list_standards_control_associations", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__compliance_standard__aws_list_standards_control_associations__standards_arn"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_account__aws_list_standards_control_associations__security_control_arn"]}], "optionalAttributes": [{"name": "standard_id", "exp": "regexp_replace(temp_id, '[/\\\\-]', ' ')", "occurrence": "LAST"}, {"name": "rel_account_id", "exp": "REGEXP_EXTRACT(SecurityControlArn, '([0-9]{12})')", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_list_standards_control_associations__compliance_standard_associated_with_cloud_account", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_list_standards_control_associations__compliance_standard_associated_with_cloud_account"}, "relationship": {"rel_name": "Compliance Standard Associated With Cloud Account", "name": "AWS", "feedName": "List Standards Control Associations"}}