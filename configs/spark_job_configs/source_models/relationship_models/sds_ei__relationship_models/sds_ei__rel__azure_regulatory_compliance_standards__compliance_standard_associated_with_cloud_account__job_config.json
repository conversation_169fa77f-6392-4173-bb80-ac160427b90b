{"name": "Compliance Standard Associated With Cloud Account", "origin": "MS Azure", "inverseRelationshipName": "Cloud Account Associated With Compliance Standard", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources", "origin": "MS Azure", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__compliance_standard__azure_regulatory_compliance_standards__name"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_account__azure_regulatory_compliance_standards_account_id"]}], "optionalAttributes": [{"name": "standard_id", "exp": "regexp_replace(temp_id, '[/\\\\-]', ' ')", "occurrence": "LAST"}, {"name": "rel_account_id", "exp": "lower(REGEXP_EXTRACT(id, '\\\\\\/[^\\\\\\/]+\\\\\\/([^\\\\\\/]+)'))", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_regulatory_compliance_standards__compliance_standard_associated_with_cloud_account", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__azure_regulatory_compliance_standards__compliance_standard_associated_with_cloud_account"}, "relationship": {"rel_name": "Compliance Standard Associated With Cloud Account", "name": "Microsoft Azure", "feedName": "Security Resources"}}