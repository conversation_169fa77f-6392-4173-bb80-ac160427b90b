{"name": "Security Control Associated With Cloud Account", "origin": "MS Azure", "inverseRelationshipName": "Cloud Account Associated With Security Control", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources", "origin": "MS Azure", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__security_control__azure_regulatory_compliance_control__name"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_account__azure_regulatory_compliance_control__account_id"]}], "optionalAttributes": [{"name": "control_id", "exp": "upper(name)", "occurrence": "LAST"}, {"name": "control_title", "exp": "properties.description", "occurrence": "LAST"}, {"name": "rel_account_id", "exp": "lower(REGEXP_EXTRACT(id, '\\\\\\/[^\\\\\\/]+\\\\\\/([^\\\\\\/]+)'))", "occurrence": "LAST"}, {"name": "rel_associated_standards", "exp": "transform(temp_stand_acc,x->REGEXP_REPLACE(x, '[/\\\\\\\\-]', ' '))", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_regulatory_compliance_controls__security_control_associated_with_cloud_account", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__azure_regulatory_compliance_controls__security_control_associated_with_cloud_account"}, "relationship": {"rel_name": "Security Control Associated With Cloud Account", "name": "Microsoft Azure", "feedName": "Security Resources"}}