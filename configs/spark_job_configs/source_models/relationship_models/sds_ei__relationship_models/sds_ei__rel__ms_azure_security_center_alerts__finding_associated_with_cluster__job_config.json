{"name": "Finding Associated With Cluster", "origin": "MS Azure", "inverseRelationshipName": "Cluster Has Finding", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_center_alerts", "origin": "MS Azure", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__finding__ms_azure_security_center_alerts__id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cluster__ms_azure_security_alerts__azure_resource_id"]}], "optionalAttributes": [{"name": "relationship_first_seen_date", "exp": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.startTimeUtc)))", "occurrence": "FIRST"}, {"name": "relationship_last_seen_date", "exp": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.endTimeUtc)))", "occurrence": "LAST"}, {"name": "rel_finding_status", "exp": "case when lower(properties.status) in ('dismissed','resolved','suppressed') then 'Closed' when lower(properties.status) not in ('dismissed','resolved','suppressed') and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(event_timestamp_epoch/1000))) > 2 then 'Closed' Else 'Open' END", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_center_alerts__finding_associated_with_cluster", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__ms_azure_security_center_alerts__finding_associated_with_cluster"}, "relationship": {"rel_name": "Finding Associated With Cluster", "name": "Microsoft Azure", "feedName": "Security Center Alerts"}}