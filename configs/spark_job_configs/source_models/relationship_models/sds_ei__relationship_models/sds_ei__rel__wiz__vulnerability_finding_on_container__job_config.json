{"name": "Vulnerability Finding On Container", "inverseRelationshipName": "Container Has Vulnerability Finding", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.wiz__vulnerability_findings", "origin": "Wiz", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__vulnerability__wiz_vulnerability_findings__name"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__container__wiz_vulnerability_findings__vulnerableassetid"], "enrichments": [{"lookupInfo": {"tableName": "<%LOOKUP_SCHEMA_NAME%>.wiz__vulnerability_findings_software", "enrichmentColumns": ["software_version", "software_name", "software_vendor", "software_full_name", "software_product"]}, "joinType": "LEFT", "joinCondition": "s.name <=> e.vulnerability_name AND s.vulnerableAsset.id <=> e.vulnerable_asset_id AND s.locationPath <=> e.vulnerability_location"}]}], "optionalAttributes": [{"name": "relationship_first_seen_date", "exp": "first_seen_timestamp_epoch", "occurrence": "FIRST"}, {"name": "relationship_last_seen_date", "exp": "UNIX_MILLIS(TIMESTAMP(to_timestamp(lastDetectedAt)))", "occurrence": "LAST"}, {"name": "vendor_status", "exp": "status", "occurrence": "LAST"}, {"name": "initial_status", "exp": "'Open'", "occurrence": "FIRST"}, {"name": "current_status", "exp": "CASE WHEN lower(status)='resolved' THEN 'Closed' ELSE 'Open' END", "occurrence": "LAST"}, {"name": "software_product", "exp": "software_product", "occurrence": "LAST"}, {"name": "software_full_name", "exp": "software_full_name", "occurrence": "LAST"}, {"name": "software_name", "exp": "software_name", "occurrence": "LAST"}, {"name": "software_vendor", "exp": "software_vendor", "occurrence": "LAST"}, {"name": "software_version", "exp": "software_version", "occurrence": "LAST"}, {"name": "path_details", "exp": "locationPath", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id", "first_seen_timestamp_epoch", "software_full_name"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__wiz__vulnerability_finding_on_container", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__wiz__vulnerability_finding_on_container"}, "relationship": {"rel_name": "Vulnerability Finding On Container", "name": "Wiz", "feedName": "Vulnerability"}}