{"name": "Cluster Resource Belongs To Cloud Account", "origin": "MS Azure", "inverseRelationshipName": "Cloud Account Has Cluster Resource", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details", "origin": "MS Azure", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cluster__ms_azure_resource_details__id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_account__azure_resource_details__account_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_resource_details__cluster_resource_belongs_to_cloud_account", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__azure_resource_details__cluster_resource_belongs_to_cloud_account"}, "relationship": {"rel_name": "Cluster Resource Belongs To Cloud Account", "name": "Microsoft Azure", "feedName": "Resource Details"}}