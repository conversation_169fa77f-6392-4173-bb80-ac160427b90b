{"name": "Vulnerability Finding On Host", "origin": "Qualys Host Vulnerability", "inverseRelationshipName": "Host Has Vulnerability Finding", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.qualys__host_vulnerability", "origin": "Qualys", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__vulnerability__qualys_host_vulnerability__qid"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__qualys_host_vulnerability__host_id"], "enrichments": [{"lookupInfo": {"tableName": "<%LOOKUP_SCHEMA_NAME%>.qualys__host_vulnerability_software", "enrichmentColumns": ["software_version", "software_name", "software_vendor", "software_full_name", "software_product"]}, "joinCondition": "s.qid = e.qid AND e.host_id = s.host_id"}]}], "temporaryProperties": [{"colName": "last_reopened_temp", "colExpr": "greatest(UNIX_MILLIS(TIMESTAMP(to_timestamp(first_reopened_datetime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(last_reopened_datetime))))"}], "optionalAttributes": [{"name": "relationship_first_seen_date", "exp": "first_found_datetime_epoch", "occurrence": "FIRST"}, {"name": "relationship_last_seen_date", "exp": "UNIX_MILLIS(TIMESTAMP(to_timestamp(last_found_datetime)))", "occurrence": "LAST"}, {"name": "last_reopened_date", "exp": "greatest(UNIX_MILLIS(TIMESTAMP(to_timestamp(first_reopened_datetime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(last_reopened_datetime))))", "occurrence": "LAST"}, {"name": "vulnerability_fixed_date", "exp": "case when (max(event_timestamp_epoch) OVER (PARTITION BY target_p_id,source_p_id,software_full_name,first_seen_timestamp_epoch ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING))=event_timestamp_epoch then (CASE WHEN ((CASE WHEN lower(status)='fixed' OR (datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(UNIX_MILLIS(TIMESTAMP(to_timestamp(last_found_datetime)))/1000))) > 180  )  THEN 'Closed' ELSE 'Open' END) = 'Closed') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(last_found_datetime))) ELSE NULL END)END", "occurrence": "LAST"}, {"name": "initial_status", "exp": "'Open'", "occurrence": "FIRST"}, {"name": "vulnerability_latest_open_date", "exp": "CASE  WHEN last_reopened_temp IS NULL THEN first_found_datetime_epoch ELSE last_reopened_temp END", "occurrence": "FIRST"}, {"name": "current_status", "exp": "CASE WHEN lower(status)='fixed' OR (datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(UNIX_MILLIS(TIMESTAMP(to_timestamp(last_found_datetime)))/1000))) > 180  )  THEN 'Closed' ELSE 'Open' END", "occurrence": "LAST"}, {"name": "vendor_status", "exp": "status", "occurrence": "LAST"}, {"name": "qualys_vulnerability_id", "exp": "qid", "occurrence": "COLLECT"}, {"name": "qualys_host_id", "exp": "host_id", "occurrence": "COLLECT"}, {"name": "software_product", "exp": "software_product", "occurrence": "LAST"}, {"name": "software_full_name", "exp": "software_full_name", "occurrence": "LAST"}, {"name": "software_vendor", "exp": "software_vendor", "occurrence": "LAST"}, {"name": "software_name", "exp": "software_name", "occurrence": "LAST"}, {"name": "software_version", "exp": "software_version", "occurrence": "LAST"}, {"name": "inactivity_period", "exp": "180", "occurrence": "LAST"}, {"name": "path_details", "exp": "results", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id", "first_found_datetime_epoch", "software_full_name"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__qualys_host_vulnerability__vulnerability_finding_on_host", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__qualys_host_vulnerability__vulnerability_finding_on_host"}, "relationship": {"rel_name": "Vulnerability Finding On Host", "name": "Qualys", "feedName": "Host Vulnerability"}}