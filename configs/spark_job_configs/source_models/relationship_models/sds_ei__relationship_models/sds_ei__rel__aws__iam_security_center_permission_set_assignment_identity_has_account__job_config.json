{"name": "Identity Has Account", "inverseRelationshipName": "Account Associated With Identity", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__iam_security_center_permission_set_assignment", "origin": "AWS IAM Center", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__identity__aws__iam_security_center_permission_set_assignment__user_name"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__account__aws__iam_security_center_permission_set_assignment__user_name"]}], "optionalAttributes": [{"name": "relationship_first_seen_date", "exp": "UNIX_MILLIS(TIMESTAMP(to_timestamp(CreatedDate)))", "occurrence": "FIRST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws__iam_security_center_permission_set_assignment_identity_has_account", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws__iam_security_center_permission_set_assignment__identity_has_account"}, "relationship": {"rel_name": "Identity Has Account", "name": "AWS", "feedName": "IAM Center"}}