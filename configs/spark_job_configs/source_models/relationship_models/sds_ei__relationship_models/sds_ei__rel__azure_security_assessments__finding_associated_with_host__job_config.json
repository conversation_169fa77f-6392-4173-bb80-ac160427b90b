{"name": "Finding Associated With Host", "origin": "MS Azure", "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_security_assessments__finding_associated_with_host", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__azure_security_assessments__finding_associated_with_host"}, "inverseRelationshipName": "Host Has Finding", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources", "origin": "MS Azure", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__finding__ms_azure_security_assessment__id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__ms_azure_assessments__cloud_resource_id"]}], "optionalAttributes": [{"name": "rel_finding_status", "exp": "case when LOWER(properties.status.code) in ('healthy','notapplicable','offbypolicy') then 'Closed' when LOWER(properties.status.code) not in ('healthy','notapplicable','offbypolicy') and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(event_timestamp_epoch/1000))) > 2 then 'Closed' else 'Open' END", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "relationship": {"rel_name": "Finding Associated With Host", "name": "Microsoft Azure", "feedName": "Security Resources"}}