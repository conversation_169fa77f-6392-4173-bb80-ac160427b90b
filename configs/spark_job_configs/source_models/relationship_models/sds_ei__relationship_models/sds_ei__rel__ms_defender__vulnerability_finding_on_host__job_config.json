{"name": "Vulnerability Finding On Host", "origin": "MS Defender", "inverseRelationshipName": "Host Has Vulnerability Finding", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_software_vuln_delta", "origin": "MS Defender", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__vulnerability__ms_defender_device_tvm_software_vulnerabilities_delta__cve_id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__ms_defender_device_tvm_software_vulnerabilities_delta__device_id"], "enrichments": [{"lookupInfo": {"tableName": "<%LOOKUP_SCHEMA_NAME%>.microsoft_azure__defender_device_software_vuln_delta_software", "enrichmentColumns": ["software_version", "software_name", "software_vendor", "software_full_name", "software_product"]}, "joinCondition": "s.cveId = e.cveId AND e.deviceId = s.deviceId"}]}], "optionalAttributes": [{"name": "ms_recommended_update", "exp": "recommendedSecurityUpdate", "occurrence": "LAST"}, {"name": "ms_recommended_update_id", "exp": "recommendedSecurityUpdateId", "occurrence": "LAST"}, {"name": "vulnerability_fixed_date", "exp": "case when (max(event_timestamp_epoch) OVER (PARTITION BY target_p_id,source_p_id,software_full_name,first_seen_timestamp_epoch ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING))=event_timestamp_epoch then (CASE WHEN ((CASE WHEN lower(status)='fixed' OR (datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp)))/1000))) > 180  )  THEN 'Closed' ELSE 'Open' END) = 'Closed') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp))) ELSE NULL END)END", "occurrence": "LAST"}, {"name": "current_status", "exp": "CASE WHEN lower(status)='fixed' OR (datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp)))/1000))) > 180  )  THEN 'Closed' ELSE 'Open' END", "occurrence": "LAST"}, {"name": "initial_status", "exp": "'Open'", "occurrence": "LAST"}, {"name": "vendor_status", "exp": "status", "occurrence": "LAST"}, {"name": "inactivity_period", "exp": "180", "occurrence": "LAST"}, {"name": "software_vendor", "exp": "software_vendor", "occurrence": "LAST"}, {"name": "software_name", "exp": "software_name", "occurrence": "LAST"}, {"name": "software_version", "exp": "software_version", "occurrence": "LAST"}, {"name": "software_product", "exp": "software_product", "occurrence": "LAST"}, {"name": "software_full_name", "exp": "software_full_name", "occurrence": "LAST"}, {"name": "relationship_first_seen_date", "exp": "first_seen_timestamp_epoch", "occurrence": "FIRST"}, {"name": "relationship_last_seen_date", "exp": "UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp)))", "occurrence": "LAST"}, {"name": "path_details", "exp": "diskPaths", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id", "software_full_name", "first_seen_timestamp_epoch"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender__vulnerability_finding_on_host", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__ms_defender__vulnerability_finding_on_host"}, "relationship": {"rel_name": "Vulnerability Finding On Host", "name": "Microsoft Defender For Endpoint", "feedName": "Device Software Vulnerability"}}