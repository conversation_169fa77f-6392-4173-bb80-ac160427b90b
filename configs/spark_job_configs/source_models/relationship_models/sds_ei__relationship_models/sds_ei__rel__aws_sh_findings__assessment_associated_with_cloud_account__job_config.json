{"name": "Assessment Associated With Cloud Account", "origin": "AWS", "inverseRelationshipName": "Cloud Account Associated With Assessment", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__assessment__aws_sh_findings__config_rule_name"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_account__aws_sh_finding__account_id"]}], "optionalAttributes": [{"name": "assessment_id", "exp": "ProductFields.RelatedAWSResources__0__name", "occurrence": "LAST"}, {"name": "rel_account_id", "exp": "CAST(AwsAccountId as string)", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_findings__assessment_associated_with_cloud_account", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_sh_findings__assessment_associated_with_cloud_account"}, "relationship": {"rel_name": "Assessment Associated With Cloud Account", "name": "AWS", "feedName": "AWS SH Findings"}}