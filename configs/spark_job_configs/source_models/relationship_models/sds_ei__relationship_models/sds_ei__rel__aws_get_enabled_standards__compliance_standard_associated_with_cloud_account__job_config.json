{"name": "Compliance Standard Associated With Cloud Account", "origin": "AWS", "inverseRelationshipName": "Cloud Account Associated With Compliance Standard", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__sh_get_enabled_standards", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__compliance_standard__aws_get_enabled_standards__standards_arn"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_account__aws_get_enabled_standards__standards_subscription_arn"]}], "optionalAttributes": [{"name": "standard_id", "exp": "regexp_replace(temp_stand_id, '[/\\\\-]', ' ')", "occurrence": "LAST"}, {"name": "rel_account_id", "exp": "REGEXP_EXTRACT(StandardsSubscriptionArn, '([0-9]{12})')", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_get_enabled_standards__compliance_standard_associated_with_cloud_account", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_get_enabled_standards__compliance_standard_associated_with_cloud_account"}, "relationship": {"rel_name": "Compliance Standard Associated With Cloud Account", "name": "AWS", "feedName": "Get Enabled Standards"}}