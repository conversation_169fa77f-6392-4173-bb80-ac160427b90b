{"name": "Virtual Machine Belongs To Compute Instance Group", "origin": "MS Azure", "inverseRelationshipName": "Compute Instance Group Has Virtual Machine", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__virtual_machine", "origin": "MS Azure", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__ms_azure_virtual_machine__resource_id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_compute__ms_azure_virtual_machine__azure_vmss_key"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_virtual_machine__virtual_machine_belongs_to_compute_instance_group", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__ms_azure_virtual_machine__virtual_machine_belongs_to_compute_instance_group"}, "relationship": {"rel_name": "Virtual Machine Belongs To Compute Instance Group", "name": "Microsoft Azure", "feedName": "Virtual Machine"}}