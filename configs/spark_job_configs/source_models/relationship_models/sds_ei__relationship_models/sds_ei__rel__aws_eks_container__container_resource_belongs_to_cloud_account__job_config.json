{"name": "Container Resource Belongs To Cloud Account", "origin": "AWS", "inverseRelationshipName": "Cloud Account Has Container Resource", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws_eks_container", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__container__aws_eks_container__containerid"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_account__aws_eks_container__account_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_eks_container__container_resource_belongs_to_cloud_account", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_eks_container__container_resource_belongs_to_cloud_account"}, "relationship": {"rel_name": "Container Resource Belongs To Cloud Account", "name": "AWS", "feedName": "<PERSON><PERSON> Container"}}