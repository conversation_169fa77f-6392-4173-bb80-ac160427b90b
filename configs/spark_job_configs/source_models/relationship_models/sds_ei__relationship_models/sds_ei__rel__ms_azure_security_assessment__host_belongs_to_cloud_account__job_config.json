{"name": "Host Belongs To Cloud Account", "origin": "MS Azure", "inverseRelationshipName": "Cloud Account Has Host", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources", "origin": "MS Azure", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__ms_azure_assessments__cloud_resource_id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_account__ms_azure_security_assessment__account_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_assessment__host_belongs_to_cloud_account", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__ms_azure_security_assessment__host_belongs_to_cloud_account"}, "relationship": {"rel_name": "Host Belongs To Cloud Account", "name": "Microsoft Azure", "feedName": "Security Resources"}}