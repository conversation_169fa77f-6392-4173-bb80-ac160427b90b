{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_finding__finding_associated_with_cluster", "name": "sds_ei__rel__aws_sh_finding__finding_associated_with_cluster"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_security_assessments__finding_associated_with_cluster", "name": "sds_ei__rel__azure_security_assessments__finding_associated_with_cluster"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_center_alerts__finding_associated_with_cluster", "name": "sds_ei__rel__ms_azure_security_center_alerts__finding_associated_with_cluster"}], "disambiguation": {"disambiguationGrouping": {"type": "Union"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_cluster"}, "entity": {"name": "Finding Associated With Cluster"}}