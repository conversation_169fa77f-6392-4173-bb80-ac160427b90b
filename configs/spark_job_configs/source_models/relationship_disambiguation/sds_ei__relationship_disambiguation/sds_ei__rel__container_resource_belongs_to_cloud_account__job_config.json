{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_ecs_container__container_resource_belongs_to_cloud_account", "name": "sds_ei__rel__aws_ecs_container__container_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_eks_container__container_resource_belongs_to_cloud_account", "name": "sds_ei__rel__aws_eks_container__container_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_resource_details__container_resource_belongs_to_cloud_account", "name": "sds_ei__rel__aws_resource_details__container_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_aci_container__container_resource_belongs_to_cloud_account", "name": "sds_ei__rel__azure_aci_container__container_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_resource_details__container_resource_belongs_to_cloud_account", "name": "sds_ei__rel__azure_resource_details__container_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_findings__container_resource_belongs_to_cloud_account", "name": "sds_ei__rel__aws_sh_findings__container_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_assessment__container_resource_belongs_to_cloud_account", "name": "sds_ei__rel__ms_azure_security_assessment__container_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_center_alerts__container_resource_belongs_to_cloud_account", "name": "sds_ei__rel__ms_azure_security_center_alerts__container_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__wiz_cloud_resource__container_resource_belongs_to_cloud_account", "name": "sds_ei__rel__wiz_cloud_resource__container_resource_belongs_to_cloud_account"}], "disambiguation": {"disambiguationGrouping": {"type": "VariablesBased", "blockVariables": ["source_p_id", "target_p_id"]}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__container_resource_belongs_to_cloud_account"}, "entity": {"name": "Container Resource Belongs To Cloud Account"}}