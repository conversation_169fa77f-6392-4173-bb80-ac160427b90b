{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__wiz__vulnerability_finding_on_container", "name": "sds_ei__rel__wiz__vulnerability_finding_on_container"}, {"tableName": "<%EI_SCHEMA_NAME%>.dummy", "name": "dummy"}], "disambiguation": {"disambiguationGrouping": {"type": "VulnerabilityFinding", "blockVariables": ["source_p_id", "target_p_id", "software_full_name"], "statusField": "current_status"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin", "vendor_status", "path_details"], "aggregation": []}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_container"}, "entity": {"name": "Vulnerability Finding On Container"}}