{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_resource_details__cluster_resource_belongs_to_cloud_account", "name": "sds_ei__rel__azure_resource_details__cluster_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_resource_details__cluster_resource_belongs_to_cloud_account", "name": "sds_ei__rel__aws_resource_details__cluster_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_emr_cluster__cluster_resource_belongs_to_cloud_account", "name": "sds_ei__rel__aws_emr_cluster__cluster_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_emr_ec2_fleet__cluster_resource_belongs_to_cloud_account", "name": "sds_ei__rel__aws_emr_ec2_fleet__cluster_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_findings__cluster_resource_belongs_to_cloud_account", "name": "sds_ei__rel__aws_sh_findings__cluster_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_assessment__cluster_resource_belongs_to_cloud_account", "name": "sds_ei__rel__ms_azure_security_assessment__cluster_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_center_alerts__cluster_resource_belongs_to_cloud_account", "name": "sds_ei__rel__ms_azure_security_center_alerts__cluster_resource_belongs_to_cloud_account"}], "disambiguation": {"disambiguationGrouping": {"type": "VariablesBased", "blockVariables": ["source_p_id", "target_p_id"]}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__cluster_resource_belongs_to_cloud_account"}, "entity": {"name": "Cluster Resource Belongs To Cloud Account"}}