{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_resource_details__host_belongs_to_cloud_account", "name": "sds_ei__rel__azure_resource_details__host_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_virtual_machine__host_belongs_to_cloud_account", "name": "sds_ei__rel__azure_virtual_machine__host_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_resource_details__host_belongs_to_cloud_account", "name": "sds_ei__rel__aws_resource_details__host_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_ec2_instance__host_belongs_to_cloud_account", "name": "sds_ei__rel__aws_ec2_instance__host_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_emr_ec2_instance__host_belongs_to_cloud_account", "name": "sds_ei__rel__aws_emr_ec2_instance__host_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_findings__host_belongs_to_cloud_account", "name": "sds_ei__rel__aws_sh_findings__host_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_assessment__host_belongs_to_cloud_account", "name": "sds_ei__rel__ms_azure_security_assessment__host_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_center_alerts__host_belongs_to_cloud_account", "name": "sds_ei__rel__ms_azure_security_center_alerts__host_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__wiz_cloud_resource__host_belongs_to_cloud_account", "name": "sds_ei__rel__wiz_cloud_resource__host_belongs_to_cloud_account"}], "disambiguation": {"disambiguationGrouping": {"type": "VariablesBased", "blockVariables": ["source_p_id", "target_p_id"]}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__host_belongs_to_cloud_account"}, "entity": {"name": "Host Belongs To Cloud Account"}}