{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_emr__compute_instance_group_belongs_to_mapreduce_cluster", "name": "sds_ei__rel__aws_emr__compute_instance_group_belongs_to_mapreduce_cluster"}], "disambiguation": {"disambiguationGrouping": {"type": "Union"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__compute_instance_group_belongs_to_mapreduce_cluster"}, "entity": {"name": "Compute Instance Group Belongs To Mapreduce Cluster"}}