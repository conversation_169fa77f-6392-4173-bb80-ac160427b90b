{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_finding__finding_associated_with_container", "name": "sds_ei__rel__aws_sh_finding__finding_associated_with_container"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_security_assessments__finding_associated_with_container", "name": "sds_ei__rel__azure_security_assessments__finding_associated_with_container"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_center_alerts__finding_associated_with_container", "name": "sds_ei__rel__ms_azure_security_center_alerts__finding_associated_with_container"}], "disambiguation": {"disambiguationGrouping": {"type": "Union"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__finding_associated_with_container"}, "entity": {"name": "Finding Associated With Container"}}