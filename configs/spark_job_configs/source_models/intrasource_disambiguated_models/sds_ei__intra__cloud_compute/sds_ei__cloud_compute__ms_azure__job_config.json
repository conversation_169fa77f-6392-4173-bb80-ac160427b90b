{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_azure_resource_details__id", "name": "sds_ei__cloud_compute__ms_azure_resource_details__id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_azure_assessments__resource_id", "name": "sds_ei__cloud_compute__ms_azure_assessments__resource_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_azure_security_alerts__azure_resource_id", "name": "sds_ei__cloud_compute__ms_azure_security_alerts__azure_resource_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_azure_virtual_machine__azure_vmss_key", "name": "sds_ei__cloud_compute__ms_azure_virtual_machine__azure_vmss_key"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_azure_aci_container__cluster_id", "name": "sds_ei__cloud_compute__ms_azure_aci_container__cluster_id"}], "disambiguation": {"candidateKeys": ["primary_key", "azure_vmss_key"], "confidenceMatrix": ["sds_ei__cloud_compute__ms_azure_resource_details__id", "sds_ei__cloud_compute__ms_azure_assessments__resource_id", "sds_ei__cloud_compute__ms_azure_security_alerts__azure_resource_id", "sds_ei__cloud_compute__ms_azure_virtual_machine__azure_vmss_key", "sds_ei__cloud_compute__ms_azure_aci_container__cluster_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "azure_resource_created_date", "function": "min"}, {"field": "login_last_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "operational_state", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Azure'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}, {"colName": "properties", "colExpr": "cast(null as string)"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_azure", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "MS Azure"}, "entity": {"name": "Cloud Compute"}}