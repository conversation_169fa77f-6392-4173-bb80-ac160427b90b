{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_resource_details__arn", "name": "sds_ei__cloud_compute__aws_resource_details__arn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_sh_findings__resource_id", "name": "sds_ei__cloud_compute__aws_sh_findings__resource_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_emr_cluster__resource_id", "name": "sds_ei__cloud_compute__aws_emr_cluster__resource_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_emr_ec2_fleet__resource_id", "name": "sds_ei__cloud_compute__aws_emr_ec2_fleet__resource_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_resource_details__aws_autoscaling_group_key", "name": "sds_ei__cloud_compute__aws_resource_details__aws_autoscaling_group_key"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_emr_ec2_fleet__cluster_id", "name": "sds_ei__cloud_compute__aws_emr_ec2_fleet__cluster_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_resource_details__aws_eks_cluster_key", "name": "sds_ei__cloud_compute__aws_resource_details__aws_eks_cluster_key"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_emr_ec2_instance__fleet_id", "name": "sds_ei__cloud_compute__aws_emr_ec2_instance__fleet_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_ecs_task_container__cluster_arn", "name": "sds_ei__cloud_compute__aws_ecs_task_container__cluster_arn"}], "disambiguation": {"candidateKeys": ["primary_key", "aws_autoscaling_group_key", "aws_eks_cluster_key"], "confidenceMatrix": ["sds_ei__cloud_compute__aws_resource_details__arn", "sds_ei__cloud_compute__aws_emr_ec2_fleet__resource_id", "sds_ei__cloud_compute__aws_emr_cluster__resource_id", "sds_ei__cloud_compute__aws_resource_details__aws_autoscaling_group_key", "sds_ei__cloud_compute__aws_emr_ec2_fleet__cluster_id", "sds_ei__cloud_compute__aws_resource_details__aws_eks_cluster_key", "sds_ei__cloud_compute__aws_emr_ec2_instance__fleet_id", "sds_ei__cloud_compute__aws_sh_findings__resource_id", "sds_ei__cloud_compute__aws_ecs_task_container__cluster_arn"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "aws_resource_created_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "operational_state", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'AWS'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}, {"colName": "properties", "colExpr": "cast(null as string)"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "AWS"}, "entity": {"name": "Cloud Compute"}}