{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad_registered_users__aad_user_id", "name": "sds_ei__person__ms_azure_ad_registered_users__aad_user_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad_users__aad_id", "name": "sds_ei__person__ms_azure_ad_users__aad_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad_sign_in__user_id", "name": "sds_ei__person__ms_azure_ad_sign_in__user_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__person__ms_azure_ad_users__aad_id", "sds_ei__person__ms_azure_ad_registered_users__aad_user_id", "sds_ei__person__ms_azure_ad_sign_in__user_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "aggregation": [{"field": "first_seen_date", "function": "min"}, {"field": "last_active_date", "function": "max"}], "rollingUpFields": ["external_email_id"]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Azure AD'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "MS Azure AD"}, "entity": {"name": "Person"}}