{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__wiz_cloud_resource__account_id", "name": "sds_ei__cloud_account__wiz_cloud_resource__account_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__cloud_account__wiz_cloud_resource__account_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'Wiz'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__wiz", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Wiz"}, "entity": {"name": "Cloud Account"}}