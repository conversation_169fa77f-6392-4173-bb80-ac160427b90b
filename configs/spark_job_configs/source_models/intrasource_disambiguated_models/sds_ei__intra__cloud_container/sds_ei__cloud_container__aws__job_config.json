{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container__aws_resource_details__arn", "name": "sds_ei__cloud_container__aws_resource_details__arn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container__aws_eks_container__containerid", "name": "sds_ei__cloud_container__aws_eks_container__containerid"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container__aws_ecs_task_container__containerarn", "name": "sds_ei__cloud_container__aws_ecs_task_container__containerarn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container__aws_sh_findings__resource_id", "name": "sds_ei__cloud_container__aws_sh_findings__resource_id"}], "disambiguation": {"candidateKeys": ["primary_key", "resource_id"], "confidenceMatrix": ["sds_ei__cloud_container__aws_resource_details__arn", "sds_ei__cloud_container__aws_eks_container__containerid", "sds_ei__cloud_container__aws_ecs_task_container__containerarn", "sds_ei__cloud_container__aws_sh_findings__resource_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "aws_resource_created_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "operational_state", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'AWS'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}, {"colName": "properties", "colExpr": "cast(null as string)"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container__aws", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "AWS"}, "entity": {"name": "Cloud Container"}}