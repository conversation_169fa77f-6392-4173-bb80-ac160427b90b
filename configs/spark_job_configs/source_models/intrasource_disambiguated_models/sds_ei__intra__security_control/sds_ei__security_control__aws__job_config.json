{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__aws_list_security_control_definitions__security_control_id", "name": "sds_ei__security_control__aws_list_security_control_definitions__security_control_id", "isEnrichSource": true, "removeFields": ["first_found_date", "last_found_date", "last_active_date", "recency", "observed_lifetime", "lifetime", "recent_activity"]}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__aws_list_standards_control_associations__security_control_id", "name": "sds_ei__security_control__aws_list_standards_control_associations__security_control_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__aws_describe_standards_controls__control_id", "name": "sds_ei__security_control__aws_describe_standards_controls__control_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__aws_sh_findings__security_control_id", "name": "sds_ei__security_control__aws_sh_findings__security_control_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__security_control__aws_list_standards_control_associations__security_control_id", "sds_ei__security_control__aws_describe_standards_controls__control_id", "sds_ei__security_control__aws_list_security_control_definitions__security_control_id", "sds_ei__security_control__aws_sh_findings__security_control_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "fieldLevelConfidenceMatrix": [{"field": "status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "severity", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}]}}, "rollingUpFields": ["associated_standards"], "derivedProperties": [{"colName": "origin", "colExpr": "'AWS'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}, {"colName": "status", "colExpr": "case when size(data_source_subset_name)<=1 and data_source_subset_name[0]=='AWS SH Findings' then 'Disabled' else 'Enabled' end"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__aws", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "filter": "case when origin_entity_enrich is not null then data_source_subset_name!=origin_entity_enrich else true end"}, "dataSource": {"name": "AWS"}, "entity": {"name": "Security Control"}}