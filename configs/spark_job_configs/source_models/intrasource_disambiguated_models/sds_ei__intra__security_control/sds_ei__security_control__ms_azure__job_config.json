{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__azure_regulatory_compliance_control__name", "name": "sds_ei__security_control__azure_regulatory_compliance_control__name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__azure_regulatory_compliance_assessments__control_id", "name": "sds_ei__security_control__azure_regulatory_compliance_assessments__control_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__security_control__azure_regulatory_compliance_control__name", "sds_ei__security_control__azure_regulatory_compliance_assessments__control_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "fieldLevelConfidenceMatrix": [{"field": "status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}]}}, "rollingUpFields": ["associated_standards"], "derivedProperties": [{"colName": "origin", "colExpr": "'MS Azure'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__ms_azure", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "MS Azure"}, "entity": {"name": "Security Control"}}