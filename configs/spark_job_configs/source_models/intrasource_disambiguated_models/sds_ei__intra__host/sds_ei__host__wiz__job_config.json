{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__wiz_cloud_resource__id", "name": "sds_ei__host__wiz_cloud_resource__id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__wiz_vulnerability_findings__vulnerableasset_id", "name": "sds_ei__host__wiz_vulnerability_findings__vulnerableasset_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__host__wiz_cloud_resource__id", "sds_ei__host__wiz_vulnerability_findings__vulnerableasset_id"], "excludeValues": ["Unknown", "Other", "-", "No Data"], "strategy": {"aggregation": [{"field": "first_seen_date", "function": "min"}, {"field": "last_active_date", "function": "max"}], "rollingUpFields": ["ip"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'Wiz'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__wiz", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Wiz"}, "entity": {"name": "Host"}}