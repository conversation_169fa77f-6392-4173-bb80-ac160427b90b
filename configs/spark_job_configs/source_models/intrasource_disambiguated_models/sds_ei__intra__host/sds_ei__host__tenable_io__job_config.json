{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_io_assets__id", "name": "sds_ei__host__tenable_io_assets__id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_io_vulnerabilities__asset_uuid", "name": "sds_ei__host__tenable_io_vulnerabilities__asset_uuid"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__host__tenable_io_assets__id", "sds_ei__host__tenable_io_vulnerabilities__asset_uuid"], "excludeValues": ["Unknown", "Other", "-", "No Data"], "strategy": {"rollingUpFields": ["tenable_io_ipv4_addresses", "tenable_io_ipv6_addresses", "ip"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "vm_last_scan_date", "function": "max"}, {"field": "tenable_io_last_authenticated_scan_date", "function": "max"}, {"field": "tenable_io_last_scan_date", "function": "max"}, {"field": "tenable_io_asset_updated_at", "function": "max"}, {"field": "tenable_io_asset_aws_terminated_date", "function": "max"}, {"field": "tenable_io_onboarding_date", "function": "min"}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'Tenable.io'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_io", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Tenable.io"}, "entity": {"name": "Host"}}