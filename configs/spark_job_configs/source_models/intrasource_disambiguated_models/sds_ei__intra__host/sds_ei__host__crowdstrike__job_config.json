{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__crowdstrike_host__device_id", "name": "sds_ei__host__crowdstrike_host__device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__crowdstrike_zta__aid", "name": "sds_ei__host__crowdstrike_zta__aid"}], "disambiguation": {"candidateKeys": ["crowdstrike_device_id"], "confidenceMatrix": ["sds_ei__host__crowdstrike_host__device_id", "sds_ei__host__crowdstrike_zta__aid"], "excludeValues": ["Unknown", "Other", "-", "No Data"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "crowdstrike_modified_date", "function": "max"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "host_last_reboot_date", "function": "max"}, {"field": "crowdstrike_last_report_date", "function": "max"}, {"field": "crowdstrike_agent_local_date", "function": "max"}, {"field": "crowdstrike_onboarding_date", "function": "min"}, {"field": "crowdstrike_reboot_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "rollingUpFields": ["ip"], "fieldLevelConfidenceMatrix": [{"field": "crowdstrike_onboarding_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'CrowdStrike'"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__crowdstrike", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Crowdstrike"}, "entity": {"name": "Host"}}