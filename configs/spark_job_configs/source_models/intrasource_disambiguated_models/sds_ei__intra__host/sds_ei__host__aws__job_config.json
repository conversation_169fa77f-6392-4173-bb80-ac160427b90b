{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_resource_details__arn", "name": "sds_ei__host__aws_resource_details__arn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_ec2_instance__instanceid", "name": "sds_ei__host__aws_ec2_instance__instanceid"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_sh_findings__cloud_instance_id", "name": "sds_ei__host__aws_sh_findings__cloud_instance_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_emr_ec2_instance__instanceid", "name": "sds_ei__host__aws_emr_ec2_instance__instanceid"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_resource_details__instance_id", "name": "sds_ei__host__aws_resource_details__instance_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__host__aws_resource_details__arn", "sds_ei__host__aws_ec2_instance__instanceid", "sds_ei__host__aws_emr_ec2_instance__instanceid", "sds_ei__host__aws_resource_details__instance_id", "sds_ei__host__aws_sh_findings__cloud_instance_id"], "excludeValues": ["Unknown", "Other", "-", "No Data"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "aws_instance_launch_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "operational_state", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'AWS'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "AWS"}, "entity": {"name": "Host"}}