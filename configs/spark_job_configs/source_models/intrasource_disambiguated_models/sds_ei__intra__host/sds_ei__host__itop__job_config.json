{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__itop_pc__host_name", "name": "sds_ei__host__itop_pc__host_name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__itop_servers__host_name", "name": "sds_ei__host__itop_servers__host_name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__itop_vm__host_name", "name": "sds_ei__host__itop_vm__host_name"}], "disambiguation": {"candidateKeys": ["host_name", "hardware_serial_number"], "confidenceMatrix": ["sds_ei__host__itop_pc__host_name", "sds_ei__host__itop_servers__host_name", "sds_ei__host__itop_vm__host_name"], "excludeValues": ["Unknown", "Other", "-", "No Data"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}], "rollingUpFields": []}}, "derivedProperties": [{"colName": "origin", "colExpr": "'iTOP'"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__itop", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "iTOP"}, "entity": {"name": "Host"}}