{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad_devices__device_id", "name": "sds_ei__host__ms_azure_ad_devices__device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad_registered_users__id", "name": "sds_ei__host__ms_azure_ad_registered_users__id"}, {"name": "sds_ei__host__ms_azure_ad__user_sign_in__device_id", "path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad__user_sign_in__device_id"}], "disambiguation": {"candidateKeys": ["primary_key", "aad_device_id"], "confidenceMatrix": ["sds_ei__host__ms_azure_ad_devices__device_id", "sds_ei__host__ms_azure_ad_registered_users__id", "sds_ei__host__ms_azure_ad__user_sign_in__device_id"], "excludeValues": ["Unknown", "Other", "No Data", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}], "rollingUpFields": ["ip"], "fieldLevelConfidenceMatrix": [{"field": "login_last_user", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Azure AD'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "MS Azure AD"}, "entity": {"name": "Host"}}