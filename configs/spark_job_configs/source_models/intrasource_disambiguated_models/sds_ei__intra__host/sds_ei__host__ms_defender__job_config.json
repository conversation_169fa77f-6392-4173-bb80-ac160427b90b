{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_list__id", "name": "device_list"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_software_inventory__device_id", "name": "software"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_tvm__device_id", "name": "tvm"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_events__device_id", "name": "events"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_tvm_software_vulnerabilities_delta__device_id", "name": "vulnerability"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["device_list", "software", "tvm", "events", "vulnerability"], "excludeValues": ["Unknown", "Other", "-", "No Data"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "defender_health_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "defender_exposure_level", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "av_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "av_block_malicious_code_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "fw_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "defender_onboarding_date", "function": "min"}, {"field": "vulnerability_last_observed_date", "function": "max"}, {"field": "vm_last_scan_date", "function": "max"}], "rollingUpFields": ["defender_threat_name", "defender_action_type"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "defender_detection_method", "confidenceMatrix": ["Defender Agent", "Network Scan"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Defender'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "MS Defender"}, "entity": {"name": "Host"}}