{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_sc_analysis__tenable_uuid", "name": "tenable_analysis"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_sc_assets__defineddnsnames", "name": "tenable_sc_assets"}], "disambiguation": {"candidateKeys": ["host_name", "dns_name"], "confidenceMatrix": ["tenable_analysis", "tenable_sc_assets"], "excludeValues": ["Unknown", "Other", "-", "No Data"], "strategy": {"rollingUpFields": ["tenablesc_repositories", "tenablesc_asset_groups"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'Tenable.sc'"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_sc", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Tenable.sc"}, "entity": {"name": "Host"}}