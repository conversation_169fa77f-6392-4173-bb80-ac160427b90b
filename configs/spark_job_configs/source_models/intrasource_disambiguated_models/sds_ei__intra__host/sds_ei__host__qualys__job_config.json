{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys_host_list__host_id", "name": "sds_ei__host__qualys_host_list__host_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys_host_summary__host_id", "name": "sds_ei__host__qualys_host_summary__host_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys_host_vulnerability__host_id", "name": "sds_ei__host__qualys_host_vulnerability__host_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__host__qualys_host_list__host_id", "sds_ei__host__qualys_host_summary__host_id", "sds_ei__host__qualys_host_vulnerability__host_id"], "excludeValues": ["Unknown", "Other", "-", "No Data"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "vm_last_scan_date", "function": "max"}], "rollingUpFields": ["vm_tracking_method", "qualys_detection_method", "ip"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'Qualys'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Qualys"}, "entity": {"name": "Host"}}