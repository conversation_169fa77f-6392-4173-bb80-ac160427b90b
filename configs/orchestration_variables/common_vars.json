{"INSIGHT_API_SSL_VERIFY": true, "MANAGEMENT_API_SSL_VERIFY": {"verify": true}, "vra_start_epoch_default": "0000000000000", "SDS_VRA_ANALYTICAL_JOBS_VARIABLES": {"analysis_period": "day"}, "sds_software_extraction_start_epoch_default": "0000000000000", "sds_ei_start_epoch_default": "0000000000000", "EI_PUBLISH_SCHEMA_NAME": "<%EI_PUBLISH_SCHEMA_NAME%>", "EI_FRAGMENT_PUBLISH_SCHEMA_NAME": "<%EI_FRAGMENT_PUBLISH_SCHEMA_NAME%>", "EI_RELATIONSHIP_SCHEMA_NAME": "<%EI_PUBLISH_SCHEMA_NAME%>", "KG_FRAGMENT_SCHEMA": "<%KG_FRAGMENT_SCHEMA%>", "EI_SCHEMA_NAME": "<%EI_SCHEMA_NAME%>", "hive_metastore_uri": "thrift://hivemetastore.<%KUBE_NAMESPACE%>.svc.cluster.local:9083", "EI_PUBLISHER_CONFIGS_LIST_PATH": "<%CONFIG_ARTIFACTORY_URI%><%EI_PUBLISHER_LIST_CONFIG_BASE_PATH%>", "EI_PUBLISHER_CONFIGS_BASE_PATH": "<%CONFIG_ARTIFACTORY_URI%><%EI_PUBLISHER_CONFIG_BASE_PATH%>", "SDS_ORCHESTRATOR_CONFIG": {"SDS_EI_PIPELINE": {"head_dag_id": "sds_ei_start", "tail_dag_id": "puppy_graph_olap_pipeline", "dependencies": [{"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__account_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__application_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__cloud_account_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__cloud_compute_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__cloud_container_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__cloud_storage_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__finding_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__host_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__identity_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__person_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__vulnerability_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__assessment_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__compliance_standard_dag"}, {"dependencies": ["sds_ei_start"], "dependent_dag": "sds_ei__loader__security_control_dag"}, {"dependencies": ["sds_ei__loader__account_dag"], "dependent_dag": "sds_ei__intra__account_dag"}, {"dependencies": ["sds_ei__loader__application_dag"], "dependent_dag": "sds_ei__intra__application_dag"}, {"dependencies": ["sds_ei__loader__cloud_account_dag"], "dependent_dag": "sds_ei__intra__cloud_account_dag"}, {"dependencies": ["sds_ei__loader__cloud_compute_dag"], "dependent_dag": "sds_ei__intra__cloud_compute_dag"}, {"dependencies": ["sds_ei__loader__cloud_container_dag"], "dependent_dag": "sds_ei__intra__cloud_container_dag"}, {"dependencies": ["sds_ei__loader__cloud_storage_dag"], "dependent_dag": "sds_ei__intra__cloud_storage_dag"}, {"dependencies": ["sds_ei__loader__finding_dag"], "dependent_dag": "sds_ei__intra__finding_dag"}, {"dependencies": ["sds_ei__loader__host_dag"], "dependent_dag": "sds_ei__intra__host_dag"}, {"dependencies": ["sds_ei__loader__identity_dag"], "dependent_dag": "sds_ei__intra__identity_dag"}, {"dependencies": ["sds_ei__loader__person_dag"], "dependent_dag": "sds_ei__intra__person_dag"}, {"dependencies": ["sds_ei__loader__assessment_dag"], "dependent_dag": "sds_ei__intra__assessment_dag"}, {"dependencies": ["sds_ei__loader__compliance_standard_dag"], "dependent_dag": "sds_ei__intra__compliance_standard_dag"}, {"dependencies": ["sds_ei__loader__vulnerability_dag"], "dependent_dag": "sds_ei__intra__vulnerability_dag"}, {"dependencies": ["sds_ei__loader__security_control_dag"], "dependent_dag": "sds_ei__intra__security_control_dag"}, {"dependencies": ["sds_ei__intra__account_dag"], "dependent_dag": "sds_ei__inter__account_dag"}, {"dependencies": ["sds_ei__intra__application_dag"], "dependent_dag": "sds_ei__inter__application_dag"}, {"dependencies": ["sds_ei__intra__cloud_account_dag"], "dependent_dag": "sds_ei__inter__cloud_account_dag"}, {"dependencies": ["sds_ei__intra__cloud_compute_dag"], "dependent_dag": "sds_ei__inter__cloud_compute_dag"}, {"dependencies": ["sds_ei__intra__cloud_container_dag"], "dependent_dag": "sds_ei__inter__cloud_container_dag"}, {"dependencies": ["sds_ei__intra__cloud_storage_dag"], "dependent_dag": "sds_ei__inter__cloud_storage_dag"}, {"dependencies": ["sds_ei__intra__finding_dag"], "dependent_dag": "sds_ei__inter__finding_dag"}, {"dependencies": ["sds_ei__intra__host_dag"], "dependent_dag": "sds_ei__inter__host_dag"}, {"dependencies": ["sds_ei__intra__identity_dag"], "dependent_dag": "sds_ei__inter__identity_dag"}, {"dependencies": ["sds_ei__intra__person_dag"], "dependent_dag": "sds_ei__inter__person_dag"}, {"dependencies": ["sds_ei__intra__vulnerability_dag"], "dependent_dag": "sds_ei__inter__vulnerability_dag"}, {"dependencies": ["sds_ei__intra__assessment_dag"], "dependent_dag": "sds_ei__inter__assessment_dag"}, {"dependencies": ["sds_ei__intra__compliance_standard_dag"], "dependent_dag": "sds_ei__inter__compliance_standard_dag"}, {"dependencies": ["sds_ei__intra__security_control_dag"], "dependent_dag": "sds_ei__inter__security_control_dag"}, {"dependencies": ["sds_ei__inter__account_dag", "sds_ei__inter__application_dag", "sds_ei__inter__cloud_account_dag", "sds_ei__inter__cloud_compute_dag", "sds_ei__inter__cloud_container_dag", "sds_ei__inter__cloud_storage_dag", "sds_ei__inter__finding_dag", "sds_ei__inter__host_dag", "sds_ei__inter__identity_dag", "sds_ei__inter__person_dag", "sds_ei__inter__vulnerability_dag", "sds_ei__inter__assessment_dag", "sds_ei__inter__compliance_standard_dag", "sds_ei__inter__security_control_dag"], "dependent_dag": "sds_ei__relationship_models_dag"}, {"dependencies": ["sds_ei__relationship_models_dag"], "dependent_dag": "sds_ei__relationship_disambiguation_dag"}, {"dependencies": ["sds_ei__relationship_disambiguation_dag"], "dependent_dag": "sds_ei__enrich__entity_dag"}, {"dependencies": ["sds_ei__enrich__entity_dag"], "dependent_dag": "sds_ei__publisher__entity_dag"}, {"dependencies": ["sds_ei__enrich__entity_dag"], "dependent_dag": "sds_ei__publisher__relation_dag"}, {"dependencies": ["sds_ei__publisher__entity_dag"], "dependent_dag": "sds_ei_entity_data_dictionary_validation"}, {"dependencies": ["sds_ei__publisher__relation_dag"], "dependent_dag": "sds_ei_relationship_data_dictionary_validation"}, {"dependencies": ["sds_ei_relationship_data_dictionary_validation", "sds_ei_entity_data_dictionary_validation"], "dependent_dag": "sds_ei_graph_olap"}, {"dependencies": ["sds_ei_graph_olap"], "dependent_dag": "puppy_graph_olap_pipeline"}], "sequential_pipeline_run": "true"}}, "SDS_EI_ANALYTICAL_JOBS_VARIABLES": {"analysis_period": "day"}, "sds_ei_intersource_disambiguated_models_orchestration_config_template": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar", "runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}, "args": ["--spark-service", "spark"], "base_config_path": "<%CONFIG_ARTIFACTORY_URI%><%EI_INTERSOURCE_CONFIG_BASE_PATH%>", "class_name": "ai.prevalent.entityinventory.disambiguator.Disambiguator", "conf": {"spark.dynamicAllocation.enabled": "true", "spark.dynamicAllocation.maxExecutors": "<%INTERSOURCE_EXECUTOR_INSTANCES%>", "spark.cleaner.referenceTracking.cleanCheckpoints": "true", "spark.blacklist.decommissioning.timeout": "600s", "spark.checkpoint.dir": "<%DATALAKE_URI%>/ei-checkpoint/", "spark.driver.maxResultSize": "200M", "spark.executor.heartbeatInterval": "10000", "spark.sql.caseSensitive": "true", "spark.sql.defaultCatalog": "iceberg_catalog", "spark.sql.shuffle.partitions": "<%INTERSOURCE_SHUFFLE_PARTITIONS%>", "spark.ui.port": "4086", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.datahub.metadata.dataset.env": "<%DATAHUB_DATASET_ENV%>"}, "driver_cores": "<%INTERSOURCE_DRIVER_CORES%>", "driver_memory": "<%INTERSOURCE_DRIVER_MEMORY%>", "executor_cores": "<%INTERSOURCE_EXECUTOR_CORES%>", "executor_instances": "<%INTERSOURCE_EXECUTOR_INSTANCES%>", "executor_memory": "<%INTERSOURCE_EXECUTOR_MEMORY%>"}, "sds_ei_intrasource_disambiguated_models_orchestration_config_template": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar", "runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}, "args": ["--spark-service", "spark"], "base_config_path": "<%CONFIG_ARTIFACTORY_URI%><%EI_INTRASOURCE_CONFIG_BASE_PATH%>", "class_name": "ai.prevalent.entityinventory.disambiguator.Disambiguator", "conf": {"spark.dynamicAllocation.enabled": "true", "spark.dynamicAllocation.maxExecutors": "<%INTRASOURCE_EXECUTOR_INSTANCES%>", "spark.cleaner.referenceTracking.cleanCheckpoints": "true", "spark.blacklist.decommissioning.timeout": "600s", "spark.checkpoint.dir": "<%DATALAKE_URI%>/ei-checkpoint/", "spark.driver.maxResultSize": "200M", "spark.executor.heartbeatInterval": "10000", "spark.sql.caseSensitive": "true", "spark.sql.defaultCatalog": "iceberg_catalog", "spark.sql.shuffle.partitions": "<%INTRASOURCE_SHUFFLE_PARTITIONS%>", "spark.ui.port": "4086", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.datahub.metadata.dataset.env": "<%DATAHUB_DATASET_ENV%>"}, "driver_cores": "<%INTRASOURCE_DRIVER_CORES%>", "driver_memory": "<%INTRASOURCE_DRIVER_MEMORY%>", "executor_cores": "<%INTRASOURCE_EXECUTOR_CORES%>", "executor_instances": "<%INTRASOURCE_EXECUTOR_INSTANCES%>", "executor_memory": "<%INTRASOURCE_EXECUTOR_MEMORY%>"}, "sds_ei_inventory_models_orchestration_config_template": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar", "runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}, "args": ["--spark-service", "spark"], "base_config_path": "<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>", "class_name": "ai.prevalent.entityinventory.loader.Loader", "conf": {"spark.dynamicAllocation.enabled": "true", "spark.dynamicAllocation.maxExecutors": "<%LOADER_EXECUTOR_INSTANCES%>", "spark.cleaner.referenceTracking.cleanCheckpoints": "true", "spark.blacklist.decommissioning.timeout": "600s", "spark.checkpoint.dir": "<%DATALAKE_URI%>/ei-checkpoint/", "spark.driver.maxResultSize": "200M", "spark.sql.caseSensitive": "true", "spark.sql.defaultCatalog": "iceberg_catalog", "spark.executor.heartbeatInterval": "10000", "spark.sql.shuffle.partitions": "<%LOADER_SHUFFLE_PARTITIONS%>", "spark.ui.port": "4074", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.sds.ei.config.table": "ei_util_<%KUBE_NAMESPACE%>.sds_ei__job_configs", "spark.datahub.metadata.dataset.env": "<%DATAHUB_DATASET_ENV%>"}, "driver_cores": "<%LOADER_DRIVER_CORES%>", "driver_memory": "<%LOADER_DRIVER_MEMORY%>", "executor_cores": "<%LOADER_EXECUTOR_CORES%>", "executor_instances": "<%LOADER_EXECUTOR_INSTANCES%>", "executor_memory": "<%LOADER_EXECUTOR_MEMORY%>"}, "sds_ei_relationship_disambiguation_orchestration_config_template": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar", "runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}, "args": ["--spark-service", "spark"], "base_config_path": "<%CONFIG_ARTIFACTORY_URI%><%EI_RELATION_DIS_CONFIG_BASE_PATH%>", "class_name": "ai.prevalent.entityinventory.relationship.disambiguation.Disambiguation", "conf": {"spark.dynamicAllocation.enabled": "true", "spark.dynamicAllocation.maxExecutors": "<%RELATIONSHIP_EXECUTOR_INSTANCES%>", "spark.cleaner.referenceTracking.cleanCheckpoints": "true", "spark.blacklist.decommissioning.timeout": "600s", "spark.checkpoint.dir": "<%DATALAKE_URI%>/ei-checkpoint/", "spark.driver.maxResultSize": "200M", "spark.executor.heartbeatInterval": "10000", "spark.sds.ei.experimental.enabled": "true", "spark.sql.caseSensitive": "true", "spark.sql.defaultCatalog": "iceberg_catalog", "spark.sql.shuffle.partitions": "<%RELATIONSHIP_SHUFFLE_PARTITIONS%>", "spark.ui.port": "4074", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.datahub.metadata.dataset.env": "<%DATAHUB_DATASET_ENV%>"}, "driver_cores": "<%RELATIONSHIP_DRIVER_CORES%>", "driver_memory": "<%RELATIONSHIP_DRIVER_MEMORY%>", "executor_cores": "<%RELATIONSHIP_EXECUTOR_CORES%>", "executor_instances": "<%RELATIONSHIP_EXECUTOR_INSTANCES%>", "executor_memory": "<%RELATIONSHIP_EXECUTOR_MEMORY%>"}, "sds_ei_relationship_models_orchestration_config_template": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar", "runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}, "args": ["--spark-service", "spark"], "base_config_path": "<%CONFIG_ARTIFACTORY_URI%><%EI_RELATION_CONFIG_BASE_PATH%>", "class_name": "ai.prevalent.entityinventory.relationship.extractor.Extractor", "conf": {"spark.dynamicAllocation.enabled": "true", "spark.dynamicAllocation.maxExecutors": "<%RELATIONSHIP_EXECUTOR_INSTANCES%>", "spark.cleaner.referenceTracking.cleanCheckpoints": "true", "spark.blacklist.decommissioning.timeout": "600s", "spark.checkpoint.dir": "<%DATALAKE_URI%>/ei-checkpoint/", "spark.driver.maxResultSize": "200M", "spark.executor.heartbeatInterval": "10000", "spark.sql.caseSensitive": "true", "spark.sql.defaultCatalog": "iceberg_catalog", "spark.sql.shuffle.partitions": "<%RELATIONSHIP_SHUFFLE_PARTITIONS%>", "spark.ui.port": "4074", "spark.network.timeout": "15000", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.sds.ei.config.table": "ei_util_<%KUBE_NAMESPACE%>.sds_ei__job_configs", "spark.datahub.metadata.dataset.env": "<%DATAHUB_DATASET_ENV%>"}, "driver_cores": "<%RELATIONSHIP_DRIVER_CORES%>", "driver_memory": "<%RELATIONSHIP_DRIVER_MEMORY%>", "executor_cores": "<%RELATIONSHIP_EXECUTOR_CORES%>", "executor_instances": "<%RELATIONSHIP_EXECUTOR_INSTANCES%>", "executor_memory": "<%RELATIONSHIP_EXECUTOR_MEMORY%>"}, "sds_ei_data_quality_job_template": {"application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/src/data_quality/", "args": [], "runtime_config": {"spec": {"image": "docker.io/prevalentai/spark:3.2.3-2.12-corretto8-bullseye11.6-sds-iceberg-v1-0-pyspark-nodeps"}}, "conf": {"spark.sds.dependencies_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/dependencies.tar.gz"}, "driver_cores": "<%DATA_QUALITY_DRIVER_CORES%>", "driver_memory": "<%DATA_QUALITY_DRIVER_MEMORY%>", "executor_cores": "<%DATA_QUALITY_EXECUTOR_CORES%>", "executor_instances": "<%DATA_QUALITY_EXECUTOR_INSTANCES%>", "executor_memory": "<%DATA_QUALITY_EXECUTOR_MEMORY%>", "py_files": ["<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/application.zip"]}, "sds_ei_publisher_orchestration_config_template": {"runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}, "args": ["--spark-service", "spark"], "base_config_path": "<%CONFIG_ARTIFACTORY_URI%><%EI_PUBLISHER_CONFIG_BASE_PATH%>", "class_name": "ai.prevalent.entityinventory.publisher.Publisher", "conf": {"spark.dynamicAllocation.enabled": "true", "spark.dynamicAllocation.maxExecutors": "<%PUBLISHER_EXECUTOR_INSTANCES%>", "spark.cleaner.referenceTracking.cleanCheckpoints": "true", "spark.checkpoint.dir": "<%DATALAKE_URI%>/ei-checkpoint/", "spark.blacklist.decommissioning.timeout": "600s", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.sql.shuffle.partitions": "<%PUBLISHER_SHUFFLE_PARTITIONS%>", "spark.driver.maxResultSize": "200M", "spark.executor.heartbeatInterval": "10000", "spark.ui.port": "4074", "spark.sql.caseSensitive": "true", "spark.sql.defaultCatalog": "iceberg_catalog", "spark.sds.restapi.configArtifactoryUri": "<%CONFIG_ARTIFACTORY_URI%>", "spark.sds.restapi.eiSparkConfigsBasePath": "<%EI_SPARK_CONFIGS_BASE_PATH%>", "spark.sds.publisher.writebackBasePath": "<%CONFIG_ARTIFACTORY_URI%><%EI_PUBLISHER_LIST_CONFIG_BASE_PATH%>", "spark.datahub.metadata.dataset.env": "<%DATAHUB_DATASET_ENV%>"}, "driver_cores": "<%PUBLISHER_DRIVER_CORES%>", "driver_memory": "<%PUBLISHER_DRIVER_MEMORY%>", "executor_cores": "<%PUBLISHER_EXECUTOR_CORES%>", "executor_memory": "<%PUBLISHER_EXECUTOR_MEMORY%>", "executor_instances": "<%PUBLISHER_EXECUTOR_INSTANCES%>", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar"}, "sds_ei__graph_olap__resolved": {"runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}, "app_name": "sds_ei__graph_olap__resolved", "args": ["--spark-service", "spark", "--current-updated-date", "{{ ti.xcom_pull(task_ids='sds_ei_graph_ui_posting', key='end_epoch') }}"], "class_name": "ai.prevalent.entityinventory.grapholap.GraphOlap", "conf": {"spark.sds.kg.olap.schema": "<%KG_OLAP_SCHEMA%>", "spark.cleaner.referenceTracking.cleanCheckpoints": "true", "spark.checkpoint.dir": "<%DATALAKE_URI%>/ei-checkpoint/", "spark.blacklist.decommissioning.timeout": "600s", "spark.dynamicAllocation.enabled": "false", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.sql.shuffle.partitions": "1", "spark.driver.maxResultSize": "200M", "spark.executor.heartbeatInterval": "10000", "spark.ui.port": "4074", "spark.sql.caseSensitive": "true", "spark.sql.defaultCatalog": "iceberg_catalog", "spark.sds.restapi.configArtifactoryUri": "<%CONFIG_ARTIFACTORY_URI%>", "spark.sds.restapi.eiSparkConfigsBasePath": "<%EI_SPARK_CONFIGS_BASE_PATH%>", "spark.sds.publisher.writebackBasePath": "<%CONFIG_ARTIFACTORY_URI%><%EI_PUBLISHER_LIST_CONFIG_BASE_PATH%>", "spark.sds.olap.writebackBasePath": "<%CONFIG_ARTIFACTORY_URI%><%EI_OLAP_LIST_CONFIG_BASE_PATH%>"}, "driver_cores": "1", "driver_memory": "2g", "executor_cores": "1", "executor_memory": "2g", "executor_instances": "1", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar"}, "sds_ei__graph_olap__fragment": {"runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}, "app_name": "sds_ei__graph_olap__fragment", "args": ["--spark-service", "spark", "--current-updated-date", "{{ ti.xcom_pull(task_ids='sds_ei_graph_ui_posting', key='end_epoch') }}", "--config-path", "NOT_APPLICABLE"], "class_name": "ai.prevalent.entityinventory.grapholap.GraphFragmentOlap", "conf": {"spark.sds.kg.olap.fragment.schema": "<%KG_OLAP_FRAGMENT_SCHEMA%>", "spark.cleaner.referenceTracking.cleanCheckpoints": "true", "spark.checkpoint.dir": "<%DATALAKE_URI%>/ei-checkpoint/", "spark.blacklist.decommissioning.timeout": "600s", "spark.dynamicAllocation.enabled": "false", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.sql.shuffle.partitions": "1", "spark.driver.maxResultSize": "200M", "spark.executor.heartbeatInterval": "10000", "spark.ui.port": "4074", "spark.sql.caseSensitive": "true", "spark.sql.defaultCatalog": "iceberg_catalog", "spark.sds.restapi.configArtifactoryUri": "<%CONFIG_ARTIFACTORY_URI%>", "spark.sds.restapi.eiSparkConfigsBasePath": "<%EI_SPARK_CONFIGS_BASE_PATH%>", "spark.sds.inter.writebackBasePath": "<%CONFIG_ARTIFACTORY_URI%><%EI_INTER_LIST_CONFIG_BASE_PATH%>"}, "driver_cores": "1", "driver_memory": "2g", "executor_cores": "1", "executor_memory": "2g", "executor_instances": "1", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar"}, "sds_ei_entity_rel_enrich_orchestration_config_template": {"runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}, "args": [], "base_config_path": "<%CONFIG_ARTIFACTORY_URI%><%EI_ENTITY_REL_ENRICH_CONFIG_BASE_PATH%>", "class_name": "ai.prevalent.entityinventory.relationship.entityenrich.EntityEnrich", "conf": {"spark.dynamicAllocation.enabled": "true", "spark.dynamicAllocation.maxExecutors": "<%ENRICHER_EXECUTOR_INSTANCES%>", "spark.cleaner.referenceTracking.cleanCheckpoints": "true", "spark.checkpoint.dir": "<%DATALAKE_URI%>/ei-checkpoint/", "spark.blacklist.decommissioning.timeout": "600s", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.sql.shuffle.partitions": "<%ENRICHER_SHUFFLE_PARTITIONS%>", "spark.driver.maxResultSize": "200M", "spark.executor.heartbeatInterval": "10000", "spark.ui.port": "4074", "spark.sql.caseSensitive": "true", "spark.sql.defaultCatalog": "iceberg_catalog", "spark.datahub.metadata.dataset.env": "<%DATAHUB_DATASET_ENV%>"}, "driver_cores": "<%ENRICHER_DRIVER_CORES%>", "driver_memory": "<%ENRICHER_DRIVER_MEMORY%>", "executor_cores": "<%ENRICHER_EXECUTOR_CORES%>", "executor_memory": "<%ENRICHER_EXECUTOR_MEMORY%>", "executor_instances": "<%ENRICHER_EXECUTOR_INSTANCES%>", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar"}, "data_upgrade_spark_job_config": {"sds_ei__upgrade_entity_inventory__resolver": {"app_name": "sds_ei__upgrade_entity_inventory__resolver", "args": ["--input-table-name", "<%EI_SCHEMA_NAME%>.sds_ei_inter_source_resolver", "--output-schema-name", "<%KG_UPGRADE%>", "--table-type", "resolver"], "driver_cores": "1", "driver_memory": "3g", "executor_cores": "2", "executor_instances": "2", "executor_memory": "5g", "class_name": "ai.prevalent.entityinventory.preupgrade.dataupgrade.DataUpgrade", "conf": {"spark.app.name": "sds_ei__upgrade_entity_inventory__resolver", "spark.sql.caseSensitive": "true", "spark.blacklist.decommissioning.timeout": "600s", "spark.dynamicAllocation.enabled": "false", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.ui.port": "4074"}, "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar", "runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}}, "sds_ei__upgrade_entity_inventory__resolution": {"app_name": "sds_ei__upgrade_entity_inventory__resolution", "args": ["--input-table-name", "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei__publish_transformer__entity_inventory", "--output-schema-name", "<%KG_UPGRADE%>", "--table-type", "resolution"], "driver_cores": "1", "driver_memory": "3g", "executor_cores": "2", "executor_instances": "2", "executor_memory": "5g", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar", "conf": {"spark.app.name": "sds_ei__upgrade_entity_inventory__resolution", "spark.sql.caseSensitive": "true", "spark.blacklist.decommissioning.timeout": "600s", "spark.dynamicAllocation.enabled": "false", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.ui.port": "4074"}, "runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}}, "sds_ei__upgrade_entity_inventory__fragments": {"app_name": "sds_ei__upgrade_entity_inventory__fragments", "args": ["--input-table-name", "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei__publish_transformer__non_disambiguated", "--output-schema-name", "<%KG_UPGRADE%>", "--table-type", "fragments"], "driver_cores": "1", "driver_memory": "3g", "executor_cores": "2", "executor_instances": "2", "executor_memory": "5g", "class_name": "ai.prevalent.entityinventory.preupgrade.dataupgrade.DataUpgrade", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar", "conf": {"spark.app.name": "sds_ei__upgrade_entity_inventory__fragments", "spark.sql.caseSensitive": "true", "spark.blacklist.decommissioning.timeout": "600s", "spark.dynamicAllocation.enabled": "false", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.ui.port": "4074"}, "runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}}, "sds_ei__upgrade_relationship__resolver": {"app_name": "sds_ei__upgrade_relationship__resolver", "args": ["--input-table-name", "<%EI_SCHEMA_NAME%>.sds_ei_rel_disambiguation_resolver", "--output-schema-name", "<%KG_UPGRADE%>", "--table-type", "resolver"], "driver_cores": "1", "driver_memory": "3g", "executor_cores": "2", "executor_instances": "2", "executor_memory": "5g", "class_name": "ai.prevalent.entityinventory.preupgrade.dataupgrade.DataUpgrade", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar", "conf": {"spark.app.name": "sds_ei__upgrade_relationship__resolver", "spark.sql.caseSensitive": "true", "spark.blacklist.decommissioning.timeout": "600s", "spark.dynamicAllocation.enabled": "false", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.ui.port": "4074"}, "runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}}, "sds_ei__upgrade_relationship__resolution": {"app_name": "sds_ei__upgrade_relationship__resolution", "args": ["--input-table-name", "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei__publish_transformer__relationship", "--output-schema-name", "<%KG_UPGRADE%>", "--table-type", "resolution"], "driver_cores": "1", "driver_memory": "3g", "executor_cores": "2", "executor_instances": "2", "executor_memory": "5g", "class_name": "ai.prevalent.entityinventory.preupgrade.dataupgrade.DataUpgrade", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar", "conf": {"spark.app.name": "sds_ei__upgrade_relationship__resolution", "spark.sql.caseSensitive": "true", "spark.blacklist.decommissioning.timeout": "600s", "spark.dynamicAllocation.enabled": "false", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.ui.port": "4074"}, "runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}}, "sds_ei__upgrade_relationship__fragments": {"app_name": "sds_ei__upgrade_relationship__fragments", "args": ["--input-table-name", "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei_relationship_non_resolved", "--output-schema-name", "<%KG_UPGRADE%>", "--table-type", "fragments"], "driver_cores": "1", "driver_memory": "3g", "executor_cores": "2", "executor_instances": "2", "executor_memory": "5g", "class_name": "ai.prevalent.entityinventory.preupgrade.dataupgrade.DataUpgrade", "conf": {"spark.app.name": "sds_ei__upgrade_relationship__fragments", "spark.sql.caseSensitive": "true", "spark.blacklist.decommissioning.timeout": "600s", "spark.dynamicAllocation.enabled": "false", "spark.yarn.heterogeneousExecutors.enabled": "false", "spark.ui.port": "4074"}, "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar", "runtime_config": {"spec": {"image": "<%EI_SPARK_IMAGE_NAME%>"}}}}, "sds_software_extraction_defender__job_config": {"app_name": "sds-software-extraction-defender-job", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/src/sofware_extraction/software_extraction.py", "args": ["--parsedIntervalStartEpoch", "{{ ti.xcom_pull(dag_id='sds_software_extraction_dag', task_ids='sds_software_extraction.find_task_runs_sds_software_extraction_defender__job',include_prior_dates=True, key='start_epoch') }}", "--parsedIntervalEndEpoch", "{{ str(pendulum.parse(dag_run.conf['data_interval_end']).timestamp()*1000).split('.')[0] }}", "--eventTimestampEndEpoch", "{{ get_end_date(data_interval_start, 'day','utc') }}", "--source", "defender", "--input<PERSON><PERSON>", "iceberg_catalog.<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_software_vuln_delta;iceberg_catalog.<%SRDM_SCHEMA_NAME%>.open_data__nvd_vulnerability;iceberg_catalog.<%SRDM_SCHEMA_NAME%>.cisa__vulnrichment", "--outputPath", "iceberg_catalog.<%LOOKUP_SCHEMA_NAME%>.microsoft_azure__defender_device_software_vuln_delta_software"], "runtime_config": {"spec": {"image": "<%EI_PYSPARK_IMAGE_NAME%>"}}, "conf": {"spark.sds.dependencies_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/dependencies.tar.gz#env"}, "driver_memory": "<%SDS_SOFTWARE_EXTRACTION_DRIVER_MEMORY%>", "driver_cores": "<%SDS_SOFTWARE_EXTRACTION_DRIVER_CORES%>", "executor_cores": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_CORES%>", "executor_memory": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_MEMORY%>", "executor_instances": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_INSTANCES%>", "py_files": ["<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/application.zip"]}, "sds_software_extraction_qualys__job_config": {"app_name": "sds-software-extraction-qualys-job", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/src/sofware_extraction/software_extraction.py", "args": ["--parsedIntervalStartEpoch", "{{ ti.xcom_pull(dag_id='sds_software_extraction_dag', task_ids='sds_software_extraction.find_task_runs_sds_software_extraction_qualys__job',include_prior_dates=True, key='start_epoch') }}", "--parsedIntervalEndEpoch", "{{ str(pendulum.parse(dag_run.conf['data_interval_end']).timestamp()*1000).split('.')[0] }}", "--eventTimestampEndEpoch", "{{ get_end_date(data_interval_start, 'day','utc') }}", "--source", "qualys", "--input<PERSON><PERSON>", "iceberg_catalog.<%SRDM_SCHEMA_NAME%>.qualys__host_vulnerability;iceberg_catalog.<%SRDM_SCHEMA_NAME%>.qualys__knowledge_base;iceberg_catalog.<%SRDM_SCHEMA_NAME%>.open_data__nvd_vulnerability;iceberg_catalog.<%SRDM_SCHEMA_NAME%>.cisa__vulnrichment", "--outputPath", "iceberg_catalog.<%LOOKUP_SCHEMA_NAME%>.qualys__host_vulnerability_software"], "runtime_config": {"spec": {"image": "<%EI_PYSPARK_IMAGE_NAME%>"}}, "conf": {"spark.sds.dependencies_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/dependencies.tar.gz#env"}, "driver_memory": "<%SDS_SOFTWARE_EXTRACTION_DRIVER_MEMORY%>", "driver_cores": "<%SDS_SOFTWARE_EXTRACTION_DRIVER_CORES%>", "executor_cores": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_CORES%>", "executor_memory": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_MEMORY%>", "executor_instances": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_INSTANCES%>", "py_files": ["<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/application.zip"]}, "sds_software_extraction_tenable__job_config": {"app_name": "sds-software-extraction-tenable-job", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/src/sofware_extraction/software_extraction.py", "args": ["--parsedIntervalStartEpoch", "{{ ti.xcom_pull(dag_id='sds_software_extraction_dag', task_ids='sds_software_extraction.find_task_runs_sds_software_extraction_tenable__job',include_prior_dates=True, key='start_epoch') }}", "--parsedIntervalEndEpoch", "{{ str(pendulum.parse(dag_run.conf['data_interval_end']).timestamp()*1000).split('.')[0] }}", "--eventTimestampEndEpoch", "{{ get_end_date(data_interval_start, 'day','utc') }}", "--source", "tenable", "--input<PERSON><PERSON>", "iceberg_catalog.<%SRDM_SCHEMA_NAME%>.emea_tenable_vuln_tenable_sc_vulns", "--outputPath", "iceberg_catalog.<%LOOKUP_SCHEMA_NAME%>.emea_tenable_vuln_tenable_sc_vulns_software"], "runtime_config": {"spec": {"image": "<%EI_PYSPARK_IMAGE_NAME%>"}}, "conf": {"spark.sds.dependencies_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/dependencies.tar.gz#env"}, "driver_memory": "<%SDS_SOFTWARE_EXTRACTION_DRIVER_MEMORY%>", "driver_cores": "<%SDS_SOFTWARE_EXTRACTION_DRIVER_CORES%>", "executor_cores": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_CORES%>", "executor_memory": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_MEMORY%>", "executor_instances": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_INSTANCES%>", "py_files": ["<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/application.zip"]}, "sds_software_extraction_wiz__job_config": {"app_name": "sds-software-extraction-wiz-job", "application_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/src/sofware_extraction/software_extraction.py", "args": ["--parsedIntervalStartEpoch", "{{ ti.xcom_pull(dag_id='sds_software_extraction_dag', task_ids='sds_software_extraction.find_task_runs_sds_software_extraction_wiz__job',include_prior_dates=True, key='start_epoch') }}", "--parsedIntervalEndEpoch", "{{ str(pendulum.parse(dag_run.conf['data_interval_end']).timestamp()*1000).split('.')[0] }}", "--eventTimestampEndEpoch", "{{ get_end_date(data_interval_start, 'day','utc') }}", "--source", "wiz", "--input<PERSON><PERSON>", "iceberg_catalog.<%SRDM_SCHEMA_NAME%>.wiz__vulnerability_findings;iceberg_catalog.<%SRDM_SCHEMA_NAME%>.open_data__nvd_vulnerability;iceberg_catalog.<%SRDM_SCHEMA_NAME%>.cisa__vulnrichment", "--outputPath", "iceberg_catalog.<%LOOKUP_SCHEMA_NAME%>.wiz__vulnerability_findings_software"], "runtime_config": {"spec": {"image": "<%EI_PYSPARK_IMAGE_NAME%>"}}, "conf": {"spark.sds.dependencies_file": "<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/dependencies.tar.gz#env"}, "driver_memory": "<%SDS_SOFTWARE_EXTRACTION_DRIVER_MEMORY%>", "driver_cores": "<%SDS_SOFTWARE_EXTRACTION_DRIVER_CORES%>", "executor_cores": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_CORES%>", "executor_memory": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_MEMORY%>", "executor_instances": "<%SDS_SOFTWARE_EXTRACTION_EXECUTOR_INSTANCES%>", "py_files": ["<%ARTIFACTORY_URI%>/sds/data-analytics/lib/latest/application.zip"]}}