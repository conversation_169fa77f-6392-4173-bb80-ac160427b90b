POST        /generate         config.delta.controllers.gen.EIMainDeltaGenController.publishDeltaToCM()
POST        /generate/relationship_models         config.delta.controllers.gen.RelationDeltaGenController.publishDeltaToCM()
POST        /generate/relationship_disambiguation         config.delta.controllers.gen.RelationResolutionDeltaGenController.publishDeltaToCM()
POST        /generate/publisher         config.delta.controllers.gen.PublisherDeltaGenController.publishDeltaToCM()
POST        /generate/entity_rel_enrich         config.delta.controllers.gen.EntityEnrichDeltaGenController.publishDeltaToCM()
POST        /auto-resolve         config.delta.controllers.resolve.EIMainDeltaResolveController.resolveAutoDelta()

