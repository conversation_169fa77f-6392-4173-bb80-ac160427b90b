POST          /v1/old/inventorymodel/validate/expression              v1.inventorymodel.InventoryModelController.validateExpression(selectedCols:Option[Seq[String]])
POST          /v1/old/inventorymodel/validate/schema                  v1.inventorymodel.InventoryModelController.validateSchema
POST          /v1/old/inventorymodel/execute/config                   v1.inventorymodel.InventoryModelController.execute(selectedCols:Option[Seq[String]],onlyInventoryConfigFields:Option[Seq[String]],distinct:Option[Seq[String]],limit:Option[Seq[String]])


GET           /healthcheck                                            common.HealthCheckController.healthCheck
GET           /shutdown                                               v2.controllers.AdminController.shutdown


POST          /v1/old/relationshipmodel/validate/expression           v1.relationshipmodel.Controller.validateExpression
POST          /v1/old/relationshipmodel/validate/schema               v1.relationshipmodel.Controller.validateSchema

POST          /v1/old/globalentity/validate/expression                v1.globalentity.GlobalEntityController.validateExpression
POST          /v1/old/globalentity/validate/schema                    v1.globalentity.GlobalEntityController.validateSchema

POST          /v1/session/up                                          v1.session.SessionController.up
POST          /v1/session/down                                        v1.session.SessionController.down
POST          /v1/session/info                                        v1.session.SessionController.info

POST          /v1/inventorymodel/validate/expression                  v2.controllers.LoaderController.validateExpression
POST          /v1/inventorymodel/validate/schema                      v2.controllers.LoaderController.validateSchema
POST          /v1/inventorymodel/execute/config                       v2.controllers.LoaderController.run(params: v2.models.QueryParams)

POST          /v1/intramodel/validate/expression                      v2.controllers.LoaderController.validateExpression
POST          /v1/intramodel/execute/config                           v2.controllers.IntraController.run(params: v2.models.QueryParams)

POST          /v1/intermodel/validate/expression                      v2.controllers.InterController.validateExpression
POST          /v1/intermodel/execute/config                           v2.controllers.InterController.run(params: v2.models.QueryParams)

POST          /v1/relationshipmodel/validate/expression               v2.controllers.RelationshipExtractionController.validateExpression
POST          /v1/relationshipmodel/execute/config                    v2.controllers.RelationshipExtractionController.run(params: v2.models.QueryParams)


POST          /v1/relationshipdisambiguation/validate/expression      v2.controllers.RelationshipDisambiguationController.validateExpression
POST          /v1/relationshipdisambiguation/execute/config           v2.controllers.RelationshipDisambiguationController.run(params: v2.models.QueryParams)


POST          /v1/entityrelenrich/validate/expression                 v2.controllers.EntityRelationEnrichmentController.validateExpression
POST          /v1/entityrelenrich/execute/config                      v2.controllers.EntityRelationEnrichmentController.run(params: v2.models.QueryParams)

POST          /v1/publisher/execute/config                            v2.controllers.PublisherController.run(params: v2.models.QueryParams)


POST          /v1/globalentity/validate/expression                    v2.controllers.GlobalEntityController.validateExpression
POST          /v1/globalentity/validate/schema                        v2.controllers.GlobalEntityController.validateSchema

POST           /v1/query                                     v2.controllers.QueryController.query(params: v2.models.QueryParams)

->  /delta/v1   delta.v1.Routes

POST          /v2/redis/clearhash                                     v2.controllers.RedisController.deleteRedisCache()
POST          /v2/redis/setkey                                        v2.controllers.RedisController.setKey()

GET           /v2/gethash                                             v2.controllers.LoaderController.currentHash
GET           /v2/gethashall                                          v2.controllers.LoaderController.allHash
GET           /v2/clearhash                                           v2.controllers.LoaderController.clearHash
POST          /v2/sethash                                             v2.controllers.LoaderController.setDummyHash()
GET           /getcount                                               v2.controllers.ThreadController.threadCount
GET           /getcountnormal                                         v2.controllers.ThreadController.normalCount

-> /iceberg/v1 iceberg.v1.Routes