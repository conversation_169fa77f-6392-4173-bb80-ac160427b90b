@Library('sds-common') _

 coverageLimit = 0
 stageResult = 'PASS'



pipeline {


    agent {
        kubernetes {
            label 'sbt'
            defaultContainer 'sbt-1-6-2'
        }
    }


    parameters {
        choice(
            name: 'pushToDockerHub',
            choices: ['No', 'Yes'],
            description: 'Do you want to push this build to dockerhub ?'
        )
    }


        environment {
        repository                      = 'ei-validator'
        registryCredential              = 'dockerhub'
        repoName = 'sds-ei-validator'
        COSIGN_PASSWORD                 = credentials('cosign-password')

        }


    



    stages {
        stage ('runUnitTest') {
            steps {
                    script {
                        scala.runUnitTest()
                    }
            }
        }


        stage ('version') {

            agent {
                kubernetes {
                    label 'debian'
                    defaultContainer 'debian'
                }
            }
            steps {
                script {
                    if ("${pushToDockerHub}" == "Yes") {
                        env.buildNumber = getNewDockerBuildVersion()
                    }  else {
                        env.buildNumber = getNewBuildVersion()
                    }
                }
            }
        }


        stage ('generateCoverageReport') {
            steps {
                script {
                    scala.generateCoverageReports()
                    stash includes: '**/target/**/index.html', name: 'report'
                }
            }
        }

        stage('publishCoverageJenkins') {
            steps {
                    script {
                        scala.publishCoverageJenkins()
                    }
            }
        }

        stage ('coverageStatus') {
            agent {
                kubernetes {
                    label 'python'
                    defaultContainer 'apt-pkg-python-3-8-12'
                }
            }
            steps {
                script {
                        unstash 'report'
                        sh "chown -R jenkins ${WORKSPACE}"
                        sh "su jenkins"
                        env.coverageNumber = scala.getCoverageStatus(coverageLimit:"${coverageLimit}")

                    }
                }
            }

        stage ('build') {
            steps {
                script {
                    env.build = scala.sbtJarBuild(env.buildNumber)
                    sh 'cd ${WORKSPACE}'                    
                    stash includes: 'target/scala-2.12/', name: 'jars'
                }
            }
        }


        stage('Building the image') {
            when {
                expression { 
                    return env.pushToDockerHub == 'No';
                }
            }
            agent {
                kubernetes {
                    label 'dockerBuild'
                    defaultContainer 'kaniko'
                }
            }
            steps{
                script {
                    unstash 'jars'
                    dockerBuild.kanikoBuild(buildNumber:"${buildNumber}", repoName:"${repoName}")
                }
            }
        }


        stage('Build and Push Image') {
            when {
                expression { 
                    return env.pushToDockerHub == 'Yes';
                }
            }
            agent {
                kubernetes {
                    label 'dockerBuild'
                    defaultContainer 'kaniko'
                }
            }
            steps{
                script {
                    unstash 'jars'
                    def buildTag = sh(script: "echo ${buildNumber} | sed \"s/+/-/g\" | sed \"s/\\./-/g\"", returnStdout: true)
                    imageName = "${docker_registery}/"+"${repository}:"+"${repoName}-"+"${buildTag}"
                    dockerBuild.kanikoBuildAndPushToDockerHub(imageName: "${imageName}", buildNumber:"${buildNumber}", repoName:"${repoName}")
                }
            }
        }

        stage('sign the container image') {
            when {
                expression { 
                    return env.pushToDockerHub == 'Yes';
                }
            }
            agent {
                kubernetes {
                    label 'dockerBuild'
                    defaultContainer 'cosign'
                }
            }
            steps {
                script{
                    cosign.imageSigning(imageName: "${imageName}")
                }
            }
        }
        
        stage('verify the container image') {
            when {
                expression { 
                    return env.pushToDockerHub == 'Yes';
                }
            }
            agent {
                kubernetes {
                    label 'dockerBuild'
                    defaultContainer 'cosign'
                }
            }
            steps {
                script{
                    cosign.imageVerification(imageName: "${imageName}")
                }
            }
        }


        stage ('notification'){
            steps{
                script {
                        deploy.notifyOnBuild(buildVersion:"${buildNumber}")
                        deploy.highlightCoverageStatus(coverageNumber:"${coverageNumber}")
                    }
                }
            }
    }

}
