
# db connections = ((physical_core_count * 2) + effective_spindle_count)
fixedConnectionPool = 5

repository.dispatcher {
  executor = "thread-pool-executor"
  throughput = 1
  thread-pool-executor {
    fixed-pool-size = ${fixedConnectionPool}
  }
}


play.http.secret.key="changeme"
play.http.secret.key="1qpUy<YxcM8ScZD:>M`d1NWP59CaNhnS2wwEZJmK0S/Q8OJPbKolyQ[JO5IW6ahC"


spark.master="local"
spark.app.name="SparkSession Startup Test"
spark.executor.instances="1"
spark.executor.memory="1g"
spark.executor.core="1"
spark.driver.bindAddress="0.0.0.0"
disableQueryValidation=false
