{
  "primaryKey": "object_guid",
  "entityConfig": {
    "entityClass": "Host"
  },
  "dataSource": {
    "name": "Microsoft Defender For Endpoint",
    "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_defender_for_endpoint__defender_device_software_vuln_delta",
    "feedName": "Device Software Vulnerability"
  },
  "filterBy": "LOWER(sam_account_type) LIKE '%machine_account%' AND object_guid IS NOT NULL AND TRIM(object_guid)!=''",
  "origin": "'MS Active Directory'",
  "temporaryProperties": [
    {
      "colName": "temp_fqdn",
      "colExpr": "CASE WHEN TRIM(LOWER(dns_hostname)) RLIKE '[.](com|net|ads|uk|org|edu|gov|info|in|azure|internal|at|corp|io|local)$' THEN LOWER(dns_hostname) ELSE NULL END"
    },
    {
      "colName": "temp_host_type"
      "colExpr": "CASE WHEN LOWER(distinguished_name) LIKE '%server%' OR LOWER(operating_system) LIKE '%server%' THEN 'Server' ELSE 'Endpoint' END"
    },
    {
      "colName": "temp_os",
      "colExpr": "CASE WHEN operating_system IS NOT NULL OR operating_system RLIKE '^\\s*$' THEN TRIM(concat_ws(' ', TRIM(operating_system), TRIM(operating_system_version))) ELSE NULL END"
    }
  ],
  "commonProperties": [
    {
      "colName": "class",
      "colExpr": "'Host'"
    },
    {
      "colName": "type",
      "colExpr": "CASE WHEN LOWER(distinguished_name) LIKE '%server%' OR LOWER(operating_system) LIKE '%server%' THEN 'Server' ELSE 'Endpoint' END"
    },
    {
      "colName": "display_label",
      "colExpr": "coalesce((CASE WHEN TRIM(LOWER(dns_hostname)) RLIKE '[.](com|net|ads|uk|org|edu|gov|info|in|azure|internal|at|corp|io|local)$' THEN LOWER(dns_hostname) ELSE NULL END),(CASE WHEN cn='' THEN NULL ELSE UPPER(cn) END),object_guid)"
    },
    {
      "colName": "entity_tag",
      "colExpr": "CONCAT_WS('','{\"Host Type\":\"',trim(temp_host_type),'\",\"OS\":\"',trim(temp_os),'\"}' )"
    }
  ],
  "entitySpecificProperties": [
    {
      "colName": "host_name",
      "colExpr": "CAST(CASE WHEN cn='' THEN NULL ELSE UPPER(cn) END AS STRING)"
    },
    {
      "colName": "os",
      "colExpr": "CASE WHEN operating_system IS NULL OR trim(operating_system) RLIKE '^\\s*$' THEN NULL ELSE TRIM(concat_ws(' ', TRIM(operating_system), TRIM(operating_system_version))) END"
    },
    {
      "colName": "os_platform",
      "colExpr": "CASE WHEN operating_system IS NULL OR operating_system == '' THEN 'Unknown' WHEN LOWER(TRIM(operating_system)) LIKE '%windows%' THEN 'Windows' WHEN lower(regexp_replace(operating_system,'\\\\s+','')) LIKE '%macos%' THEN 'macOS' WHEN LOWER(TRIM(operating_system)) RLIKE '.*linux.*|.*ubuntu.*|.*centos.*|.*fedora.*|.*webos.*|.*chromeos.*|.*ciscoasa.*|.*cisconx.*|.*debian.*|.*redhat.*|.*tizen.*|.*panos.*|.*f5.*|openwrt|.*embeddedos.*' THEN 'Linux'  WHEN LOWER(TRIM(operating_system)) LIKE '%ios%' OR LOWER(TRIM(operating_system)) LIKE '%ipados%' THEN 'iOS' WHEN LOWER(TRIM(operating_system)) LIKE '%android%' THEN 'Android' ELSE 'Other' END"
    },
    {
      "colName": "fqdn",
      "colExpr": "temp_fqdn"
    },
    {
      "colName": "domain",
      "colExpr": "CASE WHEN temp_fqdn like '%.local%' AND temp_fqdn RLIKE '^[^.]*\\.[^.]*$' THEN 'local' WHEN temp_fqdn like '%.corp%' AND temp_fqdn RLIKE '^[^.]*\\.[^.]*$' THEN 'corp' WHEN temp_fqdn LIKE '%.%.%' THEN REGEXP_EXTRACT(temp_fqdn, '(?<=\\\\.).*', 0) ELSE NULL END"
    },
    {
      "colName": "host_type",
      "colExpr": "temp_host_type"
    },
    {
      "colName": "status",
      "colExpr": "CASE WHEN CAST(user_account_control AS STRING) IS NULL OR TRIM(CAST(user_account_control AS STRING))=='' THEN 'Unknown' WHEN LOWER(CAST(user_account_control AS STRING)) LIKE '%disable%' THEN 'Disabled' ELSE 'Active' END"
    },
    {
      "colName": "azure_ad_device_id",
      "colExpr": "object_guid"
    },
    {
      "colName": "activity_status",
      "colExpr": "CASE WHEN last_logon_epoch IS NULL OR last_logon_epoch =='' THEN 'Unknown' WHEN DATEDIFF(FROM_UNIXTIME(updated_at\/1000),FROM_UNIXTIME(last_logon_epoch\/1000)) > 180 THEN 'Inactive' ELSE 'Active' END"
    },
    {
      "colName": "last_active_date",
      "colExpr": "last_logon_synced_epoch"
    }
  ],
  "sourceSpecificProperties": [
    {
      "colName": "last_login_date",
      "colExpr": "last_logon_epoch"
    },
    {
      "colName": "description",
      "colExpr": "description"
    },
    {
      "colName": "last_sync_date",
      "colExpr": "last_logon_synced_epoch"
    },
    {
      "colName": "when_created_date",
      "colExpr": "when_created_epoch"
    },
    {
      "colName": "when_changed_date",
      "colExpr": "when_changed_epoch"
    },
    {
      "colName": "sam_account_type",
      "colExpr": "sam_account_type"
    },
    {
      "colName": "sam_account_name",
      "colExpr": "sam_account_name"
    },
    {
      "colName": "user_account_control",
      "colExpr": "CAST(user_account_control AS STRING)"
    },
    {
      "colName": "distinguished_name",
      "colExpr": "distinguished_name"
    },
    {
      "colName": "account_expiration_date",
      "colExpr": "account_expires"
    }
  ]
}