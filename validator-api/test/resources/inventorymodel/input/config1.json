{"origin": "'MS Defender'", "dataSource": {"name": "Microsoft Defender For Endpoint", "srdm": "test.ei_table", "feedName": "Device Software Vulnerability"}, "entityConfig": {"fieldSpec": {"persistNonNullValue": true}, "entityClass": "Cloud Compute and Container", "lastUpdateFields": ["vendor_severity"]}, "primaryKey": "cveId", "outputTable": "ei.sds_ei__vulnerability__ms_defender_device_tvm_software_vulnerabilities_delta__cve_id", "commonProperties": [{"colExpr": "'Vulnerability'", "colName": "type", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "LEAST(last_active_date,first_found_date,vulnerability_first_observed_date)", "colName": "first_seen_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "CASE WHEN (lastSeenTimestamp!='' AND (lastSeenTimestamp IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp)))ELSE NULL END", "colName": "last_active_date", "fieldsSpec": {"aggregateFunction": "max"}}, {"colExpr": "min(activity_status_temp) over(partition by primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)", "colName": "activity_status"}], "temporaryProperties": [{"colExpr": "GREATEST(UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp))),event_timestamp_epoch)", "colName": "event_timestamp_epoch"}, {"colExpr": "to_timestamp(event_timestamp_epoch/1000)", "colName": "event_timestamp_ts"}, {"colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(firstSeenTimestamp)))", "colName": "first_seen_timestamp_epoch"}, {"colExpr": "case when LOWER(status) LIKE '%new%' then 'Active' when LOWER(status) LIKE '%updated%' then 'Active' Else 'Inactive' END", "colName": "activity_status_temp"}], "entitySpecificProperties": [{"colExpr": "primary_key", "colName": "cve_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "cast(cvssScore as STRING)", "colName": "v31_score"}, {"colExpr": "vulnerabilitySeverityLevel", "colName": "vendor_severity"}, {"colExpr": "cast(securityUpdateAvailable as string)", "colName": "patch_available"}, {"colExpr": "CASE WHEN exploitabilityLevel LIKE 'NoExploit' THEN false ELSE true END", "colName": "exploit_available"}, {"colExpr": "recommendationReference", "colName": "recommendation"}, {"colExpr": "recommendedSecurityUpdate", "colName": "ms_recommended_update"}, {"colExpr": "recommendedSecurityUpdateId", "colName": "ms_recommended_update_id"}, {"colExpr": "CASE WHEN (firstSeenTimestamp!='' AND (firstSeenTimestamp IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(firstSeenTimestamp)))ELSE NULL END", "colName": "vulnerability_first_observed_date", "fieldsSpec": {"aggregateFunction": "min"}}], "sourceSpecificProperties": [{"colExpr": "primary_key", "colName": "vendor_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "true", "colName": "found_in_organisation", "fieldsSpec": {"isInventoryDerived": true}}]}