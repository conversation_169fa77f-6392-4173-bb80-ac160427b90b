{"primaryKey": "object_guid", "entityConfig": {"entityClass": "Host", "lastUpdateFields": ["h"]}, "origin": "MS Active Directory", "dataSource": {"name": "Microsoft Defender For Endpoint", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_defender_for_endpoint__defender_device_software_vuln_delta", "feedName": "Device Software Vulnerability"}, "outputTable": "test.test", "commonProperties": [{"colName": "entity_tag", "colExpr": "CAST(CASE WHEN cn='' THEN NULL ELSE UPPER(temp_fqdn) END AS STRING)"}, {"colName": "common_null", "colExpr": "cast ( null as string)"}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "CAST(CASE WHEN cn='' THEN NULL ELSE UPPER(cn) END AS STRING)"}, {"colName": "entity_null", "colExpr": "cast(NULL AS STRING)"}], "sourceSpecificProperties": [{"colName": "last_login_date", "colExpr": "last_logon_epoch"}, {"colName": "source_null", "colExpr": "cast(null AS string)"}], "temporaryProperties": [{"colName": "temp_fqdn", "colExpr": "LOWER(dns_hostname)"}, {"colName": "temp_null", "colExpr": "cast(null as string)"}]}