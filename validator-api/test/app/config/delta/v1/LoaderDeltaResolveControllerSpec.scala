//package app.config.delta.v1
//
//import com.github.tomakehurst.wiremock.WireMockServer
//import com.github.tomakehurst.wiremock.client.WireMock
//import com.github.tomakehurst.wiremock.client.WireMock._
//import com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig
//import com.google.inject.AbstractModule
//import com.holdenkarau.spark.testing.DataFrameSuiteBase
//import common.SDSConf
//import config.delta.controllers.gen.EIMainDeltaGenController
//import config.delta.controllers.resolve.{EIMainDeltaResolveController}
//import net.codingwell.scalaguice.ScalaModule
//import org.mockito.MockitoSugar
//import org.scalatest.BeforeAndAfter
//import org.scalatestplus.play.PlaySpec
//import org.scalatestplus.play.guice.GuiceOneAppPerSuite
//import play.api.Application
//import play.api.Play.materializer
//import play.api.inject.guice.GuiceApplicationBuilder
//import play.api.libs.json.Json
//import play.api.mvc.ControllerComponents
//import play.api.test.Helpers.{POST, contentAsJson, defaultAwaitTimeout}
//import play.api.test.{FakeRequest, Helpers}
//
//import scala.concurrent.ExecutionContext
//
//class LoaderDeltaResolveControllerSpec extends PlaySpec with BeforeAndAfter with DataFrameSuiteBase with GuiceOneAppPerSuite   {
//
//  val wireMockServer = new WireMockServer(wireMockConfig().port(8181))
//
//  before{
//    wireMockServer.start()
//    setupTheConfigManagerAPI()
//  }
//
//  after{
//    wireMockServer.stop()
//  }
//
//  override def fakeApplication(): Application = {
//    new GuiceApplicationBuilder()
//      .build()
//  }
//  def setupTheConfigManagerAPI(): Unit = {
////    spark.conf.set("spark.sds.config.artifactory.uri","http://127.0.0.1:8181")
//    spark.conf.set("spark.sds.config.artifactory.uri","https://sds3-qa.solution.prevalent.ai")
////    spark.conf.set("spark.sds.config.artifactory.uri","https://internal-sds-dev-476115029.ap-south-1.elb.amazonaws.com")
//    spark.conf.set("spark.sds.config.item.base-path","/sds_mgmnt/config-manager/api/v1/config-item")
//    spark.conf.set("spark.sds.config.upgrade-status.base-path","/sds_mgmnt/upgrade-manager/check-upgrade-status/ei")
//    spark.conf.set("spark.sds.config.delta.base-path","/sds_mgmnt/config-manager/api/v1/config-delta")
//    configureFor("127.0.0.1", 8181)
//
//    val solutionConfPathOld = SDSConf.configIteamMetaList + "?config_item_type=inventory_models&solution_edition=old"
//    stubFor(WireMock.get(urlEqualTo(solutionConfPathOld)).willReturn(aResponse.withBody("""[{"id":59702,"name":"sds_ei__host__ms_azure_virtual_machine__resource_id","config_item_type":"inventory_models","config_item_level":"solution"},{"id":53106,"name":"sds_ei__host__aws_emr_ec2_instance__instanceid","config_item_type":"inventory_models","config_item_level":"solution"}]""")))
//
//    configureFor("127.0.0.1", 8181)
//    val solutionConfPathNew = SDSConf.configIteamMetaList + "?config_item_type=inventory_models&solution_edition=new"
//    stubFor(WireMock.get(urlEqualTo(solutionConfPathNew)).willReturn(aResponse.withBody("""[{"id":531234,"name":"sds_ei__host__ms_azure_virtual_machine__resource_id","config_item_type":"inventory_models","config_item_level":"solution"},{"id":530024,"name":"sds_ei__account__ms_azure_ad_user_registration__user_principal_name","config_item_type":"inventory_models","config_item_level":"solution"}]""")))
//
//    val oldConfigItemPath = SDSConf.CONFIG_ITEM_BASE_PATH + "/sds_ei__host__ms_azure_virtual_machine__resource_id?config_item_level=solution&solution_edition=old&config_item_type=inventory_models&no_merge=true"
//    stubFor(WireMock.get(urlEqualTo(oldConfigItemPath)).willReturn(aResponse.withBody("""{"id":59702,"name":"sds_ei__host__ms_azure_virtual_machine__resource_id","config_item_type":"inventory_models","config_item_level":"client","config_deploy_type":"spark_job_configs","client_revision":19,"created_at":"2024-09-19T08:27:54.534245Z","updated_at":"2024-09-19T08:28:44.800398Z","created_by":"service-account-sds-confidential-client","updated_by":"service-account-sds-confidential-client","entity_name":"host","solution_name":"ei","data_source_feed_name":"virtual_machine","data_source_feed_label":"Virtual Machine","data_source_name":"microsoft_azure","data_source_label":"Microsoft Azure","version":"1-1-0-********-***************","deployed":true,"pushed_to_git":true,"draft":false,"edit_lock":false,"is_available":true,"is_deleted":false,"config_schema":null,"config_value":{"origin":"'MS Azure Virtual Machine'","dataSource":{"name":"Microsoft Azure","srdm":"<%SRDM_SCHEMA_NAME%>.microsoft_azure__virtual_machine","feedName":"Virtual Machine"},"primaryKey":"case when lower(type)='microsoft.compute/virtualmachinescalesets/virtualmachines' then concat(REGEXP_EXTRACT(id,'(.*/virtualMachines/)',0),name) else id end","enrichments":[{"lookupInfo":{"tableName":"<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup","enrichmentColumns":["location_country","location_city"]},"joinCondition":"s.location = e.region"}],"outputTable":"<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_virtual_machine__resource_id","commonProperties":[{"colExpr":"azure_resource_created_date","colName":"first_seen_date","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"GREATEST(last_updated_attrs.cloud_operational_state.last_changed.last_found_date,CASE WHEN cloud_operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)","colName":"last_active_date","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"2","colName":"cloud_inactivity_period","fieldsSpec":{"isInventoryDerived":true}}],"temporaryProperties":[{"colExpr":"GREATEST(UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.timeCreated))),ingested_timestamp_epoch)","colName":"event_timestamp_epoch"},{"colExpr":"to_timestamp(event_timestamp_epoch/1000)","colName":"event_timestamp_ts"},{"colExpr":"type","colName":"temp_type"}],"entitySpecificProperties":[{"colExpr":"UPPER(name)","colName":"host_name"},{"colExpr":"INITCAP(properties.storageProfile.osDisk.osType)","colName":"os"},{"colExpr":"'Azure'","colName":"cloud_provider","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"primary_key","colName":"cloud_resource_id","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"'Virtual Machine'","colName":"cloud_resource_type","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"location","colName":"cloud_region"},{"colExpr":"(SPLIT(id,'/')[2])","colName":"cloud_account_id"},{"colExpr":"properties.provisioningState","colName":"provisioning_state"},{"colExpr":"'Azure Virtual Machine'","colName":"cloud_native_type","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"lower(azure_vm_lifecycle)","colName":"cloud_instance_lifecycle","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"CASE WHEN LOWER(azure_vm_power_state) LIKE '%running%' then 'Active' WHEN LOWER(azure_vm_power_state) LIKE '%stopped%' then 'Inactive' else null end","colName":"cloud_operational_state","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"CASE WHEN size(zones) = 1 THEN 'Single' WHEN size(zones) > 1 THEN 'Multiple' ELSE null END","colName":"cloud_zone_availability"}],"sourceSpecificProperties":[{"colExpr":"UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.timeCreated)))","colName":"azure_resource_created_date"},{"colExpr":"properties.priority","colName":"azure_vm_lifecycle"},{"colExpr":"tags","colName":"azure_tags"},{"colExpr":"case when lower(cast(properties.instanceView.statuses.code as string)) like '%running%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%creating%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%starting%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopping%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopped%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocated%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocating%' then 'Stopped' else null end","colName":"azure_vm_power_state"},{"colExpr":"CASE WHEN LOWER(case when lower(cast(properties.instanceView.statuses.code as string)) like '%running%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%creating%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%starting%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopping%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopped%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocated%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocating%' then 'Stopped' else null end) LIKE '%running%' THEN event_timestamp_epoch ELSE NULL END","colName":"active_operational_date"}]},"deployed_version_available":true,"draft_version_available":false}""")))
//    val newConfigItemPath = SDSConf.CONFIG_ITEM_BASE_PATH + "/sds_ei__host__ms_azure_virtual_machine__resource_id?config_item_level=solution&solution_edition=new&config_item_type=inventory_models&no_merge=true"
//    stubFor(WireMock.get(urlEqualTo(newConfigItemPath)).willReturn(aResponse.withBody("""{"id":59702,"name":"sds_ei__host__ms_azure_virtual_machine__resource_id","config_item_type":"inventory_models","config_item_level":"solution","config_deploy_type":"spark_job_configs","client_revision":19,"created_at":"2024-09-20T08:27:54.534245Z","updated_at":"2024-09-20T08:28:44.800398Z","created_by":"service-account-sds-confidential-client","updated_by":"service-account-sds-confidential-client","entity_name":"host","solution_name":"ei","data_source_feed_name":"virtual_machine","data_source_feed_label":"Virtual Machine","data_source_name":"microsoft_azure","data_source_label":"Microsoft Azure","version":"1-2-0-********-***************","deployed":true,"pushed_to_git":true,"draft":false,"edit_lock":false,"is_available":true,"is_deleted":false,"config_schema":null,"config_value":{"origin":"'MS Azure Virtual Machine'","dataSource":{"name":"Microsoft Azure","srdm":"<%SRDM_SCHEMA_NAME%>.microsoft_azure__virtual_machine","feedName":"Virtual Machine"},"primaryKey":"case when lower(type)='microsoft.compute/virtualmachinescalesets/virtualmachines' then concat(REGEXP_EXTRACT(id,'(.*/virtualMachines/)',0),name) else id end","enrichments":[{"lookupInfo":{"tableName":"<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup","enrichmentColumns":["location_country","location_city"]},"joinCondition":"s.location = e.region"}],"outputTable":"<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_virtual_machine__resource_id","commonProperties":[{"colExpr":"GREATEST(last_updated_attrs.cloud_operational_state.last_changed.last_found_date,CASE WHEN cloud_operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)","colName":"last_active_date","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"2","colName":"cloud_inactivity_period","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"CONCAT_WS(' ',os,cloud_native_type)","colName":"internal_contributor","fieldsSpec":{"isInventoryDerived":true}}],"temporaryProperties":[{"colExpr":"GREATEST(UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.timeCreated))),ingested_timestamp_epoch)","colName":"event_timestamp_epoch"},{"colExpr":"to_timestamp(event_timestamp_epoch/1000)","colName":"event_timestamp_ts"},{"colExpr":"type","colName":"temp_type"}],"entitySpecificProperties":[{"colExpr":"UPPER(name)","colName":"host_name"},{"colExpr":"INITCAP(properties.storageProfile.osDisk.osType)","colName":"os"},{"colExpr":"'Azure'","colName":"cloud_provider","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"primary_key","colName":"cloud_resource_id","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"'Virtual Machine'","colName":"cloud_resource_type","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"location","colName":"cloud_region"},{"colExpr":"(SPLIT(id,'/')[2])","colName":"cloud_account_id"},{"colExpr":"properties.provisioningState","colName":"provisioning_state"},{"colExpr":"'Azure Virtual Machine'","colName":"cloud_native_type","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"lower(azure_vm_lifecycle)","colName":"cloud_instance_lifecycle","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"CASE WHEN LOWER(azure_vm_power_state) LIKE '%running%' then 'Active' WHEN LOWER(azure_vm_power_state) LIKE '%stopped%' then 'Inactive' else null end","colName":"cloud_operational_state","fieldsSpec":{"isInventoryDerived":true}},{"colExpr":"CASE WHEN size(zones) = 1 THEN 'Single' WHEN size(zones) > 1 THEN 'Multiple' ELSE null END","colName":"cloud_zone_availability"}],"sourceSpecificProperties":[{"colExpr":"UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.timeCreated)))","colName":"azure_resource_created_date"},{"colExpr":"properties.priority","colName":"azure_vm_lifecycle"},{"colExpr":"tags","colName":"azure_tags"},{"colExpr":"case when lower(cast(properties.instanceView.statuses.code as string)) like '%running%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%creating%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%starting%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopping%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopped%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocated%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocating%' then 'Stopped' else null end","colName":"azure_vm_power_state"},{"colExpr":"CASE WHEN LOWER(case when lower(cast(properties.instanceView.statuses.code as string)) like '%running%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%creating%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%starting%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopping%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopped%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocated%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocating%' then 'Stopped' else null end) LIKE '%running%' THEN event_timestamp_epoch ELSE NULL END","colName":"active_operational_date"}]},"deployed_version_available":true,"draft_version_available":false}""")))
//
//    val clinetPathNew = SDSConf.configIteamMetaList + "?config_item_type=inventory_models&solution_edition=new"
//    configureFor("127.0.0.1", 8181)
//    println(clinetPathNew)
//    stubFor(WireMock.get(urlEqualTo(clinetPathNew)).willReturn(aResponse.withBody("""[{"name":"sds_ei__host__ms_azure_virtual_machine__resource_id","solution_config_exists":true,"config_item_level":"client"}]""")))
//    val clientDeltapath =  SDSConf.deltaAPIPath()+s"?config_item_name=sds_ei__host__ms_azure_virtual_machine__resource_id&level=client"
//    stubFor(WireMock.get(urlEqualTo(clientDeltapath)).willReturn(aResponse.withBody("""[{"id":23,"config_item_name":"sds_ei__host__ms_azure_virtual_machine__resource_id","delta_category":"added","config_item_type":"inventory_models","level":"client","version":null,"resolved_status":false,"created_by":"","updated_by":"","latest_comment":"","created_at":"2024-08-06T07:00:48.722780Z","updated_at":"2024-08-06T07:00:48.722828Z","delta_config_item":"Rename","name":"epoch_time","delta_config":[{"Old Name":"epoch_time"},{"New Name":"epoch_time_in_millis"}]}]""")))
//    println(s"client delta path $clientDeltapath")
//    val sds_ei__host__ms_azure_virtual_machine__resource_idClientConfURL = SDSConf.CONFIG_ITEM_BASE_PATH + "/sds_ei__host__ms_azure_virtual_machine__resource_id?config_item_level=client&solution_edition=new"
//    stubFor(WireMock.get(urlEqualTo(sds_ei__host__ms_azure_virtual_machine__resource_idClientConfURL)).willReturn(aResponse.withBody("""{"id":94040,"name":"sds_ei__host__ms_azure_virtual_machine__resource_id","config_item_type":"inventory_models","config_item_level":"client","version":"1-1-2-********-***************","client_revision":null,"solution_name":"vra","entity_name":"host","updated_at":"2024-10-18T14:45:27.003084Z","updated_by":"service-account-sds-confidential-client","created_at":"2024-10-18T14:45:27.003071Z","created_by":"service-account-sds-confidential-client","is_available":false,"is_deleted":false,"data_source_feed_name":null,"data_source_feed_label":null,"data_source_name":null,"data_source_label":null,"dashboard_identifier":null,"display_name":"Host","deployed":false,"pushed_to_git":true,"draft":true,"edit_lock":false,"upgraded_state":null,"config_schema":null,"config_value":{"test":"as","temporaryProperties":[{"colName":"event_timestamp_epoch","colExpr":"GREATEST(UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.timeCreated))),ingested_timestamp_epoch)"},{"colName":"event_timestamp_ts","colExpr":"to_timestamp(epoch_time/1000)"}],"commonProperties":[{"colName":"epoch_time","colExpr":"GREATEST(UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.timeCreated))),ingested_timestamp_epoch)"},{"colName":"event_timestamp_ts_entitySpecificProperties","colExpr":"to_timestamp(epoch_time/1000)"}],"entitySpecificProperties":[{"colName":"event_timestamp_ts_entitySpecificProperties","colExpr":"to_timestamp(epoch_time/1000)"}],"sourceSpecificProperties":[{"colName":"event_timestamp_ts_sourceSpecificProperties","colExpr":"to_timestamp(epoch_time/1000)"}]},"deployed_version_available":false,"draft_version_available":true}""")))
//
////    configureFor("127.0.0.1", 8181)
////    stubFor(
////      post(urlEqualTo(SDSConf.deltaAPIPath()))
////        .willReturn(
////          aResponse()
////            .withStatus(200)
////            .withHeader("Content-Type", "application/json")
////            .withBody("""{"message":"success"}""")
////        )
////    )
//
//    wireMockServer.addMockServiceRequestListener((request,listner) => println(s"received in wiremock ${request.getUrl}, ${request.getMethod}${request.getBodyAsString}"))
//  }
//
//
//  "Loader Delta Service be able to post loader delta" in {
//    val injector = app.injector
//    val loaderCotroller = injector.instanceOf[EIMainDeltaResolveController]
//    wireMockServer.prettifier
//    val fakeRequest = FakeRequest(POST, "/delta/v1/auto-resolve").withHeaders("Content-Type" -> "application/json")
//    val response = loaderCotroller.resolveAutoDelta().apply(fakeRequest)
//    val jsonResponse = contentAsJson(response)
//    println(jsonResponse)
//    Thread.sleep(1200000)
//  }
//}
//
