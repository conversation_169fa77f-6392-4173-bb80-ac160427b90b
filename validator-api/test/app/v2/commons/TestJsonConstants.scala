package app.v2.commons

object TestJsonConstants {

  val SIMPLE_SELECT_WITH_SUBQUERY =
    """
      |{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "table_a",
      |          "subQuery": {
      |            "dataSource": {
      |              "dataSource": "table_b"
      |            },
      |            "alias": "a",
      |            "queryParams": {
      |              "fields": [
      |                "a.column_a",
      |                "a.column_b"
      |              ],
      |              "aggregate": [],
      |              "group": [],
      |
      |              "sort": []
      |            }
      |          }
      |        },
      |        "alias": "a",
      |        "queryParams": {
      |          "fields": [
      |            "a.column_a",
      |            "a.column_b"
      |          ],
      |          "aggregate": [],
      |          "group": [],
      |
      |          "sort": []
      |        }
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role":"auditor"
      |}
      |""".stripMargin
  val SIMPLE_JOIN =
    """
      |{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "table_a"
      |        },
      |        "join": [
      |          {
      |            "joinType": "INNER JOIN",
      |            "onCondition": "a.column_a = b.column_a",
      |            "query": {
      |              "dataSource": {
      |                "dataSource": "table_b"
      |              },
      |              "alias": "b",
      |              "queryParams": {
      |                "fields": [
      |                  "b.column_a"
      |                ],
      |                "aggregate": [],
      |                "group": [],
      |                "sort": []
      |              }
      |            }
      |          }
      |        ],
      |        "alias": "a",
      |        "queryParams": {
      |          "fields": [
      |            "a.column_a",
      |            "a.column_b",
      |            "b.column_a"
      |          ],
      |          "aggregate": [],
      |          "group": [],
      |          "sort": []
      |        }
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role":"auditor"
      |}
      |""".stripMargin
  val SIMPLE_SELECT_WITH_NESTED_SUBQUERY =
    """
      |{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "table_a",
      |          "subQuery": {
      |            "dataSource": {
      |              "dataSource": "table_b",
      |              "subQuery": {
      |                "dataSource": {
      |                  "dataSource": "table_a"
      |                },
      |                "alias": "a",
      |                "queryParams": {
      |                  "fields": [
      |                    "a.column_a",
      |                    "a.column_b"
      |                  ],
      |                  "aggregate": [],
      |                  "group": [],
      |                  "sort": []
      |                }
      |              }
      |            },
      |            "alias": "a",
      |            "queryParams": {
      |              "fields": [
      |                "a.column_a",
      |                "a.column_b"
      |              ],
      |              "aggregate": [],
      |              "group": [],
      |              "sort": []
      |            }
      |          }
      |        },
      |        "alias": "a",
      |        "queryParams": {
      |          "fields": [
      |            "a.column_a",
      |            "a.column_b"
      |          ],
      |          "aggregate": [],
      |          "group": [],
      |          "sort": []
      |        }
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |
      |  "role":"auditor"
      |}
      |""".stripMargin
  val SIMPLE_SELECT_WITH_SUBQUERY_WITHOUT_ALIAS =
    """
      |{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "table_a",
      |          "subQuery": {
      |            "dataSource": {
      |              "dataSource": "table_b"
      |            },
      |            "queryParams": {
      |              "fields": [
      |                "a.column_a",
      |                "a.column_b"
      |              ],
      |              "aggregate": [],
      |              "group": [],
      |
      |              "sort": []
      |            }
      |          }
      |        },
      |
      |        "queryParams": {
      |          "fields": [
      |            "a.column_a",
      |            "a.column_b"
      |          ],
      |          "aggregate": [],
      |          "group": [],
      |
      |          "sort": []
      |        }
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role":"auditor"
      |}
      |""".stripMargin
  val BASIC_SELECT =
    """{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "Orders"
      |        },
      |        "alias": "a",
      |        "queryParams": {
      |          "fields": [
      |            "a.OrderID",
      |            "a.OrderDate"
      |          ],
      |          "aggregate": [
      |            [
      |              "max",
      |              "date"
      |            ]
      |          ],
      |          "group": [
      |            "orderNumber"
      |          ],
      |          "filter": "date = 6",
      |          "sort": [
      |            [
      |              "date",
      |              true
      |            ]
      |          ],
      |          "offset": 3,
      |          "limit": "5",
      |          "distinct": true
      |        }
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role": "auditor",
      |  "filterString": {
      |    "filterMap": {
      |      "Product": ["prod.id = 5"]
      |    }
      |  }
      |}""".stripMargin
  val QUERY1 =
    """{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "Orders"
      |        },
      |        "alias": "a",
      |        "queryParams": {
      |          "fields": [
      |            "a.OrderID",
      |            "b.OrderDate"
      |          ],
      |          "aggregate": [
      |            [
      |              "max",
      |              "date"
      |            ]
      |          ],
      |          "filter": "date=6",
      |          "group": [
      |            "operating_system__inv"
      |          ],
      |          "limit": ""
      |        },
      |        "join": [
      |          {
      |            "joinType": "INNER JOIN",
      |            "query": {
      |              "dataSource": {
      |                "dataSource": "Customers"
      |              },
      |              "alias": "b",
      |              "queryParams": {
      |                "fields": [
      |                  "Customers.CustomerName"
      |                ]
      |              }
      |            },
      |            "onCondition": "a.CustomerID=b.CustomerID"
      |          }
      |        ]
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role": "auditor",
      |  "filterString": {
      |    "filterMap": {
      |      "Orders_a": [
      |        "orders.id = '5' AND orders.date = '6'"
      |      ],
      |      "Customers_b": [
      |        "cust.id = '7'"
      |      ]
      |    }
      |  }
      |}""".stripMargin
  val QUERY2 =
    """{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "Orders",
      |          "subQuery": {
      |            "dataSource": {
      |              "dataSource": "Product"
      |            },
      |            "alias": "a",
      |            "queryParams": {
      |              "fields": [
      |                "a.OrderID",
      |                "a.OrderDate"
      |              ],
      |              "filter": "b=c",
      |              "group": [
      |                "operating_system__inv"
      |              ],
      |              "limit": "5",
      |              "sort": [
      |                [
      |                  "date",
      |                  true
      |                ]
      |              ]
      |            },
      |            "join": [
      |              {
      |                "joinType": "INNER JOIN",
      |                "query": {
      |                  "dataSource": {
      |                    "dataSource": "Customers"
      |                  },
      |                  "alias": "b",
      |                  "queryParams": {
      |                    "fields": [
      |                      "Customers.CustomerName"
      |                    ]
      |                  }
      |                },
      |                "onCondition": "a.CustomerID=b.CustomerID"
      |              }
      |            ]
      |          }
      |        },
      |        "alias": "c",
      |        "queryParams": {
      |          "fields": [
      |            "c.ID",
      |            "c.Date"
      |          ],
      |          "filter": "c=d",
      |          "group": [
      |            "system"
      |          ],
      |          "limit": "5"
      |        }
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role": "auditor",
      |  "filterString": {
      |    "filterMap": {
      |      "Product_a": [
      |        "prod.id = '5'"
      |      ],
      |      "Customers_b": [
      |        "cust.id = '7'"
      |      ]
      |    }
      |  }
      |}""".stripMargin
  val NESTED_SUBQUERY_WITH_JOIN =
    """
      |{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "subQuery": {
      |            "dataSource": {
      |              "dataSource": "Product"
      |            },
      |            "alias": "a",
      |            "queryParams": {
      |              "fields": [
      |                "a.OrderID",
      |                "a.OrderDate"
      |              ],
      |              "filter": "b=c",
      |              "group": [],
      |              "sort": []
      |            },
      |            "join": [
      |              {
      |                "joinType": "INNER JOIN",
      |                "query": {
      |                  "dataSource": {
      |                    "dataSource": "Customers"
      |                  },
      |                  "alias": "b",
      |                  "queryParams": {
      |                    "fields": [
      |                      "Customers.CustomerName"
      |                    ]
      |                  }
      |                },
      |                "onCondition": "a.CustomerID=b.CustomerID"
      |              }
      |            ]
      |          }
      |        },
      |        "alias": "c",
      |        "queryParams": {
      |          "fields": [
      |            "c.ID",
      |            "c.Date"
      |          ],
      |          "group": []
      |        }
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API"
      |}
      |""".stripMargin
  val QUERY3 =
    """{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "Orders",
      |          "subQuery": {
      |            "dataSource": {
      |              "dataSource": "Product",
      |              "subQuery": {
      |                "dataSource": {
      |                  "dataSource": "Customers"
      |                },
      |                "alias": "c",
      |                "queryParams": {
      |                  "fields": [
      |                    "c.ID",
      |                    "c.Name"
      |                  ],
      |                  "aggregate": [
      |                    [
      |                      "avg",
      |                      "device_number"
      |                    ]
      |                  ],
      |                  "group": [
      |                    "device"
      |                  ],
      |                  "filter": "deviceid=7",
      |                  "limit": "7",
      |                  "distinct": true
      |                }
      |              }
      |            },
      |            "alias": "b",
      |            "queryParams": {
      |              "fields": [
      |                "b.ID",
      |                "b.Name"
      |              ],
      |              "sort": [
      |                [
      |                  "date",
      |                  true
      |                ]
      |              ],
      |              "limit": "5"
      |            }
      |          }
      |        },
      |        "alias": "a",
      |        "queryParams": {
      |          "fields": [
      |            "a.OrderID",
      |            "a.OrderDate"
      |          ],
      |          "aggregate": [
      |            [
      |              "max",
      |              "date"
      |            ]
      |          ],
      |          "group": [
      |            "system"
      |          ],
      |          "filter": "date=7",
      |          "limit": "6",
      |          "distinct": true
      |        }
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role": "auditor",
      |  "filterString": {
      |    "filterMap": {
      |
      |      "Product_b": [
      |        "prod.id = '5'"
      |      ],
      |      "Customers_c": [
      |        "cust.id = '7'"
      |      ]
      |    }
      |  }
      |}""".stripMargin
  val QUERY4 =
    """{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "Orders"
      |        },
      |        "alias": "a",
      |        "queryParams": {
      |          "fields": [
      |            "a.OrderID",
      |            "a.OrderDate",
      |            "c.Name",
      |            "b.ID",
      |            "b.Name"
      |          ],
      |          "filter": "id=5",
      |          "group": [
      |            "operating_system__inv"
      |          ],
      |          "limit": "5"
      |        },
      |        "join": [
      |          {
      |            "joinType": "INNER JOIN",
      |            "query": {
      |              "dataSource": {
      |                "dataSource": "Customers"
      |              },
      |              "alias": "b",
      |              "queryParams": {
      |                "fields": [
      |                  "b.ID"
      |                ]
      |              },
      |              "join": [
      |                {
      |                  "joinType": "INNER JOIN",
      |                  "query": {
      |                    "dataSource": {
      |                      "dataSource": "Product"
      |                    },
      |                    "alias": "c",
      |                    "queryParams": {
      |                      "fields": [
      |                        "c.Name"
      |                      ]
      |                    }
      |                  },
      |                  "onCondition": "c.CustomerID=b.CustomerID"
      |                }
      |              ]
      |            },
      |            "onCondition": "a.CustomerID=b.CustomerID"
      |          }
      |        ]
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role": "auditor",
      |  "filterString": {
      |    "filterMap": {
      |      "Orders_a": [
      |        "orders.id = '5'",
      |        "orders.date = '6'"
      |      ],
      |      "Product_c": [
      |        "prod.id = '5'"
      |      ],
      |      "Customers_b": [
      |        "b.id = '5'"
      |      ]
      |    }
      |  }
      |}""".stripMargin
  val NESTED_JOIN_QUERY =
    """
      |{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "a"
      |        },
      |        "alias": "a",
      |        "queryParams": {
      |          "fields": [
      |            "a.OrderID"
      |          ],
      |          "group": []
      |        },
      |        "join": [
      |          {
      |            "joinType": "INNER JOIN",
      |            "query": {
      |              "dataSource": {
      |                "dataSource": "b"
      |              },
      |              "alias": "b",
      |              "queryParams": {
      |                "fields": [
      |                  "b.ID"
      |                ]
      |              },
      |              "join": [
      |                {
      |                  "joinType": "INNER JOIN",
      |                  "query": {
      |                    "dataSource": {
      |                      "dataSource": "c"
      |                    },
      |                    "alias": "c",
      |                    "queryParams": {
      |                      "fields": [
      |                        "c.Name"
      |                      ]
      |                    }
      |                  },
      |                  "onCondition": "c.CustomerID=b.CustomerID"
      |                }
      |              ]
      |            },
      |            "onCondition": "a.CustomerID=b.CustomerID"
      |          }
      |        ]
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role":"auditor"
      |}
      |""".stripMargin
  val QUERY5 =
    """{
      |	"dataQuery": [{
      |		"query": {
      |			"alias": "b",
      |			"dataSource": {
      |				"dataSource": "domain_ui"
      |			},
      |			"queryParams": {
      |				"aggregate": [],
      |				"fields": [
      |					"b.domain",
      |					"b.cnames",
      |					"a.insight"
      |				],
      |				"filter": "",
      |				"group": [],
      |				"limit": "",
      |				"sort": []
      |			},
      |			"join": [{
      |				"onCondition": "b.__time=a.__time",
      |				"joinType": "JOIN",
      |				"query": {
      |					"alias": "a",
      |					"dataSource": {
      |						"dataSource": "summary_ui"
      |					},
      |
      |					"queryParams": {
      |						"aggregate": [],
      |						"fields": [
      |							"a.insight"
      |						],
      |						"filter": "",
      |						"group": [],
      |						"limit": "",
      |						"sort": []
      |					}
      |				}
      |			}]
      |		},
      |		"dataLabel": "yoyo"
      |	}],
      |	"moduleName": "SDS_INSIGHTS_DFP"
      |}""".stripMargin
  val QUERY6 =
    """{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "Orders",
      |          "subQuery": {
      |            "dataSource": {
      |              "dataSource": "Product"
      |            },
      |            "alias": "a",
      |            "queryParams": {
      |              "fields": [
      |                "a.OrderID",
      |                "a.OrderDate"
      |              ],
      |              "filter": "b=c",
      |              "group": [
      |                "operating_system__inv"
      |              ],
      |              "limit": "5",
      |              "sort": [
      |                [
      |                  "date",
      |                  true
      |                ]
      |              ]
      |            },
      |            "join": [
      |              {
      |                "joinType": "INNER JOIN",
      |                "query": {
      |                  "dataSource": {
      |                    "dataSource": "Customers"
      |                  },
      |                  "alias": "b",
      |                  "queryParams": {
      |                    "fields": [
      |                      "b.CustomerName"
      |                    ]
      |                  }
      |                },
      |                "onCondition": "a.CustomerID=b.CustomerID"
      |              }
      |            ]
      |          }
      |        },
      |        "alias": "c",
      |        "queryParams": {
      |          "fields": [
      |            "c.ID",
      |            "c.Date"
      |          ],
      |          "filter": "c=d",
      |          "group": [
      |            "system"
      |          ],
      |          "limit": "5"
      |        },
      |        "join": [
      |          {
      |            "joinType": "INNER JOIN",
      |            "query": {
      |              "dataSource": {
      |                "dataSource": "Market"
      |              },
      |              "alias": "d",
      |              "queryParams": {
      |                "fields": [
      |                  "d.id"
      |                ]
      |              }
      |            },
      |            "onCondition": "c.CustomerID=d.CustomerID"
      |          }
      |        ]
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role": "auditor",
      |  "filterString": {
      |    "filterMap": {
      |      "Orders": [
      |        "orders.id = '5'",
      |        "orders.date = '6'"
      |      ],
      |      "Product_a": [
      |        "prod.id = '5'"
      |      ],
      |      "Customers_b": [
      |        "cust.id = '7'"
      |      ],
      |      "Market_d": [
      |        "cust.id = '7'"
      |      ]
      |    }
      |  }
      |}""".stripMargin
  val QUERY7 =
    """{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "Orders",
      |          "subQuery": {
      |            "dataSource": {
      |              "dataSource": "Product"
      |            },
      |            "alias": "a",
      |            "queryParams": {
      |              "fields": [
      |                "a.OrderID",
      |                "a.OrderDate"
      |              ],
      |              "filter": "b=c",
      |              "group": [
      |                "operating_system__inv"
      |              ],
      |              "limit": "5",
      |              "sort": [
      |                [
      |                  "date",
      |                  true
      |                ]
      |              ]
      |            }
      |          }
      |        },
      |        "alias": "c",
      |        "queryParams": {
      |          "fields": [
      |            "c.ID",
      |            "c.Date"
      |          ],
      |          "filter": "c=d",
      |          "group": [
      |            "system"
      |          ],
      |          "limit": "5"
      |        }
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role": "auditor",
      |  "filterString": {
      |    "filterMap": {
      |      "Orders": [
      |        "orders.id = '5'",
      |        "orders.date = '6'"
      |      ],
      |      "Product_a": [
      |        "prod.id = '5'"
      |      ],
      |      "Customers": [
      |        "cust.id = '7'"
      |      ]
      |    }
      |  }
      |}""".stripMargin
  val QUERY8 =
    """{
      |	"dataQuery": [{
      |
      |		"query": {
      |			"dataSource": {
      |				"dataSource": "Orders"
      |			},
      |			"alias": "a",
      |			"queryParams": {
      |				"fields": [
      |				],
      |        "aggregate":[["max","date"]]
      |			}
      |		},
      |		"dataLabel": "orders_data"
      |
      |	}],
      |	"moduleName": "INSIGHT_API",
      | "role":"auditor"
      |
      |}""".stripMargin

  val Query9 =
    """
      |{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "dataSource": "domain_ui",
      |          "subQuery": {
      |            "dataSource": {
      |              "dataSource": "domain_ui",
      |              "subQuery": {
      |                "dataSource": {
      |                  "dataSource": "summary_ui"
      |                },
      |                "queryParams": {
      |                  "aggregate": [],
      |                  "fields": [
      |                    "__time"
      |                  ],
      |                  "group": [],
      |                  "sort": []
      |                },
      |                "join": []
      |              }
      |            },
      |            "queryParams": {
      |              "aggregate": [],
      |              "fields": [
      |                "__time"
      |              ],
      |              "group": [],
      |              "sort": []
      |            },
      |            "join": []
      |          }
      |        },
      |        "queryParams": {
      |          "aggregate": [],
      |          "fields": [
      |            "__time"
      |          ],
      |          "group": [],
      |          "sort": []
      |        },
      |        "join": []
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role": "auditor"
      |}
      |""".stripMargin

  val JSON_OBJECT_QUERY= """{
                           |    "dataQuery": [
                           |        {
                           |            "query": {
                           |                "dataSource": {
                           |                    "subQuery": {
                           |                        "dataSource": {
                           |                            "dataSource": "sds_ei_entity_inventory"
                           |                        },
                           |                        "queryParams": {
                           |                            "fields": [
                           |                                "STRING_TO_MV(origin, '<-->') withAlias origin",
                           |                                "STRING_TO_MV(data_source_subset_name, '<-->') withAlias data_feed",
                           |                                "class",
                           |                                "case when count_of_origin = 1 then 'Unique' else 'Corroborated' end withAlias un",
                           |                                "count(*) withAlias main_count"
                           |                            ],
                           |                            "aggregate": [],
                           |                            "group": [
                           |                                "STRING_TO_MV(origin, '<-->')",
                           |                                "STRING_TO_MV(data_source_subset_name, '<-->')",
                           |                                "class",
                           |                                "case when count_of_origin = 1 then 'Unique' else 'Corroborated' end withAlias un"
                           |                            ],
                           |                            "filter": "class = 'Host'",
                           |                            "sort": [],
                           |                            "limit": ""
                           |                        }
                           |                    }
                           |                },
                           |                "queryParams": {
                           |                    "fields": [
                           |                        "JSON_OBJECT(KEY 'KEY_1' VALUE data_feed, KEY 'KEY_2' VALUE origin, KEY 'count' VALUE main_count, KEY 'LEVEL' VALUE 1) withAlias l1",
                           |                        "JSON_OBJECT(KEY 'KEY_1' VALUE un, KEY 'KEY_2' VALUE class, KEY 'count' VALUE SUM(main_count) OVER (PARTITION BY class, un, origin), KEY 'LEVEL' VALUE 2) withAlias l2",
                           |                        "JSON_OBJECT(KEY 'KEY_1' VALUE origin, KEY 'KEY_2' VALUE un, KEY 'count' VALUE SUM(main_count) OVER (PARTITION BY class, un), KEY 'LEVEL' VALUE 2) withAlias l3",
                           |                        "row_number() OVER (PARTITION BY class, un,origin) = 1 withAlias is_l2",
                           |                        "row_number() OVER (PARTITION BY class, un) = 1 withAlias is_l3"
                           |                    ],
                           |                    "aggregate": [],
                           |                    "group": [],
                           |                    "sort": [],
                           |                    "limit": ""
                           |                }
                           |            },
                           |            "dataLabel": "inventory"
                           |        }
                           |    ],
                           |    "dashboard": "#entity-inventory/contributing-sources-overview",
                           |    "moduleName": "SDS_INSIGHTS_ENTITY"
                           |}""".stripMargin
  val NESTED_SUBQUERPY_WITH_ALIAS =
    """
      |{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "subQuery": {
      |            "dataSource": {
      |              "subQuery": {
      |                "dataSource": {
      |                  "dataSource": "Customers"
      |                },
      |                "alias": "c",
      |                "queryParams": {
      |                  "fields": [
      |                    "c.ID",
      |                    "c.Name"
      |                  ],
      |                  "aggregate": [],
      |                  "group": []
      |                }
      |              }
      |            },
      |            "alias": "b",
      |            "queryParams": {
      |              "fields": [
      |                "b.ID",
      |                "b.Name"
      |              ],
      |              "sort": []
      |            }
      |          }
      |        },
      |        "alias": "a",
      |        "queryParams": {
      |          "fields": [
      |            "a.OrderID",
      |            "a.OrderDate"
      |          ],
      |          "aggregate": [],
      |          "group": []
      |        }
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role":"auditor"
      |}
      |""".stripMargin

  val CORRECT_SELECT_FIELDS_SYNTAX_QUERY =
    """{
      |	"dataQuery": [{
      |
      |		"query": {
      |			"dataSource": {
      |				"dataSource": "Orders"
      |			},
      |			"alias": "a",
      |			"queryParams": {
      |				"fields": ["sum(success) filter(where a=b)","CASE WHEN a=b then 0 else 1 end as flag"
      |				],
      |        "aggregate":[]
      |			}
      |		},
      |		"dataLabel": "orders_data"
      |
      |	}],
      |	"moduleName": "INSIGHT_API",
      | "role":"auditor"
      |
      |}""".stripMargin

  val SELECT_FIELDS_WITH_FROM_QUERY =
    """{
      |	"dataQuery": [{
      |
      |		"query": {
      |			"dataSource": {
      |				"dataSource": "Orders"
      |			},
      |			"alias": "a",
      |			"queryParams": {
      |				"fields": ["sum(success) filter(where a=b)","(select a from b)"
      |				],
      |        "aggregate":[]
      |			}
      |		},
      |		"dataLabel": "orders_data"
      |
      |	}],
      |	"moduleName": "INSIGHT_API",
      | "role":"auditor"
      |
      |}""".stripMargin

  val SELECT_FIELDS_WITH_DELETE_QUERY =
    """{
      |	"dataQuery": [{
      |
      |		"query": {
      |			"dataSource": {
      |				"dataSource": "Orders"
      |			},
      |			"alias": "a",
      |			"queryParams": {
      |				"fields": ["sum(success) filter(where a=b)",";DELETE FROM a"
      |				],
      |        "aggregate":[]
      |			}
      |		},
      |		"dataLabel": "orders_data"
      |
      |	}],
      |	"moduleName": "INSIGHT_API",
      | "role":"auditor"
      |
      |}""".stripMargin

  val SELECT_FIELDS_WITH_DROP_QUERY =
    """{
      |	"dataQuery": [{
      |
      |		"query": {
      |			"dataSource": {
      |				"dataSource": "Orders"
      |			},
      |			"alias": "a",
      |			"queryParams": {
      |				"fields": ["a",";DROP table b"
      |				],
      |        "aggregate":[]
      |			}
      |		},
      |		"dataLabel": "orders_data"
      |
      |	}],
      |	"moduleName": "INSIGHT_API",
      | "role":"auditor"
      |
      |}""".stripMargin

  val SELECT_FIELDS_WITH_UPDATE_QUERY =
    """{
      |	"dataQuery": [{
      |
      |		"query": {
      |			"dataSource": {
      |				"dataSource": "Orders"
      |			},
      |			"alias": "a",
      |			"queryParams": {
      |				"fields": ["a",";UPDATE b set c='value' where c is null"
      |				],
      |        "aggregate":[]
      |			}
      |		},
      |		"dataLabel": "orders_data"
      |
      |	}],
      |	"moduleName": "INSIGHT_API",
      | "role":"auditor"
      |
      |}""".stripMargin

  val SELECT_FIELDS_WITH_INSERT_QUERY =
    """{
      |	"dataQuery": [{
      |
      |		"query": {
      |			"dataSource": {
      |				"dataSource": "Orders"
      |			},
      |			"alias": "a",
      |			"queryParams": {
      |				"fields": ["a",";INSERT INTO b values(1,2)"
      |				],
      |        "aggregate":[]
      |			}
      |		},
      |		"dataLabel": "orders_data"
      |
      |	}],
      |	"moduleName": "INSIGHT_API",
      | "role":"auditor"
      |
      |}""".stripMargin

  val INVALID_JOIN_CONDITION_QUERY =  """{
                                        |	"dataQuery": [{
                                        |		"query": {
                                        |			"alias": "b",
                                        |			"dataSource": {
                                        |				"dataSource": "domain_ui"
                                        |			},
                                        |			"queryParams": {
                                        |				"aggregate": [],
                                        |				"fields": [
                                        |					"b.domain",
                                        |					"b.cnames",
                                        |					"a.insight"
                                        |				],
                                        |				"filter": "",
                                        |				"group": [],
                                        |				"limit": "",
                                        |				"sort": []
                                        |			},
                                        |			"join": [{
                                        |				"onCondition": "b.__time=a.__time)",
                                        |				"joinType": "JOIN",
                                        |				"query": {
                                        |					"alias": "a",
                                        |					"dataSource": {
                                        |						"dataSource": "summary_ui"
                                        |					},
                                        |
                                        |					"queryParams": {
                                        |						"aggregate": [],
                                        |						"fields": [
                                        |							"a.insight"
                                        |						],
                                        |						"filter": "",
                                        |						"group": [],
                                        |						"limit": "",
                                        |						"sort": []
                                        |					}
                                        |				}
                                        |			}]
                                        |		},
                                        |		"dataLabel": "yoyo"
                                        |	}],
                                        |	"moduleName": "SDS_INSIGHTS_DFP"
                                        |}""".stripMargin
  val INVALID_FILTER_QUERY =  """{
                                |	"dataQuery": [{
                                |		"query": {
                                |			"alias": "b",
                                |			"dataSource": {
                                |				"dataSource": "domain_ui"
                                |			},
                                |			"queryParams": {
                                |				"aggregate": [],
                                |				"fields": [
                                |					"b.domain",
                                |					"b.cnames",
                                |					"a.insight"
                                |				],
                                |				"filter": ")",
                                |				"group": [],
                                |				"limit": "",
                                |				"sort": []
                                |			},
                                |			"join": [{
                                |				"onCondition": "b.__time=a.__time",
                                |				"joinType": "JOIN",
                                |				"query": {
                                |					"alias": "a",
                                |					"dataSource": {
                                |						"dataSource": "summary_ui"
                                |					},
                                |
                                |					"queryParams": {
                                |						"aggregate": [],
                                |						"fields": [
                                |							"a.insight"
                                |						],
                                |						"filter": "",
                                |						"group": [],
                                |						"limit": "",
                                |						"sort": []
                                |					}
                                |				}
                                |			}]
                                |		},
                                |		"dataLabel": "yoyo"
                                |	}],
                                |	"moduleName": "SDS_INSIGHTS_DFP"
                                |}""".stripMargin
  val INVALID_SORT_QUERY =
    """{
      |	"dataQuery": [{
      |		"query": {
      |			"alias": "b",
      |			"dataSource": {
      |				"dataSource": "domain_ui"
      |			},
      |			"queryParams": {
      |				"aggregate": [],
      |				"fields": [
      |					"b.domain",
      |					"b.cnames",
      |					"a.insight"
      |				],
      |				"filter": "",
      |				"group": [],
      |				"limit": "",
      |				"sort": [")"]
      |			},
      |			"join": [{
      |				"onCondition": "b.__time=a.__time",
      |				"joinType": "JOIN",
      |				"query": {
      |					"alias": "a",
      |					"dataSource": {
      |						"dataSource": "summary_ui"
      |					},
      |
      |					"queryParams": {
      |						"aggregate": [],
      |						"fields": [
      |							"a.insight"
      |						],
      |						"filter": "",
      |						"group": [],
      |						"limit": "",
      |						"sort": []
      |					}
      |				}
      |			}]
      |		},
      |		"dataLabel": "yoyo"
      |	}],
      |	"moduleName": "SDS_INSIGHTS_DFP"
      |}""".stripMargin

  val INVALID_SUBQUERY =
    """
      |{
      |  "dataQuery": [
      |    {
      |      "query": {
      |        "dataSource": {
      |          "subQuery": {
      |            "dataSource": {
      |              "subQuery": {
      |                "dataSource": {
      |                  "dataSource": "Customers"
      |                },
      |                "alias": "c",
      |                "queryParams": {
      |                  "fields": [
      |                    "c.ID",
      |                    "c.Name"
      |                  ],
      |                  "aggregate": [],
      |                  "group": [")"]
      |                }
      |              }
      |            },
      |            "alias": "b",
      |            "queryParams": {
      |              "fields": [
      |                "b.ID",
      |                "b.Name"
      |              ],
      |              "sort": []
      |            }
      |          }
      |        },
      |        "alias": "a",
      |        "queryParams": {
      |          "fields": [
      |            "a.OrderID",
      |            "a.OrderDate"
      |          ],
      |          "aggregate": [],
      |          "group": []
      |        }
      |      },
      |      "dataLabel": "orders_data"
      |    }
      |  ],
      |  "moduleName": "INSIGHT_API",
      |  "role":"auditor"
      |}
      |""".stripMargin

  val INVALID_FIELD_QUERY ="""{
                             |    "dataQuery": [
                             |        {
                             |            "query": {
                             |                "dataSource": {
                             |                    "dataSource": "sds_ei_entity_inventory"
                             |                },
                             |                "queryParams": {
                             |                    "fields": ["type,os"],
                             |                    "group": ["type","os"],
                             |                    "aggregate":[["count","type"],["count","os"]],
                             |                    "filter": "(__time between TIMESTAMP '2023-05-08 00:00:00' AND TIMESTAMP '2023-05-09 00:00:00')",
                             |                    "sort": [["type",true]],
                             |                    "limit": "2",
                             |                    "offset": 4,
                             |                    "distinct":false
                             |                }
                             |            },
                             |            "dataLabel": "array_constructor_test"
                             |        }
                             |    ],
                             |    "moduleName": "v3_test"
                             |}
                             |""".stripMargin
  val INVALID_FIELD_QUERY_1 ="""{
                               |    "dataQuery": [
                               |        {
                               |            "query": {
                               |                "dataSource": {
                               |                    "dataSource": "sds_ei_entity_inventory"
                               |                },
                               |                "queryParams": {
                               |                    "fields": ["ARRAY_CONCAT(STRING_TO_ARRAY(asset_group, ':'),ARRAY['TESTTTT']) withAlias test_array","asset_group","host_name","type","class]"],
                               |                    "group": [],
                               |                    "filter": "(class = 'Host' AND type = 'Server') AND (__time between TIMESTAMP '2023-05-08 00:00:00' AND TIMESTAMP '2023-05-09 00:00:00')",
                               |                    "sort": [],
                               |                    "limit": "10"
                               |                }
                               |            },
                               |            "dataLabel": "array_constructor_test"
                               |        }
                               |    ],
                               |    "moduleName": "v3_test"
                               |}""".stripMargin

  val INVALID_ALIAS_QUERY = """{
                              |    "dataQuery": [
                              |        {
                              |            "query": {
                              |                "dataSource": {
                              |                    "dataSource": "sds_ei_entity_inventory"
                              |                },
                              |                "queryParams": {
                              |                    "fields": ["ARRAY_CONCAT(STRING_TO_ARRAY(asset_group, ':'),ARRAY['TESTTTT'])withAlias test_array","asset_group","host_name","type","class"],
                              |                    "group": [],
                              |                    "filter": "(class = 'Host' AND type = 'Server') AND (__time between TIMESTAMP '2023-05-08 00:00:00' AND TIMESTAMP '2023-05-09 00:00:00')",
                              |                    "sort": [],
                              |                    "limit": "10"
                              |                }
                              |            },
                              |            "dataLabel": "array_constructor_test"
                              |        }
                              |    ],
                              |    "moduleName": "v3_test"
                              |}""".stripMargin
  val INVALID_QUERY_WITHOUT_FIELDS = """{
                                       |    "dataQuery": [
                                       |        {
                                       |            "query": {
                                       |                "dataSource": {
                                       |                    "dataSource": "sds_ei_entity_inventory"
                                       |                },
                                       |                "queryParams": {
                                       |                    "field": ["type"],
                                       |                    "group": ["type"],
                                       |                    "aggregate":[["count","type"]],
                                       |                    "filter": "(__time between TIMESTAMP '2023-05-08 00:00:00' AND TIMESTAMP '2023-05-09 00:00:00')",
                                       |                    "sort": [["type",true]],
                                       |                    "limit": "2",
                                       |                    "offset": 4,
                                       |                    "distinct":false
                                       |                }
                                       |            },
                                       |            "dataLabel": "array_constructor_test"
                                       |        }
                                       |    ],
                                       |    "moduleName": "v3_test"
                                       |}
                                       |""".stripMargin
  val INVALID_FIELD_QUERY_2 = """{
                                |    "dataQuery": [
                                |        {
                                |            "query": {
                                |                "dataSource": {
                                |                    "dataSource": "sds_ei_entity_inventory"
                                |                },
                                |                "queryParams": {
                                |                    "fields": ["ARRAY_CONCAT(STRING_TO_ARRAY(asset_group, ':'),ARRAY['TESTTTT']))&9765fnb ","asset_group","host_name","type","class"],
                                |                    "group": [],
                                |                    "filter": "(class = 'Host' AND type = 'Server') AND (__time between TIMESTAMP '2023-05-08 00:00:00' AND TIMESTAMP '2023-05-09 00:00:00')",
                                |                    "sort": [],
                                |                    "limit": "5",
                                |                    "offset": 4
                                |                },
                                |				"alias":"alias"
                                |            },
                                |            "dataLabel": "array_constructor_teshht"
                                |        }
                                |    ],
                                |    "moduleName": "v3_test"
                                |}
                                |""".stripMargin
  val INVALID_GROUP_QUERY = """{
                              |    "dataQuery": [
                              |        {
                              |            "query": {
                              |                "dataSource": {
                              |                    "dataSource": "sds_ei_entity_inventory"
                              |                },
                              |                "queryParams": {
                              |                    "fields": ["type","os"],
                              |                    "group": ["type, os"],
                              |                    "aggregate":[["count","type"],["count","os"]],
                              |                    "filter": "(__time between TIMESTAMP '2023-05-08 00:00:00' AND TIMESTAMP '2023-05-09 00:00:00')",
                              |                    "sort": [["type",true]],
                              |                    "limit": "2",
                              |                    "offset": 4,
                              |                    "distinct":false
                              |                }
                              |            },
                              |            "dataLabel": "array_constructor_test"
                              |        }
                              |    ],
                              |    "moduleName": "v3_test"
                              |}""".stripMargin
  val VALID_QUERY_WITH_FROM_KEYWORD_IN_FIELDS = """{
                                                  |  "dataQuery": [
                                                  |      {
                                                  |          "query": {
                                                  |              "dataSource": {
                                                  |                  "dataSource":"sds_ei_entity_inventory",
                                                  |                  "subQuery":{
                                                  |                      "dataSource":{
                                                  |                          "dataSource":"sds_ei_entity_inventory"
                                                  |                          },
                                                  |
                                                  |                      "queryParams":{
                                                  |                          "fields":["MILLIS_TO_TIMESTAMP(CAST(updated_at_ts as BIGINT)) withAlias updated_ts"],
                                                  |                          "filter": "(__time between TIMESTAMP '2023-05-08 00:00:00' AND TIMESTAMP '2023-05-09 00:00:00')"
                                                  |                      }
                                                  |                  }
                                                  |              },
                                                  |              "queryParams": {
                                                  |                  "fields": ["CONCAT(EXTRACT(YEAR FROM updated_ts), '-', EXTRACT(MONTH from updated_ts), '-', EXTRACT(DAY from updated_ts)) withAlias Extracted_updated","updated_ts"],
                                                  |                  "group": [],
                                                  |                  "aggregate":[],
                                                  |                  "sort": []
                                                  |              }
                                                  |          },
                                                  |          "dataLabel": "array_constructor_test"
                                                  |      }
                                                  |  ],
                                                  |  "moduleName": "v3_test"
                                                  |}""".stripMargin

  val QUERY_WITH_HAVING = """{
                            |    "dataQuery": [
                            |        {
                            |            "query": {
                            |                "dataSource": {
                            |                    "dataSource": "sds_ei_entity_inventory"
                            |                },
                            |                "queryParams": {
                            |                    "fields": [
                            |                        "sum(fragments) withAlias count_fragments",
                            |                        "count(*) withAlias count_resolved",
                            |                        "class"
                            |                    ],
                            |                    "group": [
                            |                        "class"
                            |                    ],
                            |                    "having": "COUNT(*) > 0 and class ='Person'"
                            |                }
                            |            },
                            |            "dataLabel": "test_dataLabel"
                            |        }
                            |    ],
                            |    "moduleName": "test_moduleName"
                            |}""".stripMargin
  val QUERY_WITH_HAVINGWITHOUT_GROUP_BY = """{
                                            |    "dataQuery": [
                                            |        {
                                            |            "query": {
                                            |                "dataSource": {
                                            |                    "dataSource": "sds_ei_entity_inventory"
                                            |                },
                                            |                "queryParams": {
                                            |                    "fields": [
                                            |                        "sum(fragments) withAlias count_fragments",
                                            |                        "count(*) withAlias count_resolved",
                                            |                        "class"
                                            |                    ],
                                            |                    "group": [],
                                            |                    "having": "COUNT(*) > 0 and class ='Person'"
                                            |                }
                                            |            },
                                            |            "dataLabel": "test_dataLabel"
                                            |        }
                                            |    ],
                                            |    "moduleName": "test_moduleName"
                                            |}""".stripMargin

  val JOIN_QUERY_WITH_SUBQUERY_ALIAS = """{
                                         |    "dataQuery": [
                                         |        {
                                         |            "query": {
                                         |                "dataSource": {
                                         |                    "dataSource": "sds_ei_entity_inventory"
                                         |                },
                                         |                "queryParams": {
                                         |                    "group": [
                                         |                        "test",
                                         |                        "class"
                                         |                    ],
                                         |                    "fields": [
                                         |                        "test withAlias cei_status",
                                         |                        "COUNT(*) withAlias total",
                                         |                        "class"
                                         |                    ],
                                         |                    "filter": "(__time between TIMESTAMP '2023-12-31 00:00:00' AND TIMESTAMP '2023-12-31 23:59:59.999')",
                                         |                    "sort": [
                                         |                        [
                                         |                            "class",
                                         |                            true
                                         |                        ]
                                         |                    ]
                                         |                },
                                         |                "join": [
                                         |                    {
                                         |                        "joinType": "INNER JOIN",
                                         |                        "query": {
                                         |                            "alias": "sds_ccm_analytics_cei_metric",
                                         |                            "dataSource": {
                                         |                                "dataSource": "sds_ccm_analytics_cei_metric",
                                         |                                "subQuery": {
                                         |                                    "dataSource": {
                                         |                                        "dataSource": "sds_ccm_analytics_cei_metric"
                                         |                                    },
                                         |                                    "queryParams": {
                                         |                                        "group": [],
                                         |                                        "fields": [
                                         |                                            "FLATTEN(p_id) withAlias p_id",
                                         |                                            "cei_status"
                                         |                                        ],
                                         |                                        "filter": "(__time between TIMESTAMP '2023-12-31 00:00:00' AND TIMESTAMP '2023-12-31 23:59:59.999') AND cei_code in ('CEI-181', 'CEI-182', 'CEI-183', 'CEI-184', 'CEI-189', 'CEI-191', 'CEI-194', 'CEI-195', 'CEI-196', 'CEI-216', 'CEI-217', 'CEI-218', 'CEI-219', 'CEI-231', 'CEI-233', 'CEI-234', 'CEI-239', 'CEI-253', 'CEI-261', 'CEI-262', 'CEI-372', 'CEI-416', 'CEI-418', 'CEI-421', 'CEI-424', 'CEI-425', 'CEI-426', 'CEI-427', 'CEI-439', 'CEI-440', 'CEI-452', 'CEI-453', 'CEI-458', 'CEI-459', 'CEI-460', 'CEI-461', 'CEI-462', 'CEI-463', 'CEI-465', 'CEI-466', 'CEI-467', 'CEI-468', 'CEI-469', 'CEI-471', 'CEI-472', 'CEI-489', 'CEI-513', 'CEI-525', 'CEI-611', 'CEI-631', 'CEI-641', 'CEI-642', 'CEI-643', 'CEI-649', 'CEI-658', 'CEI-687')"
                                         |                                    }
                                         |                                }
                                         |                            },
                                         |                            "queryParams": {
                                         |                                "group": [
                                         |                                    "p_id"
                                         |                                ],
                                         |                                "fields": [
                                         |                                    "p_id withAlias cei_metric_p_id",
                                         |                                    "SUM(CASE WHEN cei_status!='Successful' THEN 1 ELSE 0 END) withAlias status_count"
                                         |                                ]
                                         |                            }
                                         |                        },
                                         |                        "onCondition": "sds_ei_entity_inventory.p_id=sds_ccm_analytics_cei_metric.cei_metric_p_id"
                                         |                    }
                                         |                ]
                                         |            },
                                         |            "dataLabel": "distribution"
                                         |        }
                                         |    ],
                                         |    "moduleName": "ccm"
                                         |}""".stripMargin
}
