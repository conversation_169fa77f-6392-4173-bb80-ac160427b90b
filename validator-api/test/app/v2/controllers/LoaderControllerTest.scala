//package app.v2.controllers
//
//import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
//import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
//import akka.util.Timeout
//import com.google.inject.{AbstractModule, Guice}
//import com.holdenkarau.spark.testing.DataFrameSuiteBase
//import common.{InvalidJsonError, InvalidSQLError}
//import net.codingwell.scalaguice.ScalaModule
//import org.apache.spark.SparkConf
//import org.apache.spark.sql.functions.{col, days, to_timestamp}
//import org.mockito.MockitoSugar
//import org.scalatest.BeforeAndAfter
//import org.scalatestplus.play.PlaySpec
//import play.api.Configuration
//import play.api.http.Status.{BAD_REQUEST, OK}
//import play.api.libs.json.{JsV<PERSON><PERSON>, <PERSON><PERSON>}
//import play.api.mvc.{ControllerComponents, Results}
//import play.api.test.{FakeRequest, Helpers}
//import play.api.test.Helpers.{POST, status}
//import v2.common.ExpressionRequest
//import v2.controllers.{BaseExecutionController, LoaderController}
//import v2.models.QueryParams
//import v2.utils.LiveContext
//
//import java.io.File
//import scala.concurrent.ExecutionContext
//import scala.concurrent.duration.DurationInt
//import scala.reflect.io.Directory
//
//class InventoryModelControllerTest extends PlaySpec with Results with BeforeAndAfter with DataFrameSuiteBase {
//
//  implicit val timeout: Timeout = 5.seconds
//  var writer: SDSTableWriter = _
//  var reader: SDSTableReader = _
//
//
//  def warehousePath: String = {
//    new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse")).createDirectory()
//    getClass().getResource("/iceberg_warehouse").getPath
//  }
//
//  override def conf: SparkConf = {
//    val conf = super.conf
//    conf.set("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions")
//      .set("spark.sql.catalog.iceberg_catalog", "org.apache.iceberg.spark.SparkCatalog")
//      .set("spark.sds.hive.catalog", "iceberg_catalog")
//      .set("spark.sql.catalog.iceberg_catalog.type", "hadoop")
//      .set("spark.sql.catalog.iceberg_catalog.warehouse", warehousePath)
//      .set("spark.sql.defaultCatalog", "iceberg_catalog")
//
//  }
//
//  before {
//    LiveContext.spark = spark
//    spark.sql("CREATE SCHEMA ei")
//    writer = SDSTableWriterFactory.get(SDSIcebergConnect.name, spark)
//    reader = SDSTableReaderFactory.get(SDSIcebergConnect.name, spark)
//  }
//
//  after {
//    new Directory(new File(warehousePath)).deleteRecursively()
//  }
//
//
////  "validateSchema" should {
////    "return success response for valid json" in {
////      val injector = Guice.createInjector(new InventoryModelControllerGuice())
////      val inventoryController = injector.getInstance(classOf[LoaderController])
////      val jsonContent = scala.io.Source.fromFile(getClass().getResource("/inventorymodel/input/valid_schema.json").getPath).mkString
////      val fakeRequest = FakeRequest(POST, "")
////        .withBody[JsValue](Json.parse(jsonContent))
////      val response = inventoryController.validateSchema().apply(fakeRequest)
////      status(response) mustBe OK
////
////    }
////  }
////
////  "validateSchema" should {
////    "raise exception for invalid json object" in {
////      val injector = Guice.createInjector(new InventoryModelControllerGuice())
////      val inventoryController = injector.getInstance(classOf[LoaderController])
////      val jsonContent = scala.io.Source.fromFile(getClass().getResource("/inventorymodel/input/invalid_schema.json").getPath).mkString
////
////      assertThrows[Exception] {
////        val fakeRequest = FakeRequest(POST, "")
////          .withBody[JsValue](Json.parse(jsonContent))
////        inventoryController.validateSchema().apply(fakeRequest)
////      }
////    }
////  }
////
////  "validate expression" should {
////    "return success response for valid sql expression" in {
////      val injector = Guice.createInjector(new InventoryModelControllerGuice())
////      val inventoryController = injector.getInstance(classOf[BaseExecutionController])
////      val jsonContent = scala.io.Source.fromFile(getClass().getResource("/inventorymodel/input/valid_expression.json").getPath).mkString
////      val fakeRequest = FakeRequest(POST, "")
////        .withBody[ExpressionRequest](Json.parse(jsonContent).as[ExpressionRequest])
////      val response = inventoryController.validateExpression().apply(fakeRequest)
////      status(response) mustBe OK
////
////    }
////  }
////
////  "validate expression" should {
////    "raise exception for invalid sql expression" in {
////      val injector = Guice.createInjector(new InventoryModelControllerGuice())
////      val inventoryController = injector.getInstance(classOf[BaseExecutionController])
////      val jsonContent = scala.io.Source.fromFile(getClass().getResource("/inventorymodel/input/invalid_expression.json").getPath).mkString
////      assertThrows[InvalidSQLError] {
////        val fakeRequest = FakeRequest(POST, "")
////          .withBody[ExpressionRequest](Json.parse(jsonContent).as[ExpressionRequest])
////        inventoryController.validateExpression().apply(fakeRequest)
////      }
////    }
////  }
//
//
//  "execute api" should {
//    "validate the given expression" in {
//
//
//      val data = spark.read.format("json").load(getClass.getResource("/inventorymodel/input/srdm_data.json").getPath)
//      val srdmData = data.withColumn("event_timestamp_ts", to_timestamp(col("event_timestamp_ts")))
//        .withColumn("parsed_interval_timestamp_ts", to_timestamp(col("parsed_interval_timestamp_ts")))
//      writer.overwritePartition(srdmData, "test.ei_table", Array(days(col("event_timestamp_ts")), days(col("parsed_interval_timestamp_ts"))))
//
//
//      val injector = Guice.createInjector(new InventoryModelControllerGuice())
//      val inventoryController = injector.getInstance(classOf[BaseExecutionController])
//      val jsonContent = scala.io.Source.fromFile(getClass().getResource("/inventorymodel/input/config1.json").getPath).mkString
//      val fakeRequest = FakeRequest(POST, "")
//        .withBody[JsValue](Json.parse(jsonContent))
//      val queryParams = QueryParams(null,null,null,null)
//      val result = inventoryController.run(queryParams).apply(fakeRequest)
//      status(result) mustBe OK
//
//
//    }
//  }
////
////  "execute api" should {
////    "raise exception if srdm table is not present" in {
////
////      val injector = Guice.createInjector(new InventoryModelControllerGuice())
////      val inventoryController = injector.getInstance(classOf[LoaderController])
////      val jsonContent = scala.io.Source.fromFile(getClass().getResource("/inventorymodel/input/config1.json").getPath).mkString
////      assertThrows[InvalidJsonError] {
////        val fakeRequest = FakeRequest(POST, "")
////          .withBody[JsValue](Json.parse(jsonContent))
////        spark.sql("CREATE SCHEMA test")
////
////        inventoryController.run(Some(Seq.empty[String]),Some(Seq("true")),Some(Seq("true")),Some(Seq("50"))).apply(fakeRequest)
////      }
////
////
////    }
////  }
//
//
//}
//
//class InventoryModelControllerGuice extends AbstractModule with ScalaModule with MockitoSugar {
//  val conf = mock[Configuration]
//  val controller = Helpers.stubControllerComponents()
//
//  override def configure(): Unit = {
//    bind[ControllerComponents].toInstance(controller)
//    bind[Configuration].toInstance(conf)
//    bind[ExecutionContext].toInstance(scala.concurrent.ExecutionContext.Implicits.global)
//    bind[BaseExecutionController].toInstance()
//
//
//  }
//}
