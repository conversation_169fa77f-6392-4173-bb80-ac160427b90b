//package app.common
//
//import com.google.inject.{AbstractModule, Guice}
//import common.HealthCheckController
//import net.codingwell.scalaguice.ScalaModule
//import org.scalatestplus.play.PlaySpec
//import play.api
//import play.api.http.Status.{NOT_FOUND, OK}
//import play.api.inject
//import play.api.inject.guice.GuiceApplicationBuilder
//import play.api.mvc.{ControllerComponents, Results}
//import play.api.test.{FakeRequest, Helpers}
//import play.api.test.Helpers.{GET, defaultAwaitTimeout, route, running, status, writeableOf_AnyContentAsEmpty}
//
//
//class HealthCheckControllerTest extends PlaySpec with Results {
//
//
//  "HealthCheckRouter" in {
//    val injector = Guice.createInjector(new HealthCheckControllerGuice)
//    val controller = injector.getInstance(classOf[HealthCheckController])
//    val app = new GuiceApplicationBuilder()
//      .overrides(inject.bind[HealthCheckController].to(controller)).build()
//    running(app){
//      val Some(response) = route(app, FakeRequest(GET, "/healthcheck"))
//      status(response) mustBe OK
//
//    }
//  }
//
//
//}
//
//class HealthCheckControllerGuice extends AbstractModule with ScalaModule {
//  val controller = Helpers.stubControllerComponents()
//
//  override def configure(): Unit = {
//    bind[ControllerComponents].toInstance(controller)
//
//  }
//}
