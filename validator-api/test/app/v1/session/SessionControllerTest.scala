//package app.v1.session
//
//import com.google.inject.{AbstractModule, Guice}
//import com.holdenkarau.spark.testing.DataFrameSuiteBase
//import net.codingwell.scalaguice.ScalaModule
//import org.mockito.MockitoSugar
//import org.scalatest.BeforeAndAfter
//import org.scalatestplus.play.PlaySpec
//import play.api.libs.json.{JsValue, Json}
//import play.api.mvc.{ControllerComponents, Results}
//import play.api.test.Helpers.POST
//import play.api.test.{FakeRequest, Helpers}
//import v1.session.SessionController
//import v1.utils.LiveContext
//
//import scala.concurrent.duration.DurationInt
//
//class SessionControllerTest extends PlaySpec with Results with BeforeAndAfter with DataFrameSuiteBase {
//
//  LiveContext.spark = this.spark
//
//  "SessionController" should {
//    "be up" in {
//
//      val injector = Guice.createInjector(new SessionControllerGuice())
//      val sessionController = injector.getInstance(classOf[SessionController])
//      val content = scala.io.Source.fromFile(getClass().getResource("/inventorymodel/input/valid_schema.json").getPath).mkString
//      val fakeRequest = FakeRequest(POST, "")
//        .withBody[JsValue](Json.parse(content))
//      val response = sessionController.up.apply(fakeRequest)
////      status(response) mustBe OK
//
//    }
//  }
//
//  "session controller" should {
//    "be down" in {
//      val injector = Guice.createInjector(new SessionControllerGuice())
//      val sessionController = injector.getInstance(classOf[SessionController])
//      val jsonContent = scala.io.Source.fromFile(getClass().getResource("/inventorymodel/input/valid_schema.json").getPath).mkString
//      val fakeRequest = FakeRequest(POST, "v1/session/down")
//        .withBody[JsValue](Json.parse(jsonContent))
//      val response = sessionController.down.apply(fakeRequest)
////      status(response) mustBe OK
//
//    }
//  }
//
//}
//
//class SessionControllerGuice extends AbstractModule with ScalaModule with MockitoSugar {
//  val controller = Helpers.stubControllerComponents()
//
//  override def configure(): Unit = {
//    bind[ControllerComponents].toInstance(controller)
//
//  }
//}
