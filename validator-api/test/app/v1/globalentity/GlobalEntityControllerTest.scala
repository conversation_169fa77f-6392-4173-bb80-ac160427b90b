//package app.v1.globalentity
//
//import akka.util.Timeout
//import com.google.inject.{AbstractModule, Guice}
//import common.InvalidSQLError
//import net.codingwell.scalaguice.ScalaModule
//import org.scalatestplus.play.PlaySpec
//import play.api.http.Status.OK
//import play.api.libs.json.{JsValue, Json}
//import play.api.mvc.{ControllerComponents, Results}
//import play.api.test.Helpers.{POST, contentAsString, status}
//import play.api.test.{FakeRequest, Helpers}
//import v1.common.ExpressionRequest
//import v1.globalentity.GlobalEntityController
//
//import scala.concurrent.ExecutionContext
//import scala.concurrent.duration.DurationInt
//
//class GlobalEntityControllerTest extends PlaySpec with Results {
//  implicit val timeout: Timeout = 5.seconds
//
//  "validate schema" should {
//    "give ok response for valid json" in {
//      val injector = Guice.createInjector(new GlobalEntityControllerTestGuice())
//      val globalEntityController = injector.getInstance(classOf[GlobalEntityController])
//      val json_content = scala.io.Source.fromFile(getClass().getResource("/globalentity/input/valid_schema.json").getPath).mkString
//      val fakeRequest = FakeRequest(POST, "")
//        .withBody[JsValue](Json.parse(json_content))
//      val response = globalEntityController.validateSchema().apply(fakeRequest)
//      val message = (Json.parse(contentAsString(response)) \ "message").as[String]
//      assert(message == "Valid JSON Object")
//      status(response) mustBe OK
//    }
//  }
//
//
//  "validate schema" should {
//    "raise exception for invalid json" in {
//      val injector = Guice.createInjector(new GlobalEntityControllerTestGuice())
//      val globalEntityController = injector.getInstance(classOf[GlobalEntityController])
//      val json_content = scala.io.Source.fromFile(getClass().getResource("/globalentity/input/invalid_schema.json").getPath).mkString
//      assertThrows[Exception] {
//        val fakeRequest = FakeRequest(POST, "")
//          .withBody[JsValue](Json.parse(json_content))
//        globalEntityController.validateSchema().apply(fakeRequest)
//      }
//    }
//  }
//
//  "validate expression" should {
//    "give ok response for valid expression" in {
//      val injector = Guice.createInjector(new GlobalEntityControllerTestGuice())
//      val globalEntityController = injector.getInstance(classOf[GlobalEntityController])
//      val json_content = scala.io.Source.fromFile(getClass().getResource("/globalentity/input/valid_expression.json").getPath).mkString
//      val fakeRequest = FakeRequest(POST, "")
//        .withBody[ExpressionRequest](Json.parse(json_content).as[ExpressionRequest])
//      val response = globalEntityController.validateExpression().apply(fakeRequest)
//      val message = (Json.parse(contentAsString(response)) \ "message").as[String]
//      assert(message == "Valid SQL Expression")
//      status(response) mustBe OK
//    }
//  }
//
//  "validateExpression" should {
//    "raise exception for invalid expression" in {
//      val injector = Guice.createInjector(new GlobalEntityControllerTestGuice())
//      val globalEntityController = injector.getInstance(classOf[GlobalEntityController])
//      val jsonContent = scala.io.Source.fromFile(getClass().getResource("/globalentity/input/invalid_expression.json").getPath).mkString
//      assertThrows[InvalidSQLError] {
//        val fakeRequest = FakeRequest(POST, "")
//          .withBody[ExpressionRequest](Json.parse(jsonContent).as[ExpressionRequest])
//        globalEntityController.validateExpression.apply(fakeRequest)
//      }
//    }
//  }
//
//
//}
//
//class GlobalEntityControllerTestGuice extends AbstractModule with ScalaModule {
//  val controller = Helpers.stubControllerComponents()
//
//  override def configure(): Unit = {
//    bind[ControllerComponents].toInstance(controller)
//
//  }
//}
