package v2.controllers

import jakarta.inject.Inject
import play.api.libs.json.JsValue
import play.api.mvc.{AbstractController, Action, ControllerComponents}
import v2.common.{RedisBase, ResolutionLevel}
import v2.models.ExecutionModule

import scala.concurrent.ExecutionContext

class RedisController @Inject()(cc: ControllerComponents,
                                         redisBase: RedisBase)
                                        (implicit ec:ExecutionContext)extends AbstractController(cc){

  def deleteRedisCache(): Action[JsValue] = Action(parse.json){
    implicit  request => {
      val requestBody: JsValue = request.body
      val name= (requestBody \ "name").as[String]
      val resolutionLevel = (requestBody \ "resolution_level").asOpt[String].getOrElse(ResolutionLevel.ENTITY_RESOLUTION)
      redisBase.deleteCacheByPattern(name,resolutionLevel)
      Ok("Json.toJson(resultsService.getResultAsJson(name))")
    }
  }

  def setKey(): Action[JsValue] = Action(parse.json){
    implicit  request => {
      val requestBody: JsValue = request.body
      val name= (requestBody \ "name").as[String]
      val value = (requestBody \ "value").asOpt[String].getOrElse(ExecutionModule.COMPLETED)
      redisBase.setCacheDefault(name,value)
      Ok("key set ")
    }
  }
}