package v2.controllers


import akka.actor.ActorSystem
import play.api.Configuration
import play.api.mvc._
import v2.services.{DependencyManagerService, LineageService, ResultsService}

import scala.concurrent.ExecutionContext
import javax.inject.Inject
import v2.common.Modules

class IntraController @Inject()(
                                 cc: ControllerComponents,
                                 dependencyManagerService: DependencyManagerService,
                                 appConfig: Configuration,
                                 resultsService: ResultsService,
                                 lineageService: LineageService,
                                 actorSystem: ActorSystem
                               )
  extends BaseExecutionController(cc, dependencyManagerService, appConfig, resultsService, lineageService,actorSystem) {


  override def getModuleName: String = {
    Modules.INTRA
  }
}