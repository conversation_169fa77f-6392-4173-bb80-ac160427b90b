package v2.controllers


import akka.actor.ActorSystem
import v2.configs.Config
import common.InvalidJsonError
import org.json4s._
import play.api.Configuration
import play.api.libs.json._
import play.api.mvc._
import v2.services.{DependencyManagerService, LineageService, ResultsService}
import v2.utils.ExecuteExpressionResponse

import javax.inject.Inject
import scala.concurrent.ExecutionContext
import v2.models.{ExecutionModule, ExecutionResult}



class GlobalEntityController @Inject()(
                                  cc: ControllerComponents,
                                  dependencyManagerService: DependencyManagerService,
                                  appConfig: Configuration,
                                  resultsService: ResultsService,
                                  lineageService: LineageService,
                                  actorSystem: ActorSystem
                                )
  extends BaseExecutionController(cc, dependencyManagerService, appConfig, resultsService,lineageService,actorSystem) {



  override def getModuleName: String = "global_entity_config"


  private def isSnakeCase(str: String): Boolean =  "^[a-z]+[a-z0-9]*(_[a-z0-9]+)*$".r.matches(str)

  def validateSchema: Action[JsValue] = Action(parse.json) { request =>
    val config: Config = try {
      val body = request.body.toString()
      org.json4s.jackson.JsonMethods.parse(body).extract[Config]
    } catch {
      case ex: Exception => throw InvalidJsonError(ex.getMessage)

    }
    val configName = (request.body \ "entityClass").as[String]
    val lastUpdateFields = config.lastUpdateFields.map{str=> str->isSnakeCase(str)}

    val commonSQLValidation = dependencyManagerService.validateSQL(config.commonProperties, "commonProperties")
    val entitySQLValidation = dependencyManagerService.validateSQL(config.entitySpecificProperties, "entitySpecificProperties")
    val fieldLevelSpecValidation = dependencyManagerService.validateSQL(config.fieldLevelSpec,"fieldLevelSpec")

    val allSQLValidationErrors = commonSQLValidation ++ entitySQLValidation ++ fieldLevelSpecValidation

    val allSnakeCaseValid = lastUpdateFields.forall(_._2)
    val allSQLValid = allSQLValidationErrors.isEmpty

    if (allSnakeCaseValid && allSQLValid) {
      val output = Json.arr(Json.obj("status"->"Successfully Validated"))
      Ok(Json.toJson(ExecutionResult(configName=configName,status=ExecutionModule.COMPLETED,result = Json.toJson(ExecuteExpressionResponse(output = output,headers = Json.toJson(Array("status")))))))
    } else {
      val output = Json.arr(Json.obj("status"->"Invalid JSON Payload"))
      val failedSnakeCase = lastUpdateFields.filterNot(_._2).map(_._1)
      val errorMessage = Json.obj(
        "invalidSnakeCase" -> failedSnakeCase,
        "invalidSQL" -> allSQLValidationErrors
      )

      val result: List[String] = errorMessage.fields.flatMap {
        case ("invalidSnakeCase", JsArray(values)) =>
          if (values.nonEmpty){List(s"invalidSnakeCase : ${values.map(_.as[String]).mkString(" , ")}")}
          else{
            None
          }


        case (_, JsArray(values)) =>
          values.map(_.as[String]).toList

        case _ => Nil // In case there are any other formats
      }.toList

      BadRequest(Json.toJson(ExecutionResult(configName=configName,status = ExecutionModule.ERROR,executionFlow = result,result =Json.toJson(ExecuteExpressionResponse(message="Invalid JSON Payload",output = output,headers = Json.toJson(Array("status")))))))

    }
  }



}