package v2.controllers

import akka.actor.ActorSystem
import play.api.Logger
import play.api.libs.json.Json
import play.api.mvc.{AbstractController, Action, AnyContent, ControllerComponents}

import javax.inject.Inject
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters._


class ThreadController @Inject()(cc: ControllerComponents, actorSystem: ActorSystem, ec1: ExecutionContext) extends AbstractController(cc) {
  private val LOGGER = Logger(getClass)

  def threadCount: Action[AnyContent] = Action.async {
    implicit val ec: ExecutionContext = actorSystem.dispatchers.lookup("play.spark.dispatcher")
    Future {
      val dispatcherThreads = Thread.getAllStackTraces.keySet().asScala
        .map(_.getName)
        .count(_.startsWith("application-play.spark.dispatcher-"))
      LOGGER.error(s"[${Thread.currentThread().getName}] ${System.currentTimeMillis()} - Job X started")
      LOGGER.error(s"[${Thread.activeCount()}] ${System.currentTimeMillis()} - Job X started")
      //      Ok(s"${Thread.activeCount()}]")
      Ok(Json.obj("count" -> s"${Thread.activeCount()}",
        "current thread" -> s"${Thread.currentThread().getName}",
        "time" -> s"${System.currentTimeMillis()}",
        "dispatcher thread count" -> s"$dispatcherThreads"))
    }(ec)
  }


  def normalCount(): Action[AnyContent] = Action.async {
    Future {
      Ok(Json.obj("count" -> s"${Thread.activeCount()}",
        "current thread" -> s"${Thread.currentThread().getName}",
        "time" -> s"${System.currentTimeMillis()}"))
    }(ec1)
  }
}
