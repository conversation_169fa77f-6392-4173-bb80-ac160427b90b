package v2.utils

import play.api.libs.json.{JsA<PERSON>y, JsValue, <PERSON>son}




case class ExecuteExpressionResponse(message:String="Successfully extracted required fields",
                                     output:JsArray=Json.arr(),
                                     headers:JsValue=Json.arr())

case class ExecuteExpressionDummyResponse(configName:String="Successfully extracted required fields",
                                          status:String,
                                          result:JsValue)

case class ExecuteExpressionErrorResponse(message:String="ERROR",
                                          error:String)


object ExecuteExpressionResponse{
  implicit val executeExpressionResponse=Json.format[ExecuteExpressionResponse]

}

object ExecuteExpressionErrorResponse{
  implicit val executeExpressionErrorResponse=Json.format[ExecuteExpressionErrorResponse]

}


object ExecuteExpressionDummyResponse{
  implicit val executeExpressionDummyResponse=Json.format[ExecuteExpressionDummyResponse]

}


