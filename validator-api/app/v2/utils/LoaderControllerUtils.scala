package v2.utils

import ai.prevalent.entityinventory.common.configs
import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReaderFactory
import common.TableNotFoundError
import org.apache.spark.sql.{Row, SparkSession}
import org.apache.spark.sql.functions.expr
import org.json4s.DefaultFormats
import org.json4s.jackson.JsonMethods.parse
import play.api.Logger
import play.api.libs.json.JsResult
import v2.common.{Modules, ResolutionLevel}




object LoaderControllerUtils {
  private val LOGGER = Logger(getClass)

  case class TimeStampDetails(parsedIntervalEndEpoch: Long, parsedIntervalStartEpoch: Long,
                              previousEndEpoch: Long, eventTimestampEndEpoch: Long)

  def getPropertyColNames(property: Array[configs.Property]): Array[String] = {
    property.map(item => item.colName)
  }
  def getNullColFilteredConfig(config: Config): Config = {
    val pattern = """(?i)cast\s*\(\s*null as\s+(\w+)\)""".r
    val filteredConfig = config.copy(
      commonProperties = config.commonProperties.filterNot(prop => pattern.pattern.matcher(prop.colExpr).matches),
      entitySpecificProperties = config.entitySpecificProperties.filterNot(prop => pattern.pattern.matcher(prop.colExpr).matches),
      sourceSpecificProperties = config.sourceSpecificProperties.filterNot(prop => pattern.pattern.matcher(prop.colExpr).matches),
      temporaryProperties = config.temporaryProperties.filterNot(prop => pattern.pattern.matcher(prop.colExpr).matches)
    )
    filteredConfig
  }

  def checkTableExist(srdm: String, spark: SparkSession): Boolean = {
    try{
      SDSTableReaderFactory.getDefault(spark).isTableExists(srdm)
    }
    catch {
      case ex: Exception =>
        LOGGER.error(s"${ex.getMessage}, ${ex.getStackTrace.map(_.toString).mkString("\n")}" )
        throw TableNotFoundError(message = ex.getMessage ++ s"Table $srdm")
    }
  }

  def getTimeStampDetails(srdm: String, spark: SparkSession,resolution_level:String = ResolutionLevel.ENTITY_RESOLUTION): (TimeStampDetails, Boolean) = {
    implicit val formats: DefaultFormats.type = DefaultFormats

    val srdmInterval = resolution_level match {
      case ResolutionLevel.ENTITY_RESOLUTION => sys.env.getOrElse("SRDM_INTERVAL_DAYS", 10)
      case ResolutionLevel.CANDIDATE_KEY_RESOLUTION =>1
    }

    LOGGER.info(s"Validator::: srdmInterval = $srdmInterval ")

    var sql =
      s"""SELECT
         |    date(event_timestamp_ts) AS event_timestamp_ts_day,
         |    date(parsed_interval_timestamp_ts) AS parsed_interval_timestamp_ts_day
         |FROM
         |    $srdm
         |ORDER BY
         |    parsed_interval_timestamp_ts DESC,
         |    event_timestamp_ts DESC
         |LIMIT 1;""".stripMargin

    // Check if the "partition" column exists
    val describeResult = spark.sql(s"DESCRIBE $srdm.partitions")
    if (describeResult.filter("col_name = 'partition'").count() > 0) {
      sql = s"""SELECT
               |    partition.event_timestamp_ts_day,
               |    partition.parsed_interval_timestamp_ts_day
               |FROM
               |    $srdm.partitions
               |WHERE
               |	partition.event_timestamp_ts_day is not null
               |	AND partition.parsed_interval_timestamp_ts_day is not null
               |ORDER BY
               |    partition.parsed_interval_timestamp_ts_day DESC,
               |    partition.event_timestamp_ts_day DESC
               |LIMIT 1;""".stripMargin
    }

    LOGGER.info(s"***************** Timestamp SQL: $sql *************************")

    val defaultJson =
      """{"parsedIntervalEndEpoch":0, "parsedIntervalStartEpoch":0, "previousEndEpoch":0,
        |        "eventTimestampEndEpoch":0}""".stripMargin
    val timeStampDetails = spark.sql(sql)
      .withColumn("eventTimestampEndEpoch",
        expr("to_timestamp(date_trunc('day', CURRENT_TIMESTAMP()) + INTERVAL '1 DAY - 1 MILLISECOND')"))
      .withColumn("eventTimestampEndEpoch",
        expr("concat(unix_timestamp(eventTimestampEndEpoch), date_format(eventTimestampEndEpoch, 'SSS'))"))
      .withColumn("parsedIntervalEndEpoch",
        expr("to_timestamp(date_trunc('hour', parsed_interval_timestamp_ts_day) + INTERVAL '1 DAY - 1 MILLISECOND')"))
      .withColumn("parsedIntervalEndEpoch",
        expr("concat(unix_timestamp(parsedIntervalEndEpoch), date_format(parsedIntervalEndEpoch, 'SSS'))"))
      .withColumn("parsedIntervalStartEpoch",
        expr(s"to_timestamp(date_trunc('hour', parsed_interval_timestamp_ts_day) - INTERVAL $srdmInterval DAY)"))
      .withColumn("parsedIntervalStartEpoch",
        expr("concat(unix_timestamp(parsedIntervalStartEpoch), date_format(parsedIntervalStartEpoch, 'SSS'))"))
      .withColumn("previousEndEpoch",
        expr(s"to_timestamp(date_trunc('hour', event_timestamp_ts_day) - INTERVAL 10 DAY)"))
      .withColumn("previousEndEpoch",
        expr("concat(unix_timestamp(previousEndEpoch), date_format(previousEndEpoch, 'SSS'))"))
      .selectExpr("CAST(parsedIntervalEndEpoch AS LONG)", "CAST(parsedIntervalStartEpoch AS LONG)",
        "CAST(previousEndEpoch AS LONG)", "CAST(eventTimestampEndEpoch AS LONG)").collect()
    if (timeStampDetails.length > 0)
      (parse(timeStampDetails(0).json).extract[TimeStampDetails], true)
    else (parse(defaultJson).extract[TimeStampDetails], false)
  }
}