package v2.common
import ai.prevalent.entityinventory.common.configs.EIJobArgs
import play.api.libs.json.{JsA<PERSON>y, JsResult, JsSuccess, JsValue, <PERSON>son, <PERSON><PERSON>, Js<PERSON>rror}

case class cdsRequest(EIJobArgs: EIJobArgs,JarLocation : String)



object cdsRequest {
  implicit val eiJobArgsReads: Reads[EIJobArgs] = new Reads[EIJobArgs] {
    def reads(json: JsValue): JsResult[EIJobArgs] = {
      try {
        val args = new EIJobArgs
        args.configPath = (json \ "configPath").as[String]
        args.parsedIntervalStartEpoch = (json \ "parsedIntervalStartEpoch").asOpt[Long].getOrElse(0L)
        args.srdmHistoricalParsedIntervalStartEpoch = (json \ "srdmHistoricalParsedIntervalStartEpoch").asOpt[Long].getOrElse(0L)
        args.parsedIntervalEndEpoch = (json \ "parsedIntervalEndEpoch").asOpt[Long].getOrElse(-1L)
        args.currentUpdateDate = (json \ "currentUpdateDate").as[Long]
        args.prevUpdateDate = (json \ "prevUpdateDate").asOpt[Long].getOrElse(-1L)
        args.eIConfigVersion = (json \ "eIConfigVersion").asOpt[String].getOrElse("new")
        args.processDeltaProperty = (json \ "processDeltaProperty").asOpt[Boolean].getOrElse(true)
        JsSuccess(args)
      } catch {
        case e: Throwable => JsError(e.getMessage)
      }
    }
  }
  implicit val reads: Reads[cdsRequest] = Json.reads[cdsRequest]
}
