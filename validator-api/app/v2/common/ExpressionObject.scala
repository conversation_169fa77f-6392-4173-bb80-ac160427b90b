package v2.common

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Reads}

case class ExpressionResponse(message:String="Valid SQL Expression",output:String)

object ExpressionResponse{
  implicit val expressionResponse=Json.format[ExpressionResponse]
}


case class ExpressionRequest(expression: String)

object ExpressionRequest {
  implicit val reads: Reads[ExpressionRequest] = Json.reads[ExpressionRequest]
}


