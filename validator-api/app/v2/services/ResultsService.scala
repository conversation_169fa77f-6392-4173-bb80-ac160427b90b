package v2.services

import org.apache.spark.sql.DataFrame
import akka.http.scaladsl.model.Uri.Query.Empty.distinct
import jdk.xml.internal.XMLSecurityManager.Limit
import org.apache.spark.sql.catalyst.plans.logical.Distinct
import org.apache.spark.sql.functions.col
import play.api.Logger

import java.util.concurrent.ConcurrentHashMap
import scala.jdk.CollectionConverters._
import play.api.libs.json.{JsValue, Json}
import v2.models.{ExecutionModule, ExecutionResult}

import javax.inject._

@Singleton
class ResultsService {

  private val resultsMap = new ConcurrentHashMap[String, ExecutionResult]().asScala
  private val logger = Logger(getClass)

  def storeResult(configName: String, status: String = ExecutionModule.IN_PROGRESS, executionFlow: List[String] = List.empty, result: JsValue = Json.obj()): Unit = {
    val updatedResult = resultsMap.get(configName) match {
      case Some(existingResult) =>
        if (existingResult.status==ExecutionModule.ERROR){
          // Append to existing execution flow
          ExecutionResult(
            configName= configName,
            status = ExecutionModule.ERROR,
            executionFlow = existingResult.executionFlow ++ executionFlow,
            result = result
          )
        }
        else{
          ExecutionResult(
            configName= configName,
            status = status,
            executionFlow = existingResult.executionFlow ++ executionFlow,
            result = result
          )
        }

      case None =>
        // Create new result entry
        ExecutionResult(
          configName= configName,
          status = status,
          executionFlow = executionFlow,
          result = result
        )
    }
    logger.info(s"Validator::: status &&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&& $status")
    logger.info(s"Validator::: updatedResult.executionFlow = ${updatedResult.executionFlow} " )

    resultsMap.update(configName, updatedResult)
  }

  def getResultAsJson(configName: String): Option[ExecutionResult] = {
    resultsMap.get(configName).map(result => result)
  }

  def clearResult(configName: String): Unit = {
    resultsMap.remove(configName)
  }

  def getCurrentSearches: Map[String, ExecutionResult] = {
    resultsMap.view
      .filter(_._2.status == "Completed")
      .toMap
  }
  def clearAllResult(): Unit = {
    resultsMap.clear()
  }

  def getAllItems: JsValue = {
    val allResults = resultsMap.values.toList
    Json.toJson(allResults)
  }

  def processOutput(fields: Array[String], resultSet: DataFrame, limit: Option[Seq[String]], distinct:Option[Seq[String]] ): (Array[JsValue], DataFrame) = {
    var field = fields.intersect(resultSet.columns)
    val fullSet = resultSet.select(field.map(col): _*)
    val reducedSet = if (distinct.getOrElse(Seq("true")).headOption.forall(_.toBoolean)) fullSet.distinct() else fullSet
    val output: Array[JsValue] = reducedSet.limit(limit.getOrElse(Seq("50")).headOption.map(_.toInt).getOrElse(50)).toJSON.collect().map(Json.parse)

    (output, reducedSet)

  }
}