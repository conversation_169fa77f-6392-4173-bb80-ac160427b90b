package v2.services

import ai.prevalent.entityinventory.common.configs.Property
import ai.prevalent.sdspecore.utils.PidUtils.LOGGER
import akka.actor.ActorSystem
import v2.models.{ModuleServiceBase, QueryParams}
import common.InvalidSQLError
import org.apache.spark.sql.functions.expr
import play.api.libs.json.{JsValue, Json}
import v2.controllers.ModuleVariables

import scala.concurrent.{ExecutionContext, Future}
import javax.inject.Inject
import scala.reflect.runtime.universe._
import scala.concurrent.Await
import v2.common.Modules
import akka.actor.ActorSystem

import scala.collection.parallel.CollectionConverters._

class DependencyManagerService @Inject()(lineageService: LineageService,
                                         loaderService: LoaderService,
                                         interService: InterService,
                                         intraService: IntraService,
                                         relationshipExtractorService: RelationshipExtractionService,
                                         relationshipDisambiguationService: RelationshipDisambiguationService,
                                         publisherService: PublisherService,
                                         entityRelEnrichService: EntityRelEnrichService,
                                         resultsService: ResultsService,
                                         actorSystem: ActorSystem)
                                         {
  implicit val ec: ExecutionContext =actorSystem.dispatchers.lookup("play.spark.dispatcher")

  def runModule(params: QueryParams, config: JsValue,runModuleVariables: ModuleVariables): Future[Unit] = {
    Future {
      resultsService.storeResult(configName = runModuleVariables.configName,executionFlow = List(s"${runModuleVariables.configName} In-progress"))
      resolveAndRunDependencies(runModuleVariables.validatorModule, params, config: JsValue, runModuleVariables).recover {
        case ex: Exception =>
          storeResult(
            configName = runModuleVariables.configName, status = "Error",
            executionFLow = List(s"Failed to run module ${runModuleVariables.validatorModule}: ${ex.getMessage}")
          )
      }
    }
  }

  private def resolveAndRunDependencies(moduleName: String, params: QueryParams, config: JsValue, runModuleVariables: ModuleVariables): Future[Unit] = {
    for {
      dependencies <- lineageService.getDependencies(moduleName)
      _ <- resolveDependencies(dependencies, params, config: JsValue, runModuleVariables)
      _ <- executeSubModules(moduleName, params, config: JsValue, runModuleVariables)
      _ <- executeModule(moduleName, params, config: JsValue, runModuleVariables)
    } yield ()
  }

  private def resolveDependencies(dependencies: Seq[String], params: QueryParams, config: JsValue, runModuleVariables: ModuleVariables): Future[Unit] = {
    dependencies.foldLeft(Future.successful(())) { (acc, dep) =>
      acc.flatMap { _ =>
        resolveAndRunDependencies(dep, params, config, runModuleVariables)
      }
    }

  }

  private def executeSubModules(moduleName: String, params: QueryParams, config: JsValue, runModuleVariables: ModuleVariables): Future[Unit] = {
    val executionMode: String = s"${sys.env.getOrElse("EXECUTION_MODE", "SEQUENTIAL")}"

    LOGGER.info(s"Executing submodules in $executionMode mode for ${lineageService.getSubModules(runModuleVariables.lineageData, moduleName)}")
    lineageService.getSubModules(runModuleVariables.lineageData, moduleName).flatMap { subModules =>
      if (executionMode == "PARALLEL") {
        LOGGER.info(s"Executing submodules in parallel for $moduleName, ${subModules.mkString(",")}")
        implicit val ec: ExecutionContext =actorSystem.dispatchers.lookup("play.spark.dispatcher")

        Future.traverse(
          subModules) { subModule =>
            executeSubModule(moduleName, subModule, params, runModuleVariables)
          }
        .map(_ => ())(ec)
      } else {
        subModules.foldLeft(Future.successful(())) { (acc, subModule) =>
          acc.flatMap { _ =>
            executeSubModule(moduleName, subModule, params, runModuleVariables)
          }
        }
      }
    }

  }

  private def executeSubModule(moduleName: String, subModuleName: String, params: QueryParams, runModuleVariables: ModuleVariables): Future[Unit] = {

    getService(moduleName).flatMap {
      case loaderService: LoaderService => loaderService.execute(runModuleVariables.configName, subModuleName, params, schemaName = runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl)
      case intraService: IntraService => intraService.executeDisambiguator(runModuleVariables.configName, subModuleName, params, schemaName = runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl,eiFragmentSchemaName=runModuleVariables.eiFragmentSchemaName)
      case interService: InterService => interService.executeDisambiguator(runModuleVariables.configName, subModuleName, params, schemaName = runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl,eiFragmentSchemaName=runModuleVariables.eiFragmentSchemaName)
      case relationshipService: RelationshipExtractionService => relationshipService.executeRelationship(runModuleVariables.configName, subModuleName, params, schemaName = runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl,eiFragmentSchemaName= runModuleVariables.eiFragmentSchemaName)
      case relationshipDisambiguationService: RelationshipDisambiguationService => relationshipDisambiguationService.executeRelationship(runModuleVariables.configName, subModuleName, params, schemaName = runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl,eiFragmentSchemaName= runModuleVariables.eiFragmentSchemaName)
      case publisherService: PublisherService  => publisherService.executeWithPubSchema(runModuleVariables.configName, subModuleName, params, schemaName = runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl, publishSchemaName = runModuleVariables.publisherSchemaName)
      case entityRelEnrichService: EntityRelEnrichService  => entityRelEnrichService.executeWithPubSchema(runModuleVariables.configName, subModuleName, params, schemaName = runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl, eiEnrichSchemaName = runModuleVariables.eiEnrichSchemaName)
    }
  }

  private def executeModule(moduleName: String, params: QueryParams, config: JsValue, runModuleVariables: ModuleVariables): Future[Unit] = {
    if (runModuleVariables.validatorModule == moduleName) {
      Future {
        getService(moduleName).flatMap {
          case loaderService: LoaderService => loaderService.execute(runModuleVariables.configName, runModuleVariables.configName, params, config, runModuleVariables.schemaName, configApiUrl = runModuleVariables.configApiUrl)
          case intraService: IntraService => intraService.executeDisambiguator(runModuleVariables.configName, runModuleVariables.configName, params, config, runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl,runModuleVariables.eiFragmentSchemaName)
          case interService: InterService => interService.executeDisambiguator(runModuleVariables.configName, runModuleVariables.configName, params, config, runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl,runModuleVariables.eiFragmentSchemaName)
          case relationshipService: RelationshipExtractionService => relationshipService.executeRelationship(runModuleVariables.configName, runModuleVariables.configName, params, config, runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl,eiFragmentSchemaName=runModuleVariables.eiFragmentSchemaName)
          case relationshipDisambiguationService: RelationshipDisambiguationService  => relationshipDisambiguationService.executeRelationship(runModuleVariables.configName, runModuleVariables.configName, params, config, runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl,eiFragmentSchemaName=runModuleVariables.eiFragmentSchemaName)
          case publisherService: PublisherService  => publisherService.executeWithPubSchema(runModuleVariables.configName, runModuleVariables.configName, params, config, runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl, publishSchemaName = runModuleVariables.publisherSchemaName)
          case entityRelEnrichService: EntityRelEnrichService  => entityRelEnrichService.executeWithPubSchema(runModuleVariables.configName, runModuleVariables.configName, params, config, runModuleVariables.schemaName,configApiUrl = runModuleVariables.configApiUrl, eiEnrichSchemaName = runModuleVariables.eiEnrichSchemaName)

        }
      }
    }
    else {
      Future.successful(())
    }
  }

  private def getService(moduleName: String): Future[ModuleServiceBase] = Future.successful {
    moduleName.toLowerCase match {
      case Modules.LOADER => loaderService
      case Modules.INTER => interService
      case Modules.INTRA => intraService
      case Modules.RELATION_EXTRACTOR => relationshipExtractorService
      case Modules.RELATION_DISAMBIGUATION => relationshipDisambiguationService
      case Modules.PUBLISHER => publisherService
      case Modules.ENTITY_ENRICHMENT => entityRelEnrichService
      case _ => throw new IllegalArgumentException(s"Unknown ExecutionModule: $moduleName")
    }
  }

  def storeResult(configName: String, status: String, executionFLow: List[String]= List(""), result: JsValue = Json.obj()) = {
    resultsService.storeResult(
      configName = configName, status, executionFLow, result
    )
  }

  def getProperty(instance: Any, propertyName: String): Option[Any] = {
    val mirror = runtimeMirror(instance.getClass.getClassLoader)
    val instanceMirror = mirror.reflect(instance)

    try {
      val symbol = instanceMirror.symbol.typeSignature.member(TermName(propertyName))
      val value = instanceMirror.reflectField(symbol.asTerm).get
      Some(value)
    } catch {
      case _: Exception => None // Handle the case where the property doesn't exist
    }
  }

  def validate(fields:Array[Property], group:String): Unit ={
    fields match {
      case properties =>
        properties.foreach { prop =>
          isValidSQL(prop.colExpr) match {
            case (false,error) =>
              throw InvalidSQLError(s"Invalid SQL expression in $group in colName: ${prop.colName} with error: s$error")
            case _ => // valid, continue
          }
        }
      case _ =>
    }
  }

  def isValidSQL(expression: String): (Boolean,String) = {
    try{
      expr(expression).expr
      (true,"valid")
    }catch {
      case ex: Exception => (false,ex.getMessage)
    }
  }

  def validateSQL(sqlCols: Seq[Property] = Array.empty[Property], groupName: String ="" ): Seq[String] = {
    sqlCols.collect {
      case col if !isValidSQL(col.colExpr)._1 => s"$groupName: ${col.colName} with invalid SQL expression ${isValidSQL(col.colExpr)._2}"
    }
  }
}