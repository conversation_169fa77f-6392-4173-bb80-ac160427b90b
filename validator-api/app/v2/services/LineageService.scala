package v2.services

import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.sdspecore.utils.{CommonUtils, ConfigUtils}
import ai.prevalent.sdspecore.utils.api.HTTPUtil.getAccessToken
import org.apache.spark.sql.DataFrame
import akka.actor.ActorSystem
import org.json4s.DefaultFormats
import play.api.{Configuration, Logger}

import scala.concurrent.Future
import javax.inject.Inject
import scala.concurrent.ExecutionContext
import play.api.libs.json.{JsArray, JsObject, JsString, JsValue, Json}
import play.api.libs.ws._
import scalaj.http.Http
import v1.utils.LiveContext
import v2.common.Modules

class LineageService @Inject()(AppConfig: Configuration, actorSystem: ActorSystem) (implicit ec: ExecutionContext = actorSystem.dispatchers.lookup("play.spark.dispatcher")){

  private val LOGGER = Logger(getClass)

  // Simulate fetching dependencies from an external API vendor
  def getDependencies(moduleName: String): Future[Seq[String]] = Future {

      val dependenciesMap = Map(
        Modules.LOADER -> Seq.empty[String],
        Modules.INTRA -> Seq(Modules.LOADER),
        Modules.INTER -> Seq(Modules.INTRA),
        Modules.RELATION_EXTRACTOR -> Seq(Modules.INTER),
        Modules.RELATION_DISAMBIGUATION -> Seq(Modules.RELATION_EXTRACTOR),
        Modules.ENTITY_ENRICHMENT -> Seq(Modules.RELATION_DISAMBIGUATION),
        Modules.PUBLISHER -> Seq(Modules.ENTITY_ENRICHMENT)
      )
      dependenciesMap.getOrElse(moduleName, Seq.empty[String])

  }(ec)
  def postConfiguration(apiUrl: String, data: JsValue, headers: Seq[(String, String)] = Seq.empty): JsValue = {
    val response = CommonUtils.retryWithBackoff() {
      Http(apiUrl)
        .timeout(connTimeoutMs = 10000, readTimeoutMs = 30000)
        .headers(headers)
        .postData(s"""${data}""")
        .asString.body

    }

    Json.parse(response)
  }


  def getSubModules(lineageData: Future[JsValue], moduleName: String): Future[Seq[String]] = {
    lineageData.map { lineage =>
      val data: Seq[String] = (lineage \ moduleName).asOpt[Seq[String]].getOrElse(Seq.empty[String])
      data
    }(ec)
  }


  def getLineageData(ConfigName: String, ModuleName: String): Future[JsValue] = {
        LOGGER.error(s"ModuleName::: ${ModuleName}")
        val lineageApiurl = sys.env.getOrElse("LINEAGE_API_URL", null)
        val jsonPayload = Json.obj(
          "name" -> ConfigName
        )

        val headers: Seq[(String, String)] = Seq(
          "Content-Type" -> "application/json",
          "Accept" -> "application/json",
          "Authorization" -> s"Bearer ${getAccessToken()}"
        )

        Future {
          postConfiguration(lineageApiurl, jsonPayload, headers)

        }(ec)

  }

  def getApiData(apiUrl: String, headers: Seq[(String, String)]): JsValue = {
    val response = CommonUtils.retryWithBackoff() {
      Http(apiUrl)
        .timeout(connTimeoutMs = 10000, readTimeoutMs = 30000)
        .headers(headers)
        .asString
        .body // The body of the response is returned as a String
    }
    LOGGER.info(s"Validator::: Calling URL $apiUrl")

    Json.parse(response)
  }

  def getConfig(configURL: String): JsValue = {
    val headers: Seq[(String, String)] = Seq(
      "Accept" -> "application/json",
      "Authorization" -> s"Bearer ${getAccessToken()}"
    )
    getApiData(configURL, headers)
  }

  def getConfigContext(): JsValue = {
    var contextURL = s"${sys.env.getOrElse("CONFIG_API_URL", null)}/context"
    try {
      contextURL = checkUpgradeStatus(contextURL)
    }
    catch {
      case ex:Exception =>
        LOGGER.error(ex.getMessage)
    }
    getConfig(contextURL)
  }
  def checkUpgradeStatus(url: String): String = {
    var new_url: String = url
    var upgradeStatusKey: String = (sys.env.getOrElse("UPGRADE_STATUS", "false"))
    val upgradeCheckURL = s"${sys.env.getOrElse("DOMAIN_NAME", null)}/${sys.env.getOrElse("UPGRADE_STATUS_API_ENDPOINT",null)}"
    val upgradeStatus: Option[String]= (getConfig(upgradeCheckURL) \ "is_upgrading").asOpt[String]
    if (upgradeStatusKey == "true" || upgradeStatus.getOrElse("false") == "true") {
      new_url = s"${url}?solution_edition=new"
    }
    new_url
  }

  

}