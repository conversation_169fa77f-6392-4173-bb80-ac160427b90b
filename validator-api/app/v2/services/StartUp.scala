package v2.services

import play.api.Configuration
import redis.clients.jedis.Jedis

import javax.inject._
import play.api.inject.ApplicationLifecycle
import redis.clients.jedis.params.ScanParams
import redis.clients.jedis.resps.ScanResult
import v1.utils.LiveContext
import v2.common. RedisBase
import v2.models.ExecutionModule

import scala.concurrent.Future

trait StartUp {
  def redisCleanUp(): Unit
  def sessionUp(): Unit
}

@Singleton
class StartUpImpl @Inject() (appLifecycle: ApplicationLifecycle , config:Configuration) extends  RedisBase(config) with StartUp {
  override def redisCleanUp(): Unit = {
    val jedis: Jedis = jedisPool.getResource
    LOGGER.info("Validator ::: Play Startup or Shutdown Initiated. Deleting redis keys with In-progress value")
    try {
      jedis.select(dbIndex)
      var cursor = "0"
      val scanParams = new ScanParams().count(100)
      do {
        val scanResult: ScanResult[String] = jedis.scan(cursor, scanParams)
        val keys = scanResult.getResult

        keys.forEach { key =>
          val value = jedis.get(key)
          if (value == ExecutionModule.IN_PROGRESS) {
            jedis.del(key)
            LOGGER.info(s"Validator ::: Deleted key: $key")
          }
        }

        cursor = scanResult.getCursor
      } while (cursor != "0")
    } catch {
      case e: Exception =>
        LOGGER.error(s"Error while deleting in progress while startup", e)
        throw e // Rethrow or handle accordingly
    } finally {
      jedis.close()
    }
  }

  override def sessionUp(): Unit ={
    LOGGER.info("Initializing Spark session")
    try {
      LiveContext.buildSessionFromEnvSpec()
      LOGGER.info("Validator::: Session Created")
    } catch {
      case ex: Exception =>
        LOGGER.error("Validator::: Error while creating spark session")
        LOGGER.error(ex.getMessage)
    }
  }

  appLifecycle.addStopHook { () =>
    redisCleanUp()
    Future.successful(())
  }

  redisCleanUp()

  Future {
    LOGGER.info("Validator ::: Starting Spark session after full startup")
    try {
      sessionUp()
    }
  }(scala.concurrent.ExecutionContext.global)
}