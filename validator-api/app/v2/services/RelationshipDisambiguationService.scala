package v2.services

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.relationship.disambiguation.{Disambiguation, DisambiguationUtils}
import ai.prevalent.entityinventory.relationship.disambiguation.config.{Config => RelDisamConfig}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UPDATED_AT_TS
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReaderFactory
import common.InvalidJsonError
import org.apache.spark.sql.functions.{col, days}
import org.json4s.Formats
import play.api.{Configuration, Logger}
import play.api.libs.json.{JsValue, Json}
import v2.common.{CacheUtil, Modules}
import v2.utils.{IcebergUtils, ResultSetUtils}
import scala.concurrent.Future
import javax.inject.Inject
import v2.models.{ExecutionModule, ModuleServiceBase, QueryParams}
import v2.utils.TimestampUtils.getTimeStampDetails
import v1.utils.LiveContext

class RelationshipDisambiguationService @Inject()( resultsService: ResultsService,
                                                  cacheUtils: CacheUtil,icebergUtils: IcebergUtils, resultSetUtils: ResultSetUtils,configuration:Configuration) extends ModuleServiceBase {

  implicit val formats: Formats = Disambiguation.configFormats
  private val LOGGER = Logger(getClass)

  def execute(configName: String, subModuleName: String, params: QueryParams, configJson: JsValue = null, schemaName: String = "ei_validator",configApiUrl:String = "/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/"): Future[Unit] = {
    Future.successful(())
  }

  def executeRelationship(configName: String, subModuleName: String, params: QueryParams, configJson:JsValue = null, schemaName: String="ei_validator"
              ,configApiUrl:String ="/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/",eiFragmentSchemaName:String=""): Future[Unit] = {

    LOGGER.info(s"Executing Relationship Disambiguation: $subModuleName")
    var configData: JsValue = null
    val relArgs = new EIJobArgs()

    try {
      val distinct = params.distinct
      val limit = params.limit

      configData = resultSetUtils.getConfig(configJson=configJson,configApiUrl=configApiUrl,subModuleName=subModuleName,schemaName = schemaName,Modules.RELATION_DISAMBIGUATION,eiFragmentSchema = eiFragmentSchemaName)
      val config:RelDisamConfig= resultSetUtils.getConfig(updatedJson = configData,manifest = manifest[RelDisamConfig],formats = formats)


      Disambiguation.spark = LiveContext.spark
      val timeStampDetails = getTimeStampDetails(LiveContext.spark)



      relArgs.currentUpdateDate = timeStampDetails.eventTimestampEndEpoch

      val cachedStatusOpt = cacheUtils.getCache(Json.toJson(configData),subModuleName, relArgs.currentUpdateDate)

      cachedStatusOpt match {
        case Some(cachedStatus) =>
          cachedStatus match {
            case ExecutionModule.COMPLETED =>
              LOGGER.info(s"Validator::: Inside Completed $subModuleName")
              if (configJson != null) {
                val resultSet = icebergUtils.reader(LiveContext.spark, config.output.disambiguatedModelLocation, relArgs.currentUpdateDate)

                resultSetUtils.processResultSet(
                  resultSet,
                  distinct,
                  limit,
                  configJson,
                  configName,
                  subModuleName,
                  Modules.RELATION_DISAMBIGUATION
                )
              }
              else{
                resultsService.storeResult(
                  configName = configName,
                  executionFlow = List(s"${Modules.RELATION_DISAMBIGUATION}: $subModuleName Completed (loaded from cache)")
                )
              }
              Future.successful(println("Executing logic for COMPLETED"))

            case ExecutionModule.IN_PROGRESS =>
              LOGGER.info(s"Validator::: Inside In-progress $subModuleName")
              resultsService.storeResult(
                configName = configName,
                executionFlow = List(s"${Modules.RELATION_DISAMBIGUATION}: $subModuleName in-progressing")
              )
              Future.successful(println("Executing logic for IN_PROGRESS"))
          }

        case None =>
          LOGGER.info(s"Validator::: Inside Start $subModuleName")
          val reader = SDSTableReaderFactory.getDefault(LiveContext.spark)

          val outputDF = DisambiguationUtils.build(config, relArgs, reader, LiveContext.spark)
          icebergUtils.writer(LiveContext.spark, outputDF.resolvedDF,config.output.disambiguatedModelLocation)
          if(config.output.resolverLocation.isDefined)
            icebergUtils.writer(LiveContext.spark, outputDF.resolverDF,config.output.resolverLocation.get, Array(days(col(UPDATED_AT_TS)), col("relationship_name"),col("data_source_name")), purgeTable = false ,tabSchema = configuration.get[String]("eiFragmentSchema"))

          resultSetUtils.processResultSet(
            outputDF.resolvedDF,
            distinct,
            limit,
            configJson,
            configName,
            subModuleName,
            Modules.RELATION_DISAMBIGUATION
          )
          cacheUtils.setCache(Json.toJson(configData),subModuleName, relArgs.currentUpdateDate, ExecutionModule.COMPLETED)
          Future.successful(println("None"))
      }
    }
    catch {
      case ex: Exception =>
        LOGGER.error("Exception while processing request", ex)
        resultsService.storeResult(
          configName = configName,
          status = ExecutionModule.ERROR,
          executionFlow = List(s"Error while execution of Relationship disambiguation : $subModuleName, ${ex.getMessage}")
        )
        cacheUtils.deleteCache(Json.toJson(configData),subModuleName, relArgs.currentUpdateDate)
        throw InvalidJsonError(ex.getMessage)
    }

  }
}