package v2.services

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UPDATED_AT_TS
import ai.prevalent.entityinventory.relationship.entityenrich.EntityEnrich
import ai.prevalent.entityinventory.relationship.entityenrich.configs.{ Config => EntityEnrichConfig}
import ai.prevalent.entityinventory.relationship.entityenrich.EntityEnrich.modelWithDerivedProperties
import ai.prevalent.entityinventory.relationship.entityenrich.EntityEnrichUtil
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReaderFactory
import common.InvalidJsonError
import org.apache.spark.sql.functions.{col, days}
import org.json4s.Formats
import v1.utils.LiveContext
import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>}
import play.api.{Configuration, Logger}
import v2.common.{CacheUtil, Modules}
import v2.models.{ExecutionModule, ModuleServiceBase, QueryParams}
import v2.utils.TimestampUtils.getTimeStampDetails
import v2.utils.{IcebergUtils, ResultSetUtils}

import javax.inject.Inject
import scala.concurrent.Future

class EntityRelEnrichService @Inject()( resultsService: ResultsService,
                                       cacheUtils: CacheUtil, icebergUtils: IcebergUtils,resultSetUtils: ResultSetUtils, configuration:Configuration) extends ModuleServiceBase {

  private val LOGGER = Logger(getClass)
  implicit val formats: Formats = EntityEnrich.configFormats

  def execute(configName: String, subModuleName: String, params: QueryParams, configJson: JsValue = null, schemaName: String = "ei_validator",configApiUrl:String = "/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/"): Future[Unit] = {
    Future.successful(())
  }

  def executeWithPubSchema(configName: String, subModuleName: String, params: QueryParams, configJson: JsValue = null, schemaName: String = "ei_validator"
                           ,configApiUrl:String ="/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/", eiEnrichSchemaName: String=""): Future[Unit] = {

    LOGGER.info(s"Validator::: Executing Entity rel enrich submodule: $subModuleName")
    val entityArgs = new EIJobArgs()
    var configData: JsValue = null

    try {
      LiveContext.spark.sparkContext.setCheckpointDir(LiveContext.spark.conf.get("spark.checkpoint.dir"))
      val distinct = params.distinct
      val limit = params.limit

      val reader = SDSTableReaderFactory.getDefault(LiveContext.spark)

      configData = resultSetUtils.getConfig(configJson=configJson,configApiUrl=configApiUrl,subModuleName=subModuleName,schemaName = schemaName,Modules.ENTITY_ENRICHMENT,eiEnrichSchemaName = eiEnrichSchemaName)
      val config:EntityEnrichConfig= resultSetUtils.getConfig(updatedJson = configData,manifest = manifest[EntityEnrichConfig],formats = formats)


      EntityEnrich.spark = LiveContext.spark
      val timeStampDetails = getTimeStampDetails(LiveContext.spark)


      entityArgs.currentUpdateDate = timeStampDetails.eventTimestampEndEpoch

      val cachedStatusOpt = cacheUtils.getCache(Json.toJson(configData), subModuleName, entityArgs.currentUpdateDate)

      cachedStatusOpt match {
        case Some(cachedStatus) =>
          cachedStatus match {
            case ExecutionModule.COMPLETED =>
              LOGGER.info(s"Validator::: Inside Completed $subModuleName")
              if (configJson != null) {
                val resultSet = icebergUtils.reader(LiveContext.spark, config.output.outputTableName, entityArgs.currentUpdateDate, configuration.get[String]("validatorSchema"))

                resultSetUtils.processResultSet(
                  resultSet,
                  distinct,
                  limit,
                  configJson,
                  configName,
                  subModuleName,
                  Modules.ENTITY_ENRICHMENT
                )
              }
              else{
                resultsService.storeResult(
                  configName = configName,
                  executionFlow = List(s"${Modules.ENTITY_ENRICHMENT}: $subModuleName Completed (loaded from cache)")
                )
              }

              Future.successful(println("Executing logic for COMPLETED"))

            case ExecutionModule.IN_PROGRESS =>
              LOGGER.info(s"Validator::: Inside In-progress $subModuleName")
              resultsService.storeResult(
                configName = configName,
                executionFlow = List(s"${Modules.ENTITY_ENRICHMENT}: $subModuleName in-progressing")
              )
              Future.successful(println("Executing logic for IN_PROGRESS"))
          }

        case None =>
          LOGGER.info(s"Validator::: Inside Start $subModuleName")

          cacheUtils.setCache(Json.toJson(configData), subModuleName, entityArgs.currentUpdateDate, ExecutionModule.IN_PROGRESS)
          val reader = SDSTableReaderFactory.getDefault(LiveContext.spark)
          val entityDF = reader.read(config.entityTableName).filter(s"$UPDATED_AT_TS = to_timestamp(${entityArgs.currentUpdateDate}/1000)")
          val enrichedEntity = EntityEnrichUtil.build(config, entityDF, entityArgs)
          val outputDf = modelWithDerivedProperties(enrichedEntity, config)
          val nullRemovedOutputDf = removeNullFields(outputDf)
          icebergUtils.writer(
            LiveContext.spark, nullRemovedOutputDf, config.output.outputTableName,
            Array(days(col(SDSProperties.schema.UPDATED_AT_TS))) ,
            configuration.get[String]("validatorSchema")
          )

          resultSetUtils.processResultSet(
            nullRemovedOutputDf,
            distinct,
            limit,
            configJson,
            configName,
            subModuleName,
            Modules.ENTITY_ENRICHMENT
          )

          cacheUtils.setCache(Json.toJson(configData), subModuleName, entityArgs.currentUpdateDate, ExecutionModule.COMPLETED)
          Future.successful(println("None"))
      }
    }
    catch {
      case ex: Exception =>
        LOGGER.error("Exception while processing request", ex)
        resultsService.storeResult(
          configName = configName,
          status = ExecutionModule.ERROR,
          executionFlow = List(s"Error while execution of Publisher disambiguation : $subModuleName, ${ex.getMessage}")
        )
        cacheUtils.deleteCache(Json.toJson(configData), subModuleName, entityArgs.currentUpdateDate)

        Future.successful(())

        throw InvalidJsonError(ex.getMessage)
    }

  }
}