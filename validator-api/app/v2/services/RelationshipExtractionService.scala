package v2.services

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.relationship.extractor.Extractor
import v1.utils.LiveContext
import ai.prevalent.entityinventory.relationship.extractor.configs.specs.{DisambiguationResolvers, Config => RelExractorConfig}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{KG_CONTENT_TYPE, UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.relationship.extractor.Extractor.readInputSources
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.entityinventory.utils.{EIUtil, SparkUtil}
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReaderFactory
import common.InvalidJsonError
import org.apache.spark.sql.functions.{col, days, expr, lit}
import org.apache.spark.sql.types.{DataTypes, StringType}
import org.json4s.Formats
import play.api.Logger
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>son}
import v2.common.{CacheUtil, Modules}
import v2.utils.{IcebergUtils, ResultSetUtils}
import scala.concurrent.Future
import javax.inject.Inject
import v2.models.{ExecutionModule, ModuleServiceBase, QueryParams}
import v2.utils.LoaderControllerUtils.checkTableExist
import v2.utils.LoaderControllerUtils.getTimeStampDetails


class RelationshipExtractionService @Inject()( resultsService: ResultsService,
                                              cacheUtils: CacheUtil, icebergUtils: IcebergUtils, resultSetUtils: ResultSetUtils) extends ModuleServiceBase {

  implicit val formats: Formats = Extractor.configFormats
  private val LOGGER = Logger(getClass)

  def execute(configName: String, subModuleName: String, params: QueryParams, configJson: JsValue = null, schemaName: String = "ei_validator",configApiUrl:String = "/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/"): Future[Unit] = {
    Future.successful(())
  }

  def executeRelationship(configName: String, subModuleName: String, params: QueryParams, configJson:JsValue = null, schemaName: String="ei_validator"
              ,configApiUrl:String ="/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/",eiFragmentSchemaName:String=""): Future[Unit] = {

    LOGGER.info(s"Executing Relationship extractor submodule: $subModuleName")
    var configData: JsValue = null
    val relationExtArgs = new EIJobArgs()

    try {
      val distinct = params.distinct
      val limit = params.limit
      val reader = SDSTableReaderFactory.getDefault(LiveContext.spark)

      configData = resultSetUtils.getConfig(configJson=configJson,configApiUrl=configApiUrl,subModuleName=subModuleName,schemaName = schemaName,Modules.RELATION_EXTRACTOR,eiFragmentSchema = eiFragmentSchemaName)
      val config:RelExractorConfig= resultSetUtils.getConfig(updatedJson = configData,manifest = manifest[RelExractorConfig],formats = formats)

      Extractor.spark = LiveContext.spark


      config.inputSourceInfo.foreach(input =>{
        val tableExists: Boolean = scala.util.Try(checkTableExist(input.sdmPath, LiveContext.spark)).getOrElse(false)

        if (tableExists){
          val timeStampDetails = getTimeStampDetails(input.sdmPath, LiveContext.spark)

          if (timeStampDetails._2) {
            relationExtArgs.parsedIntervalEndEpoch = timeStampDetails._1.parsedIntervalEndEpoch
            relationExtArgs.parsedIntervalStartEpoch = timeStampDetails._1.parsedIntervalStartEpoch
            relationExtArgs.prevUpdateDate = timeStampDetails._1.previousEndEpoch
            relationExtArgs.currentUpdateDate = timeStampDetails._1.eventTimestampEndEpoch
          }
        }
      })

      val cachedStatusOpt = cacheUtils.getCache(Json.toJson(configData), subModuleName, relationExtArgs.currentUpdateDate)

      cachedStatusOpt match {
        case Some(cachedStatus) =>
          cachedStatus match {
            case ExecutionModule.COMPLETED =>
              LOGGER.info(s"Validator::: Inside Completed $subModuleName")

              if (configJson != null) {
                val resultSet = icebergUtils.reader(LiveContext.spark, config.output.outputTable, relationExtArgs.currentUpdateDate)

                resultSetUtils.processResultSet(
                  resultSet,
                  distinct,
                  limit,
                  configJson,
                  configName,
                  subModuleName,
                  Modules.RELATION_EXTRACTOR
                )
              }
              else{
                resultsService.storeResult(
                  configName = configName,
                  executionFlow = List(s"${Modules.RELATION_EXTRACTOR}: $subModuleName Completed (loaded from cache)")
                )
              }

              Future.successful(println("Executing logic for COMPLETED"))

            case ExecutionModule.IN_PROGRESS =>
              LOGGER.info(s"Validator::: Inside In-progress $subModuleName")
              resultsService.storeResult(
                configName = configName,
                executionFlow = List(s"${Modules.RELATION_EXTRACTOR}: $subModuleName in-progressing")
              )
              Future.successful(println("Executing logic for IN_PROGRESS"))
          }

        case None =>
          LOGGER.info(s"Validator::: Inside Start $subModuleName")

          var prevMinSDMFull = reader.readOrElse(config.output.prevMiniSDM,LiveContext.spark.emptyDataFrame
            .withColumn(UPDATED_AT_TS,lit(null).cast(DataTypes.TimestampType))
            .withColumn(KG_CONTENT_TYPE, lit(null).cast(StringType))
            .withColumn("kg_config",lit(null).cast(StringType))
          )

          prevMinSDMFull = if(prevMinSDMFull.columns.contains(KG_CONTENT_TYPE))
            prevMinSDMFull
          else
            prevMinSDMFull.withColumn(KG_CONTENT_TYPE, lit("data")).withColumn("kg_config",lit(null).cast(StringType))

          val prevMinSDM = EIUtil.readPrevDF(args = relationExtArgs, eiDF = prevMinSDMFull)
            .withColumn(UPDATED_AT,expr(s"UNIX_MILLIS($UPDATED_AT_TS)"))
          LOGGER.info(s"Previous MiniSDM columns: ${prevMinSDM.columns.mkString(", ")}")

          val prevConfig: Option[String] = EIUtil.getPrevConfig(prevMinSDM, relationExtArgs)
          LOGGER.info(s"Relationship Prev Config: $prevConfig")


          val (inputSources,prevMiniSDMDf) = readInputSources(config.inputSourceInfo,prevMinSDM,reader, relationExtArgs)

          val resolvers = {
            val latestFilter = expr(s"$UPDATED_AT_TS = to_timestamp(${relationExtArgs.currentUpdateDate}/1000)")
            val intraDF = EIUtil.safeReadResolver(config.intraSourcePath, latestFilter, reader)
            val interSourceDF = EIUtil.safeReadResolver(config.interSourcePath, latestFilter, reader)
            val interTargetDF = EIUtil.safeReadResolver(config.interTargetPath, latestFilter, reader)
            val interDF = SparkUtil.unionByName(interSourceDF, interTargetDF)
            DisambiguationResolvers(intraDF, interDF)
          }

          val relationInfo = config.execute(inputSources, prevMiniSDMDf.filter(s"$KG_CONTENT_TYPE='data' OR $KG_CONTENT_TYPE IS NULL"), config ,resolvers, relationExtArgs, prevConfig)

          val outputDF = relationInfo.relationDF
            .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
          val nullRemovedOutDf  = removeNullFields(outputDF)
          icebergUtils.writer(LiveContext.spark,nullRemovedOutDf,config.output.outputTable, Array(days(col(UPDATED_AT_TS))) ,restrictOutput = true,output_table_limit = params.output_table_limit)

          val miniSDMDF = relationInfo.miniSDM
            .withColumn(UPDATED_AT, lit(relationExtArgs.currentUpdateDate))
            .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
          val nullRemovedMiniSDMDf  = removeNullFields(miniSDMDF)
//          icebergUtils.writer(LiveContext.spark,nullRemovedMiniSDMDf,config.output.prevMiniSDM, Array(days(col(UPDATED_AT_TS))) , restrictOutput = true , output_table_limit = params.output_table_limit)


          resultSetUtils.processResultSet(
            outputDF,
            distinct,
            limit,
            configJson,
            configName,
            subModuleName,
            Modules.RELATION_EXTRACTOR
          )

          cacheUtils.setCache(Json.toJson(configData),subModuleName, relationExtArgs.currentUpdateDate, ExecutionModule.COMPLETED)
          Future.successful(println("None"))
      }
    }
    catch {
      case ex: Exception =>
        LOGGER.error("Exception while processing request", ex)
        resultsService.storeResult(
          configName = configName,
          status = ExecutionModule.ERROR,
          executionFlow = List(s"Error while execution of Relationship extractor : $subModuleName, ${ex.getMessage}")
        )
        cacheUtils.deleteCache(Json.toJson(configData), subModuleName, relationExtArgs.currentUpdateDate)
        throw InvalidJsonError(ex.getMessage)
    }

  }
}
