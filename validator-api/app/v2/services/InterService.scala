package v2.services

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.disambiguator.Disambiguator
import ai.prevalent.entityinventory.disambiguator.configs.specs.{Config => DisambiguatorConfig}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UPDATED_AT_TS
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReaderFactory
import common.InvalidJsonError
import v1.utils.LiveContext
import org.apache.spark.sql.functions.{col, days}
import org.json4s.Formats
import play.api.{Configuration, Logger}
import play.api.libs.json.{JsValue, Json}
import v2.common.{CacheUtil, Modules, ResolutionLevel}
import scala.concurrent.Future
import javax.inject.Inject
import v2.models.{ExecutionModule, ModuleServiceBase, QueryParams}
import v2.utils.{IcebergUtils, ResultSetUtils}
import v2.utils.TimestampUtils.getTimeStampDetails



class InterService @Inject()(resultsService: ResultsService,
                             cacheUtils: CacheUtil,icebergUtils: IcebergUtils, resultSetUtils: ResultSetUtils ,configuration:Configuration) extends ModuleServiceBase {

  implicit val formats: Formats = Disambiguator.configFormats
  private val LOGGER = Logger(getClass)

  def execute(configName: String, subModuleName: String, params: QueryParams, configJson: JsValue = null, schemaName: String = "ei_validator",configApiUrl:String = "/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/"): Future[Unit] = {
    Future.successful(())
  }

  def executeDisambiguator(configName: String, subModuleName: String, params: QueryParams, configJson:JsValue = null, schemaName: String="ei_validator"
              ,configApiUrl:String ="/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/",eiFragmentSchemaName:String=""): Future[Unit] = {

    LOGGER.info(s"Executing Inter submodule: $subModuleName")
    val disambiguationArgs = new EIJobArgs()
    var configData: JsValue = null
    val disambiguation_resolution_level = params.disambiguation_resolution_level.getOrElse(Seq(ResolutionLevel.ENTITY_RESOLUTION)).head

    try {
      LiveContext.spark.sparkContext.setCheckpointDir(LiveContext.spark.conf.get("spark.checkpoint.dir"))
      val distinct = params.distinct
      val limit = params.limit

      val reader = SDSTableReaderFactory.getDefault(LiveContext.spark)

      configData = resultSetUtils.getConfig(configJson=configJson,configApiUrl=configApiUrl,subModuleName=subModuleName,schemaName = schemaName,Modules.INTER,eiFragmentSchema = eiFragmentSchemaName)
      configData = resultSetUtils.updateDisambiguationConfig(configData,disambiguation_resolution_level)
      LOGGER.error(s"Validator API : Config after __ck ${configData.toString()}")
      val config:DisambiguatorConfig= resultSetUtils.getConfig(updatedJson = configData,manifest = manifest[DisambiguatorConfig],formats = formats)
      Disambiguator.spark = LiveContext.spark

      val timeStampDetails = getTimeStampDetails(LiveContext.spark)


      disambiguationArgs.currentUpdateDate = timeStampDetails.eventTimestampEndEpoch
      disambiguationArgs.prevUpdateDate = timeStampDetails.previousEndEpoch


      val cachedStatusOpt = cacheUtils.getCache(Json.toJson(configData),subModuleName, disambiguationArgs.currentUpdateDate,resolutionLevel = disambiguation_resolution_level)

      cachedStatusOpt match {
        case Some(cachedStatus) =>
          cachedStatus match {
            case ExecutionModule.COMPLETED =>
              LOGGER.info(s"Validator::: Inside Completed $subModuleName")

              if (configJson != null) {
                val resultSet = icebergUtils.reader(LiveContext.spark, config.output.disambiguatedModelLocation, disambiguationArgs.currentUpdateDate, disambiguation_level = disambiguation_resolution_level)

                resultSetUtils.processResultSet(
                  resultSet,
                  distinct,
                  limit,
                  configJson,
                  configName,
                  subModuleName,
                  Modules.INTER
                )
              }
              else{
                resultsService.storeResult(
                  configName = configName,
                  executionFlow = List(s"${Modules.INTER}: $subModuleName Completed (loaded from cache)")
                )
              }

              Future.successful(println("Executing logic for COMPLETED"))

            case ExecutionModule.IN_PROGRESS =>
              LOGGER.info(s"Validator::: Inside In-progress $subModuleName")
              resultsService.storeResult(
                configName = configName,
                executionFlow = List(s"${Modules.INTER}: $subModuleName in-progressing")
              )
              Future.successful(println("Executing logic for IN_PROGRESS"))
          }

        case None =>
          LOGGER.info(s"Validator::: Inside Start $subModuleName")
          cacheUtils.setCache(Json.toJson(configData), subModuleName, disambiguationArgs.currentUpdateDate, ExecutionModule.IN_PROGRESS ,resolutionLevel = disambiguation_resolution_level)

          val resultSet = resultSetUtils.disambiguatorCalculationUtil(disambiguation_resolution_level, config, disambiguationArgs, reader)

          icebergUtils.writer(LiveContext.spark,resultSet.interSourceDisambiguatedInventoryModel, config.output.disambiguatedModelLocation, disambiguation_level = disambiguation_resolution_level)
          if(resultSet.interSourceResolver.isDefined)
            icebergUtils.writer(LiveContext.spark,resultSet.interSourceResolver.get, config.output.resolverLocation,Array(days(col(UPDATED_AT_TS)), col("class"),col("data_source_name")), purgeTable = false,tabSchema = configuration.get[String]("eiFragmentSchema"), disambiguation_level = disambiguation_resolution_level)
          if(resultSet.nonResolvedDF.isDefined)
            icebergUtils.writer(LiveContext.spark,resultSet.nonResolvedDF.get, config.output.fragmentLocation.getOrElse(""),tabSchema = configuration.get[String]("eiFragmentSchema"), disambiguation_level = disambiguation_resolution_level)
          if (config.output.resolverGraphLocation.isDefined){
            icebergUtils.writer(LiveContext.spark,resultSet.resolutionGraph.get, config.output.resolverGraphLocation.get,tabSchema = configuration.get[String]("eiFragmentSchema"), partitionCols =  Array.empty, disambiguation_level = disambiguation_resolution_level)
          }

          resultSetUtils.processResultSet(
            resultSet.interSourceDisambiguatedInventoryModel,
            distinct,
            limit,
            configJson,
            configName,
            subModuleName,
            Modules.INTER
          )

          cacheUtils.setCache(Json.toJson(configData), subModuleName, disambiguationArgs.currentUpdateDate, ExecutionModule.COMPLETED,resolutionLevel = disambiguation_resolution_level)

          Future.successful(println("None"))

        //            Future.successful(Ok(Json.toJson(ExecuteExpressionResponse(output = JsArray(output), headers = Json.toJson(headers)))))
      }
    }
    catch {
      case ex: Exception =>
        LOGGER.error("Exception while processing request", ex)
        resultsService.storeResult(
          configName = configName,
          status = ExecutionModule.ERROR,
          executionFlow = List(s"Error while execution of Inter disambiguation : $subModuleName, ${ex.getMessage}")
        )
        cacheUtils.deleteCache(Json.toJson(configData),subModuleName, disambiguationArgs.currentUpdateDate,resolutionLevel = disambiguation_resolution_level)
        Future.successful(())
        throw InvalidJsonError(ex.getMessage)
    }

  }
}