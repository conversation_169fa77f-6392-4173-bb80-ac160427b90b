package v2.models

object Pattern {

  val stringWithoutSpace="^[a-zA-Z0-9_]+$|^$"
  val stringWithDotWithoutSpace = "^[a-zA-Z0-9_.]+$|^$"
  val stringWithAlias="^(?:(?!(?:\\s+FROM\\s+|\\s*INSERT INTO\\s+|\\s*DELETE\\s+|\\s*DROP\\s+|\\s*ALTER\\s+|TRUNCATE+|\\s*UPDATE\\s+)).)*$"
  val stringWithFrom = """.*\b(EXTRACT|TRIM|POSITION)\s*\([^)]*(?:\s+FROM\s+[^(]+)+\)\s*(?![^()]*\s*FROM).*"""
  val joinTypePattern="\\s*?(INNER JOIN|RIGHT JOIN|LEFT JOIN|FULL JOIN|JOIN)\\s*"

}
