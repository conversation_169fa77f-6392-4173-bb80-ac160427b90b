package config.delta.controllers.gen

import ai.prevalent.entityinventory.delta.LoaderConfigDelta
import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.entityinventory.loader.configs.specs.Config
import config.delta.service.{CMService, DeltaGenerateService, ManualDeltaService}
import org.json4s.Formats
import play.api.mvc.ControllerComponents

import javax.inject.Inject
import scala.concurrent.ExecutionContext

class LoaderDeltaGenController @Inject()(cc: ControllerComponents, manualDeltaService:ManualDeltaService,deltaService: DeltaGenerateService[Config, LoaderConfigDelta],
                                         deltaPublishService: CMService)(implicit ec: ExecutionContext)
  extends BaseDeltaGenController[Config, LoaderConfigDelta](cc,manualDeltaService,deltaService, deltaPublishService) {

  def configItemType = "inventory_models"
  override def formats: Formats = Loader.configFormats
}
