package config.delta.controllers.gen

import ai.prevalent.entityinventory.delta.{GlobalEntityConfigDelta, LoaderConfigDelta}
import ai.prevalent.entityinventory.common.configs.GlobalEntityConfig
import ai.prevalent.entityinventory.loader.Loader
import config.delta.service.{CMService, DeltaGenerateService, ManualDeltaService}
import org.json4s.Formats
import play.api.mvc.ControllerComponents

import javax.inject.Inject
import scala.concurrent.ExecutionContext

class GlobalEntityDeltaGenController @Inject()(cc: ControllerComponents, manualDeltaService:ManualDeltaService,deltaService: DeltaGenerateService[GlobalEntityConfig, GlobalEntityConfigDelta],
                                               deltaPublishService: CMService)(implicit ec: ExecutionContext)
  extends BaseDeltaGenController[GlobalEntityConfig, GlobalEntityConfigDelta](cc,manualDeltaService,deltaService, deltaPublishService) {

  def configItemType = "global_entity_config"
  override def formats: Formats = Loader.configFormats

}
