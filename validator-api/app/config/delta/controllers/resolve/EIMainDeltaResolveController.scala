package config.delta.controllers.resolve

import ai.prevalent.entityinventory.delta.LoaderConfigDelta
import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.sdspecore.jobbase.LoggerBase
import config.delta.service.{AllDeltaGenControllers, CMService, DeltaGenerateService, DeltaResolveService}
import config.model.UpgradeStatus
import play.api.libs.json.Json
import play.api.mvc.ControllerComponents
import v2.utils.LiveContext

import javax.inject.Inject
import scala.concurrent.{ExecutionContext, Future}

class EIMainDeltaResolveController  @Inject()(cc: ControllerComponents,  deltaResolveService: DeltaResolveService,
                                              cmPublishService: CMService)(implicit ec: ExecutionContext)
  extends BaseDeltaResolveController(cc, deltaResolveService,cmPublishService) with LoggerBase{

  def configItemType = "inventory_models"

  override def resolveAutoDelta() = Action.async { implicit request =>
    try {
      LiveContext.buildSessionFromEnvSpec()
    } catch {
      case ex: Exception =>
        LOGGER.error(ex.getMessage)
        cmPublishService.autoDeltaFailed(Some("Unable to build Spark Session"),Some(ex))
        Future.failed(ex)
    }

    Future {
      try{
        resolve()
        cmPublishService.autoDeltaSuccess()
      }catch {
        case ex: Exception => cmPublishService.autoDeltaFailed(Some("Auto Delta Resolve failed due to an internal error"),Some(ex))
      }


    }
    Future.successful(Ok(Json.obj("status" -> "In Progress")))
  }

//  override def resolve() = {
//    LOGGER.info(s"Resolving $configItemType Auto Delta")
//    println(s"Resolving $configItemType Auto Delta")
//  }
}
