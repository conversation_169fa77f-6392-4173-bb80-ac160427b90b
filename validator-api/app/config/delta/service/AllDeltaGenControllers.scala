package config.delta.service

import config.delta.controllers.gen.{EntityDictionaryDeltaGenController, EntityEnrichDeltaGenController, GlobalEntityDeltaGenController, InterDeltaGenController, IntraDeltaGenController, LoaderDeltaGenController, PublisherDeltaGenController, RelationDeltaGenController, RelationResolutionDeltaGenController, RelationshipDictionaryDeltaGenController}

import javax.inject.Inject

class AllDeltaGenControllers @Inject()(_loader: LoaderDeltaGenController, _relation: RelationDeltaGenController,
                                       _globalController:GlobalEntityDeltaGenController, _relDict:RelationshipDictionaryDeltaGenController,
                                       _entityDict:EntityDictionaryDeltaGenController,
                                       _intra:IntraDeltaGenController,
                                       _inter:InterDeltaGenController,
                                       _relResol:RelationResolutionDeltaGenController,
                                       _publish:PublisherDeltaGenController,
                                       _entiyRelEnrich:EntityEnrichDeltaGenController) extends{
  def loader: LoaderDeltaGenController = _loader
  def relation: RelationDeltaGenController = _relation
  def globalEntity: GlobalEntityDeltaGenController = _globalController
  def relationDictionary: RelationshipDictionaryDeltaGenController = _relDict
  def entityDictionary:EntityDictionaryDeltaGenController = _entityDict
  def intra:IntraDeltaGenController = _intra
  def inter:InterDeltaGenController = _inter
  def relResol:RelationResolutionDeltaGenController = _relResol
  def publish:PublisherDeltaGenController = _publish
  def entityRelEnrich:EntityEnrichDeltaGenController = _entiyRelEnrich
}