package config.model

import ai.prevalent.entityinventory.delta.Change
import play.api.libs.json.{Json, OFormat}

case class CMDelta(`type`:String, config_item_type:String, config_item_name: String, name:String, level:String,
                   category:String, version:String, solution:String, resolved_status:Boolean, reason:String,
                   delta_config:Map[String, String])

object CMDelta {

  implicit val format: OFormat[CMDelta] = Json.format[CMDelta]

  def apply( change:Change, configItemType:String, configItemName: String,
            level:String, version:String, solution:String): CMDelta = {
    new CMDelta( `type` = change.changeType, config_item_type = configItemType, config_item_name = configItemName,
      name = change.name, level = level, category = change.category, version = version, solution = solution,
      resolved_status = false, reason = change.reason.getOrElse(""), delta_config = change.message)
  }
}

case class CMOutDelta(id:Int,`type`:String, delta_config: Array[AddInfoCM]=Array.empty)

case class AddInfoCM(field:String, value:String)