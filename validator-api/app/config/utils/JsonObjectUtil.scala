package config.utils

import play.api.libs.json.{JsObject}

object JsonObjectUtil {

  def mergeJsonObjects(json1: JsObject, json2: JsObject): JsObject = {
    // Merge the two JSON objects
    json1.fields.foldLeft(json2) {
      case (acc, (key, value)) =>
        acc.value.get(key) match {
          case Some(existingValue: JsObject) if value.isInstanceOf[JsObject] =>
            // If both values are JsObjects, merge them recursively
            acc + (key -> mergeJsonObjects(existingValue.as[JsObject], value.as[JsObject]))
          case _ =>
            // Otherwise, take the value from json2 (higher precedence)
            acc + (key -> value)
        }
    }
  }
}
