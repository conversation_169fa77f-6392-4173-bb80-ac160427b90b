package common

import akka.actor.ActorSystem
import play.api.mvc._
import play.api.libs.json._

import scala.concurrent.{ExecutionContext, Future}





class HealthCheckController @javax.inject.Inject()(
                                                    cc: ControllerComponents,actorSystem: ActorSystem)
                                                   extends AbstractController(cc) {
  implicit val ec: ExecutionContext = actorSystem.dispatchers.lookup("health-check-dispatcher")

    def healthCheck: Action[AnyContent] = Action.async {
      request =>
        Future{
          Ok(Json.obj(
            "message" -> "Config Validator API Running",
            "dispatcher" -> s"${Thread.currentThread.getName}"
          ))
        }(ec)


    }


}