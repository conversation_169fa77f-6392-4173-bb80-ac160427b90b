package common

import org.apache.spark.sql.SparkSession

object SDSConf {
  def spark = SparkSession.active
  def CONFIG_ARTIFACTORY_URI = spark.conf.get("spark.sds.config.artifactory.uri")
  def SPARK_JOB_CONFIG_BASE_PATH = spark.conf.get("spark.sds.config.spark-job.base-path")
  def CONFIG_ITEM_BASE_PATH = spark.conf.get("spark.sds.config.item.base-path")
  def DELTA_BASE_PATH = spark.conf.get("spark.sds.config.delta.base-path")
  def UPGRADE_STATUS_BASE_PATH = spark.conf.get("spark.sds.config.upgrade-status.base-path")

  def configIteamMetaList = CONFIG_ARTIFACTORY_URI+"/sds_mgmnt/config-manager/api/v1/config-item/list-configs-meta/"
  def configItemAPIPath() = CONFIG_ARTIFACTORY_URI+CONFIG_ITEM_BASE_PATH
  def deltaAPIPath() = CONFIG_ARTIFACTORY_URI+DELTA_BASE_PATH
  def upgradeStatusEndPoint = CONFIG_ARTIFACTORY_URI+UPGRADE_STATUS_BASE_PATH
}
