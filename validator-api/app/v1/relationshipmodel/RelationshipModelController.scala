package v1.relationshipmodel

import ai.prevalent.entityinventory.relationship.extractor.configs.specs.Config
import common.{InvalidJsonError, InvalidSQLError}
import org.apache.spark.sql.functions.expr
import org.json4s._
import play.api.libs.json.{JsValue, Json}
import play.api.mvc._
import v1.common.{ExpressionRequest, ExpressionResponse}

import scala.concurrent.Future


class Controller @javax.inject.Inject()(cc: ControllerComponents) extends AbstractController(cc) {
  implicit val formats: Formats = DefaultFormats
  def validateSchema: Action[JsValue] = Action(parse.json) { request =>

    try {
      val body = request.body.toString()
      val config = org.json4s.jackson.JsonMethods.parse(body).extract[Config]
      Ok(Json.obj(
        "message" -> "Valid JSON Object",
        "output" -> org.json4s.jackson.Serialization.write(config)
      ))
    } catch {
      case ex: Exception =>

        BadRequest(Json.obj(
          "message" -> "Invalid JSON Schema",
          "error" -> ex.getMessage
        ))
    }

}


  def validateExpression: Action[ExpressionRequest] = Action(parse.json[ExpressionRequest]  ).async { implicit request =>

    try {
      val body = request.body.expression
      val expression = expr(body).expr
      expression.childrenResolved
      Future.successful(Ok(Json.toJson(ExpressionResponse(output = body))))

    } catch {
      case ex: Exception =>
        throw InvalidSQLError(ex.getMessage)
    }

  }



}