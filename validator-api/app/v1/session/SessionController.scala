package v1.session

import org.apache.spark.sql.SparkSession
import org.json4s.{DefaultFormats, JValue}
import play.api.libs.json.{JsValue, Json}
import play.api.mvc.{AbstractController, Action, ControllerComponents}
import org.json4s.jackson.Serialization.write
import v1.utils.LiveContext

class SessionController @javax.inject.Inject()(cc: ControllerComponents) extends AbstractController(cc) {

  implicit val formats: DefaultFormats.type = org.json4s.DefaultFormats
  def up: Action[JsValue] = Action(parse.json) { request =>
    try {
      val requestString = request.body.toString()
      val config = org.json4s.jackson.JsonMethods.parse(requestString).extract[Map[String, String]]
      LiveContext.create(config)
      Ok(Json.obj(
        "message" -> "Successfully created session"
      ))
    } catch {
      case ex: Exception => {
        Ok(Json.obj(
          "message" -> "Unable to create new session",
          "error" -> ex.getMessage
        ))
      }
    }


  }

  def down: Action[JsValue] = Action(parse.json) { request =>

    try {

      LiveContext.close()

      Ok(Json.obj(
        "message" -> "Successfully closed session",
        "output" -> "looks good"
      ))
    } catch {
      case ex: Exception => {
        Ok(Json.obj(
          "message" -> "Unable to close the session",
          "error" -> ex.getMessage
        ))
      }
    }

  }

  def info: Action[JsValue] = Action(parse.json) { request =>

    try {

      Ok(Json.obj(
        "message" -> "Successfully extracted the live session configuration",
        "output" -> Json.parse(write(LiveContext.info()))
      ))
    } catch {
      case ex: Exception => {
        Ok(Json.obj(
          "message" -> "Unable to get the session info",
          "error" -> ex.getMessage
        ))
      }
    }

  }

}
