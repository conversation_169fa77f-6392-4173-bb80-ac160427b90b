package v1.inventorymodel

import play.api.libs.json.{JsA<PERSON>y, JsValue, <PERSON>son}




case class ExecuteExpressionResponse(message:String="Successfully extracted required fields",
                                     output:JsArray=Json.arr(),
                                     headers:JsValue=Json.arr())

object ExecuteExpressionResponse{
  implicit val executeExpressionResponse=Json.format[ExecuteExpressionResponse]
}


