package v1.inventorymodel

import ai.prevalent.entityinventory.common.configs.{DataSource, EIJobArgs}
import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.entityinventory.loader.configs.specs.Config
import common.{InvalidJsonError, InvalidSQLError, TableNotFoundError}
import org.apache.spark.sql.functions.{col, expr}
import org.json4s._
import play.api.libs.json.JsValue
import play.api.mvc._
import play.api.libs.json._
import org.json4s.jackson.Serialization.write
import play.api.{Configuration, Logger}
import v1.common.{ExpressionRequest, ExpressionResponse}
import v1.inventorymodel.InventoryModelControllerUtils.{checkTableExist, getNullColFilteredConfig, getTimeStampDetails}
import v1.utils.LiveContext
import ai.prevalent.entityinventory.utils.SparkUtil
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReaderFactory, SDSTableWriterFactory}

import scala.concurrent.{ExecutionContext, Future}
import common.RedisClient
import org.apache.spark.sql.SparkSession


class InventoryModelController @javax.inject.Inject()(cc: ControllerComponents,redisClient: RedisClient, configuration: Configuration)(implicit ec: ExecutionContext) extends AbstractController(cc) {

  implicit val formats: Formats = DefaultFormats
  private val LOGGER = Logger(getClass)

  def validateSchema: Action[JsValue] = Action(parse.json) { request =>

    try {
      val body = request.body.toString()
      val config = org.json4s.jackson.JsonMethods.parse(body).extract[Config]
      Ok(Json.obj(
        "message" -> "Valid JSON Object",
        "output" -> Json.parse(write(config))
      ))
    } catch {
      case ex: Exception =>
        LOGGER.error("Exception while validating schema", ex)
        BadRequest(Json.obj(
          "message" -> "Invalid JSON Schema",
          "error" -> ex.getMessage
        ))

    }

  }

  def validateExpression(selectedCols:Option[Seq[String]]): Action[ExpressionRequest] = Action(parse.json[ExpressionRequest]).async { implicit request =>

    try {
      redisClient.setStatus("loaders","30 Aug")
      redisClient.getStatus("loader").flatMap {
        case Some(value)=> Future.successful(println(value))
        case null => Future.successful(BadRequest("No Active"))
      }
      val body = request.body.expression
      expr(body).expr
      Future.successful(Ok(Json.toJson(ExpressionResponse(output = body))))

    } catch {
      case ex: Exception =>
        LOGGER.error("Exception while validating expression", ex)
        throw InvalidSQLError(ex.getMessage)

    }
  }


  def execute(selectedCols:Option[Seq[String]],onlyInventoryConfigFields:Option[Seq[String]],distinct:Option[Seq[String]],
              limit:Option[Seq[String]]): Action[JsValue] = Action(parse.json).async { implicit request =>

    try {
      LiveContext.buildSessionFromEnvSpec()
    } catch {
      case ex: Exception =>
        LOGGER.error(ex.getMessage)
        Future.successful(BadRequest("No Active Spark session"))
    }

    try {

      val reader = SDSTableReaderFactory.getDefault(LiveContext.spark)
      val writer = SDSTableWriterFactory.getDefault(LiveContext.spark, tableProperties = Map.empty, options = Map("partitionOverwriteMode" -> "dynamic"))
      val loaderJobArgs = new EIJobArgs()
      val queryParams: Map[String, Seq[String]] = request.queryString
      val body = request.body.toString()
      val config = org.json4s.jackson.JsonMethods.parse(body).extract[Config]
      val filteredConfig = getNullColFilteredConfig(config)

      val datasource: DataSource = org.json4s.jackson.JsonMethods.parse(body).extract[Config].dataSource.get
      val srdm = datasource.srdm

      Loader.spark = LiveContext.spark
      checkTableExist(srdm, LiveContext.spark)

      val timeStampDetails = getTimeStampDetails(srdm, LiveContext.spark)
      if (timeStampDetails._2) {
        loaderJobArgs.parsedIntervalEndEpoch = timeStampDetails._1.parsedIntervalEndEpoch
        loaderJobArgs.parsedIntervalStartEpoch = timeStampDetails._1.parsedIntervalStartEpoch
        loaderJobArgs.prevUpdateDate = timeStampDetails._1.previousEndEpoch
        loaderJobArgs.currentUpdateDate = timeStampDetails._1.eventTimestampEndEpoch


        val paramsPriority = if (selectedCols.getOrElse(Seq.empty[String]).nonEmpty & onlyInventoryConfigFields.getOrElse(Seq("true")).head.toBoolean)
          "selectedCols" else "onlyInventoryConfigFields"

        var fields :Array[String]= if (paramsPriority == "selectedCols") {
          val fieldsFrom = SparkUtil.getDependendProperty(filteredConfig.allProperties,selectedCols.get.head).toList.reverse
          LOGGER.info(s"Validator Fields $fieldsFrom")
          fieldsFrom.toArray
        }
        else {
          InventoryModelControllerUtils.getPropertyColNames(filteredConfig.commonProperties ++
            filteredConfig.entitySpecificProperties ++ filteredConfig.sourceSpecificProperties) ++ Array("primary_key")
        }
        val updConfig:Config = if (paramsPriority == "selectedCols") {
          val copyField = fields
          val srDMprop= SparkUtil.getSRDMProperty(copyField,filteredConfig)
          fields = fields++srDMprop.map(f => f.colName).toSet
          filteredConfig.copy(sourceSpecificProperties = filteredConfig.sourceSpecificProperties++srDMprop)
        }
        else filteredConfig
        val resultSet = Loader.build(loaderJobArgs, updConfig,reader,writer)
        fields = fields.intersect(resultSet.columns)
        var fullSet = resultSet.select(fields.map(col): _*)
        val reducedSet = if (distinct.getOrElse(Seq("true")).headOption.forall(_.toBoolean)) fullSet.distinct() else fullSet
        val output: Array[JsValue] = reducedSet.limit(limit.getOrElse(Seq("50")).headOption.map(_.toInt).getOrElse(50)).toJSON.collect().map(Json.parse)
        val headers: Array[String] = if (paramsPriority == "selectedCols") {
          fields
        } else reducedSet.columns
        Future.successful(Ok(Json.toJson(ExecuteExpressionResponse(output = JsArray(output), headers = Json.toJson(headers)))))

      }
      else {
        Future.successful(Ok(Json.toJson(ExecuteExpressionResponse(message = s"No data available for table $srdm"))))

      }
    } catch {
      case ex: Exception =>
        LOGGER.error("Exception while processing request", ex)

        throw InvalidJsonError(ex.getMessage)
    }
  }
}