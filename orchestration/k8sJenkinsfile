@Library('sds-common') _
coverageLimit = 10
stageResult = 'PASS'
unitTestImageTag = "prevalentai/airflow:sds-pe-orchestration-1-5-0-16"
AIRFLOW_HOME = "/opt/airflow"
cpCmd = "cp -rf"


pipeline {
  agent none
  parameters {
    choice(
      name: 'pushToDockerHub',
      choices: ['No', 'Yes'],
      description: 'Do you want to push this build to dockerhub ?'
    )
  }
  environment {
    repository = 'ei-airflow'
    registryCredential = 'dockerhub'
    repoName = 'sds-ei-orchestration'
    COSIGN_PASSWORD = credentials('cosign-password')
  }

  stages {
    stage('unitTest') {
      agent {
        kubernetes {
          yaml """\
        apiVersion: v1
        kind: Pod
        metadata:
          labels:
            app: unit-test
        spec:
          containers:
          - name: unittest
            image: ${unitTestImageTag}
            command:
            - cat
            tty: true
          imagePullSecrets:
            - name: docker-secret
          securityContext:
            runAsUser: 50000
            runAsGroup: 50000
        """.stripIndent()
        }
      }
      steps {
        container('unittest') {
          script {
            try {
              sh "${cpCmd} ./dags/* ${AIRFLOW_HOME}/dags/ && ${cpCmd} ./plugins ${AIRFLOW_HOME}/ && ${cpCmd} ./commons ${AIRFLOW_HOME}/ && ${cpCmd} ./tests ${AIRFLOW_HOME}/ && mkdir ${AIRFLOW_HOME}/shared && ${cpCmd} ./tests/config/* ${AIRFLOW_HOME}/shared && ${cpCmd} requirements.txt ${AIRFLOW_HOME} && ${cpCmd} start_test.sh ${AIRFLOW_HOME}"
              sh "pip install -r ${AIRFLOW_HOME}/requirements.txt"
              sh "chmod +x ${AIRFLOW_HOME}/start_test.sh && cd ${AIRFLOW_HOME} && ./start_test.sh"
             }
            catch(err) {
              catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
                sh "exit 1"
              }
            }
          }
        }
      }
    }

    stage ('version') {
      agent {
        kubernetes {
          label 'debian'
          defaultContainer 'debian'
        }
      }
      steps {
        script {
          if ("${pushToDockerHub}" == "Yes") {
            env.buildNumber = getNewDockerBuildVersion()
          } else {
            env.buildNumber = getNewBuildVersion()
          }
        }
      }
    }

    stage ('generatePythonCoverageReport') {
      agent {
        kubernetes {
          yaml """\
        apiVersion: v1
        kind: Pod
        metadata:
          labels:
            app: unit-test
        spec:
          containers:
          - name: unittest
            image: ${unitTestImageTag}
            command:
            - cat
            tty: true
          imagePullSecrets:
            - name: docker-secret
          securityContext:
            runAsUser: 50000
            runAsGroup: 50000
        """.stripIndent()
        }
      }
      steps {
        container('unittest') {
          script {
            try {
              sh "${cpCmd} ./dags/* ${AIRFLOW_HOME}/dags/ && ${cpCmd} ./plugins ${AIRFLOW_HOME}/ && ${cpCmd} ./commons ${AIRFLOW_HOME}/ && ${cpCmd} ./tests ${AIRFLOW_HOME}/ && mkdir ${AIRFLOW_HOME}/shared && ${cpCmd} ./tests/config/* ${AIRFLOW_HOME}/shared && ${cpCmd} requirements.txt ${AIRFLOW_HOME} && ${cpCmd} start_test.sh ${AIRFLOW_HOME}"
              sh "pip install -r ${AIRFLOW_HOME}/requirements.txt"
              sh "chmod +x ${AIRFLOW_HOME}/start_test.sh && cd ${AIRFLOW_HOME} && ./start_test.sh --coverage-run"
              python.publishCoverageJenkins()
              env.coverageNumber = python.getCoverageStatus(coverageLimit:"${coverageLimit}")

            }
            catch (err) {
              catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
                sh "exit 1"
              }
            }
          }
        }
      }
    }

    stage ('buildContainerImage') {
      when {
        expression {
          return env.pushToDockerHub == 'No';
        }
      }
      agent {
        kubernetes {
          label 'dockerBuild'
          defaultContainer 'kaniko'
        }
      }
      steps {
        script {
          dockerBuild.kanikoBuild(buildNumber:"${buildNumber}", repoName:"${repoName}")
        }
      }
    }

    stage ('buildAndPushImage') {
      when {
        expression {
          return env.pushToDockerHub == 'Yes';
        }
      }
      agent {
        kubernetes {
          label 'dockerBuild'
          defaultContainer 'kaniko'
        }
      }
      steps {
        script {
          def buildTag = sh(script: "echo ${buildNumber} | sed \"s/+/-/g\" | sed \"s/\\./-/g\"", returnStdout: true)
          imageName = "${docker_registery}/"+"${repository}:"+"${repoName}-"+"${buildTag}"
          dockerBuild.kanikoBuildAndPushToDockerHub(imageName: "${imageName}", buildNumber:"${buildNumber}", repoName:"${repoName}")
        }
      }
    }

    stage ('signContainerImage') {
      when {
        expression {
          return env.pushToDockerHub == 'Yes';
        }
      }
      agent {
        kubernetes {
          label 'dockerBuild'
          defaultContainer 'cosign'
        }
      }
      steps {
        script {
          cosign.imageSigning(imageName: "${imageName}")
        }
      }
    }

    stage ('validateContainerImage') {
      when {
        expression {
          return env.pushToDockerHub == 'Yes';
        }
      }
      agent {
        kubernetes {
          label 'dockerBuild'
          defaultContainer 'cosign'
        }
      }
      steps {
        script {
          cosign.imageVerification(imageName: "${imageName}")
        }
      }
    }

    stage ('notification') {
      agent {
        kubernetes {
          label 'debian'
          defaultContainer 'debian'
        }
      }
      steps {
        script {
          deploy.notifyOnBuild(buildVersion:"${buildNumber}")
          deploy.highlightCoverageStatus(coverageNumber:"${coverageNumber}")
        }
      }
    }
  }
}
