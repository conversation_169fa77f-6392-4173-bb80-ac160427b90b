ARG AIRFLOW_BASE_IMAGE_VERSION="1-6-0-21"
ARG AIRFLOW_BASE_IMAGE="prevalentai/airflow:sds-pe-orchestration-"
# Using Platform airflow image as base image which has all platform pipelines and requirements.
FROM ${AIRFLOW_BASE_IMAGE}${AIRFLOW_BASE_IMAGE_VERSION}

USER airflow

# Adding ei orchestration components into the image
COPY ./dags ${AIRFLOW_HOME}/dags/
COPY ./ei_plugins ${AIRFLOW_HOME}/ei_plugins
COPY ./ei_commons ${AIRFLOW_HOME}/ei_commons

COPY ./requirements.txt ${AIRFLOW_HOME}
RUN pip install -r requirements.txt
ENTRYPOINT ["/usr/bin/dumb-init", "--", "/entrypoint"]
CMD []
USER ${AIRFLOW_UID}
