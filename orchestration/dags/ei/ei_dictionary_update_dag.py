from commons.pe.common_utils import Utils
from commons.ei.ei_constants.ei_constants import EIConstants
from urllib.parse import urljoin
from airflow.operators.python import PythonOperator
from commons.pe.common_utils import Utils
import json
from datetime import datetime
import pendulum
from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from commons.pe.common_utils import Utils
from commons.pe.sds_date_utils import SDSDateUtils
from commons.pe.models.sds_dags import SDSDAG
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.ei.ei_data_dictionary_update_utils import EntityDatadictionaryUpdate,get_metadata_api_response



Tasks=get_metadata_api_response()

default_args = {
    "start_date": datetime(2020, 1, 1),
    "catchup": False,
}
entity_updater = EntityDatadictionaryUpdate()

dag_name="sds_ei_data_dictionary_update"
rel_group="relationship_data_dictionary_update"
entity_group="entity_data_dictionary_update"

with SDSPipelineDAG(
    pipeline_id=EIConstants.SDS_EI_MODULE_NAME,
    dag_id=dag_name,
    default_args=default_args,
    description="SDS EI Data Dictionary Update",
    schedule_interval=None,
    tags=[EIConstants.SDS_EI_MODULE_NAME,"DATA_ANALYTICS","DATA_DICTIONARY_UPDATE"],
    user_defined_macros={
        "render_variable": Utils.render_variable,
        "get_start_date": SDSDateUtils.get_start_date,
        "get_end_date": SDSDateUtils.get_end_date,
        "json": json,
        "pendulum": pendulum,
        "urljoin": urljoin
    },
    # Limit maximum tasks running at once in the entire DAG
    concurrency=5,
) as dag:
    start = EmptyOperator(task_id=f"{dag_name}_start", wait_for_downstream=True)
    end = EmptyOperator(task_id=f"{dag_name}_end")
    # Task groups for Entity and Relationship Data Dictionaries
    with TaskGroup(group_id=entity_group) as entity_data_dict_group:
        for task in Tasks:
            # If task name contains '__data_dictionary', group it under Entity Data Dictionary
            if "__data_dictionary" in task:
                task_operator = PythonOperator(
                    task_id=f"sds_{task}_update",
                    python_callable=entity_updater.get_api_response,  # Assuming this is a shared function
                    op_kwargs={"entity_data_dict": task},
                    retries=2
                )
                start >> task_operator  # Link to start
                

    with TaskGroup(group_id=rel_group) as relationship_data_dict_group:
        for task in Tasks:
            # If task name does not contain '__data_dictionary', group it under Relationship Data Dictionary
            if "__data_dictionary" not in task:
                task_operator = PythonOperator(
                    task_id=f"sds_{task}_data_dictionary_update",
                    python_callable=entity_updater.get_api_response,  # Assuming this is a shared function
                    op_kwargs={"entity_data_dict": task},
                    retries=2
                )
                start >> task_operator  # Link to start
                


    status_update = PythonOperator(
        task_id="status_update",
        python_callable=entity_updater.get_patch_api_response,  # Call the get_patch_api_response function
        retries=3,
        op_kwargs={}
    )

    start >> entity_data_dict_group
    start >> relationship_data_dict_group

    entity_data_dict_group >> status_update
    relationship_data_dict_group >> status_update
    
    status_update >> end



