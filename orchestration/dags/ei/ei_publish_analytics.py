import json
import os
from datetime import datetime
from urllib.parse import urljoin
from redis.exceptions import LockError
import pendulum
import logging
from airflow.providers.redis.hooks.redis import RedisHook
from airflow.models.baseoperator import chain
from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from commons.pe.common_utils import Utils
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.sds_date_utils import SDSDateUtils
from airflow.models.variable import Variable
from commons.ei.ei_constants.common_constants import DruidApiConstants
from commons.ei.ei_constants.ei_constants import EIConstants
from commons.ei.analytical_utils import AnalyticalUtils
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
olap_context = os.getenv('OLAP_CONTEXT', 'DRUID')

def get_pyiceberg_schema(publish_table_name, ei_table_name):
    from pyiceberg.catalog import load_catalog
    catalog = load_catalog('default')
    try:
        table_publish = catalog.load_table(publish_table_name)
        return table_publish.schemas()[len(table_publish.schemas()) - 1].columns
    except Exception as e:
        if 'Table does not exists' in str(e):
            table_ei = catalog.load_table(ei_table_name)
            return table_ei.schemas()[len(table_ei.schemas()) - 1].columns

def update_druid_dimension(**kwargs):
    table_name = kwargs['table_name']
    druid_task_name = f'{table_name}__model'
    logger = logging.getLogger('ei_publish_task')
    ei_publish_schema = Variable.get('EI_PUBLISH_SCHEMA_NAME')
    ei_schema = Variable.get('EI_SCHEMA_NAME')
    publish_table_name = f'{ei_publish_schema}.{table_name}'
    ei_table_name = f'{ei_schema}.{table_name}'
    logger.info(f'Publish Table Name {publish_table_name}')
    logger.info(f'EI Table Name {ei_table_name}')
    schema = get_pyiceberg_schema(publish_table_name, ei_table_name)
    standard_columns = filter(lambda x: 'array' not in x.field_type.json().replace('"', '') and 'list' not in x.field_type.json().replace('"', '') and ('struct' not in x.field_type.json().replace('"', '')) and ('int' not in x.field_type.json().replace('"', '')) and ('decimal' not in x.field_type.json().replace('"', '')), schema)
    decimal_columns = filter(lambda x: x.field_type.json().replace('"', '').startswith('decimal'), schema)
    decimal_columns_dict = list(map(lambda col: {'name': col.name, 'type': 'double'}, decimal_columns))
    int_columns = filter(lambda x: x.field_type.json().replace('"', '') == 'int', schema)
    int_columns_dict = list(map(lambda col: {'name': col.name, 'type': 'long'}, int_columns))
    standard_fields_dict = list(map(lambda col: {'name': col.name, 'type': col.field_type.json().replace('"', '')}, standard_columns))
    complex_columns = filter(lambda x: 'array' in x.field_type.json().replace('"', '') or 'list' in x.field_type.json().replace('"', '') or 'struct' in x.field_type.json().replace('"', ''), schema)
    complex_fields_dict = list(map(lambda col: {'name': col.name, 'type': 'string'}, complex_columns))
    dimension_dict = standard_fields_dict + complex_fields_dict + int_columns_dict + decimal_columns_dict
    for dimen_dict in dimension_dict:
        if dimen_dict.get('type') == 'timestamptz':
            dimen_dict['type'] = 'long'
    logger.info(f'Dimensions list {dimension_dict}')
    druid_dict = Variable.get('sds_ei_druid_indexing_config', deserialize_json=True)
    redis_hook = RedisHook(redis_conn_id='redis_default')
    while True:
        try:
            with redis_hook.get_conn().lock(name='druid_indexing_task_lock', blocking_timeout=10):
                if druid_dict[druid_task_name]['spec']['dataSchema']['dimensionsSpec']['dimensions']:
                    merged_dimensions_dict = {dim['name']: dim for dim in dimension_dict}
                    for dim in druid_dict[druid_task_name]['spec']['dataSchema']['dimensionsSpec']['dimensions']:
                        merged_dimensions_dict[dim['name']] = dim
                    merged_dimensions = list(merged_dimensions_dict.values())
                    druid_dict[druid_task_name]['spec']['dataSchema']['dimensionsSpec']['dimensions'] = merged_dimensions
                    Variable.set('sds_ei_druid_indexing_config', json.dumps(druid_dict, indent=3))
                else:
                    druid_dict[druid_task_name]['spec']['dataSchema']['dimensionsSpec']['dimensions'] = dimension_dict
                    Variable.set('sds_ei_druid_indexing_config', json.dumps(druid_dict, indent=3))
            break
        except LockError:
            pass
with SDSPipelineDAG(pipeline_id=EIConstants.SDS_EI_MODULE_NAME, dag_id='sds_ei_publish_dag', description='SDS EI Analytics', start_date=datetime(2000, 1, 1), render_template_as_native_obj=True, catchup=False, concurrency=6, user_defined_filters={'tojson': lambda s: json.dumps(s)}, tags=[EIConstants.SDS_EI_MODULE_NAME, 'DATA_ANALYTICS'], user_defined_macros={'render_variable': Utils.render_variable, 'get_start_date': SDSDateUtils.get_start_date, 'get_end_date': SDSDateUtils.get_end_date, 'json': json, 'str': str, 'pendulum': pendulum, 'urljoin': urljoin, 'DruidApiConstants': DruidApiConstants}) as sds_ei_publish_analytics:
    sds_ei_publish_model_config_value = Utils.read_data_from_shared_filesystem(relative_file_path=EIConstants.SDS_EI_PUBLISH_MODEL_CONFIG_KEYS)
    sds_ei_entity_enrich_config_value = Utils.read_data_from_shared_filesystem(relative_file_path=EIConstants.SDS_EI_ENTITY_ENRICH_CONFIG_KEYS)
    sds_ei_olap_dag_factory_config = Utils.read_data_from_shared_filesystem(relative_file_path=EIConstants.SDS_EI_OLAP_DAG_FACTORY_CONFIG)
    sds_ei_druid_variables_update_config = [job['config_key'] for job in sds_ei_olap_dag_factory_config['sds_ei_druid_indexing']['task_group'][0]['job_list']]
    sds_ei_publish_model_config = {'parallel': sds_ei_publish_model_config_value}
    sds_ei_publish_analytics_start = EmptyOperator(task_id='sds_ei_publish_analytics_start', wait_for_downstream=True)
    sds_ei_publish_analytics_end = EmptyOperator(task_id='sds_ei_publish_analytics_end')
    with TaskGroup(group_id='sds_ei_aggregate_entities_jobs') as sds_ei_aggregate_entities_jobs:
        sds_ei_aggregate_entities_start = EmptyOperator(task_id='sds_ei_aggregate_entities_start', wait_for_downstream=True)
        sds_ei_aggregate_entities_end = EmptyOperator(task_id='sds_ei_aggregate_entities_end')
        for source in sds_ei_publish_model_config_value:
            sds_ei_analytics_task = SparkOperatorFactory.get(task_id=source, retries=2, from_var=f'{source}')
            sds_ei_aggregate_entities_start >> sds_ei_analytics_task >> sds_ei_aggregate_entities_end
    with TaskGroup(group_id='sds_ei_enrich_entities_jobs') as sds_ei_enrich_entities_jobs:
        sds_ei_enrich_entities_start = EmptyOperator(task_id='sds_ei_enrich_entities_start', wait_for_downstream=True)
        sds_ei_enrich_entities_end = EmptyOperator(task_id='sds_ei_enrich_entities_end')
        for source in sds_ei_entity_enrich_config_value:
            sds_ei_analytics_task = SparkOperatorFactory.get(task_id=source, retries=2, from_var=f'{source}')
            sds_ei_enrich_entities_start >> sds_ei_analytics_task >> sds_ei_enrich_entities_end
    if olap_context == 'DREMIO':
        sds_ei_publish_analytics_start >> sds_ei_aggregate_entities_jobs >> sds_ei_enrich_entities_jobs >> sds_ei_publish_analytics_end
    else:
        with TaskGroup(group_id='sds_ei_druid_variables_update_jobs') as sds_ei_druid_variables_update_jobs:
            chain([AnalyticalUtils.get_druid_vars_update_job_tasks(analytical_variables=sds_ei_druid_variables_update_config, update_druid_dimension=update_druid_dimension)])
        sds_ei_publish_analytics_start >> sds_ei_aggregate_entities_jobs >> sds_ei_enrich_entities_jobs >> sds_ei_druid_variables_update_jobs >> sds_ei_publish_analytics_end