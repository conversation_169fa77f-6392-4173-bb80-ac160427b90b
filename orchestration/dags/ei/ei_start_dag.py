"""
This is the HEAD DAG for EI pipeline
"""
from datetime import datetime

from airflow.operators.dummy import DummyOperator
from commons.pe.sds_dag_orchestrator import SDSHeadDAG
from commons.ei.ei_constants.ei_constants import EIConstants

with SDSHeadDAG(
        dag_id="sds_ei_start",
        description="DAG to start EI",
        pipeline_id=EIConstants.SDS_EI_MODULE_NAME,
        start_date=datetime(2000, 1, 1),
        schedule_interval="0 6 * * *",
        render_template_as_native_obj=True,
        catchup=False,
        concurrency=1,
        tags=[EIConstants.SDS_EI_MODULE_NAME, "DATA_ANALYTICS"],
) as ei_start_dag:
    start_ei_pipeline = DummyOperator(task_id="start_ei_pipeline")