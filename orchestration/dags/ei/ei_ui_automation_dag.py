import json
import os
import requests
from datetime import datetime
from urllib.parse import urljoin
from airflow.operators.python import <PERSON><PERSON><PERSON>ator
import pendulum
from airflow.operators.empty import EmptyOperator
from airflow.providers.http.operators.http import HttpOperator
from commons.pe.common_utils import Utils
from commons.pe.constants.airflow_constants import AirflowConstants
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.sds_date_utils import SDSDateUtils
from commons.ei.ei_constants.common_constants import DruidApiConstants, UiApiContants
from commons.ei.ei_constants.ei_constants import EIConstants
from commons.ei.ui_automation_utils import ui_configuration
from airflow.models.connection import Connection
from airflow.models.variable import Variable
ssl_verify = Variable.get('MANAGEMENT_API_SSL_VERIFY', default_var=True, deserialize_json=True)

def get_api_response():
    conn = Connection.get_connection_from_secrets('management_api')
    HOST = conn.host
    PORT = conn.port
    SCHEMA = conn.schema
    url = urljoin(f'{SCHEMA}://{HOST}:{PORT}/', UiApiContants.Ui_API_URL)
    response = requests.get(url, headers={'Content-Type': 'application/json', 'Authorization': Utils.get_keycloak_token()}, verify=ssl_verify)
    if response.status_code != requests.codes.ok:
        requests.post(url.rstrip('entity_inventory/'), data=json.dumps({'id': 'entity_inventory'}), headers={'Content-Type': 'application/json', 'Authorization': Utils.get_keycloak_token()}, verify=ssl_verify)
with SDSPipelineDAG(pipeline_id=EIConstants.SDS_EI_MODULE_NAME, dag_id='sds_ei_ui_mapping_analytics', description='SDS EI UI Mapping Analytics', start_date=datetime(2000, 1, 1), render_template_as_native_obj=True, catchup=False, concurrency=6, tags=[EIConstants.SDS_EI_MODULE_NAME, 'DATA_ANALYTICS'], user_defined_macros={'render_variable': Utils.render_variable, 'get_start_date': SDSDateUtils.get_start_date, 'get_end_date': SDSDateUtils.get_end_date, 'json': json, 'pendulum': pendulum, 'urljoin': urljoin, 'DruidApiConstants': DruidApiConstants, 'get_keycloak_token': Utils.get_keycloak_token}) as sds_ei_ui_mapping_analytics:
    sds_ei_ui_mapping_start = EmptyOperator(task_id='sds_ei_ui_mapping_start', wait_for_downstream=True)
    sds_ei_ui_mapping_end = EmptyOperator(task_id='sds_ei_ui_mapping_end')
    sds_ei_ui_mapping_config_task = PythonOperator(task_id='sds_ei_ui_mapping_config', python_callable=ui_configuration, op_kwargs={'start_date': "{{ data_interval_start.start_of('day').to_datetime_string()}}", 'end_date': "{{data_interval_start.add(days=1).start_of('day').to_datetime_string()}}"}, retries=2)
    sds_ei_ui_get_api_response_task = PythonOperator(task_id='sds_ei_ui_get_api_response', python_callable=get_api_response, op_kwargs={'start_date': "{{ data_interval_start.start_of('day').to_datetime_string()}}", 'end_date': "{{data_interval_start.add(days=1).start_of('day').to_datetime_string()}}"}, retries=2)
    sds_ei_ui_configs_api_post_task = HttpOperator(task_id='sds_ei_ui_configs_api_post', retries=2, http_conn_id='management_api', method='PATCH', extra_options=ssl_verify, headers={**AirflowConstants.DEFAULT_HTTP_HEADERS, 'Authorization': '{{ get_keycloak_token() }}'}, endpoint=os.getenv('MANAGEMENT_API_UI_MAPPING_ENDPOINT', UiApiContants.Ui_API_URL), data="{{ json.dumps(ti.xcom_pull(task_ids='sds_ei_ui_mapping_config')) }}")
    sds_ei_ui_mapping_start >> sds_ei_ui_mapping_config_task >> sds_ei_ui_get_api_response_task >> sds_ei_ui_configs_api_post_task >> sds_ei_ui_mapping_end