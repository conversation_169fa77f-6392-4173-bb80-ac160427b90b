from datetime import datetime
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from airflow.operators.empty import EmptyOperator
from commons.ei.ei_constants.ei_constants import EIConstants
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
with SDSPipelineDAG(pipeline_id=EIConstants.SDS_EI_MODULE_NAME, dag_id='ei_config_snapshot_dag', description='DAG to capture pre-upgrade configs into Iceberg table', start_date=datetime(2000, 1, 1), schedule_interval=None, render_template_as_native_obj=True, catchup=False, tags=['PRE_UPGRADE']) as dag:
    pre_upgrade_start = EmptyOperator(task_id='pre_upgrade_start')
    capture_configs = SparkOperatorFactory.get(task_id='capture_pre_upgrade_configs', retries=2, from_var=f'{EIConstants.PRE_UPGRADE_CONFIG_SNAPSHOT_VARS}')
    pre_upgrade_start >> capture_configs