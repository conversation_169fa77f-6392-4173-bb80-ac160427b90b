import json
from datetime import datetime
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from commons.pe.common_utils import Utils
from commons.ei.analytical_utils import AnalyticalUtils
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.pe.sds_date_utils import SDSDateUtils
from commons.ei.ei_constants.ei_constants import EIConstants
import pendulum
from urllib.parse import urljoin

dag_factory_config = Utils.read_data_from_shared_filesystem(EIConstants.DYNAMIC_DAG_GEN_VARS)
valid_dag_config = {
    key: {k: v for k, v in value.items() if isinstance(v, list) and v}  
    for key, value in dag_factory_config.items()
    if isinstance(value, dict) and any(isinstance(v, list) and v for v in value.values())  
}
def rename_keys_recursively(config):
    prefix_mapping = {"sds_ei__inventory_model__":"sds_ei__loader__"}
    if isinstance(config, dict):
        return {
            next((key.replace(old, new, 1) for old, new in prefix_mapping.items() if key.startswith(old)), key):
            rename_keys_recursively(val)
            for key, val in config.items()
        }
    if isinstance(config, list):
        return [rename_keys_recursively(item) for item in config]
    return config

def create_dynamic_dag(template_variable: str, dag_name: str, tasks: list) -> SDSPipelineDAG:
    """
    Creates a dynamic DAG for the given template_variable and dag_name keys, using the task list.

    Args:
        template_variable (str): The top-level key in the configuration (e.g., "entity_rel_enrich").
        dag_name (str): The child key in the configuration (e.g., "graph_entity_enrich").
        tasks (list): List of tasks to include in the DAG.

    Returns:
        SDSPipelineDAG: The dynamically created DAG using the custom SDSDAG class.
    """
    dag_id = f"{dag_name}_dag"

    default_args = {
        "start_date": datetime(2020, 1, 1),
        "catchup": False,
    }

    with SDSPipelineDAG(
        pipeline_id=EIConstants.SDS_EI_MODULE_NAME,
        dag_id=dag_id,
        default_args=default_args,
        description=f"DAG for {template_variable} - {dag_name}",
        schedule_interval=None,
        tags=[EIConstants.SDS_EI_MODULE_NAME,"DATA_ANALYTICS",template_variable.upper()],
        user_defined_macros={
                "render_variable": Utils.render_variable,
                "get_dynamic_job_configs": AnalyticalUtils.get_dynamic_job_configs,
                "json": json,
                "str": str,
                "get_start_date": SDSDateUtils.get_start_date,
                "get_end_date": SDSDateUtils.get_end_date,
                "pendulum": pendulum,
                "urljoin": urljoin,
            },
    ) as dag:

        start_task = EmptyOperator(task_id=f"{dag_name}_start")
        end_task = EmptyOperator(task_id=f"end_{dag_name}_end")

        with TaskGroup(group_id=f"{dag_name}_tasks") as task_group:
            for task in tasks:
                spark_task = SparkOperatorFactory.get(
                    task_id=task,
                    from_conf=f"get_dynamic_job_configs(dag_run,'{task}', '{template_variable}', '{dag_name}')",
                    retries=2,
                )
        start_task >> task_group >> end_task

    return dag


# Validate and dynamically generate DAGs
for template_variable, dag_details in rename_keys_recursively(valid_dag_config).items():
    for dag_name, tasks in dag_details.items():
        if not tasks or not isinstance(tasks, list):
            raise ValueError(f"Invalid tasks for {template_variable}.{dag_name}")
        globals()[f"{dag_name}_dag"] = create_dynamic_dag(template_variable, dag_name, tasks)
