import json
import os
from datetime import datetime
from typing import Dict, List
from urllib.parse import urljoin
import pendulum
import logging
from collections import defaultdict
from airflow.operators.empty import EmptyOperator
from airflow.models.variable import Variable
from airflow.utils.task_group import TaskGroup
from airflow.exceptions import AirflowException
from commons.pe.common_utils import Utils
from commons.ei.ei_constants.ei_constants import EIConstants
from commons.pe.sds_date_utils import SDSDateUtils
from commons.pe.models.sds_dags import SDSDAG
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
logger = logging.getLogger(__name__)

class EIUpgradeConfig:
    """Class to handle upgrade configurations"""

    @staticmethod
    def get_upgrade_spark_job_configs(task: str) -> Dict:
        """
        Get Spark job configurations for a specific task

        Args:
            task: Task identifier

        Returns:
            Dict: Task configuration

        Raises:
            AirflowException: If config is missing or invalid
        """
        try:
            print('***************************** ')
            config = Variable.get(EIConstants.SDS_EI_UPGRADE_VARIABLE_NAME, deserialize_json=True)
            if task not in config:
                raise KeyError(f'Configuration not found for task: {task}')
            return config[task]
        except json.JSONDecodeError as e:
            raise AirflowException(f'Invalid JSON in upgrade variable: {str(e)}')
        except Exception as e:
            raise AirflowException(f'Error getting config for {task}: {str(e)}')

class TaskGroupManager:
    """Class to manage task grouping and creation"""

    @staticmethod
    def group_tasks_by_suffix(tasks: List[str]) -> Dict[str, List[str]]:
        """
        Group tasks by their suffix

        Args:
            tasks: List of task identifiers

        Returns:
            Dict: Tasks grouped by suffix
        """
        task_groups = defaultdict(list)
        for task in tasks:
            suffix = task.split('__')[-1]
            task_groups[suffix].append(task)
        return dict(task_groups)

    @staticmethod
    def create_sequential_task_group(group_name: str, tasks: List[str]) -> TaskGroup:
        """
        Create a task group with sequential task execution

        Args:
            group_name: Name of the task group
            tasks: List of tasks in the group

        Returns:
            TaskGroup: Created task group with sequential dependencies
        """
        with TaskGroup(group_id=f'sds_ei_{group_name}_group') as task_group:
            previous_task = None
            for task in tasks:
                task_operator = SparkOperatorFactory.get(task_id=task, from_conf=f"get_upgrade_spark_job_configs('{task}')")
                if previous_task is None:
                    previous_task = task_operator
                else:
                    previous_task >> task_operator
                    previous_task = task_operator
            return task_group
UPGRADE_TASKS = ['sds_ei__upgrade_entity_inventory__resolver', 'sds_ei__upgrade_entity_inventory__resolution', 'sds_ei__upgrade_entity_inventory__fragments', 'sds_ei__upgrade_relationship__resolver', 'sds_ei__upgrade_relationship__resolution', 'sds_ei__upgrade_relationship__fragments']
with SDSPipelineDAG(pipeline_id=EIConstants.SDS_EI_MODULE_NAME, dag_id='sds_ei_upgrade_dag', description='SDS EI Analytics Upgrade Pipeline', start_date=datetime(2000, 1, 1), render_template_as_native_obj=True, schedule_interval=None, catchup=False, concurrency=6, user_defined_filters={'tojson': lambda s: json.dumps(s)}, tags=[EIConstants.SDS_EI_MODULE_NAME, 'DATA_ANALYTICS'], user_defined_macros={'render_variable': Utils.render_variable, 'get_upgrade_spark_job_configs': EIUpgradeConfig.get_upgrade_spark_job_configs, 'json': json, 'str': str, 'pendulum': pendulum, 'urljoin': urljoin}) as sds_ei_upgrade_analytics:
    start_task = EmptyOperator(task_id='sds_ei_upgrade_start')
    end_task = EmptyOperator(task_id='sds_ei_upgrade_end')
    try:
        task_manager = TaskGroupManager()
        grouped_tasks = task_manager.group_tasks_by_suffix(UPGRADE_TASKS)
        task_groups = []
        for group_name, tasks in grouped_tasks.items():
            task_group = task_manager.create_sequential_task_group(group_name=group_name, tasks=tasks)
            task_groups.append(task_group)
        start_task >> task_groups >> end_task
    except Exception as e:
        logger.error(f'Error setting up DAG: {str(e)}')
        raise