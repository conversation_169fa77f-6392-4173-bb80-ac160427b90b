import json
from datetime import datetime
from urllib.parse import urljoin
from airflow.operators.python import PythonOperator
import pendulum
from airflow.operators.empty import EmptyOperator
from commons.pe.common_utils import Utils
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.sds_date_utils import SDSDateUtils
from commons.ei.ei_constants.common_constants import DruidApiConstants
from commons.ei.ei_constants.ei_constants import EIConstants
from commons.ei.ei_entity_min_max_utils import EntityTimestampManager
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.ei.analytical_utils import AnalyticalUtils

def process_timestamps(**kwargs):
    manager = EntityTimestampManager()
    end_epoch = manager.process_entity_timestamps()
    ti = kwargs['ti']
    ti.xcom_push(key='end_epoch', value=end_epoch)
with SDSPipelineDAG(pipeline_id=EIConstants.SDS_EI_MODULE_NAME, dag_id='sds_ei_graph_olap', description='GRAPH OLAP LAYER FOR PG INDEXING', start_date=datetime(2000, 1, 1), render_template_as_native_obj=True, catchup=False, concurrency=6, tags=[EIConstants.SDS_EI_MODULE_NAME, 'DATA_ANALYTICS'], user_defined_macros={'render_variable': Utils.render_variable, 'get_start_date': SDSDateUtils.get_start_date, 'get_end_date': SDSDateUtils.get_end_date, 'get_dynamic_job_configs': AnalyticalUtils.get_dynamic_job_configs, 'json': json, 'str': str, 'pendulum': pendulum, 'urljoin': urljoin, 'DruidApiConstants': DruidApiConstants, 'get_keycloak_token': Utils.get_keycloak_token}) as sds_ei_graph_olap:
    sds_ei_graph_olap_start = EmptyOperator(task_id='sds_ei_graph_olap_start', wait_for_downstream=True)
    sds_ei_graph_olap_end = EmptyOperator(task_id='sds_ei_graph_olap_end')
    sds_ei_graph_olap_task = PythonOperator(task_id='sds_ei_graph_ui_posting', python_callable=process_timestamps, retries=2)
    sds_ei__graph_olap__resolved = SparkOperatorFactory.get(task_id='sds_ei__graph_olap__resolved', from_var='sds_ei__graph_olap__resolved', retries=2)
    sds_ei__graph_olap__fragment = SparkOperatorFactory.get(task_id='sds_ei__graph_olap__fragment', from_var='sds_ei__graph_olap__fragment', retries=2)
    sds_ei_graph_olap_start >> sds_ei_graph_olap_task >> [sds_ei__graph_olap__resolved, sds_ei__graph_olap__fragment] >> sds_ei_graph_olap_end