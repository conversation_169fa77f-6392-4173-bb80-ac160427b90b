import pytest
from airflow.providers.amazon.aws.operators.s3 import S3DeleteObjectsOperator
from airflow.providers.http.operators.http import HttpOperator
from commons.ei.ei_utils import EIUtils
import commons.ei

class TestEIUtils:

    @pytest.fixture
    def mocker_patch_variable_get(self, mocker, data_sources):
        return mocker.patch('airflow.models.Variable.get', return_value=data_sources)

    @pytest.fixture
    def mocker_patch_get_source_values(self, mocker, source_values):
        return mocker.patch('commons.ei.ei_utils.get_source_values', return_value=source_values)

    @pytest.fixture
    def data_sources(self):
        return {'test_type': {'table1': 'table1', 'table2': 'table2'}}

    @pytest.fixture
    def source_values(self):
        return ['table1', 'table2']

    def test_delete_s3_sources_tasks_valid_inputs(self, mocker, mocker_patch_variable_get, mocker_patch_get_source_values):
        base_path = 'test_path'
        table_sources_key = 'test_key'
        source_type = 'test_type'
        data_sources = {'test_type': {'table1': 'table1', 'table2': 'table2'}}
        mocker.patch('airflow.models.Variable.get', return_value=data_sources)
        mocker.patch('commons.ei.ei_utils.get_source_values', return_value=['table1', 'table2'])
        mocker.patch('airflow.providers.amazon.aws.operators.s3.S3DeleteObjectsOperator.execute', return_value=None)
        tasks = EIUtils.delete_s3_sources_tasks(base_path, table_sources_key, source_type)[0]
        assert len(tasks) == 2
        for task in tasks:
            assert isinstance(task, S3DeleteObjectsOperator)

    def test_get_druid_delete_task_valid_inputs(self, mocker):
        olap_data_sources = 'test_key'
        data_sources = {'test_type': {'table1': 'table1', 'table2': 'table2'}}
        mocker.patch('airflow.models.Variable.get', return_value=data_sources)
        mocker.patch('commons.ei.ei_utils.get_source_values', return_value=['table1', 'table2'])
        mocker.patch('airflow.providers.http.operators.http.SimpleHttpOperator.execute', return_value=None)
        tasks = EIUtils.get_druid_delete_task(olap_data_sources)[0]
        assert len(tasks) == 2
        for task in tasks:
            assert isinstance(task, HttpOperator)

    def test_group_tasks_valid_inputs(self):
        source_keys = ['prefix__entity1__source1', 'prefix__entity1__source2', 'prefix__entity2__source1']
        expected_output = {'entity1': ['prefix__entity1__source1', 'prefix__entity1__source2'], 'entity2': ['prefix__entity2__source1']}
        output = EIUtils.group_tasks(source_keys)
        assert output == expected_output