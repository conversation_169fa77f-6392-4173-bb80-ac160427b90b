import commons.ei

from airflow.operators.bash import <PERSON><PERSON><PERSON>perator
from airflow.operators.python import Python<PERSON>perator
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from plugins.pe.spark.kubernetes.sensors.sds_kubernetes_spark import KubernetesSparkOperator
from airflow.models import Variable, TaskInstance, DagRun
# from dags.ei.ei_analytic_jobs import find_task_run
from commons.ei.analytical_utils import AnalyticalUtils,find_task_run
from commons.ei.backup_utils import BackupUtils
from airflow.models.variable import Variable
from commons.pe.sds_date_utils import SDSDateUtils
import unittest, pytest, json
from unittest.mock import Mock,patch
from unittest.mock import MagicMock
from datetime import datetime


# Correctly retrieves and parses the SDS_EI_ANALYTICAL_JOBS_VARIABLES variable
# Correctly retrieves and parses the SDS_EI_ANALYTICAL_JOBS_VARIABLES variable
def test_multiple_variable_get_calls(mocker):
    # Define a side effect function for handling different keys
    def mock_variable_get(key, default_var=None):
        if key == "SDS_EI_ANALYTICAL_JOBS_VARIABLES":
            return '{"analysis_period": "monthly"}'
        elif key == "sds_ei_start_epoch_default":
            return 000000000
        raise KeyError(f"Unexpected key: {key}")

    # Patch the Variable.get method with the side effect function
    mock_variable_get = mocker.patch('airflow.models.variable.Variable.get', side_effect=mock_variable_get)

    # Call the function under test, which will trigger the mocked Variable.get
    result = find_task_run(dag_run=None, task_name='test_task', dag_name='test_dag')

    # Perform your assertions on the result and mock calls
    assert result ==000000000
    mock_variable_get.assert_any_call("SDS_EI_ANALYTICAL_JOBS_VARIABLES")
    # mock_variable_get.assert_any_call("sds_ei_start_epoch_default")

class TestAnalyticalUtils:
    # Merges base and override configurations correctly
    def test_merge_base_and_override_configs(self, mocker):
        base_config = {
            "args": ["arg1"],
            "app_name": "base_app",
            "conf": {"spark.app.name": "base_app"}
        }
        custom_confs = ["custom_arg"]
        override_config = {
            "args": ["override_arg"],
            "executor_cores": "4",
            "executor_instances": "4",
            "driver_cores": 1
        }
        config_path = ["config_path_arg"]
        task_id = "task_123"

        expected_result = {
            "args": ["override_arg", "config_path_arg"],
            "app_name": "task_123",
            "conf": {"spark.app.name": "task_123"},
            "executor_cores": 4,
            "executor_instances": 4,
            "driver_cores": 1
        }

        mocker.patch('commons.pe.common_utils.Utils.merge_dictionaries', return_value={
            "args": ["arg1", "custom_arg"],
            "app_name": "task_123",
            "conf": {"spark.app.name": "task_123"},
            "executor_cores": 4,
            "executor_instances": "4",
            "driver_cores":1
        })

        result = AnalyticalUtils.config_component_handler(base_config, custom_confs, override_config, config_path, task_id)

        assert result == expected_result
    # Retrieves base orchestration config using the provided config type
    def test_retrieves_base_orchestration_config(self, mocker):

        mock_variable_get = mocker.patch('airflow.models.variable.Variable.get')
        mock_variable_get.return_value =  {
            "args": ["arg1"],
            "app_name": "base_app",
            "conf": {"spark.app.name": "base_app"},
            "executor_cores": "4",
            "executor_instances": "4",
            "driver_cores": 1

        }

        task_id = "test_task"
        parsed_interval_start = "2023-01-01T00:00:00Z"
        config_type = "loader"
        expected={"args": ["arg1", "--parsed-interval-start", "2023-01-01T00:00:00Z", "--parsed-interval-end", "{{ str(pendulum.parse(dag_run.conf['data_interval_end']).timestamp()*1000).split('.')[0] }}", "--event-timestamp-end", "{{ get_end_date(data_interval_start, 'day','utc') }}", "--previous-end-epoch", "{{ get_end_date(prev_data_interval_start_success, 'day','utc') if prev_data_interval_start_success is not none else -1 }}", "--config-path", "test_task"], "app_name": "test_task", "conf": {"spark.app.name": "test_task"}, "executor_cores": 4, "executor_instances": 4, "driver_cores": 1}
        result = AnalyticalUtils.get_analytical_job_configs(task_id, parsed_interval_start, config_type)
        json_obj2 = json.loads(result)

        assert expected== json_obj2
    def test_returns_python_operator_tasks_for_each_variable(self, mocker):
        mock_update_druid_dimension = mocker.Mock()
        analytical_variables = ['var1__model', 'var2__model']

        tasks = AnalyticalUtils.get_druid_vars_update_job_tasks(analytical_variables, mock_update_druid_dimension)

        assert len(tasks) == len(analytical_variables)
        for task, variable in zip(tasks, analytical_variables):
            table_name = variable.replace('__model', '')
            assert task.task_id == f"{table_name}_druid_var"
            assert task.python_callable == mock_update_druid_dimension
            assert task.op_kwargs == {"table_name": table_name}

    # Retrieves connection details from Airflow secrets
    def test_retrieves_connection_details_from_airflow_secrets(self, mocker):
        mock_connection = mocker.patch('airflow.models.connection.Connection.get_connection_from_secrets')
        mock_connection.return_value.host = 'localhost'
        mock_connection.return_value.port = 8080
        mock_connection.return_value.schema = 'http'

        mock_variable_get = mocker.patch('airflow.models.variable.Variable.get')
        mock_variable_get.side_effect = lambda key, *args, **kwargs: {
            "data_quality_schema_name": "ei_profiling",
            "sds_ei_data_quality_job_template": {"application_file": "/path/to/app", "app_name": "test-app"},
            "EI_SCHEMA_NAME": "ei"
        }.get(key, {})

        result = AnalyticalUtils.get_data_quality_job_configs('task', 'group_data_quality')

        assert result['app_name'] == 'taskgroup-data-quality'
        assert result['args'][0] == '--tableName'
        assert result['args'][1] == 'iceberg_catalog.ei.sds_ei__group'

    # Returns a list of task dependencies for valid analytical variables




