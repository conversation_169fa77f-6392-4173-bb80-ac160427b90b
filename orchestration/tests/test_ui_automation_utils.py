import io
import json
import commons.ei
import unittest
from urllib.parse import urljoin
from unittest.mock import patch
import pytest
from commons.ei import ui_automation_utils
from commons.ei.ui_automation_utils import properties_mapping, update_rel_data_dict_druid_mapping, \
    map_relationship_properties, map_relationship_columns, ui_configuration, \
    prepare_merged_common_properties,fetch_column_stats,generate_sql_query,extract_column_statistics, \
    update_data_dict_druid_mapping,update_attributes_with_type,process_dictionaries,transform_data_dictionary, \
    transform_relation_data_dictionary,get_candidate_keys


class TestUIAutomationUtils:
    DS_QUERY_URL = "/druid/v2/sql/"

    @pytest.fixture(autouse=True)
    def setUp(self):
        self.data_dict = {
            "entity1": {"attributes": {"field1": {}, "field2": {"group": "default"}}},
            "entity2": {"attributes": {"inventory_field1": {}, "inventory_field2": {"group": "enrichment", "width": 4,
                                                                                    "caption": "inventory_field2"}}},
        }
        self.relation_data_dict = {
            "entity1": {
                "rel1": {
                    "relationship_attributes": {"attr1": {}, "attr2": {
                        "enable_hiding": False,
                        "type": "integer",
                        "range_selection": True,
                        "min": 0,
                        "max": 100
                    }
                                                },
                    "entity_attributes": {"attr1": {}, "attr2": {}},
                    "name": "rel1",
                    "caption": "caption1",
                    "isInverse": False,
                },
                "rel2": {
                    "relationship_attributes": {"attr3": {}, "attr4": {}},
                    "entity_attributes": {"attr3": {}, "attr4": {}},
                    "name": "rel2",
                    "caption": "caption2",
                    "isInverse": True,
                },
            },
        }

    def get_mock_conn(self):
        conn_mock = unittest.mock.Mock()
        conn_mock.host = "localhost"
        conn_mock.port = "8080"
        conn_mock.schema = "http"
        return conn_mock


    # @patch("commons.ei.ui_automation_utils.extract_column_statistics",
    #        return_value={"attr1": {"width": 5}, "attr2": {"min": 1, "max": 20, "width": 10}})
    # # Extracts column statistics correctly when valid OLAP response is provided
    # # Generates SQL query with width columns when provided in columns_dict
    #
    #
    # @patch("commons.ei.ui_automation_utils.extract_column_statistics",
    #        return_value={"field2": {"width": 5}, "inventory_field2": {"width": 10}})


    def test_entity_without_attributes(self):
        data_dictionary = {"entity1": {"attributes": {}}}
        assert ui_automation_utils.get_candidate_keys(data_dictionary) == {"entity1": []}

    # @patch("commons.ei.ei_druid_utils.GetOlapResponse.get_sources", return_value=["source1", "source2", "source3"])


    # @patch("commons.ei.ei_druid_utils.GetOlapResponse.get_sources", return_value=["source1", "source2", "source3"])


    def test_valid_input_with_solutions_and_enable_hiding(self):
        properties = {"header": 'Caption', "description": 'Description', "accessorKey": 'Field', "type": 'string',
                      "removable": True, "isDate": False,
                      "id": 'Field__0', "solutions": ['EI'], "disableSortBy": True}
        additional_properties = {"data_structure": 'list', "range_selection": True, "min": 1, "max": 10,
                                 "width": 45, "candidate_key": True, "step_interval": 1, "group": "common"}
        result = ui_automation_utils.properties_mapping(properties, additional_properties, enable_hiding=False)
        expected = {'enableHiding': False, 'header': 'Caption', 'description': 'Description', 'disableSortBy': True,
                    'accessorKey': 'Field', 'type': 'string', 'removable': True, 'isDate': False, 'id': 'Field__0',
                    'solutions': ['EI'], 'data_structure': 'list', 'range_selection': True, 'min': 1, 'max': 10,
                    'width': 45, 'candidate_key': True, 'step_interval': 1, 'group': 'common'}
        assert result == expected

    def test_fetch_column_stats_rel(self):
        data_dictionary_conf = {
            "entity1": {
                "sub_entity1": {
                    "entity_attributes": {
                        "attr1": {
                            "range_selection": True,
                            "min": 1,
                            "max": 10
                        },
                        "attr2": {
                            "range_selection": True,
                            "max": 100
                        },
                        "attr3": {
                            "width": 20
                        }
                    },
                    "relationship_attributes": {
                        "attr4": {
                            "range_selection": True,
                            "min": 1,
                            "max": 10
                        },
                        "attr5": {
                            "range_selection": True,
                            "max": 100
                        },
                        "attr6": {}
                    }
                }
            }
        }
        expected_result = {
            'entity_attributes': {
                'width_columns': {'attr3'}
            },
            'relationship_attributes': {
                'width_columns': set()
            }
        }
        assert ui_automation_utils.fetch_column_stats_rel(data_dictionary_conf) == expected_result

    def test_fetch_column_stats(self):
        data_dictionary_conf = {
            "entity_type1": {
                "attributes": {
                    "attr1": {
                        "range_selection": True,
                        "min": 0,
                        "max": 10
                    },
                    "attr2": {
                        "range_selection": False
                    }
                }
            },
            "entity_type2": {
                "attributes": {
                    "attr3": {
                        "range_selection": True,
                        "min": -5,
                        "max": 5,
                        "width": 10
                    }
                }
            }
        }
        expected_result = {
            'width_columns': {'attr3'}
        }
        assert ui_automation_utils.fetch_column_stats(data_dictionary_conf) == expected_result

    def test_valid_input(self):
        # Arrange
        properties_list = {
            "header": "Test Header",
            "description": "Test Description",
            "accessorKey": "test_key",
            "type": "string",
            "removable": True,
            "isDate": False,
            "id": "test_key__0",
            "solutions": ["EI"],
            "disableSortBy": True
        }
        additional_properties = {
            "data_structure": None,
            "range_selection": None,
            "min": None,
            "max": None,
            "width": None,
            "candidate_key": None,
            "step_interval": None,
            "group": "common",
            "column_merge": None
        }
        expected_result = {
            "enableHiding": True,
            "header": "Test Header",
            "group": "common",
            "description": "Test Description",
            "accessorKey": "test_key",
            "type": "string",
            "removable": True,
            "isDate": False,
            "id": "test_key__0",
            "solutions": ["EI"],
            "disableSortBy": True
        }

        # Act
        result = properties_mapping(properties_list, additional_properties)

        # Assert
        assert result == expected_result


    def test_identifies_positive_width_columns(self):
        data_dictionary_conf = {
            "entity1": {
                "attributes": {
                    "column1": {"width": 10},
                    "column2": {"width": 0},
                    "column3": {"width": 5}
                }
            },
            "entity2": {
                "attributes": {
                    "column4": {"width": 15},
                    "column5": {"width": -3}
                }
            }
        }
        expected_result = {"width_columns": {"column1", "column3", "column4"}}
        result = fetch_column_stats(data_dictionary_conf)
        assert result == expected_result

    # Handles empty columns_dict gracefully
    def test_generate_sql_query_with_empty_columns_dict(self):
        columns_dict = {}
        table_name = 'test_table'
        start_date = '2023-01-01'
        end_date = '2023-12-31'

        result = generate_sql_query(columns_dict, table_name, start_date, end_date)
        assert result is None

    def test_generate_sql_query_with_width_columns(self):
        columns_dict = {'width_columns': ['column1', 'column2']}
        table_name = 'test_table'
        start_date = '2023-01-01'
        end_date = '2023-12-31'

        expected_query = {
            "dataQuery": [{
                "query": {
                    "dataSource": {
                        "dataSource": table_name
                    },
                    "queryParams": {
                        "fields": [
                            "MAX(LENGTH(column1)) withAlias width___column1",
                            "MAX(LENGTH(column2)) withAlias width___column2"
                        ],
                        "aggregate": [],
                        "group": [],
                        "filter": f"__time BETWEEN TIMESTAMP '{start_date}' AND TIMESTAMP '{end_date}'",
                        "sort": [],
                        "limit": ""
                    }
                },
                "dataLabel": "sqlQuery"
            }],
            "moduleName": "SDS_INSIGHTS_ENTITY"
        }

        result = generate_sql_query(columns_dict, table_name, start_date, end_date)
        assert result == expected_query

    # Correctly updates data dictionary with valid width attributes
    def test_update_with_valid_width_attributes(self, mocker):
        # Mocking the dependent functions
        mocker.patch('commons.ei.ui_automation_utils.fetch_column_stats',
                     return_value={'width_columns': {'attr1', 'attr2'}})
        mocker.patch('commons.ei.ui_automation_utils.generate_sql_query', return_value={'dataQuery': [{
            'query': {
                'queryParams': {
                    'fields': [
                        'MAX(LENGTH(attr1)) withAlias width___attr1',
                        'MAX(LENGTH(attr2)) withAlias width___attr2']}}}]})
        mocker.patch('commons.ei.ui_automation_utils.extract_column_statistics',
                     return_value={'attr1': {'width': 10}, 'attr2': {'width': 5}})

        data_dictionary_conf = {
            'entity1': {
                'attributes': {
                    'attr1': {'width': 8, 'caption': 'Attribute 1'},
                    'attr2': {'width': 3, 'caption': 'A2'}
                }
            }
        }
        start_date = '2023-01-01'
        end_date = '2023-12-31'

        updated_data_dict = update_data_dict_druid_mapping(data_dictionary_conf, start_date, end_date)

        assert updated_data_dict['entity1']['attributes']['attr1']['width'] == 11
        assert updated_data_dict['entity1']['attributes']['attr2']['width'] == 3

    # Generates SQL queries based on entity and relationship attributes
    def test_generate_sql_queries_for_attributes(self, mocker):
        # Mock dependencies
        mock_fetch_column_stats_rel = mocker.patch('commons.ei.ui_automation_utils.fetch_column_stats_rel')
        mock_generate_sql_query = mocker.patch('commons.ei.ui_automation_utils.generate_sql_query')
        mock_extract_column_statistics = mocker.patch('commons.ei.ui_automation_utils.extract_column_statistics')

        # Setup mock return values
        mock_fetch_column_stats_rel.return_value = {
            'entity_attributes': {'width_columns': {'attr1', 'attr2'}},
            'relationship_attributes': {'width_columns': {'attr3'}}
        }
        mock_generate_sql_query.side_effect = [
            {'dataQuery': [{'query': {'queryParams': {'fields': ['MAX(LENGTH(attr1)) withAlias width___attr1', 'MAX(LENGTH(attr2)) withAlias width___attr2']}}}]},
            {'dataQuery': [{'query': {'queryParams': {'fields': ['MAX(LENGTH(attr3)) withAlias width___attr3']}}}]}
        ]
        mock_extract_column_statistics.return_value = {
            'attr1': {'width': 10},
            'attr2': {'width': 15},
            'attr3': {'width': 5}
        }

        # Input data
        rel_data_dictionary_conf = {
            'entity1': {
                'sub_entity1': {
                    'entity_attributes': {
                        'attr1': {'width': 8, 'caption': 'Attribute 1'},
                        'attr2': {'width': 12, 'caption': 'Attribute 2'}
                    },
                    'relationship_attributes': {
                        'attr3': {'width': 3, 'caption': 'Attribute 3'}
                    }
                }
            }
        }
        start_date = "2023-01-01"
        end_date = "2023-12-31"

        # Call the function under test
        updated_conf = update_rel_data_dict_druid_mapping(rel_data_dictionary_conf, start_date, end_date)

        # Assertions
        assert updated_conf['entity1']['sub_entity1']['entity_attributes']['attr1']['width'] == 8
        assert updated_conf['entity1']['sub_entity1']['entity_attributes']['attr2']['width'] == 12
        assert updated_conf['entity1']['sub_entity1']['relationship_attributes']['attr3']['width'] == 11

    # Correctly updates attributes in data_dictionary with types from type_dict
    def test_updates_attributes_with_types(self):
        data_dictionary = {
            'entity1': {
                'attributes': {
                    'attr1': {'description': 'Attribute 1'},
                    'attr2': {'description': 'Attribute 2'}
                }
            }
        }
        type_dict = {
            'attr1': 'string',
            'attr2': 'integer'
        }
        type_key = 'type'

        updated_data_dictionary = update_attributes_with_type(data_dictionary, type_dict, type_key)

        assert updated_data_dictionary['entity1']['attributes']['attr1']['type'] == 'string'
        assert updated_data_dictionary['entity1']['attributes']['attr2']['type'] == 'integer'

    # Processes dictionaries with nested dashboard identifiers correctly
    def test_processes_nested_dashboard_identifiers(self):
        data = {
            'dashboard_identifier': {
                'id1': {'is_active': True},
                'id2': {'is_active': False}
            },
            'attributes': {
                'attr1': {
                    'dashboard_identifier': {
                        'id1': {'is_active': True},
                        'id2': {'is_active': False}
                    }
                }
            },
            'relationship_attributes': {
                'rel_attr1': {
                    'dashboard_identifier': {
                        'id1': {'is_active': True},
                        'id2': {'is_active': False}
                    }
                }
            }
        }
        expected_result = {
            'dashboard_identifier': {
                'id1': {'is_active': True}
            },
            'attributes': {
                'attr1': {
                    'dashboard_identifier': {
                        'id1': {'is_active': True}
                    }
                }
            },
            'relationship_attributes': {
                'rel_attr1': {
                    'dashboard_identifier': {
                        'id1': {'is_active': True}
                    }
                }
            }
        }
        result = process_dictionaries(data)
        assert result == expected_result

    # Transforms data dictionary with valid entities and attributes
    def test_transform_with_valid_entities_and_attributes(self, mocker):
        # Mocking the data_dict to simulate the shared filesystem data
        mock_data_dict = {
            'entity1': {
                'attributes': {
                    'field1': {'ui_visibility': True, 'group': 'enrichment'},
                    'field2': {'ui_visibility': False}
                }
            },
            'entity2': {
                'attributes': {
                    'field3': {'ui_visibility': True}
                }
            }
        }
        mocker.patch('commons.ei.ui_automation_utils.data_dict', mock_data_dict)

        entities = ['entity1', 'entity2']
        fields = ['field1', 'field3']
        inventory_fields = ['field1']
        type_dict = {'field1': 'typeA', 'field3': 'typeB'}
        disamb_type_dict = {'field1': 'disambTypeA'}
        start_date = '2023-01-01'
        end_date = '2023-12-31'

        result = transform_data_dictionary(entities, fields, inventory_fields, type_dict, disamb_type_dict, start_date,
                                           end_date)

        expected_result = {
            'entity1': {
                'attributes': {
                    'field1': {'ui_visibility': True, 'group': 'enrichment', 'disambiguatedType': 'typeA',
                               'nonDisambiguatedType': 'disambTypeA'}
                }
            },
            'entity2': {
                'attributes': {
                    'field3': {'ui_visibility': True, 'disambiguatedType': 'typeB'}
                }
            }
        }

        assert result == expected_result

    # Transforms relationship data dictionary based on provided lists and fields
    def test_transform_with_valid_relationships_and_fields(self, mocker):
        # Mock the relation_data_dict
        mock_relation_data_dict = {
            'entity1': {
                'rel1': {
                    'name': 'Relation 1',
                    'caption': 'Caption 1',
                    'isInverse': False,
                    'target_entity': 'entity2',
                    'entity_attributes': {
                        'field1': {'ui_visibility': True},
                        'field2': {'ui_visibility': False}
                    },
                    'relationship_attributes': {
                        'common_field1': {'ui_visibility': True, 'is_enrichment': True},
                        'common_field2': {'ui_visibility': False}
                    }
                }
            }
        }
        mocker.patch('commons.ei.ui_automation_utils.relation_data_dict', mock_relation_data_dict)

        # Define input parameters
        relationship_list = ['rel1']
        relationship_common_fields = ['common_field1']
        fields = ['field1']
        relation_fields = ['common_field1']
        start_date = '2023-01-01'
        end_date = '2023-12-31'

        # Call the function
        result = transform_relation_data_dictionary(relationship_list, relationship_common_fields, fields,
                                                    relation_fields, start_date, end_date)

        # Assert the transformation
        expected_result = {
            'entity1': {
                'rel1': {
                    'name': 'Relation 1',
                    'caption': 'Caption 1',
                    'sorting_columns': [],
                    'relationshipCheck': [],
                    'customFilter': [],
                    'isInverse': False,
                    'target_entity': 'entity2',
                    'entity_attributes': {
                        'field1': {'ui_visibility': True}
                    },
                    'relationship_attributes': {
                        'common_field1': {'ui_visibility': True, 'is_enrichment': True}
                    },
                    'dashboard_identifier': {}
                }
            }
        }
        assert result == expected_result

    # Returns correct candidate keys for valid data dictionary input
    def test_returns_correct_candidate_keys(self):
        data_dictionary = {
            'Entity1': {
                'attributes': {
                    'attr1': {'candidate_key': True},
                    'attr2': {'candidate_key': False},
                    'attr3': {'candidate_key': True}
                }
            },
            'Entity2': {
                'attributes': {
                    'attrA': {'candidate_key': False},
                    'attrB': {'candidate_key': True}
                }
            }
        }
        expected_result = {
            'Entity1': ['attr1', 'attr3'],
            'Entity2': ['attrB']
        }
        result = get_candidate_keys(data_dictionary)
        assert result == expected_result

    # Correct OLAP API response is processed and transformed into UI configuration


    # Correctly maps all properties from relation_data_dict to the result dictionary
    def test_correct_mapping_of_properties(self):
        relation_data_dict = {
            'entity1': {
                'relationship1': {
                    'attribute_type1': {
                        'field1': {
                            "type": "string",
                            "caption": "Field 1",
                            "description": "Description of field 1",
                            "min": 0,
                            "max": 100,
                            "range_selection": [0, 100],
                            "step_interval": 5,
                            "width": 200,
                            "column_merge": True,
                            "data_structure": "list",
                            "is_enrichment": True,
                            "dashboard_identifier": {"id": 123},
                            "detailed_view_hide": True,
                            "hideFeaturesDropdown": False
                        }
                    }
                }
            }
        }
        expected_result = {
            "header": "Field 1",
            "isDate": False,
            "accessorKey": 'field1',
            "description": "Description of field 1",
            "disableSortBy": True,
            "isExtra": False,
            "isEnrichment": True,
            "type": "string",
            "dashboard_identifier": {"id": 123},
            "width": 200,
            "detailedViewHide": True,
            "hideFeaturesDropdown": False,
            "min": 0,
            "max": 100,
            "range_selection": [0, 100],
            "step_interval": 5,
            "column_merge": True,
            "data_structure": "list"
        }
        result = map_relationship_properties(relation_data_dict, 'entity1', 'relationship1', 'attribute_type1', 'field1', False)
        assert result == expected_result

    # Correctly maps entity attributes to their respective categories based on group
    def test_correct_mapping_based_on_group(self, mocker):
        from commons.ei.ui_automation_utils import entity_column_mapping_build
        from commons.ei.ui_automation_utils import get_candidate_keys, properties_mapping, rename_keys
        from commons.ei.ui_automation_utils import lookup_dict

        # Mocking the dependent functions
        mocker.patch('commons.ei.ui_automation_utils.get_candidate_keys', return_value={'entity1': ['attr1']})
        mocker.patch('commons.ei.ui_automation_utils.rename_keys', side_effect=lambda x, y: x)
        mocker.patch('commons.ei.ui_automation_utils.properties_mapping', side_effect=lambda x, y, z: x)

        data_dictionary = {
            'entity1': {
                'attributes': {
                    'attr1': {
                        'group': 'common',
                        'type': 'string',
                        'description': 'Attribute 1',
                        'dashboard_identifier': {'solution1': {}}
                    },
                    'attr2': {
                        'group': 'entity_specific',
                        'type': 'integer',
                        'description': 'Attribute 2',
                        'dashboard_identifier': {'solution1': {}}
                    }
                }
            }
        }

        result = entity_column_mapping_build(data_dictionary)
        assert len(result['entity1']) == 4
        assert result['entity1'][0]['category'] == "Common"
        assert result['entity1'][1]['category'] == "Entity Specific"

    # Generates relationship configurations for each entity in the input dictionary
    def test_generate_relationship_config_for_entities(self, mocker):
        from commons.ei.ui_automation_utils import generate_relationship_config, rename_keys, \
            map_relationship_properties
        mocker.patch('commons.ei.ui_automation_utils.rename_keys', side_effect=rename_keys)
        mocker.patch('commons.ei.ui_automation_utils.map_relationship_properties',
                     side_effect=map_relationship_properties)

        relation_data_dictionary = {
            "Entity1": {
                "Relationship1": {
                    "caption": "Entity1 Relationship1",
                    "relationship_attributes": {
                        "attr1": {
                            "type": "string",
                            "caption": "Field1 Caption",
                            "description": "Field1 Caption",
                            "dashboard_identifier": {
                                "solution1": {"caption": "header"}
                            }
                        }
                    },
                    "entity_attributes": {
                        "field1": {
                            "type": "string",
                            "caption": "Field1 Caption",
                            "description": "Field1 Description"
                        }
                    },
                    "isInverse": False
                }
            }
        }

        expected_output = {
            "Entity1": [
                {
                    "title": "Entity1 Relationship1",
                    "relationship": "Relationship1",
                    "sortingColumns": [],
                    "relationshipCheck": [],
                    "customFilter": [],
                    "dashboard_identifier": {},
                    "relationshipColumns": [
                        {
                            "header": "Field1 Caption",
                            "isDate": False,
                            "accessorKey": "field1",
                            "description": "Field1 Description",
                            "disableSortBy": True,
                            "isExtra": True,
                            "isEnrichment": False,
                            "type": "string", "dashboard_identifier": {}, "width": None, "detailedViewHide": False, "hideFeaturesDropdown": False}, {"header": "Field1 Caption", "isDate": False, "accessorKey": "attr1", "description": "Field1 Caption", "disableSortBy": True, "isExtra": False,"isEnrichment": False,
                                                                                                                                                     "type": "string",
                                                                                                                                                     "dashboard_identifier": {
                                                                                                                                                         "solution1": {
                                                                                                                                                             "header": "header"
                                                                                                                                                         }
                                                                                                                                                     },
                                                                                                                                                     "width": None,
                                                                                                                                                     "detailedViewHide": False,
                                                                                                                                                     "hideFeaturesDropdown": False
                                                                                                                                                     }
                    ],
                    "targetEntity": None,
                    "queryField": "source_entity_class",
                    "inverse": False
                }
            ]
        }

        result = generate_relationship_config(relation_data_dictionary)
        assert result == expected_output

    # Merges common properties correctly based on accessorKey
    def test_merges_common_properties_based_on_accessor_key(self):
        entity_ui_mapping = [
            {
                "category": "Account",
                "config": {
                    "entityColumnMaps": [
                        {
                            "category": "Common",
                            "properties": [
                                {
                                    "enableHiding": True,
                                    "header": "Entity ID",
                                    "description": "Unique identifier generated for each entity by the Entity Inventory.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.",
                                    "accessorKey": "p_id",
                                    "type": "string",
                                    "removable": True,
                                    "isDate": False,
                                    "id": "p_id__0",
                                    "dashboard_identifier": {
                                        "EI": {},
                                        "Account": {}
                                    },
                                    "disableSortBy": True,
                                    "width": 40,
                                    "group": "common",
                                    "nonDisambiguatedType": "varchar"
                                },
                                {
                                    "enableHiding": False,
                                    "header": "Type",
                                    "description": "Represents the specific categorization or sub-classification of the entity",
                                    "accessorKey": "type",
                                    "type": "string",
                                    "removable": True,
                                    "isDate": False,
                                    "id": "type__0",
                                    "dashboard_identifier": {
                                        "EI": {},
                                        "Account": {},
                                        "CCM": {
                                            "data_structure": "list",
                                            "ccm_org_group_by_enabled": True
                                        }
                                    },
                                    "disableSortBy": True,
                                    "width": 40,
                                    "group": "common",
                                    "nonDisambiguatedType": "varchar"
                                }
                            ]
                        },
                        {
                            "category": "Entity Specific",
                            "properties": []
                        },
                        {
                            "category": "Source Specific",
                            "properties": []
                        },
                        {
                            "category": "Enrichment",
                            "properties": []
                        }
                    ],
                    "candidateKeys": [],
                    "hoverFields": []
                }
            },
            {
                "category": "Application",
                "config": {
                    "entityColumnMaps": [
                        {
                            "category": "Common",
                            "properties": [
                                {
                                    "enableHiding": True,
                                    "header": "Entity ID",
                                    "description": "Unique identifier generated for each entity by the Entity Inventory.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.",
                                    "accessorKey": "p_id",
                                    "type": "string",
                                    "removable": True,
                                    "isDate": False,
                                    "id": "p_id__1",
                                    "dashboard_identifier": {
                                        "EI": {},
                                        "Application": {}
                                    },
                                    "disableSortBy": True,
                                    "width": 40,
                                    "group": "common",
                                    "nonDisambiguatedType": "varchar"
                                },
                                {
                                    "enableHiding": False,
                                    "header": "Type",
                                    "description": "The classification of an application based on its functionality and role within a system.\nApplication entity encompass types such as Software,Tool,Application,System,Database.",
                                    "accessorKey": "type",
                                    "type": "string",
                                    "removable": True,
                                    "isDate": False,
                                    "id": "type__1",
                                    "dashboard_identifier": {
                                        "EI": {},
                                        "Application": {}
                                    },
                                    "disableSortBy": True,
                                    "width": 40,
                                    "group": "common",
                                    "nonDisambiguatedType": "varchar"
                                }
                            ]
                        },
                        {
                            "category": "Entity Specific",
                            "properties": []
                        },
                        {
                            "category": "Source Specific",
                            "properties": []
                        },
                        {
                            "category": "Enrichment",
                            "properties":[]
                        }
                    ],
                    "candidateKeys": [
                        "app_name"
                    ],
                    "hoverFields": []
                }
            }
        ]

        expected_output = [
            {
                "enableHiding": True,
                "header": "Entity ID",
                "description": "Unique identifier generated for each entity by the Entity Inventory.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.",
                "accessorKey": "p_id",
                "type": "string",
                "removable": True,
                "isDate": False,
                "id": "p_id__0",
                "dashboard_identifier": {
                    "EI": {},
                    "Account": {},
                    "Application": {}
                },
                "disableSortBy": True,
                "width": 40,
                "group": "common",
                "nonDisambiguatedType": "varchar"
            },
            {
                "enableHiding": False,
                "header": "Type",
                "description": "Represents the specific categorization or sub-classification of the entity",
                "accessorKey": "type",
                "type": "string",
                "removable": True,
                "isDate": False,
                "id": "type__0",
                "dashboard_identifier": {
                    "EI": {},
                    "Account": {},
                    "CCM": {
                        "data_structure": "list",
                        "ccm_org_group_by_enabled": True
                    },
                    "Application": {}
                },
                "disableSortBy": True,
                "width": 40,
                "group": "common",
                "nonDisambiguatedType": "varchar"
            }
        ]

        result = prepare_merged_common_properties(entity_ui_mapping)
        assert result == expected_output

    # Ensures that only keys starting with 'width___' are processed
    def test_only_width_keys_are_processed(self, mocker):
        mock_olap_api = mocker.patch('commons.ei.ui_automation_utils.GetOlapResponse')
        mock_olap_api_instance = mock_olap_api.return_value
        mock_olap_api_instance.get_response.return_value = {
            'data': [{
                'sqlQuery': {
                    'data': {
                        'width___column1': 100,
                        'height___column2': 200,
                        'width___column3': 300
                    }
                }
            }]
        }
        query = "SELECT * FROM table"
        expected_result = {
            'column1': {'width': 100},
            'column3': {'width': 300}
        }
        result = extract_column_statistics(query)
        assert result == expected_result

    # The function successfully retrieves data using the OLAP API and processes it
    def test_successful_data_retrieval_and_processing(self, mocker):
        mock_olap_api = mocker.patch('commons.ei.ui_automation_utils.GetOlapResponse')
        mock_utils = mocker.patch('commons.pe.common_utils.Utils')
        mock_transform_data_dictionary = mocker.patch('commons.ei.ui_automation_utils.transform_data_dictionary')
        mock_transform_relation_data_dictionary = mocker.patch('commons.ei.ui_automation_utils.transform_relation_data_dictionary')
        mock_entity_column_mapping_build = mocker.patch('commons.ei.ui_automation_utils.entity_column_mapping_build')
        mock_get_candidate_keys = mocker.patch('commons.ei.ui_automation_utils.get_candidate_keys')
        mock_prepare_merged_common_properties = mocker.patch('commons.ei.ui_automation_utils.prepare_merged_common_properties')
        mock_generate_relationship_config = mocker.patch('commons.ei.ui_automation_utils.generate_relationship_config')

        # Mocking the OLAP API response
        mock_olap_api_instance = mock_olap_api.return_value
        mock_olap_api_instance.get_response.return_value = {}
        mock_olap_api_instance.extract_data.return_value = []
        mock_olap_api_instance.extract_type_dict.return_value = {}

        # Mocking other function responses
        mock_transform_data_dictionary.return_value = {}
        mock_transform_relation_data_dictionary.return_value = {}
        mock_entity_column_mapping_build.return_value = {}
        mock_get_candidate_keys.return_value = {}
        mock_prepare_merged_common_properties.return_value = []
        mock_generate_relationship_config.return_value = {}

        result = ui_configuration(start_date='2023-01-01', end_date='2023-12-31')

        assert isinstance(result, str)
        assert 'entity_inventory' in result

    # Generates columns correctly for timestamp_t fields with datepickerSearch filter
    def test_generate_columns_for_timestamp_t(self, mocker):
        from commons.ei.ui_automation_utils import generate_relation_columns

        relation_data_dictionary = {
            "entity1": {
                "relationship1": {
                    "relationship_attributes": {
                        "field1": {
                            "type": "timestamp_t",
                            "caption": "Timestamp Field"
                        }
                    }
                }
            }
        }

        expected_output = [{
            "header": "Timestamp Field",
            "enableHiding": True,
            "removable": True,
            "filterType": "datepickerSearch",
            "isDate": 'true',
            "accessorKey": "field1",
            "disableSortBy": True,
            "isExtra": False,
            "dataType": 'integer',
            "dashboard_identifier": {},
            "dataSource": "sds_ei_relationship"
        }]

        result = generate_relation_columns(relation_data_dictionary)
        assert result == expected_output