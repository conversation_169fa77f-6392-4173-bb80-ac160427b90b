from commons.pe.common_utils import Utils
from commons.ei.ei_constants.ei_constants import EIConstants
from urllib.parse import urljoin
import requests
from commons.pe.common_utils import Utils
import json,os,logging
from airflow.models.connection import Connection
from airflow.models.variable import Variable
from commons.pe.common_utils import Utils
import re


ssl_verify = Variable.get("MANAGEMENT_API_SSL_VERIFY", default_var=True, deserialize_json=True)

def check_shared_config(relationship_dag,entity_dag):
        entity_configs = Utils.read_data_from_shared_filesystem(
        relative_file_path=EIConstants.DYNAMIC_DAG_GEN_VARS
        )
        regex =  r"^(?:sds_(?:\w+?)__rel__[^\r\n]+$|sds_(?:\w+?)__(\w+?)(?:__[^\r\n]*|$))" if entity_dag else r"sds_(?:\w+?)__rel__(\w+?)(?:__[^\r\n]*|$)"
        unique_items = set()
        for key, value in entity_configs["publisher"].items():
            if isinstance(value, list): 
                unique_items.update(value)
        return {match.group(1) for s in unique_items if (match := re.search(regex, s)) and match.group(1) is not None}
        
entity_list = check_shared_config(relationship_dag=False,entity_dag=True)
relationship_list = check_shared_config(relationship_dag=True,entity_dag=False)


class EntityDatadictionaryValidator:
    def __init__(self):
        self.keycloak_host = os.getenv("KEYCLOAK_HOST")
        self.headers = {'Content-Type': 'application/json',"Authorization": Utils.get_keycloak_token()}
        self.logger = logging.getLogger("ei_data_dictionary_validator")
        self.logger.info(self.headers)
        self.conn = Connection.get_connection_from_secrets("management_api")
        self.HOST = self.conn.host
        self.PORT = self.conn.port
        self.SCHEMA = self.conn.schema
        self.url = urljoin(f"{self.SCHEMA}://{self.HOST}:{self.PORT}/", EIConstants.DATA_DICTIONARY_API_ENDPOINT)
        self.logger.info(self.url)
        from pyiceberg.catalog import load_catalog
        self.catalog = load_catalog("default")
            
    def get_entity_name(self,**kwargs):
        if  'entity' in kwargs: 
            table_name=kwargs['entity']
            entity_data_dict = f'{table_name}__data_dictionary'
        elif  'relation' in kwargs: 
            table_name=kwargs['relation']
            entity_data_dict = table_name
        else:
            # If neither 'entity' nor 'relation' are found in kwargs
            self.logger.error("Neither 'entity' nor 'relation' found in kwargs")
            return None
        return (self.get_api_response(entity_data_dict))

    def get_api_response(self,entity_data_dict):
        if entity_data_dict is not None:
            api_url = f"{self.url}{entity_data_dict}"
            self.logger.info(api_url)
            try:
                # SSL Needed
                response = requests.get(api_url,headers=self.headers, verify=ssl_verify)
                self.logger.info(self.headers)
                response.raise_for_status()
                data_dictionary=response.json()
                self.logger.info(data_dictionary)
                self.logger.info(f"name: {data_dictionary['name']}")
                if data_dictionary == {}:
                    self.logger.info("No data dictionary found")
                    self.logger.info(f"No data dictionary found.{data_dictionary}")
                else:
                    data_dictionary['solution'] = data_dictionary.pop('solution_name')
                    data_dictionary['entity'] = data_dictionary.pop('entity_name')
                    data_dictionary['data_source_feed'] = data_dictionary.pop('data_source_feed_name')
                    if data_dictionary['config_item_type']=='data_dictionary_config':
                        return self.fetch_schema(data_dictionary,"entity")
                    else:
                        return self.fetch_schema(data_dictionary,"relationship")
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Error: {e}")
                raise Exception(f"Unable to get the response Error: {e}")
            


    def fetch_schema(self,response,data_dict_type):
        if data_dict_type=="entity":
            schema_name = Variable.get("EI_PUBLISH_SCHEMA_NAME", default_var=None)  # Get schema names
            fragment_schema_name = Variable.get("EI_FRAGMENT_PUBLISH_SCHEMA_NAME", default_var=None)
            prefix = "sds_ei__"
        else :
            schema_name = Variable.get("EI_RELATIONSHIP_SCHEMA_NAME", default_var=None)  # Get schema names
            fragment_schema_name = Variable.get("KG_FRAGMENT_SCHEMA", default_var=None)
            prefix = "sds_ei__rel__"
        
        self.logger.info(f"{data_dict_type}_schema:{schema_name}, {data_dict_type}_fragments_schema:{fragment_schema_name}")

        if not schema_name or not fragment_schema_name:
            self.logger.error(f"{data_dict_type.capitalize()} schema or fragment schema is missing.")
            raise Exception(f"No request processed. Error: schema or fragment schema is missing.")
        try:
            publish_tables=self.catalog.list_tables(schema_name)
            fragment_tables=self.catalog.list_tables(fragment_schema_name)
            self.logger.info(f"{data_dict_type}_publish_tables:{publish_tables} and {data_dict_type}_fragments_tables:{fragment_tables}")

            if not publish_tables and not fragment_tables:
                self.logger.info(f"No tables found in {data_dict_type}_publish_tables. {data_dict_type}_publish_tables: {publish_tables}. No tables found in {data_dict_type}_fragments_tables. {data_dict_type}_fragments_tables: {fragment_tables}.")
                return None
            api_table_name = response['name'].split('__', 2)[0]
            self.logger.info(f"api_table_name {api_table_name}")

            table_name = f"{prefix}{api_table_name}__publish"
            fragment_table_name = f"{prefix}fragment__{api_table_name}"

            table_check = self.find_table(table_name, fragment_table_name, publish_tables, fragment_tables, schema_name, fragment_schema_name)

            if data_dict_type == "entity":
                enrich_check_data = self.enrich_check(table_check, response)
            elif data_dict_type == "relationship":
                enrich_check_data = self.enrich_rel_check(table_check, response)

            if enrich_check_data:
                self.logger.info("Successfully posted in Data Dictionary Api")
            else:
                self.logger.info("Post error in Data Dictionary Api")
                raise Exception("No request processed.")
            
        except KeyError as e:
            self.logger.info(f"Schema not found: {e}")
            raise Exception(f"No request processed. Error: {e}")
        except Exception as e:
            self.logger.error(f"An unexpected error occurred: {e}")
            self.logger.info(f"No tables found in {data_dict_type}_publish_tables: {publish_tables}. No tables found in {data_dict_type}_fragments_tables: {fragment_tables}.")
            raise Exception(f"No request processed. Error: {e}")

            
    def find_table(self,ei_table_name, ei_fragment_name, entity_publish_tables, entity_fragments_tables,publish_schema,entity_fragments_schema):
        # Print input parameters for debugging
        self.logger.info(f"Searching for table: {ei_table_name} in schema: {publish_schema}")
        self.logger.info(f"entity_publish_tables: {entity_publish_tables}")

        # Check if the table exists in the publish schema
        if (publish_schema, ei_table_name) in entity_publish_tables:
            ei_table_present = True
        else:
            ei_table_present = False

        self.logger.info(f"Searching for table: {ei_fragment_name} in schema: {entity_fragments_schema}")
        self.logger.info(f"entity_fragment_tables: {ei_fragment_name}")
        
        # Check if the table exists in the fragment schema
        if (entity_fragments_schema, ei_fragment_name) in entity_fragments_tables:
            fragment_table_present = True
        else:
            fragment_table_present = False
    
        # Print results for debugging
        self.logger.info(f"ei_table_present: {ei_table_present}, fragment_table_present: {fragment_table_present}")


        return({'ei_table_present':ei_table_present,'fragment_table_present':fragment_table_present,'ei_table_name':ei_table_name,'ei_fragment_name':ei_fragment_name,'publish_schema':publish_schema,'entity_fragments_schema':entity_fragments_schema})
    

    def enrich_check(self,table_check, response):
        enriched_group=False
        attributes_updates = {}
        # Iterate over all attributes to check if they belong to the 'enrichment' group
        for attribute_name, attribute_details in response["config_value"]["attributes"].items():
            if "group" in attribute_details and attribute_details["group"] == "enrichment":
                enriched_group=True
                self.check_enrichment_attribute(enriched_group,table_check, attribute_name,response,attributes_updates)
            else:
                enriched_group=False
                self.check_enrichment_attribute(enriched_group,table_check, attribute_name,response,attributes_updates)

        return self.post_response(response, table_check['ei_table_name'], attributes_updates,"entity")
    
    def enrich_rel_check(self,table_check, response):
        enriched_group=False
        attributes_updates = {}
        if response['config_item_type']=='relationship_data_dictionary_config':
            for attribute_name, attribute_details in response["config_value"]["relationship_attributes"].items():
                if "is_enrichment" in attribute_details and attribute_details["is_enrichment"] is True:
                    enriched_group=True
                    self.check_enrichment_attribute(enriched_group,table_check, attribute_name,response,attributes_updates)
                else:
                    enriched_group=False
                    self.check_enrichment_attribute(enriched_group,table_check, attribute_name,response,attributes_updates)
        return self.rel_entity_check(response,attributes_updates,table_check)
          
    def rel_entity_check(self,response,attributes_updates,table_check):
        publish_schema = Variable.get("EI_PUBLISH_SCHEMA_NAME", default_var=None)  # Get schema names
        source_entity_table=response["config_value"]["source_entity"].replace(" ", "_")  #name_change
        target_entity_table=response["config_value"]["target_entity"].replace(" ", "_")  #name_change
        source_ei_table_name = f'sds_ei__{str(source_entity_table).lower()}__publish'
        target_ei_table_name = f'sds_ei__{str(target_entity_table).lower()}__publish'
        self.logger.info(attributes_updates)
        entity_attributes_updates={}
        self.logger.info(f"source_ei_table_name :{source_ei_table_name} and target_entity_table :{target_ei_table_name}")
        try:
            entity_publish_tables=self.catalog.list_tables(publish_schema)
            if entity_publish_tables ==[] :
                self.logger.info(f"No tables found in entity_publish_tables. entity_publish_tables : {entity_publish_tables}.")
                return False
            else:
                rel_entity_table_check=self.find_table(source_ei_table_name, target_ei_table_name, entity_publish_tables, entity_publish_tables,publish_schema,publish_schema)
                self.rel_entity_attribute_check(rel_entity_table_check,response,entity_attributes_updates)
                self.logger.info(f"final_entity_attributes_updates : {entity_attributes_updates}")
        except KeyError as e:
            self.logger.info(f"Schema not found: {e}")
            raise Exception(f"No request processed. Error: {e}")
        except Exception as e:
            self.logger.error(f"An unexpected error occurred: {e}")
            raise Exception(f"No request processed. Error: {e}")
        return self.post_response(response, table_check['ei_table_name'],attributes_updates ,"relationship",entity_attributes_updates)
    

    def rel_entity_attribute_check(self,rel_entity_table_check,response,entity_attributes_updates):
        for attribute_name,attribute_details in response["config_value"]["entity_attributes"].items():
            self.logger.info(f"attribute_name : {attribute_name}")
            if rel_entity_table_check['fragment_table_present'] ==True:
                ei_target_entity_attribute_present = self.check_attributes(attribute_name, rel_entity_table_check['ei_fragment_name'], rel_entity_table_check['entity_fragments_schema'])
                if ei_target_entity_attribute_present:
                    self.logger.info(f"{attribute_name} is present in EI target table.")
                    target_entity_data_type=self.check_data_type(attribute_name,rel_entity_table_check['ei_fragment_name'], rel_entity_table_check['publish_schema'])
                    entity_attributes_updates[attribute_name] = {
                        "is_available_in_datalake" : True,
                        "type" : target_entity_data_type
                        }
                    self.logger.info(entity_attributes_updates)
                else:
                    self.logger.info("Attribute is not present in Target table.")
                    entity_attributes_updates[attribute_name] = {
                        "is_available_in_datalake" : False
                        }
            else:
                target_table={rel_entity_table_check['ei_fragment_name']}    
                self.logger.info(f"Target Entity is not present.{target_table}")
                raise Exception(f"Target entity should be present.")


    def check_enrichment_attribute(self,enriched_group,table_check, attribute_name,response,attributes_updates):
        if table_check['ei_table_present'] ==True and table_check['fragment_table_present']==True:
                response["config_value"]["is_available_in_datalake"]=True

        if enriched_group==True and table_check['ei_table_present'] ==True:
                self.logger.info(f"Is enriched for this attribute  :{enriched_group}")
                ei_publish_attribute_present = self.check_attributes(attribute_name, table_check['ei_table_name'], table_check['publish_schema'])
                if ei_publish_attribute_present:
                    self.logger.info(f"The {attribute_name} is present in EI_publish.")
                    publish_attribute_type=self.check_data_type(attribute_name,table_check['ei_table_name'], table_check['publish_schema'])
                    attributes_updates[attribute_name] = {
                        "is_available_in_datalake" : ei_publish_attribute_present,
                        "type" : publish_attribute_type
                        }
                else:
                    self.logger.info(f"The {attribute_name} is not present in EI_publish.")
                    attributes_updates[attribute_name] = {"is_available_in_datalake" : ei_publish_attribute_present}

        elif enriched_group==False and table_check['ei_table_present']==True and table_check['fragment_table_present']==True:
                self.logger.info(f"Is enriched for this attribute  :{enriched_group}")
                ei_publish_attribute_present = self.check_attributes(attribute_name, table_check['ei_table_name'], table_check['publish_schema'])
                ei_fragment_attribute_present = self.check_attributes(attribute_name, table_check['ei_fragment_name'], table_check['entity_fragments_schema'])
                self.logger.info(f'ei_publish_attribute_present :{ei_publish_attribute_present} and ei_fragment_attribute_present : {ei_fragment_attribute_present}')

                if ei_publish_attribute_present and ei_fragment_attribute_present:
                    self.logger.info(f"The {attribute_name} is present in both EI_publish and EI_fragment_table.")
                    attributes_updates[attribute_name] = {"is_available_in_datalake" : ei_publish_attribute_present}
                    publish_attribute_type=self.check_data_type(attribute_name,table_check['ei_table_name'], table_check['publish_schema'])
                    fragment_attribute_type=self.check_data_type(attribute_name,table_check['ei_fragment_name'], table_check['entity_fragments_schema'])
                    self.logger.info(f'publish_attribute_type:{publish_attribute_type} and fragment_attribute_type :{fragment_attribute_type}')
                    attributes_updates[attribute_name]["type"] = publish_attribute_type
                    attributes_updates[attribute_name]["nonDisambiguatedType"] = fragment_attribute_type
                else:
                    self.logger.info(f"The {attribute_name} is not present in both EI_publish and EI_fragment_table.")
                    attributes_updates[attribute_name] = {"is_available_in_datalake" : False}
        else:
            response["config_value"]["is_available_in_datalake"] = False
            attributes_updates[attribute_name]= {
                "is_available_in_datalake" : False
                }
            self.logger.info(f"EI_publish = {table_check['ei_table_present']} and EI_fragment_table = {table_check['fragment_table_present']}.")


    def load_icebrg_schema(self,table_name,publish_schema):
        if publish_schema and table_name:
            table = self.catalog.load_table(f"{publish_schema}.{table_name}")
            table_schema = table.schema()
            self.logger.info(table_schema)
            self.logger.info(f"table_name schema :{table_name}")
        else:
            self.logger.info("Invalid schema or table name")
        return(table_schema)

    def check_attributes(self,attribute_name,table_name,publish_schema):
        table_schema=self.load_icebrg_schema(table_name,publish_schema)
        schema_dict = {column.name: column.field_type for column in table_schema.columns}
        self.logger.info(schema_dict)
        self.logger.info(f"Checking the attribute : {attribute_name} in the table {publish_schema}.{table_name}")
        attribute_present = attribute_name in schema_dict
        if attribute_present:
            self.logger.info(f"{attribute_name} is present in the schema.")
        else:
            self.logger.info(f"{attribute_name} is not present in the schema.")
        return(attribute_present)
    

    def check_data_type(self,attribute_name,table_name,schema_name):
        list_int = "list<int>"
        list_string = "list<string>"
        list_float = "list<float>"

        type_mapping = {
        "stringtype": "string",
        "integertype" : "int",
        "string": "string",
        "struct": "string",
        "array<struct>": "string",
        "list<struct>": "string",
        "int": "int",
        "long":"int",
        "double":"float",
        "decimal": "float",
        "float":"float",
        "list<double>":list_float,
        "list<float>":list_float,
        "list<decimal>":list_float,
        "list<int>": list_int,
        "list<long>": list_int, 
        "list<string>": list_string,
        "list<array>": "string",
        "array<list>":"string",
        "array<double>": list_float,
        "array<float>": list_float,
        "array<decimal>": list_float,
        "array<int>": list_int,
        "array<long>": list_int,
        "array<string>": list_string,
        "array<array>": "string",
        "list<list>":"string",  
        "timestamp": "timestamp",
        "timestamptz": "timestamp",
        "datetime.date":"timestamp",
        "boolean": "boolean"
        }

        table_schema=self.load_icebrg_schema(table_name,schema_name)
        self.logger.info(f"Checking for the attribute data_type :{schema_name}.{table_name}")
        for column in table_schema.columns:
            if column.name == attribute_name:
                field_type = str(column.field_type).lower()
                self.logger.info(f"The data_type of the attribute {attribute_name}: {column.field_type}")
                
                if field_type in type_mapping:
                    field_type = type_mapping[field_type]
                    self.logger.info(f"The changed data_type of the attribute {attribute_name}: {field_type}")
                else:
                    field_type = "string"
                    self.logger.info(f"No specific mapping found for field_type {field_type}. Defaulting to 'string'.")
                return str(field_type)

        return None

    def full_data_dict_check(self,**kwargs):
        entity_dag=kwargs['entity_dag']
        relationship_dag=kwargs['relationship_dag']
        api_url = f"{self.url}"
        self.logger.info(api_url)
        try:
            response = requests.get(api_url,headers=self.headers, verify=ssl_verify)
            response.raise_for_status()
            data_dictionary=response.json()
            self.logger.info(data_dictionary)
            names = []
            for item in data_dictionary:
                if entity_dag==True:
                    if "config_item_type" in item and item["config_item_type"] == "data_dictionary_config" and "name" in item and 'relationship_data_dictionary' not in item["name"]:
                        trimmed_name = item["name"].split('__', 2)[0]
                        names.append(trimmed_name)
                elif relationship_dag==True:
                    if "config_item_type" in item and item["config_item_type"] == "relationship_data_dictionary_config" and "name" in item:
                        trimmed_name = item["name"]
                        names.append(trimmed_name)
                else:
                    self.logger.info("Please assign which flow to run OR check the data dictionary api if there is any value.")
            return self.publish_and_api_validation(names,entity_dag,relationship_dag)
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error: {e}")
            raise Exception(f"No request processed. Error: {e}")
            

    def publish_and_api_validation(self,names,entity_dag,relationship_dag):
        if entity_dag == True :
            extra_in_api = [item for item in names if item not in entity_list]
            entity_data_dict=''
            if extra_in_api:
                for i in extra_in_api:
                    entity_data_dict = f'{i}__data_dictionary'
                    self.logger.info(f"Requesting missing config: {entity_data_dict}")
                    self.get_api_response(entity_data_dict)
            else:
                self.logger.info("No missing config from the data dictionary.")
        elif relationship_dag==True:
            extra_in_api = [item for item in names if item not in relationship_list]
            entity_data_dict=''
            if extra_in_api:
                for i in extra_in_api:
                    entity_data_dict = i
                    self.logger.info(f"Requesting missing config: {entity_data_dict}")
                    self.get_api_response(entity_data_dict)
            else:
                self.logger.info("No missing config from the relationship data dictionary.")
        else:
            return None
        
    def add_graph_reference_name(self,response,config_item_type):

        if "config_value" not in response:
            return response
        config_value = response["config_value"]
        if config_item_type == "entity":
            if "caption" in config_value:
                graph_reference = config_value["caption"].lower().replace(" ", "_")
                response["config_value"]["graph_reference_name"] = graph_reference
        elif config_item_type == "relationship":
            source_value = None
            if "isInverse" in config_value and config_value["isInverse"] is True:
                if "inverse_relationship_name" in config_value:
                    source_value = config_value["inverse_relationship_name"]
            else:
                if "caption" in config_value:
                    source_value = config_value["caption"]
            if source_value:
                graph_reference = source_value.lower().replace(" ", "_")
                response["config_value"]["graph_reference_name"] = graph_reference
        return response


    def post_response(self,response,publish_table,attributes_updates,config_item_type=None,entity_attributes_updates=None):
        try:
            response["config_item_level"]="client"

            for field in ["client_revision", "updated_at", "updated_by", "created_at", "created_by"]:
                response.pop(field, None)

            if entity_attributes_updates is not None and config_item_type=="relationship":
                for attribute_name, update in attributes_updates.items():
                    response["config_value"]["relationship_attributes"][attribute_name].update(update)

                for attribute_name, update in entity_attributes_updates.items():
                    response["config_value"]["entity_attributes"][attribute_name].update(update)

                self.logger.info(f"attributes_updates :{attributes_updates},entity_attributes_updates : {entity_attributes_updates}")
                data_api_table_name = publish_table.replace('sds_ei__rel__','').replace('__publish','')

            else:
                for attribute_name, update in attributes_updates.items():
                    response["config_value"]["attributes"][attribute_name].update(update)
                    
                self.logger.info(f"attributes_updates :{attributes_updates}")
                data_api_table_name = publish_table.replace('sds_ei__', '').replace('__publish', '')

            self.logger.info(f"Response: {response}")
            self.logger.info(f"Table name: {data_api_table_name}")
            if self.url.endswith('/'):
                    url = self.url[:-1]
            response = self.add_graph_reference_name(response,config_item_type)
            self.logger.info(f"Response: {response}")
            put_response = requests.post(url, data=json.dumps(response), headers=self.headers, verify=ssl_verify)
            self.logger.info(f"Response: {put_response.content}, status_code: {put_response.status_code}")
            put_response.raise_for_status()
            return True
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error: {e}")
            return False

