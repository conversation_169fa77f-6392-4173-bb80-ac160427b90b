import os
import json
import boto3
from typing import List, Dict
from urllib.parse import urlparse
import requests
import logging
import re
from airflow.models.variable import Variable
from botocore.exceptions import ClientError
from commons.pe.common_utils import Utils
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


ssl_verify = Variable.get("MANAGEMENT_API_SSL_VERIFY", default_var=True, deserialize_json=True)

class PublisherConfigReader:
    def __init__(self, list_path: str):
        if not list_path:
            raise Exception(
                "Publisher configs list path is empty. Please set EI_PUBLISHER_CONFIGS_LIST_PATH environment variable.")
        self.list_path = list_path

        self.ei_spark_configs_path = Variable.get('EI_PUBLISHER_CONFIGS_BASE_PATH')
        self.headers = {'Content-Type': 'application/json', "Authorization": Utils.get_keycloak_token()}

    def get_s3_content(self, bucket: str, key: str) -> str:
        try:
            s3_client = boto3.client('s3')
            response = s3_client.get_object(Bucket=bucket, Key=key)
            return response['Body'].read().decode('utf-8')
        except ClientError as e:
            logger.error(f"Error reading from S3: {str(e)}")
            raise

    def parse_s3_path(self, s3_path: str) -> tuple:
        parsed = urlparse(s3_path)
        bucket = parsed.netloc
        key = parsed.path.lstrip('/')
        return bucket, key

    def load_http_configs(self) -> List[Dict]:
        if not self.ei_spark_configs_path:
            raise Exception(
                "Publisher configs base path is empty. Please set EI_PUBLISHER_CONFIGS_BASE_PATH environment variable.")
        configs = []
        response = requests.get(self.list_path,headers=self.headers, verify=ssl_verify)
        response.raise_for_status()
        config_items = response.json()

        for item in config_items:
            config_url = f"{self.ei_spark_configs_path}{item['name']}"
            try:
                config_response = requests.get(config_url,headers=self.headers, verify=ssl_verify)
                config_response.raise_for_status()
                configs.append(config_response.json())
            except requests.exceptions.RequestException as e:
                logger.error(f"Error fetching config for {item['name']}: {str(e)}")

        return configs

    def load_s3_configs(self) -> List[Dict]:
        configs = []
        bucket, prefix = self.parse_s3_path(self.list_path)
        s3_client = boto3.client('s3')

        paginator = s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
            if 'Contents' in page:
                for obj in page['Contents']:
                    try:
                        content = self.get_s3_content(bucket, obj['Key'])
                        config_data = json.loads(content)
                        configs.append(config_data)
                    except json.JSONDecodeError as e:
                        logger.error(f"Error parsing config file {obj['Key']}: {str(e)}")

        return configs

    def get_configs(self) -> List[Dict]:
        try:
            parsed_uri = urlparse(self.list_path)

            if parsed_uri.scheme in ('http', 'https'):
                return self.load_http_configs()
            elif parsed_uri.scheme == 's3':
                return self.load_s3_configs()
            else:
                raise ValueError(f"Unsupported URI scheme: {parsed_uri.scheme}")

        except Exception as e:
            logger.error(f"Error loading publisher configs: {str(e)}")
            raise

    def extract_entity_from_table(self, table_name: str) -> str:
        try:
            pattern = r'(?<=__)[^_]+(?:_[^_]+)*(?=__publish)'
            match = re.search(pattern, table_name)
            if match:
                logger.info(f"Extracting the entity name {match.group()}")
                return match.group()

        except Exception as e:
            logger.error(f"Error extracting entity from table name {table_name}: {str(e)}")
        return None
    def get_entity_table_mapping(self) -> Dict[str, str]:
        configs = self.get_configs()
        entity_mapping = {}
        for config in configs:
            if (config.get("isOLAPTable",True)) and (config.get("transformSpec", {}).get("tableInfo", {}).get("tableType") == "entity"):
                table_path = config['outputTableInfo']['outputTableName']
                table_name = self.extract_entity_from_table(table_path)
                if table_name:
                    entity_mapping[table_name] = table_path
        logger.info(f"Extracted isOLAPTable True entities are {entity_mapping}")
        return entity_mapping
