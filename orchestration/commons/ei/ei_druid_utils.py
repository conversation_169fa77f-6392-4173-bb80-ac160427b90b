"""
This module contains functions for fetching schema from druid used in EI UI Automation
"""

import json,os,logging
from airflow.models.connection import Connection
import requests
from urllib.parse import urljoin
from commons.pe.common_utils import Utils
from airflow.models.variable import Variable

class GetOlapResponse:

    def get_response(self, payload_data):
        olap_insight_api_host = os.getenv("OLAP_INSIGHT_API_HOST", os.getenv("KEYCLOAK_HOST"))
        olap_insight_api_endpoint = os.getenv("OLAP_INSIGHT_API_ENDPOINT")
        api_url = f"{olap_insight_api_host}{olap_insight_api_endpoint}"
        headers = {"Authorization": Utils.get_keycloak_token()}
        try:
            insight_api_ssl_verify = Variable.get("INSIGHT_API_SSL_VERIFY", default_var=True, deserialize_json=True)
            response = requests.post(api_url, json=payload_data, headers=headers, verify=insight_api_ssl_verify)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logging.error(f"Error: {e}")
            return {}

    @staticmethod
    def extract_data(response_data, data_label, key):
        if response_data:
            return [data_item.get(key) for item in response_data.get("data", [])
                    for data_item in item.get(data_label, {}).get("data", [])]
        else:
            return None

    @staticmethod
    def extract_type_dict(olap_api_response, field_key):
        return {
            field['COLUMN_NAME']: 'string' if field['DATA_TYPE'] == 'CHARACTER VARYING' else field['DATA_TYPE'].lower()
            for entry in olap_api_response['data'] if field_key in entry
            for field in entry[field_key]['data']
        }
