"""
This module contains functions used in EI UI Automation
"""
import copy
import json
import re
import io
import logging

from commons.ei.ei_druid_utils import GetOlapResponse
from commons.ei.ei_constants.common_constants import DataDictionaryPath,OLAPConnConstants
from commons.pe.common_utils import Utils


data_dict = Utils.read_data_from_shared_filesystem(relative_file_path=DataDictionaryPath.SDS_EI_DATA_DICTIONARY_PATH)
reference_entity_data_dict = Utils.read_data_from_shared_filesystem(relative_file_path=DataDictionaryPath.SDS_EI_REFERENCE_DATA_DICTIONARY_PATH)

ei_graph_data_dict = Utils.read_data_from_shared_filesystem(
    relative_file_path=DataDictionaryPath.SDS_EI_GRAPH_DATA_DICTIONARY_PATH)
relation_data_dict = Utils.read_data_from_shared_filesystem(
    relative_file_path=DataDictionaryPath.SDS_EI_RELATIONSHIP_DATA_DICTIONARY_PATH)
reference_relation_data_dict = Utils.read_data_from_shared_filesystem(relative_file_path=DataDictionaryPath.SDS_EI_REFERENCE_RELATIONSHIP_DATA_DICTIONARY_PATH)
query_data = Utils.read_data_from_shared_filesystem(relative_file_path=OLAPConnConstants.SDS_EI_OLAP_QUERY_CONFIG_PATH)

def fetch_column_stats(data_dictionary_conf):
    width_columns = set()
    for entity_type, entity_data in data_dictionary_conf.items():
        logging.info(f"Entity type:{entity_type}")
        logging.info(f"Entity data:{entity_data}")
        attributes = entity_data.get("attributes", {})
        for attr_name, attr_data in attributes.items():
            if ("width" in attr_data and attr_data["width"] > 0):
                width_columns.add(attr_name)
    logging.info(f"Width columns:{width_columns}")
    return {"width_columns": width_columns}


def fetch_column_stats_rel(data_dictionary_conf):
    result = {}
    for entity_name, entity_data in data_dictionary_conf.items():
        for sub_entity_name, sub_entity_data in entity_data.items():
            for attribute_type, attribute_group in [('entity_attributes', sub_entity_data.get('entity_attributes', {})),
                                                    ('relationship_attributes',
                                                     sub_entity_data.get('relationship_attributes', {}))]:
                width_columns = set()
                for attr_name, attr_data in attribute_group.items():
                    if ("width" in attr_data and attr_data["width"] > 0):
                        width_columns.add(attr_name)
                result[attribute_type] = {"width_columns": width_columns}
    logging.info(f"Column stats (attribute type and width columns):{result}")
    return result


def generate_sql_query(columns_dict, table_name, start_date, end_date):
    width_columns = columns_dict.get('width_columns', [])
    sql_query = {"dataQuery": [],"moduleName":"SDS_INSIGHTS_ENTITY"}
    if width_columns:
        width_columns_sql = [f"MAX(LENGTH({col})) withAlias width___{col}" for col in width_columns]
        sql_query["dataQuery"].append({
            "query": {
                "dataSource": {
                    "dataSource": table_name
                },
                "queryParams": {
                    "fields": width_columns_sql,
                    "aggregate": [],
                    "group": [],
                    "filter": f"__time BETWEEN TIMESTAMP '{start_date}' AND TIMESTAMP '{end_date}'",
                    "sort": [],
                    "limit": ""
                }
            },
            "dataLabel": "sqlQuery"
        })
        logging.info(f"Sql Query: {sql_query}")
        return sql_query
    else:
        return None



def extract_column_statistics(query):
    olap_api = GetOlapResponse()
    olap_api_response = olap_api.get_response(query)
    response_data = olap_api_response['data'][0]['sqlQuery']['data']
    result = {}
    response_data_json = re.search(r'\{.*\}', json.dumps(response_data)).group()
    data = json.loads(response_data_json)
    for key, value in data.items():
        if key.startswith("width___"):
            field_name = key.split("___", 1)[1]
            field_info = result.get(field_name, {})
            if key.startswith("width___"):
                field_info["width"] = value if value is not None else 0
            result[field_name] = field_info
    logging.info(f"Extracted column statistics:{result}")
    return result


def update_data_dict_druid_mapping(data_dictionary_conf, start_date, end_date):
    update_columns = fetch_column_stats(data_dictionary_conf)
    sql_query = generate_sql_query(update_columns, "sds_ei_entity_inventory", start_date, end_date)
    attributes_conf = extract_column_statistics(sql_query)
    for key, value in list(attributes_conf.items()):
        if value.get("width") == "":
            del attributes_conf[key]
    for entity_type, entity_data in data_dictionary_conf.items():
        attributes = entity_data.get("attributes", {})
        for attr_name, attr_data in attributes.items():
            if attr_name in attributes_conf:
                ui_config_data = attributes_conf[attr_name]
                if ("width" in ui_config_data and "width" in attr_data):
                    attr_data["width"] = max(min(int(ui_config_data["width"]), attr_data["width"]),
                                             len(attr_data["caption"]))
    logging.info(f"Updated data dict:{data_dictionary_conf}")
    return data_dictionary_conf


def update_rel_data_dict_druid_mapping(rel_data_dictionary_conf, start_date, end_date):
    queries = []
    attributes_conf = {}
    update_columns = fetch_column_stats_rel(rel_data_dictionary_conf)
    for key, value in update_columns.items():
        if key == 'entity_attributes':
            sql_query = generate_sql_query(update_columns.get(key), "sds_ei_entity_inventory", start_date, end_date)
        elif key == 'relationship_attributes':
            sql_query = generate_sql_query(update_columns.get(key), "sds_ei_relationship", start_date, end_date)
        queries.append(sql_query)
    for query in queries:
        attributes_conf.update(extract_column_statistics(query))
    for key, value in list(attributes_conf.items()):
        if value.get("width") == "":
            del attributes_conf[key]
    for entity_name, entity_data in rel_data_dictionary_conf.items():
        for sub_entity_name, sub_entity_data in entity_data.items():
            for attribute_type, attribute_group in [
                ('relationship_attributes', sub_entity_data.get('relationship_attributes', {}))]:
                for attr_name, attr_data in attribute_group.items():
                    if attr_name in attributes_conf:
                        ui_config_data = attributes_conf[attr_name]
                        if ("width" in ui_config_data and "width" in attr_data):
                            attr_data["width"] = max(min(int(ui_config_data["width"]), attr_data["width"]),
                                                     len(attr_data["caption"]))
    logging.info(f"Updated relation data dict:{rel_data_dictionary_conf}")
    return rel_data_dictionary_conf

def update_attributes_with_type(data_dictionary, type_dict, type_key):
    for entity, data in data_dictionary.items():
        for attribute, details in data['attributes'].items():
            if attribute in type_dict:
                details[type_key] = type_dict[attribute]
    return data_dictionary

"""
    Process Dictionaries to remove dashboard_identifier keys from attributes if is_active: false
    is present in the entity level.
"""
def process_dictionaries(data):
    result = data.copy()
    def process_dashboard_identifiers(obj):
        if isinstance(obj, dict):
            if 'dashboard_identifier' in obj:
                inactive_keys = [k for k, v in obj['dashboard_identifier'].items() if isinstance(v, dict) and v.get('is_active') is False]
                if inactive_keys:
                    obj['dashboard_identifier'] = {k: v for k, v in obj['dashboard_identifier'].items() if k not in inactive_keys}
                    if 'attributes' in obj:
                        for attr in obj['attributes'].values():
                            if 'dashboard_identifier' in attr:
                                attr['dashboard_identifier'] = {k: v for k, v in attr['dashboard_identifier'].items() if k not in inactive_keys}
                    if 'relationship_attributes' in obj:
                        for rel_attr in obj['relationship_attributes'].values():
                            if 'dashboard_identifier' in rel_attr:
                                rel_attr['dashboard_identifier'] = {k: v for k, v in rel_attr['dashboard_identifier'].items() if k not in inactive_keys}
            for value in obj.values():
                process_dashboard_identifiers(value)
    process_dashboard_identifiers(result)
    return result


def transform_data_dictionary(entities, fields, inventory_fields,type_dict,disamb_type_dict, start_date, end_date):
    for entity in entities:
      sorted_common_props = sort_properties(data_dict[entity]["attributes"], reference_entity_data_dict[entity]["attributes"])
      data_dict[entity]["attributes"] = sorted_common_props
    data_dictionary = {entities: entity_attributes for entities, entity_attributes in data_dict.items() if
                       entities in entities}
    for entity, attributes in data_dictionary.items():
        if "attributes" in attributes:
            attributes["attributes"] = {entities: entity_attributes for entities, entity_attributes in
                                        attributes["attributes"].items()
                                        if ((entities in fields and entity_attributes.get("ui_visibility",
                                                                                                True)) or (
                                                    entity_attributes.get("group")
                                                    == "enrichment" and entities in inventory_fields and entity_attributes.get(
                                                "ui_visibility", True)))}
    data_dictionary = {entities: entity_attributes for entities, entity_attributes in data_dictionary.items() if
                       entity_attributes}
    for attributes in data_dictionary.values():
        if "attributes" in attributes:
            attributes["attributes"] = {entities: entity_attributes for entities, entity_attributes in
                                        attributes["attributes"].items() if entity_attributes}
    non_disamb_type_added_dictionary=update_attributes_with_type(data_dictionary,disamb_type_dict,"nonDisambiguatedType")
    type_added_dictionary = update_attributes_with_type(non_disamb_type_added_dictionary, type_dict, "disambiguatedType")
    # data_dictionary_final = update_data_dict_druid_mapping(data_dictionary, start_date, end_date)
    sorted_dict = dict(sorted(type_added_dictionary.items()))
    logging.info(f"Sorted data dict:{sorted_dict}")
    entity_level_disabling=process_dictionaries(sorted_dict)
    logging.info(f"Transformed data dict:{entity_level_disabling}")
    return entity_level_disabling


def transform_relation_data_dictionary(relationship_list, relationship_common_fields, fields,
                                       relation_fields, start_date, end_date):
    relation_dict = {}
    for entity, relationships in relation_data_dict.items():
        for rel in relationships:
          sorted_entity_props = sort_properties(relation_data_dict[entity][rel]["entity_attributes"], reference_relation_data_dict[entity][rel]["entity_attributes"])
          sorted_relation_props = sort_properties(relation_data_dict[entity][rel]["relationship_attributes"], reference_relation_data_dict[entity][rel]["relationship_attributes"])
          relation_data_dict[entity][rel]["entity_attributes"] = sorted_entity_props
          relation_data_dict[entity][rel]["relationship_attributes"] = sorted_relation_props
    for entity, relationships in relation_data_dict.items():
        new_relations = {}
        for rel, rel_data in relationships.items():
            if rel in relationship_list:
                new_data = {}
                for attr, attr_data in rel_data["relationship_attributes"].items():
                    if attr in relationship_common_fields and attr_data.get("ui_visibility",True) or (
                            attr in relation_fields and attr_data.get("is_enrichment") and attr_data.get("ui_visibility",True)) :
                        new_data[attr] = attr_data
                new_entity_attributes = {}
                for attr, attr_data in rel_data["entity_attributes"].items():
                    if attr in fields and attr_data.get("ui_visibility",True):
                        new_entity_attributes[attr] = attr_data
                new_relations[rel] = {
                    "name": rel_data["name"],
                    "caption": rel_data["caption"],
                    "sorting_columns": rel_data.get("sorting_columns", []),
                    "relationshipCheck":rel_data.get("relationshipCheck", []),
                    "customFilter": rel_data.get("customFilter", []),
                    "isInverse": rel_data["isInverse"],
                    "target_entity": rel_data["target_entity"],
                    "entity_attributes": new_entity_attributes,
                    "relationship_attributes": new_data,
                    "dashboard_identifier": rel_data.get("dashboard_identifier", {})
                }
                if "detail_view_caption" in rel_data:
                    new_relations[rel]["detail_view_caption"] = rel_data["detail_view_caption"]
        if new_relations:
            relation_dict[entity] = new_relations
    # relation_dict_final = update_rel_data_dict_druid_mapping(relation_dict, start_date, end_date)
    sorted_dict = dict(sorted(relation_dict.items()))
    logging.info(f"Sorted relation data dict:{sorted_dict}")
    entity_level_disabling = process_dictionaries(sorted_dict)
    logging.info(f"Transformed data dict:{entity_level_disabling}")
    return entity_level_disabling


def get_candidate_keys(data_dictionary: dict):
    candidate_keys = {}
    for entity in data_dictionary:
        entity_name = entity
        candidate_keys[entity_name] = []
        for attribute in data_dictionary[entity]['attributes']:
            if data_dictionary[entity]['attributes'][attribute].get('candidate_key'):
                candidate_keys[entity_name].append(attribute)
    logging.info(f"Candidate keys:{candidate_keys}")
    return candidate_keys

def properties_mapping(properties_list, additional_properties, enable_hiding=True):
    properties = ["enableHiding"]
    values = [enable_hiding]
    for property_name, property_value in properties_list.items():
        properties.append(property_name)
        values.append(
            True if property_name == 'isDate' and properties_list.get('type') == 'timestamp' else property_value)
    for property_name, property_value in additional_properties.items():
        if (property_value is not None):
            properties.append(property_name)
            values.append(property_value)
    attribute_dict = dict(zip(properties, values))
    logging.info(f"Properties and their value:{attribute_dict}")
    return attribute_dict


def rename_keys(original_dict, lookup_dict):
    # Create a new dictionary to hold the renamed keys
    renamed_dict = {}

    for key, value in original_dict.items():
        # Check if the key is in the lookup dictionary
        if key in lookup_dict:
            # Rename the key using the value from the lookup dictionary
            new_key = lookup_dict[key]
        else:
            # Keep the original key if it's not in the lookup dictionary
            new_key = key

        # Add the item to the renamed dictionary with the new or original key
        renamed_dict[new_key] = value

    return renamed_dict

lookup_dict={"caption":"header",
             "is_enrichment":"isEnrichment",
             "detailed_view_hide":"detailedViewHide",
             "is_extra":"isExtra",
             "enable_hiding":"enableHiding"
             }

def entity_column_mapping_build(data_dictionary: dict):
    entity_column_mapping = {}
    candidate_keys = get_candidate_keys(data_dictionary)
    for entity, entity_data in data_dictionary.items():
        logging.info(f"Entity:{entity}")
        logging.info(f"Entity data:{entity_data}")
        list_of_entities = list(data_dictionary.keys())
        index_of_entity = list_of_entities.index(entity)
        common_properties = []
        source_specific_properties = []
        entity_specific_properties = []
        enrichment_properties = []
        for attribute, attribute_data in entity_data['attributes'].items():
            for solution in attribute_data["dashboard_identifier"]:
                attribute_data["dashboard_identifier"][solution] = rename_keys(attribute_data["dashboard_identifier"][solution], lookup_dict)
            logging.info(f"attribute:{attribute}")
            logging.info(f"attribute data:{attribute_data}")
            caption = attribute_data.get('caption', attribute.replace('_', ' ').title())
            data_type = attribute_data['type']
            description = attribute_data['description']
            dashboard_identifier = attribute_data.get('dashboard_identifier', {})
            enable_hiding = attribute_data.get('enable_hiding', True)
            data_structure = attribute_data.get('data_structure', None)
            has_line_break = attribute_data.get('has_line_break', None)
            range_selection = attribute_data.get('range_selection', None)
            min_value = attribute_data.get('min', None)
            max_value = attribute_data.get('max', None)
            width = attribute_data.get('width', None)
            candidate_key = attribute_data.get('candidate_key', None)
            step_interval = attribute_data.get('step_interval', None)
            column_merge = attribute_data.get('column_merge', None)
            ccm_list_field = attribute_data.get('ccm_list_field', None)
            ccm_org_group_by_enabled = attribute_data.get('ccm_org_group_by_enabled', None)
            ccm_org_default = attribute_data.get('ccm_org_default', None)
            non_disambiguated_type = attribute_data.get('nonDisambiguatedType', None)
            group = attribute_data['group']
            properties = {"header": caption, "description": description, "accessorKey": attribute, "type": data_type,
                          "removable": True, "isDate": False, "id": attribute + "__" + str(index_of_entity),
                          "dashboard_identifier": dashboard_identifier, "disableSortBy": True}
            additional_properties = {"data_structure": data_structure, "range_selection": range_selection,
                                     "min": min_value, "max": max_value,"hasLineBreak": has_line_break,
                                     "width": width, "candidate_key": candidate_key, "step_interval": step_interval,
                                     "group": group, "column_merge": column_merge, "ccm_list_field": ccm_list_field,
                                     "ccm_org_group_by_enabled": ccm_org_group_by_enabled,
                                     "ccm_org_default": ccm_org_default,"nonDisambiguatedType": non_disambiguated_type}
            if attribute_data['group'] == 'common':
                common_properties.append(
                    properties_mapping(properties, additional_properties, enable_hiding))
            elif attribute_data['group'] == 'entity_specific':
                entity_specific_properties.append(
                    properties_mapping(properties, additional_properties, enable_hiding))
            elif attribute_data['group'] == 'source_specific':
                source_specific_properties.append(
                    properties_mapping(properties, additional_properties, enable_hiding))
            elif attribute_data['group'] == 'enrichment':
                enrichment_properties.append(
                    properties_mapping(properties, additional_properties, enable_hiding))
        common_mapping = {"category": "Common", "properties": common_properties}
        source_specific_properties.sort(key=lambda x: x['header'].lower())
        enrichment_properties.sort(key=lambda x: x['header'].lower())
        entity_mapping = {"category": "Entity Specific", "properties": entity_specific_properties}
        source_mapping = {"category": "Source Specific", "properties": source_specific_properties}
        enrichment_mapping = {"category": "Enrichment", "properties": enrichment_properties}
        entity_column_mapping[entity] = [common_mapping, entity_mapping,source_mapping,enrichment_mapping]
    logging.info(f"Entity column mapping:{entity_column_mapping}")
    return entity_column_mapping


def map_relationship_properties(relation_data_dict, entity, relationship, attribute_type, field, is_extra):
    data_type = relation_data_dict[entity][relationship][attribute_type][field]["type"]
    minimum = relation_data_dict[entity][relationship][attribute_type][field].get("min", None)
    maximum = relation_data_dict[entity][relationship][attribute_type][field].get("max", None)
    range_selection = relation_data_dict[entity][relationship][attribute_type][field].get("range_selection", None)
    step_interval = relation_data_dict[entity][relationship][attribute_type][field].get("step_interval", None)
    width = relation_data_dict[entity][relationship][attribute_type][field].get("width", None)
    column_merge = relation_data_dict[entity][relationship][attribute_type][field].get("column_merge", None)
    data_structure = relation_data_dict[entity][relationship][attribute_type][field].get('data_structure', None)
    attribute_map = {"min": minimum, "max": maximum, "width": width, "range_selection": range_selection,
                     "step_interval": step_interval, "column_merge": column_merge, "data_structure": data_structure}
    result = {
        "header": relation_data_dict[entity][relationship][attribute_type][field]["caption"],
        "isDate": data_type == "timestamp",
        "accessorKey": field,
        "description": relation_data_dict[entity][relationship][attribute_type][field]["description"],
        "disableSortBy": True,
        "isExtra": is_extra,
        "isEnrichment": relation_data_dict[entity][relationship][attribute_type][field].get("is_enrichment", False),
        "type": data_type,
        "dashboard_identifier": relation_data_dict[entity][relationship][attribute_type][field].get("dashboard_identifier", {}),
        "width": width,
        "detailedViewHide": relation_data_dict[entity][relationship][attribute_type][field].get("detailed_view_hide",
                                                                                                False),
        "hideFeaturesDropdown": relation_data_dict[entity][relationship][attribute_type][field].get("hideFeaturesDropdown",
                                                                                                    False)
    }
    result.update({name: value for name, value in attribute_map.items() if value is not None})
    logging.info(f"Relationship properties:{result}")
    return result


def generate_relationship_config(relation_data_dictionary: dict):
    relationship_configs = {}
    for entity in relation_data_dictionary:
        relationship_configs[entity] = []
        for relationship in relation_data_dictionary[entity]:
            for item,value in relation_data_dictionary[entity][relationship]["relationship_attributes"].items():
                for solution in value["dashboard_identifier"]:
                    value["dashboard_identifier"][solution] = rename_keys(value["dashboard_identifier"][solution], lookup_dict)
            relationship_config = {
                "title": relation_data_dictionary[entity][relationship]["caption"],
                "relationship": relationship,
                "sortingColumns": relation_data_dictionary[entity][relationship].get("sorting_columns", []),
                "relationshipCheck": relation_data_dictionary[entity][relationship].get("relationshipCheck", []),
                "customFilter": relation_data_dictionary[entity][relationship].get("customFilter", []),
                "dashboard_identifier": relation_data_dictionary[entity][relationship].get("dashboard_identifier", {}),
                "relationshipColumns": [],
                "targetEntity": relation_data_dictionary[entity][relationship].get("target_entity"),
                "queryField": "target_entity_class" if relation_data_dictionary[entity][relationship][
                    "isInverse"] else "source_entity_class",
                "inverse": relation_data_dictionary[entity][relationship]["isInverse"]
            }
            if "detail_view_caption" in relation_data_dictionary[entity][relationship]:
                relationship_config["detailViewTitle"] = relation_data_dictionary[entity][relationship][
                    "detail_view_caption"]
            for field in relation_data_dictionary[entity][relationship]["entity_attributes"]:
                relationship_config["relationshipColumns"].append(
                    map_relationship_properties(relation_data_dictionary, entity, relationship, "entity_attributes",
                                                field, is_extra=True))
            for field in relation_data_dictionary[entity][relationship]["relationship_attributes"]:
                relationship_config["relationshipColumns"].append(
                    map_relationship_properties(relation_data_dictionary, entity, relationship,
                                                "relationship_attributes", field, is_extra=False))
            relationship_configs[entity].append(relationship_config)
    logging.info(f"Relationship config: {relationship_configs}")
    return relationship_configs


def map_relationship_columns(field, field_value, filter_type, is_date, data_type):
    return {
        "header": field_value['caption'],
        "enableHiding": True,
        "removable": True,
        "filterType": filter_type,
        "isDate": is_date,
        "accessorKey": field,
        "disableSortBy": True,
        "isExtra": False,
        "dataType": data_type,
        "dashboard_identifier": field_value.get("dashboard_identifier", {}),
        "dataSource": "sds_ei_relationship"
    }


def generate_relation_columns(relation_data_dictionary: dict):
    relationship_columns_maps = []
    unique_columns = list()
    for entity in relation_data_dictionary:
        for relationship in relation_data_dictionary[entity]:
            for field, field_value in relation_data_dictionary[entity][relationship]["relationship_attributes"].items():
                if field_value['type'] == 'timestamp_t':
                    column = map_relationship_columns(field, field_value, filter_type='datepickerSearch',
                                                      is_date='true', data_type='integer')
                elif field_value['type'] == 'integer_t':
                    column = map_relationship_columns(field, field_value, filter_type='dropdownSearch', is_date='false',
                                                      data_type='integer')
                else:
                    column = map_relationship_columns(field, field_value, filter_type='dropdownSearch', is_date='false',
                                                      data_type='string')
                column_tuple = tuple(column.items())
                if column_tuple not in unique_columns:
                    relationship_columns_maps.append(column)
                    unique_columns.append(column_tuple)
    logging.info(f"Relationship columns: {relationship_columns_maps}")
    return relationship_columns_maps

def prepare_merged_common_properties(entity_ui_mapping):
    if not entity_ui_mapping:
        return []

    # Create a deep copy of the first entity's common properties
    entity_column_maps = copy.deepcopy(entity_ui_mapping[0]["config"]["entityColumnMaps"])
    common_entity_map = next((i for i in entity_column_maps if i.get("category") == "Common"), None)
    common_properties = common_entity_map["properties"] if common_entity_map else None

    list3 = []
    output = {}

    # Iterate through the remaining entities
    for entity in entity_ui_mapping:
        for config in entity["config"]["entityColumnMaps"]:
            if config["category"] == 'Common':
                # Merge properties based on the accessorKey
                for i in common_properties:
                    for j in config["properties"]:
                        if i["accessorKey"] == j["accessorKey"]:
                            list3.append([j["accessorKey"], j["dashboard_identifier"]])

    for key, value in list3:
        if key in output:
            # If the key already exists, append the value to the list
            if isinstance(output[key], list):
                output[key].append(value)
            else:
                output[key] = [output[key], value]
        else:
            # If the key doesn't exist, create a new list with the value
            output[key] = value if not isinstance(value, dict) else [value]

    data = copy.deepcopy(output)

    for item, values in data.items():
        output = {}
        for items in values:
            for key, value in items.items():
                if key not in output:
                    output[key] = value if not isinstance(value, dict) else [value]
                else:
                    if isinstance(output[key], list):
                        output[key].append(value)
                    else:
                        output[key] = [output[key], value]
        data[item] = output


    for key, value in data.items():
        for subkey, subvalue in value.items():
            # Check if the list contains only empty dictionaries
            if all(not item for item in subvalue):
                # If the list has only one element, replace it with an empty dictionary
                if len(subvalue) == 1:
                    data[key][subkey] = {}
                else:
                    # If there are duplicates of empty dictionaries, remove them and keep only one empty dictionary
                    data[key][subkey] = {}
            else:
                # Remove any empty dictionaries if they exist with non-empty ones
                data[key][subkey] = [item for item in subvalue if item]


    for key, value in data.items():
        for sol,val in value.items():

            if isinstance(val, list):
                new_dict = []
                for keys in val:
                    if "accessorKey" in keys and "label" in keys and "enableHiding" in keys :
                        new_dict.append({
                            "key": keys.pop("accessorKey"),
                            "label": keys.pop("label"),
                            "enableHiding":keys.pop("enableHiding")
                        })
                    elif "accessorKey" in keys and "label" in keys:
                        new_dict.append({
                            "key": keys.pop("accessorKey"),
                            "label": keys.pop("label")
                        })
                    elif "accessorKey" in keys:
                        new_dict.append({"key": keys.pop("accessorKey")})


                if new_dict!=[]:
                    seen = set()
                    unique_data = []
                    for item in new_dict:
                        key1 = item['key']
                        if key1 not in seen:
                            unique_data.append(item)
                            seen.add(key1)

                    keys.update({"accessorKey": unique_data})
                value[sol]=keys

    for i in common_properties:
        if i["accessorKey"] in data.keys():
            i["dashboard_identifier"]=data[i["accessorKey"]]

    return common_properties


"""Sort the dict according to reference dict and return the sorted dict. Sorting is performed based on comparator fields"""
def sort_properties(to_be_sorted_props, reference_props):
    to_be_sorted_props_dict = copy.deepcopy(to_be_sorted_props)
    reference_props_list = copy.deepcopy(reference_props)

    if not to_be_sorted_props_dict:
        logging.warning("The 'to_be_sorted_props_dict' is empty. Returning an empty dictionary.")
        return {}

    if not reference_props_list:
        logging.warning("The 'reference_props_list' is empty. Returning the original dictionary.")
        return to_be_sorted_props_dict

    sorted_dict = {key: to_be_sorted_props_dict[key] for key in reference_props_list if key in to_be_sorted_props_dict}   
    remaining_keys = {key: value for key, value in to_be_sorted_props_dict.items() if key not in reference_props_list}
    sorted_dict.update(remaining_keys)
    logging.info(f"Sorted properties: {sorted_dict}")
    return sorted_dict

def ui_configuration(**kwargs):
    start_date = kwargs.get('start_date')
    end_date = kwargs.get('end_date')
    olap_api = GetOlapResponse()
    filter_data=Utils.read_data_from_shared_filesystem(relative_file_path=OLAPConnConstants.SDS_EI_OLAP_QUERY_FILTER_PATH)
    filter_reponse=olap_api.get_response(filter_data)
    get_filter = olap_api.extract_data(filter_reponse, "getFilter", "updated_at_ts")
    filterDate=''.join(map(str, get_filter))
    logging.info(f"Date Filter: {filterDate}")
    payload_data = json.loads(json.dumps(query_data).replace("FILTER_DATE", filterDate))
    logging.info(f"Payload: {payload_data}")
    olap_api_response = olap_api.get_response(payload_data)
    get_entities = olap_api.extract_data(olap_api_response, "getEntities", "class")
    get_inventory_fields = olap_api.extract_data(olap_api_response, "getInventoryFields", "COLUMN_NAME")
    get_non_disambiguated_fields = olap_api.extract_data(olap_api_response, "getNonDisambiguatedFields",
                                                                "COLUMN_NAME")
    get_fields = list(set(get_inventory_fields) & set(get_non_disambiguated_fields))
    get_resolved_relationships = olap_api.extract_data(olap_api_response, "getRelationships", "relationship_name")
    get_inverse_resolved_relationships = olap_api.extract_data(olap_api_response, "getInverseRelationships",
                                                             "inverse_relationship_name")
    get_relationships = list(get_resolved_relationships + get_inverse_resolved_relationships)
    get_relation_fields = olap_api.extract_data(olap_api_response, "getRelationshipFields", "COLUMN_NAME")
    get_relation_non_disambiguated_fields = olap_api.extract_data(olap_api_response,
                                                                         "getRelationNonDisambiguatedFields",
                                                                         "COLUMN_NAME")
    get_relationship_common_fields=list(set(get_relation_fields) & set(get_relation_non_disambiguated_fields))
    disamb_type_dict=olap_api.extract_type_dict(olap_api_response,"getNonDisambiguatedFields")
    type_dict = olap_api.extract_type_dict(olap_api_response, "getInventoryFields")
    logging.info(f"Olap API Response: {olap_api_response}")
    data = transform_data_dictionary(entities=get_entities, fields=get_fields,
                                     inventory_fields=get_inventory_fields,
                                     type_dict=type_dict,
                                     disamb_type_dict=disamb_type_dict,
                                     start_date=start_date,
                                     end_date=end_date)
    rel_data = transform_relation_data_dictionary(relationship_list=get_relationships,
                                                  relationship_common_fields=get_relationship_common_fields,
                                                  fields=get_fields,
                                                  relation_fields=get_relation_fields,
                                                  start_date=start_date, end_date=end_date)
    entity_ui_mapping = []
    for entity in data.keys():
        properties = {"category": entity, "config": {"entityColumnMaps": entity_column_mapping_build(data)[entity],
                                                     "candidateKeys": get_candidate_keys(data)[entity],
                                                     "hoverFields": []}}
        entity_ui_mapping.append(properties)
    common_properties = prepare_merged_common_properties(entity_ui_mapping)
    ui_mapping_properties = {"id": "entity_inventory", "name": "Entity Inventory",
                             "description": "SDS Entity Inventory UI Configuration",
                             "configs": {"Common": {"properties": common_properties},
                                         "entityConfigs": entity_ui_mapping,
                                         "relationshipConfigs": generate_relationship_config(rel_data),
                                         "eiGraph": ei_graph_data_dict.get("eiGraph", {})
                                         }
                             }
    return json.dumps(ui_mapping_properties)
