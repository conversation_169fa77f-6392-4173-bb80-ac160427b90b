from airflow.operators.python import PythonOperator
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory

def get_druid_var_update_tasks(analytical_variables, update_druid_dimension):
    task_run_status_list = []
    for each_variable in analytical_variables:
        table_name = each_variable['config_key'].replace('__publish_model', '')
        druid_var_update = PythonOperator(task_id=f'{table_name}_druid_var_update', python_callable=update_druid_dimension, op_kwargs={'table_name': table_name})
        task_run_status_list.append(druid_var_update)
    return task_run_status_list

def create_software_extraction_tasks(config_keys, dag_id, find_task_run_fn):
    task_run_status_list = []
    for task_id in config_keys:
        job_name = task_id.replace('_config', '')
        sds_software_extraction_run_status_task = PythonOperator(task_id=f'find_task_runs_{job_name}', python_callable=find_task_run_fn, op_kwargs={'task_name': f'sds_software_extraction.{job_name}', 'dag_id': dag_id, 'start_epoch_default_key': 'sds_software_extraction_start_epoch_default'}, retries=2)
        sds_software_extraction_task = SparkOperatorFactory.get(task_id=f'{job_name}', from_var=f'{task_id}', retries=2)
        task_run_status_list.append(sds_software_extraction_run_status_task >> sds_software_extraction_task)
    return task_run_status_list

def create_analytical_job_tasks(config_keys):
    task_run_status_list = []
    for task_id in config_keys:
        job_name = task_id + '__job'
        sds_vra_asset_enrichment_task = SparkOperatorFactory.get(task_id=f'{job_name}', from_var=f'{task_id}__job_config', retries=2)
        task_run_status_list.append(sds_vra_asset_enrichment_task)
    return task_run_status_list