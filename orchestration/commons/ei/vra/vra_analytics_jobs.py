import json
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory

from datetime import datetime
from airflow.operators.dummy import DummyOperator
from commons.pe.common_utils import Utils
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.sds_date_utils import SDSDateUtils
from airflow.models.variable import Variable
from airflow.operators.python import PythonOperator

from commons.ei.vra.utils import create_analytical_job_tasks
from commons.ei.vra.vra_constants.vra_constants import VMConstants
import pendulum
from airflow import AirflowException
from airflow.models.dagrun import DagRun
import logging
from urllib.parse import urljoin
from airflow.utils.task_group import TaskGroup


def find_task_run(**kwargs):
    logger = logging.getLogger("sds_analytics")
    analytical_job_configs = json.loads(Variable.get("SDS_VRA_ANALYTICAL_JOBS_VARIABLES"))
    analysis_period = analytical_job_configs['analysis_period']
    print(kwargs['dag_id'])
    dag_runs = DagRun.find(dag_id=kwargs['dag_id'], state="success") + DagRun.find(
        dag_id=kwargs['dag_id'],
        state="failed")
    prev_dag_runs = [run for run in dag_runs if
                     kwargs["dag_run"].execution_date.timestamp() > run.execution_date.timestamp()]
    logger.info("DAG RUNS: {prev_dag_runs}, len: {len(prev_dag_runs)}")
    prev_dag_runs.sort(key=lambda x: x.execution_date, reverse=True)
    default_start_epoch = Variable.get(kwargs['start_epoch_default_key'],
                                       default_var=000000000)
    logger.info(f"Previous DAG run counts: {len(prev_dag_runs)}")
    logger.info(f"Previous DAG runs: {prev_dag_runs}")
    if len(prev_dag_runs) == 0:
        kwargs['ti'].xcom_push(key="start_epoch", value=default_start_epoch)
        logger.info(
            f"This is the first DAG run, hence proceeding to update the start epoch as {default_start_epoch}")
    elif prev_dag_runs[0].state == 'success':
        last_finished_task = prev_dag_runs[0].get_task_instance(
            task_id=f"{kwargs['task_name']}")
        if last_finished_task is not None:
            logger.info("This is not the first Task run, hence updating the start epoch with batch start date")
            kwargs['ti'].xcom_push(key="start_epoch",
                                   value=SDSDateUtils.get_end_date(kwargs["prev_data_interval_start_success"],
                                                                   analysis_period, 'utc') + 1)
        else:
            kwargs['ti'].xcom_push(key="start_epoch",
                                   value=default_start_epoch)
            logger.info(
                f"This is the first Task run, hence proceeding to update the start epoch as {default_start_epoch}")
    elif prev_dag_runs[0].state == 'failed':
        kwargs['ti'].xcom_push(key="start_epoch", value=default_start_epoch)
        raise AirflowException("Aborting current run due to previous DAG run failure.")

