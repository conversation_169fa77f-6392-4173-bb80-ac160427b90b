import re
from pyspark.sql.functions import udf, col, concat_ws
from pyspark.sql.types import StringType, IntegerType
from rapidfuzz import process, fuzz
from pyspark.sql import functions as F
import pandas as pd
from src.sofware_extraction import config
from src.sofware_extraction.constants import UPDATED_AT_TS
from src.sofware_extraction.common_utils import write_table
from src.sofware_extraction.software_extraction_utils import normalize_software_details, cpe_to_string


def read_table_wiz(args, spark):
    input_paths = args.inputPath.split(";")
    hv_df = spark.read.table(input_paths[0]).filter(
        f"parsed_interval_timestamp_ts >= to_timestamp({args.parsedIntervalStartEpoch}/1000) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid").withColumn(
        "updated_at_ts", F.expr(f"to_timestamp({args.eventTimestampEndEpoch}/1000)"))
    nvd_df = (spark.read.table(input_paths[1]).filter(
        f"(parsed_interval_timestamp_ts >= to_timestamp(({args.parsedIntervalStartEpoch}/1000)-(14*86400))) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid and lower(vulnStatus) not  in ('received','rejected','deferred','awaiting analysis')")
              .withColumn("row", F.expr("row_number() over (partition by id order "
                                        "by event_timestamp_epoch desc)"))
              .filter("row=1")
              .select("id", "configurations"))
    cisa_data = (spark.read.table(input_paths[2]).filter(
        f"parsed_interval_timestamp_ts >= to_timestamp(({args.parsedIntervalStartEpoch}/1000)-(2*86400)) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid")
     .withColumn("row", F.expr("row_number() over (partition by cveMetadata.cveId order "
                              "by event_timestamp_epoch desc)"))
     .filter("row=1"))
    return hv_df, nvd_df, cisa_data


# Identify the software using fuzzy matching
def extract_rapidfuzz_match_udf(desc, software_list, software_list_cleaned):
    if not pd.isnull(desc) and len(software_list) > 1:
        substitutions = [
            (r"[\\]+Microsoft Office[\\]+Office16[\\]+", " "),
            ("Windows[ ]?Apps?", " "),
            ("[\\/:<>_-]", " "),
            ("[ ]{2,}", " ")]
        for pattern, repl in substitutions:
            desc = re.sub(pattern, repl, desc)

        potential_match = process.extractOne(desc.lower(), software_list_cleaned, scorer=fuzz.token_ratio)
        if potential_match is not None:
            return software_list[software_list_cleaned.index(potential_match[0])]
    return None


# Check if fuzzy output matches with detailed name
def match_check_udf(detailed_name, software):
    match_flag = 0
    detailed_name_cleaned = re.sub("[\.\|\\/:<>_-]", " ", str(detailed_name).lower()).replace("microsoft", "").strip()
    software_cleaned = re.sub("[\.\|\\/:<>_-]", " ", str(software).lower()).replace("microsoft", "").strip()
    if not pd.isnull(software):
        product = (str(software).split("|")[1]).lower()
    else:
        product = " "
    similarity_ratio = fuzz.WRatio(detailed_name_cleaned, re.sub("[\\/:<>_-]", " ", product))
    substring_match = bool(set(detailed_name_cleaned.split(" ")).intersection(set(software_cleaned.split(" "))))
    if not substring_match and similarity_ratio < 70:
        match_flag = 1
    return match_flag


# Derive software name for unmatched cases
def cpe_closest_match(gp):
    if (gp["match_flag"] == 0).any() and (gp["match_flag"] == 1).any():
        potential_match = gp[gp["match_flag"] == 0]["software"].unique()
        if len(potential_match) == 1:
            match = potential_match[0]
            gp.loc[gp["match_flag"] == 1, "software"] = match
            gp.loc[gp["match_flag"] == 1, "match_flag"] = 2

    # edge case
    title = str(gp["detailed_name"].values[0])
    if "oracle java se runtime environment" in title.lower():
        gp["software"] = "oracle|jre"
        gp["match_flag"] = 3
    office_prod = re.findall("[Mm]icrosoft ((?:[A-Za-z]+)?[ ]?[A-Za-z]+ [0-9]+)", str(title))
    if len(office_prod) > 0:
        gp["software"] = "microsoft|" + office_prod[0]
        gp["match_flag"] = 3
    windows_prod = re.findall("([wW]indows[ ]*(?:[sS]erver)?[ ]?\d+)", str(title))
    if len(windows_prod) > 0:
        gp["software"] = "microsoft|" + windows_prod[0]
        gp["match_flag"] = 3
    return gp


def derive_wiz_software(hv_df, nvd_df, cisa_df, output_path, spark):
    hv_df = hv_df.withColumn("vulnerability_name", F.expr("name"))
    hv_df = hv_df.withColumn("detailed_name", F.expr("detailedName"))
    hv_df = hv_df.withColumn("vulnerability_location", F.expr("locationPath"))
    hv_df = hv_df.withColumn("vulnerable_asset_id", F.expr("vulnerableAsset.id"))
    hv_df.select("vulnerability_name", "detailed_name", "vulnerability_location", "vulnerable_asset_id").distinct()

    extract_sw_from_description = udf(extract_rapidfuzz_match_udf, StringType())
    match_check = udf(match_check_udf, IntegerType())
    
    # cpe enrichment from NVD
    nvd_df = nvd_df.withColumn("cpe_list_nvd", F.expr("flatten(transform(transform(configurations.nodes, node -> node.cpeMatch), x -> x[0].criteria))"))    

    hv_df = hv_df.join(nvd_df, hv_df["vulnerability_name"] == nvd_df["id"], "left", ).drop("id")
    
    # cpe enrichment from CISA
    cisa_df = cisa_df.withColumn("cve_id_cisa",F.expr("cveMetadata.cveId"))
    cisa_df = cisa_df.withColumn("cpe_list_cisa", F.expr("COALESCE(flatten(containers.cna.affected.cpes),flatten(transform(FILTER(containers.adp, x -> x.affected.cpes IS NOT NULL),x -> flatten(x.affected.cpes))))"))
    cisa_df = cisa_df.withColumn("sw_cisa",  F.expr("CASE WHEN size(containers.adp.title)>1 THEN coalesce(CASE WHEN transform(filter(containers.cna.affected,x->lower(x.vendor) in ('n/a','unknown') and lower(x.product) in ('n/a','unknown')), x->(x.product, x.vendor,x.versions.version))!=Array() THEN NULL ELSE transform(containers.cna.affected, x->(concat_ws('|',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', '')))) END, transform(filter(containers.adp,x->x.title in ('CISA ADP Vulnrichment'))[0].affected, x->(concat_ws('|',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', ''))))) ELSE coalesce(CASE WHEN transform(filter(containers.cna.affected,x->lower(x.vendor) in ('n/a','unknown') and lower(x.product) in ('n/a','unknown')), x->(x.product, x.vendor,x.versions.version))!=Array() THEN NULL ELSE transform(containers.cna.affected, x->(concat_ws('|',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', '')))) END, transform(containers.adp[0].affected, x->(concat_ws('|',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', ''))))) END"))
    cisa_df = cisa_df.withColumn("sw_cisa", F.expr("FILTER(sw_cisa, x -> x !='')"))
    cisa_df = cisa_df.select("cve_id_cisa", "cpe_list_cisa","sw_cisa")
    hv_df = hv_df.join(cisa_df,hv_df["vulnerability_name"] == cisa_df["cve_id_cisa"],"left").drop("cve_id_cisa")
    
    hv_df = hv_df.withColumn("cpe_list",F.when((F.size(F.col("cpe_list_nvd")) >= 1), F.col("cpe_list_nvd")).otherwise(F.col("cpe_list_cisa")))

    hv_df = cpe_to_string(hv_df, config.software_update_dict)
    
    # add software list from cisa
    hv_df = hv_df.withColumn("software_list",F.when((F.size(F.col("software_list")) >= 1), F.col("software_list")).otherwise(F.col("sw_cisa")))
    hv_df = hv_df.withColumn("software_list",F.when(F.col("software_list").isNull(),F.lit(F.array([]))).otherwise(F.col("software_list")))
    

    hv_df = hv_df.withColumn("detailed_name", F.expr("trim(',', detailed_name)"))
    hv_df = hv_df.withColumn("detailed_name", F.explode_outer(F.split("detailed_name", ",")))
    hv_df = hv_df.withColumn("software_list_cleaned",
                             F.expr("transform(software_list, s -> regexp_replace(trim(s), '[-_\\|]', ' '))"))
    hv_df = hv_df.withColumn("software_desc", F.concat_ws(" ", col("detailed_name"), col("vulnerability_location")))
    hv_df = hv_df.withColumn("software", F.expr(
        "CASE WHEN detailed_name LIKE '%cpe:%' THEN concat_ws('|', split(detailed_name, ':')[3], split(detailed_name, ':')[4]) "
        + "WHEN size(software_list) = 1 THEN software_list[0] END"))
    # Write intermediate output
    write_table(hv_df, output_path + '_intermediate1', None, None, spark)
    hv_df = spark.read.table(output_path + '_intermediate1')

    hv_df = hv_df.withColumn("software", F.when(F.col("software").isNull(),
                                                extract_sw_from_description(F.col("software_desc"),
                                                                            F.col("software_list"),
                                                                            F.col("software_list_cleaned"))).otherwise(
        F.col("software")))

    # Write intermediate output
    write_table(hv_df, output_path + '_intermediate2', None, None, spark)
    hv_df = spark.read.table(output_path + '_intermediate2')

    hv_df = hv_df.withColumn("match_flag", match_check(F.col("detailed_name"), F.col("software")))

    hv_df = hv_df.withColumn("id", F.monotonically_increasing_id())
    temp_df = hv_df.select("id", "software", "match_flag", "detailed_name")
    temp_df = temp_df.withColumnRenamed('id', 'temp_id')
    temp_df = temp_df.groupBy("detailed_name").applyInPandas(cpe_closest_match, schema=temp_df.schema)
    hv_df = (hv_df.drop("software", "match_flag", "detailed_name").join(temp_df, hv_df["id"] == temp_df["temp_id"],
                                                                        "left")).drop("id", "temp_id")

    # Derive software vendor, product and full name
    hv_df = hv_df.withColumn("software_vendor", F.initcap(F.split(col("software"), "\\|")[0]))
    hv_df = hv_df.withColumn("software_name", F.initcap(F.split(col("software"), "\\|")[1]))

    hv_df = hv_df.withColumn("software_vendor",
                             F.when((col("match_flag") == 1) | (col('software').isNull()), None).otherwise(
                                 col("software_vendor")))
    hv_df = hv_df.withColumn("software_name", F.when((col("match_flag") == 1) | (col('software').isNull()),
                                                     col("detailed_name")).otherwise(col("software_name")))

    hv_df = hv_df.withColumn("software_name", F.regexp_replace(col("software_name"), r"_", " "))
    hv_df = hv_df.withColumn("software_name", F.regexp_replace(col("software_name"), "\\\\", ""))
    hv_df = hv_df.withColumn("software_vendor", F.regexp_replace(col("software_vendor"), r"_", " "))

    hv_df = hv_df.withColumn("software_vendor", F.lower(col("software_vendor")))
    hv_df = hv_df.withColumn("software_name", F.lower(col("software_name")))
    hv_df = hv_df.withColumn("software_version", col("version"))
    hv_df = normalize_software_details(hv_df)

    hv_df = hv_df.select("vulnerability_name", "detailed_name", "vulnerability_location", "vulnerable_asset_id",
                         "software_vendor", "software_name", "software_version", "software_product",
                         "software_full_name",
                         "updated_at_ts").distinct()
    return hv_df
