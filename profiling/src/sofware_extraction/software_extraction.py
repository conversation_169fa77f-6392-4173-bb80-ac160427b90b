from pyspark.sql import SparkSession
spark = SparkSession.builder.getOrCreate()
sc = spark.sparkContext
sc.addPyFile(spark.conf.get("spark.sds.dependencies_file"))

from src.sofware_extraction.constants import UPDATED_AT_TS
from src.sofware_extraction.common_utils import write_table
from src.sofware_extraction.software_extraction_qualys_utils import derive_qualys_software, read_table_qualys
from src.sofware_extraction.software_extraction_wiz_utils import derive_wiz_software, read_table_wiz
from src.sofware_extraction.software_extraction_defender_utils import derive_defender_software, read_table_defender
from src.sofware_extraction.software_extraction_tenable_utils import derive_tenable_software, read_table_tenable
from src.sofware_extraction.software_extraction_utils import get_args
from pyspark.sql import functions as F

if __name__ == "__main__":
    args = get_args()
    data_source = args.source
    if data_source == 'qualys':
        hv_df, kb_df, nvd_df, cisa_df = read_table_qualys(args, spark)
        output_df = derive_qualys_software(hv_df, kb_df, nvd_df, cisa_df, args, spark)
    if data_source == 'tenable':
        hv_df = read_table_tenable(args, spark)
        output_df = derive_tenable_software(hv_df, args.outputPath, spark)
    if data_source == 'defender':
        hv_df, nvd_df, cisa_df = read_table_defender(args, spark)
        output_df = derive_defender_software(hv_df, nvd_df, cisa_df, args.outputPath, spark)
    if data_source == 'wiz':
        hv_df, nvd_df, cisa_df = read_table_wiz(args, spark)
        output_df = derive_wiz_software(hv_df, nvd_df, cisa_df, args.outputPath, spark)

    write_table(output_df, args.outputPath, F.array(UPDATED_AT_TS), [F.days(UPDATED_AT_TS)], spark)