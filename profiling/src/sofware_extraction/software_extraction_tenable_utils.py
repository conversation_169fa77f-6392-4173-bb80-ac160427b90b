from pyspark.sql.functions import col, when, concat_ws, udf, expr
from pyspark.sql.types import StringType, ArrayType
from rapidfuzz import process, fuzz
from pyspark.sql import functions as F
import re
import pandas as pd

from src.sofware_extraction import config
from src.sofware_extraction.constants import UPDATED_AT_TS
from src.sofware_extraction.common_utils import write_table
from src.sofware_extraction.software_extraction_utils import normalize_software_details


def read_table_tenable(args, spark):
    input_paths = args.inputPath.split(";")
    hv_df = spark.read.table(input_paths[0]).filter(
        f"parsed_interval_timestamp_ts >= to_timestamp({args.parsedIntervalStartEpoch}/1000) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and isValid").withColumn(
        UPDATED_AT_TS, F.expr(f"to_timestamp({args.eventTimestampEndEpoch}/1000)"))
    return hv_df


# Extract all software names from cpe
def cpe_to_string(df, software_update_dict):
    df = df.withColumn("cpe_objects", F.when(F.col("cpe").isNull() | (F.col("vulnerability_type") == "Informational"),
                                             F.array([])).otherwise(
        F.split(F.col("cpe"), "<br/>|</br>|,[ ]?(?=(p-)?cpe)")))
    df = df.withColumn("cpe_objects", F.array_remove(F.col("cpe_objects"), ""))
    df = df.withColumn("software_list", F.expr(
        "transform(cpe_objects, x -> "
        + "CASE WHEN size(split(x, ':')) = 3 THEN split(x, ':')[2] "
        + "WHEN split(x, ':')[0] LIKE 'p-cpe%' THEN concat_ws('|', split(x, ':')[2], split(x, ':')[4])"
        + "WHEN size(split(x, ':'))>3  THEN  concat_ws('|', split(x, ':')[2], split(x, ':')[3])"
        + "END)"))
    for key, value in software_update_dict.items():
        df = df.withColumn("software_list", F.expr("FILTER(software_list, x -> x IS NOT NULL)")
                           ).withColumn("software_list",
                                        F.expr(f"transform(software_list, x -> regexp_replace(x, '{key}', '{value}'))"))
    df = df.withColumn("software_list", F.array_distinct(F.col("software_list")))
    df = df.drop("cpe_objects")
    return df


# Identify the software using fuzzy matching
def extract_rapidfuzz_match_udf(desc, software_list_test):
    if not pd.isnull(desc) and len(software_list_test) > 1:
        software_list = [s for s in software_list_test if s]

        software_cleaned = [s.replace("_", " ").replace("-", " ").replace("|", " ") for s in software_list if s]
        substitutions = [(r"[\\]+Microsoft Office[\\]+Office16[\\]+", " "), ("Windows[ ]?Apps?", " "),
                         ("[\\/:<>_-]", " "), ("[ ]{2,}", " ")]
        for pattern, repl in substitutions:
            desc = re.sub(pattern, repl, desc)

        potential_match = process.extractOne(desc.lower(), software_cleaned, scorer=fuzz.token_ratio)
        if not pd.isnull(potential_match):
            return software_list[software_cleaned.index(potential_match[0])]
    return None


# Extract software
def extract_software(df, direct_match_case, output_path, spark):
    df = df.withColumn("software_list_cleaned",
                       F.expr("transform(software_list, s -> regexp_replace(trim(s), '[-_\\|]', ' '))"))
    df = df.na.fill("", subset=["pluginText", "pluginName"])
    df = df.withColumn("software_desc", F.concat_ws(" ", col("pluginText"), col("pluginName")))
    df = df.withColumn("software", F.when(
        F.size(F.col("software_list_cleaned")) == 1,
        F.col("software_list").getItem(0),
    ).otherwise(None))

    for key, value in direct_match_case.items():
        df = df.withColumn("software", F.expr(
            f"CASE WHEN LOWER(pluginText) LIKE '%{key}%' AND vulnerability_type='Vulnerability' THEN '{value}' "
            + "ELSE software "
            + "END"
        ))

    # Write intermediate output
    write_table(df, output_path + '_intermediate1', None, None, spark)
    df = spark.read.table(output_path + '_intermediate1')

    extract_sw_from_description = udf(extract_rapidfuzz_match_udf, StringType())
    df = df.withColumn("software_fuzzy_out", F.when(F.col("software").isNull(),
                                                    extract_sw_from_description(F.col("software_desc"),
                                                                                F.col("software_list")),
                                                    ).otherwise(None))

    df = df.withColumn("software",
                       F.when(F.col("software_fuzzy_out").isNotNull(), F.col("software_fuzzy_out")).otherwise(
                           F.col("software")))
    df = df.withColumn("software", F.when(col("software").isNotNull(), concat_ws("|", expr(
        "transform(split(software, '\\\\|'), sof -> initcap(replace(sof, '_', ' ')))"))).otherwise(
        col("software")))
    return df


# Extract version from pluginText
def extract_version(df, pattern_list):
    expr_str = "CASE " + " ".join(
        [f"WHEN rlike(pluginText, '{pattern}')  THEN regexp_extract_all(pluginText,  '{pattern}', 1) " for pattern in
         pattern_list]) + " ELSE NULL END"
    df = df.withColumn("version_matches", F.expr(expr_str))
    df = df.withColumn("version_matches", F.array_distinct(F.col("version_matches")))
    df = df.withColumn("version_matches",
                       F.expr("transform(version_matches, x -> regexp_replace(x, '\\\(?[bB]uild.*$', ''))"))
    return df


# Post-processing windows and office products
def post_processing_udf(software, os, title, desc, version):
    # Micosoft Windows software
    if software == "Microsoft|Windows":
        win_version = re.findall(r"[Ww]indows (\d+\.?\d?|Server \d+)", str(os))
        if win_version:
            software = software.replace("Windows", "Windows " + win_version[0])

    # Microsoft Office products
    match1 = re.findall("((?:Outlook|Excel|Word|OneNote|Access|Powerpoint|Publisher|Skype for Business) [0-9]{3,4}) ",
                        str(desc))
    if match1 and "Office" not in title and not pd.isnull(software):
        software = "Microsoft|" + match1[0]

    if "Microsoft Office" in title or "Microsoft Access" in title and not pd.isnull(software):
        match = re.findall(
            r"[pP]roduct *: *(?:Microsoft)? ?([\s\S]*?(?: for [bB]usiness)?(?: \d{3,4}?)?) [\s\S]*? (?:Remote|Installed|Channel) version *: *([\s\S]*?) ",
            str(desc)
        )
        if match:
            software = list(set([("Microsoft|" + item[0] + "|" + item[1]) for item in match]))
        else:
            software = ["Microsoft|Office|"]

    elif software:
        # edge cases
        if (software == "Microso" or software == "Mic" or software == "M") and ("teams" in desc.lower()):
            software = "Microsoft|Teams"
        if "|" not in software:
            software = str(software) + "|"
        if not version:
            software = [str(software) + "|"]
        else:
            software = [str(software) + "|" + re.sub("Should|Network", "", v) for v in version]
    return software


def derive_tenable_software(hv_df, output_path, spark):
    post_processing = udf(post_processing_udf, ArrayType(StringType()))
    hv_df = hv_df.withColumn("vulnerability_type", F.expr(
        "CASE WHEN (LOWER(riskFactor)='none' OR riskFactor IS NULL) THEN 'Informational' ELSE 'Vulnerability' END"))
    hv_df = cpe_to_string(hv_df, config.software_update_dict)
    hv_df = extract_software(hv_df, config.direct_match_case, output_path, spark)
    hv_df = extract_version(hv_df, config.pattern_list)

    # Write intermediate output
    write_table(hv_df, output_path + '_intermediate2', None, None, spark)
    hv_df = spark.read.table(output_path + '_intermediate2')

    hv_df = hv_df.withColumn("software_name_processed",
                             post_processing(F.col("software"), col("operatingSystem"), col("pluginName"),
                                             col("pluginText"), col("version_matches")))
    hv_df = hv_df.withColumn("software", F.col("software_name_processed"))

    # Derive software vendor, product and full name
    hv_df = hv_df.withColumn("software", F.explode_outer(col("software")))
    hv_df = hv_df.withColumn("software",
                             F.when((F.col("vulnerability_type") == "Informational") | (F.col("cpe").isNull()),
                                    None).otherwise(F.col("software")))
    hv_df = hv_df.withColumn("software_vendor", F.split(col("software"), "\\|")[0])
    hv_df = hv_df.withColumn("software_name", F.split(col("software"), "\\|")[1])
    hv_df = hv_df.withColumn("software_name", F.split(col("software_name"), '";')[0])
    hv_df = hv_df.withColumn("software_version", F.split(col("software"), "\\|")[2])

    hv_df = hv_df.withColumn("software_vendor", F.lower(col("software_vendor")))
    hv_df = hv_df.withColumn("software_name", F.lower(col("software_name")))
    hv_df = normalize_software_details(hv_df)
    hv_df = hv_df.withColumn("ei_host_id", expr("COALESCE(tenable_uuid,netbiosName,macAddress,dnsName,ip)"))
    hv_df = hv_df.select("cve", "cpe", "ei_host_id", "pluginID", "pluginText", "software_vendor", "software_name",
                         "software_version", "software_product", "software_full_name", "updated_at_ts").distinct()

    return hv_df
