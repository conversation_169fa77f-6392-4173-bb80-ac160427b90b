from pyspark.sql import DataFrame, Window
from pyspark.sql.session import SparkSession

spark = SparkSession.builder.getOrCreate()
sc = spark.sparkContext
sc.addPyFile(spark.conf.get("spark.sds.dependencies_file"))
from pyspark.sql.functions import col, lit, explode, when, concat_ws, regexp_replace, count, dayofyear, to_timestamp, \
    days
import logging
from pyspark.sql.types import StringType, StructField, StructType
from src.utils.common_utils import get_args, read_table, write_table, get_configs


def pattern_outlier_detection(data, features, entity_class, outlier_ratio=0.05):
    """
    Detects outliers in a dataset based on pattern analysis of specified features.

    This function identifies patterns in the data and flags outliers based on the frequency
    of these patterns. It returns a DataFrame containing the p_id, attribute, attribute_value,
    outlier_description, and outlier of the detected outliers.

    Parameters:
    - data (DataFrame): The input DataFrame containing the data to analyze.
    - features (list of str): List of feature column names to analyze for pattern-based outliers.
    - outlier_ratio (float, optional): The ratio of outliers to detect. Default is 0.05 (5%).

    Returns:
    - DataFrame: A DataFrame with columns 'p_id', 'attribute', 'attribute_value', 'outlier_description',
                 and 'outlier' containing the IDs and feature values of the detected outliers.
    """

    def convert_string_spark(df, input_col, output_col):
        return df.withColumn(output_col,
                             when(col(input_col).isNull(), lit("NaN"))
                             .otherwise(
                                 regexp_replace(
                                     regexp_replace(
                                         regexp_replace(
                                             col(input_col), '[a-zA-Z]', 'A'
                                         ), '[0-9]', 'D'
                                     ), '[^A]', 'S'
                                 )
                             )
                             )

    validation_results = None
    schema = StructType([
        StructField("p_id", StringType(), True),
        StructField("outlier", StringType(), True),
        StructField("outlier_description", StringType(), True),
        StructField("class", StringType(), True),
        StructField("attribute", StringType(), True),
        StructField("attribute_value", StringType(), True)])
    for feature in features:
        if feature not in data.columns:
            missing_record = data.select(lit(None).cast(StringType()).alias("p_id"),
                                         lit("Pattern Outlier").alias("outlier"),
                                         lit("Column Missing").alias("outlier_description"),
                                         lit(entity_class).alias("class"), lit(feature).alias("attribute"),
                                         lit(None).cast(StringType()).alias("attribute_value")).limit(1)
            if validation_results is None:
                validation_results = missing_record
            else:
                validation_results = validation_results.union(missing_record)
            continue
        if 'struct' in dict(data.dtypes)[feature]:
            struct_record = data.select(lit(None).cast(StringType()).alias("p_id")
                                        , lit("Pattern Outlier").alias("outlier")
                                        , lit("Struct Column").alias("outlier_description")
                                        , lit(entity_class).alias("class"), lit(feature).alias("attribute"),
                                        lit(None).cast(StringType()).alias("attribute_value")).limit(1)
            if validation_results is None:
                validation_results = struct_record
            else:
                validation_results = validation_results.union(struct_record)
            continue
        if data.filter(col(feature).isNotNull()).count() == 0:
            null_column_record = data.select(lit(None).cast(StringType()).alias("p_id"),
                                             lit("Pattern Outlier").alias("outlier"),
                                             lit("Null Column").alias("outlier_description")
                                             , lit(entity_class).alias("class"), lit(feature).alias("attribute"),
                                             lit(None).cast(StringType()).alias("attribute_value")).limit(1)
            if validation_results is None:
                validation_results = null_column_record
            else:
                validation_results = validation_results.union(null_column_record)
            continue
        if 'array' in dict(data.dtypes)[feature]:
            temp = data.select(['p_id', feature]).withColumn(feature, explode(col(feature)))
        else:
            temp = data.select(['p_id', feature])
        temp = temp.filter(col(feature).isNotNull())
        temp = convert_string_spark(temp, feature, f"{feature}_converted")
        value_counts = temp.groupBy(f"{feature}_converted").count()
        rare_patterns = value_counts.filter(col("count") < 2).select(f"{feature}_converted")
        rare_pids = temp.join(rare_patterns, on=f"{feature}_converted", how="inner").select("p_id").distinct()
        rare_pids_count = rare_pids.count()
        total_count = temp.count()
        if rare_pids_count > (total_count * outlier_ratio):
            result_df = rare_pids.join(temp, "p_id").select("p_id", feature).distinct()
        elif value_counts.count() > total_count * 0.25:
            windowSpec = Window.partitionBy(f"{feature}_converted")
            temp = temp.withColumn("Frequency", count("*").over(windowSpec))
            sorted_temp = temp.orderBy(col("Frequency").desc(), col(f"{feature}_converted"))
            outlier_count = int(sorted_temp.select("p_id").distinct().count() * outlier_ratio)
            outliers = sorted_temp.limit(outlier_count).select("p_id", feature).distinct()
            result_df = outliers
        else:
            if rare_pids_count != 0:
                result_df = rare_pids.join(temp, "p_id").select("p_id", feature).distinct()
            else:
                result_df = spark.createDataFrame(spark.sparkContext.emptyRDD(), schema)
        if result_df.count() > 0:
            result_df = result_df.withColumn("attribute_value", col(feature)) \
                .withColumn("attribute", lit(feature)) \
                .withColumn("outlier_description", lit(None).cast(StringType())) \
                .withColumn("class", lit(entity_class)) \
                .withColumn("outlier", lit("Pattern Outlier"))
        else:
            result_df = data.select(lit(None).cast(StringType()).alias("p_id"), lit("Pattern Outlier").alias("outlier"),
                                    lit("Success").alias("outlier_description"),
                                    lit(entity_class).alias("class"), lit(feature).alias("attribute"),
                                    lit(None).cast(StringType()).alias("attribute_value")).limit(1)
        if validation_results is None:
            validation_results = result_df.select("p_id", "outlier", "outlier_description", "class", "attribute"
                                                  , "attribute_value")
        else:
            validation_results = (validation_results.unionByName \
                                      (result_df.select("p_id", "outlier", "outlier_description", "class", "attribute",
                                                        "attribute_value"))
                                  .filter("outlier!=''"))
    return validation_results


if __name__ == "__main__":
    logger = logging.getLogger("regex_pattern_job")
    spark = SparkSession.builder.getOrCreate()
    args = get_args()
    entity_class = ' '.join(args.tableName.split("__")[-1].capitalize().split("_")).title()
    pattern_features = get_configs(spark, args.configURL).get("pattern_analysis_fields")
    dataframe = read_table(args.tableName, args.startEpoch, args.endEpoch, spark)
    outputDF = (pattern_outlier_detection(dataframe, pattern_features, entity_class)
                .withColumn("updated_at", lit(args.endEpoch))
                .withColumn("updated_at_ts", to_timestamp(col("updated_at") / 1000)))

    write_table(outputDF, args.outputPath, ["updated_at_ts", "class", "outlier"],
                [days(col("updated_at_ts")), "class", "outlier"],
                spark)
