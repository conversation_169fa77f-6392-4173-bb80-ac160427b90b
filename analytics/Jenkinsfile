@Library('sds-common') _
coverageLimit = 0

pipeline {
    agent any
    environment {
        stageResult='FAIL'
    }
    stages{
        stage('runUnitTest') {
            steps {
                    script {
                        scala.runUnitTest16()
                    }
            }
        }

        stage('version') {
            steps {
                script {
                    env.buildNumber = getBuildVersion()
                }
            }
        }

        stage('generateCoverageReport') {
            steps {
                script {
                    scala.generateCoverageReport()
                }
            }
        }

        stage('publishCoverageJenkins') {
            steps {
                    script {
                        scala.publishCoverage<PERSON><PERSON>kins()
                    }
            }
        }

        stage('coverageStatus') {
            steps {
                script {
                        env.coverageNumber = scala.coverageStatus(coverageLimit:"${coverageLimit}")

                    }
                }
            }

        stage('build') {
            steps {
                script {
                    env.build = scala.sbtBuild16Jar(env.buildNumber)
                }
            }
        }
        stage('Upload') {
            steps {
                script {
                    def uploadSpec = '''{
                    "files": [{
                       "pattern": "*.jar",
                       "target": "pai-sbt-local-qa",
                       "props": "project=platform"
                    }]
                 }'''

                    rtUpload(
                    buildName: 'sds-ei-analytics',
                    buildNumber: "${buildNumber}",
                    // Obtain an Artifactory server instance, defined in Jenkins --> <PERSON><PERSON> Jenkins --> Configure System
                    serverId: 'pe-jfrog',
                    spec: "${uploadSpec}"
                )
            }}
            }
        stage('Publish build info') {
            steps {
                echo env.buildNumbers
                rtPublishBuildInfo(
                    serverId: 'pe-jfrog',
                    buildName: 'sds-ei-analytics' ,
                    buildNumber: "${buildNumber}"

                )
            }
        }
        stage('notification') {
            steps {
                script {
                    deploy.notifyOnBuild(buildVersion:"${buildNumber}")
                    deploy.highlightCoverageStatus(coverageNumber:"${coverageNumber}")
                }
            }
        }

        }
        }
