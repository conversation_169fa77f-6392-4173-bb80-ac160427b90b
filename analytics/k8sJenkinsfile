@Library('sds-common') _
coverageLimit = 70
env.stageResult = 'PASS'

pipeline {
    agent {
        kubernetes {
            label 'sbt'
            defaultContainer 'sbt-1-6-2'
        }
    }
    environment {
        stageResult='PASS'
    }
    stages {
        stage ('runUnitTest') {
            steps {
                    script {
                        scala.runUnitTest()
                    }
            }
        }

        stage ('version') {
            agent {
                kubernetes {
                    label 'debian'
                    defaultContainer 'debian'
                }
            }
            steps {
                script {
                    env.buildNumber = getNewBuildVersion()
                }
            }
        }

        stage ('generateCoverageReport') {
            steps {
                script {
                    scala.generateCoverageReports()
                    stash includes: '**/target/**/index.html', name: 'report'
                }
            }
        }

        stage('publishCoverageJenkins') {
            steps {
                    script {
                        scala.publishCoverageJenkins()
                    }
            }
        }

        stage ('coverageStatus') {
            agent {
                kubernetes {
                    label 'python'
                    defaultContainer 'apt-pkg-python-3-8-12'
                }
            }
            steps {
                script {
                        unstash 'report'
                        sh "chown -R jenkins ${WORKSPACE}"
                        sh "su jenkins"
                        env.coverageNumber = scala.getCoverageStatus(coverageLimit:"${coverageLimit}")

                    }
                }
            }

        stage ('build') {
            steps {
                script {
                    env.build = scala.sbtJarBuild(env.buildNumber)
                }
            }
        }
        stage ('Upload') {
            steps {
                script {
                    def uploadSpec = '''{
                    "files": [{
                       "pattern": "*.jar",
                       "target": "pai-sbt-local-dev",
                       "props": "project=platform"
                    }]
                 }'''

                    rtUpload(
                    buildName: 'sds-ei-analytics',
                    buildNumber: "${buildNumber}",
                    // Obtain an Artifactory server instance, defined in Jenkins --> Manage Jenkins --> Configure System
                    serverId: 'pe-jfrog',
                    spec: "${uploadSpec}"
                )
            }}
            }
        stage ('Publish build info') {
            steps {
                echo env.buildNumbers
                rtPublishBuildInfo(
                    serverId: 'pe-jfrog',
                    buildName: 'sds-ei-analytics' ,
                    buildNumber: "${buildNumber}"

                )
            }
        }
        stage ('notification') {
            steps {
                script {
                    deploy.notifyOnBuild(buildVersion:"${buildNumber}")
                    deploy.highlightCoverageStatus(coverageNumber:"${coverageNumber}")
                }
            }
        }
    }
}
