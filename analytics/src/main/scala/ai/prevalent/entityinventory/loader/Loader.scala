package ai.prevalent.entityinventory.loader

import ai.prevalent.entityinventory.utils.LineageUtils
import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.loader.LoaderUtils.{finalSelectInvColumns, tableCreation, viewCreation, viewSqlCreation}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{EVENT_TIMESTAMP_TS, KG_CONTENT_TYPE, ORIGIN, PARSED_INTERVAL_TIMESTAMP_TS, RECENCY, UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.loader.configs.specs.{Config, ConfigSerializer, OutputTableInfoSerializer}
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.entityinventory.utils.{EILOGGER, <PERSON><PERSON><PERSON><PERSON>, Spark<PERSON>til}
import ai.prevalent.sdspecore.sparkbase.SDSSparkBaseConfigurable
import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions._
import org.json4s.Formats
import org.apache.spark.sql.types.{ArrayType, NullType, TimestampType}
import ai.prevalent.entityinventory.utils.DataFrameWithColumnLogged._


object Loader extends SDSSparkBaseConfigurable[EIJobArgs, Config]{

  override def configFormats: Formats = super.configFormats  + ConfigSerializer + OutputTableInfoSerializer

  def build(jobArgs: EIJobArgs, config: Config, reader: SDSTableReader, writer: SDSTableWriter): DataFrame = {
    val previousInRead = EIUtil.safeReadEntity(config.outputTableInfo.outputTableName, reader = reader)
    spark.sparkContext.setCheckpointDir(spark.conf.get("spark.checkpoint.dir"))
    val previousInventory = EIUtil.readPrevDF(jobArgs, previousInRead).checkpoint(true)
    val prevConfig: Option[String] = EIUtil.getPrevConfig(previousInventory, jobArgs)

    val sourceDF: DataFrame = reader.read(config.dataSource.get.srdm).filter(
      col(config.dataSource.get.dataIntervalTimestampKey) <= to_timestamp(lit(jobArgs.parsedIntervalEndEpoch).divide(1000)))
    EILOGGER.jsonMiniPrint("SRDM Source Schema", sourceDF.schema)
    LOGGER.info(s"SRDM Source Count ${sourceDF.count()}")
    val (rawSrdmDf, latestInventoryExpr) = LoaderUtils.build(sourceDF, previousInventory.filter(s"$KG_CONTENT_TYPE='data' OR $KG_CONTENT_TYPE IS NULL"), config, prevConfig, jobArgs, reader, writer)(spark)
    val selectFinalFields: List[String] = finalSelectInvColumns(rawSrdmDf, config)
    val finalSql =viewSqlCreation(rawSrdmDf, config, latestInventoryExpr, jobArgs, selectFinalFields)
    if (config.outputTableInfo.outputWrittenMode == "viewType") {
      viewCreation(config, finalSql)
    }
    val tempViewName = config.outputTableInfo.outputTableName.replace(".", "_") + "_temp"
    LoaderUtils.applyViewFilter(tempViewName, jobArgs, config)


  }

  override def execute(jobArgs: EIJobArgs, config: Config): Unit = {
    config.configValidator()
    spark.conf.set("spark.sql.caseSensitive", "true")
    val reader = SDSTableReaderFactory.getDefault(spark)
    val writer = SDSTableWriterFactory.getDefault(spark, tableProperties = Map.empty, options = Map("partitionOverwriteMode" -> "dynamic"))
    EILOGGER.jsonMiniPrint("Loader Config", config)

    val finalOutDF = build(jobArgs, config, reader, writer)

    if (config.outputTableInfo.outputWrittenMode == "tableType") {
      tableCreation(finalOutDF,config,writer)
    }

    LineageUtils.emitColumnLineage(config.dataSource.get.srdm, config.outputTableInfo.outputTableName, config.allProperties.toList)
  }

  override def getInitParams: EIJobArgs = new EIJobArgs()

  override def getConfigManifest: Manifest[Config] = manifest[Config]






}
