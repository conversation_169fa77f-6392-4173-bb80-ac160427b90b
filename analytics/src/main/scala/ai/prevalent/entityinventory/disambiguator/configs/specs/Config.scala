package ai.prevalent.entityinventory.disambiguator.configs.specs

import ai.prevalent.entityinventory.common.configs.{EIConfig, Entity, FieldsSpec, Property}
import ai.prevalent.entityinventory.delta.{Change, DisambiguationConfigDelta}
import ai.prevalent.entityinventory.disambiguator.Disambiguator
import ai.prevalent.entityinventory.enrichment.Enrichment
import ai.prevalent.entityinventory.exceptions.UnsupportedConfig
import org.json4s.JsonAST.{JBool, JField, JObject, JString}
import org.json4s.jackson.Serialization.write
import org.json4s.{CustomSerializer, DefaultFormats, JArray, MappingException}

case class Config(inventoryModelInput: Array[InventoryModelInput],
                  disambiguation: Disambiguation =null,
                  derivedProperties: Array[Property] = Array.empty,
                  output: Output =null,
                  fieldSpec: FieldsSpec=null,
                  fieldLevelSpecOverride: Array[Property] = Array.empty,
                  entity: Entity= Entity.defaultEntityConfig,
                  enrichments: Array[Enrichment] = Array.empty
                 ) extends EIConfig[Config,DisambiguationConfigDelta]{
  def configValidator(): Unit = {
    val inventoryModelInputNames = inventoryModelInput.map(_.name)
    val defaultConfidenceList = disambiguation.confidenceMatrix
    val diffList = defaultConfidenceList.filter(name => {
      if (inventoryModelInputNames.contains(name))
        false
      else true
    })
    if(diffList.nonEmpty) {
      val invalidEntires = diffList.mkString(",")
      throw new UnsupportedConfig(s"InvalidConfidenceMatrix:confidenceMatrix entries do not match inventoryModelInput list for $invalidEntires")
    }
  }

  override def toString: String = write(this)(Disambiguator.configFormats)

  override def getConfigDelta(otherConfig: Config): DisambiguationConfigDelta = DisambiguationConfigDelta(otherConfig, this)
  override def getConfigDelta(deltas: Seq[Change]): DisambiguationConfigDelta = DisambiguationConfigDelta(this, deltas)


}

case class CandidateKey(name: String, exceptionFilter: Option[String], keyToKeyMatch: Boolean, caseSensitive: Boolean, matchAttributesList:List[String]=List.empty)

case class Disambiguation(candidateKeys: Array[CandidateKey], confidenceMatrix: Array[String], temporalConfidenceMatrix : Array[String] = Array.empty[String],
                          excludeValues: Array[String] = Array(""),
                          strategy: Option[Strategy], disambiguationType:String = "CandidateKey") extends ConfidenceMatrix(confidenceMatrix)

case class FieldLevelConfidenceMatrix(field: String, temporalConfidenceMatrix: Option[Array[String]],
                                      confidenceMatrix: Array[String], persistNonNullValue: Option[Boolean], restrictToConfidenceMatrix: Boolean = false) extends ConfidenceMatrix(confidenceMatrix)
case class ValueConfidence(field: String, confidenceMatrix: Array[String] = Array.empty) extends ConfidenceMatrix(confidenceMatrix)

case class SingleSource(field: String, source:String, persistNonNullValue:Boolean = true)

case class Aggregation(field: String, function:String)
case class FrequencyConfidence(field:String, fallbackValueConfidence:Array[String] = Array.empty)
case class Strategy(fieldLevelConfidenceMatrix: Option[Array[FieldLevelConfidenceMatrix]],
                    rollingUpFields: Set[String] = Set.empty,
                    valueConfidence: Option[Array[ValueConfidence]],
                    singleSource:List[SingleSource] = List.empty,
                    frequencyConfidence:List[FrequencyConfidence] = List.empty,
                    aggregation:List[Aggregation] = List.empty)

case class InventoryModelInput(path: String, name: String, filter: String = "true",
                               removeFields: List[String] = List.empty, isEnrichSource: Boolean = false){
  override def toString: String = write(this)(DefaultFormats)
}

case class Output(filter: String = "true", disambiguatedModelLocation: String=null, resolverLocation: String=null, fragmentLocation: Option[String]=None, isFragmentOLAPTable:Boolean = true, resolverGraphLocation:Option[String])

case class ConfigurationItem(name:String)

object Strategy {
  def empty = Strategy(Option.empty, Set.empty, Option.empty)
}


object CandidateKeySerializer extends CustomSerializer[CandidateKey](format => (
  {
    case JObject(fields) =>
      val map = fields.toMap
      val name = map("name") match {
        case JString(s) => s
        case _ => throw new MappingException("name should be a string")
      }
      val exceptionFilter = map.get("exceptionFilter").flatMap {
        case JString(s) => Some(s)
        case _ => None
      }
      val keyToKeyMatch = map.getOrElse("keyToKeyMatch",JBool(false)) match {
        case JBool(b) => b
      }
      val caseSensitive: Boolean = map.getOrElse("caseSensitive",JBool(false)) match {
        case JBool(b) => b
      }
      val matchAttributesList = fields.find(_._1 == "matchAttributesList").flatMap {
        case (_, JArray(values)) => Some(values.map(_.extract[String](DefaultFormats,manifest[String])))
        case _ => None
      }.getOrElse(List.empty)
      CandidateKey(name, exceptionFilter, keyToKeyMatch, caseSensitive,matchAttributesList)

    case JString(name) => CandidateKey(name, Option.empty,false,false)
  },
  {
    case CandidateKey(name, exceptionFilter,keyToKeyMatch,caseSensitive, matchAttributesList) => JObject(List(JField("name", JString(name)), JField("exceptionFilter", JString(exceptionFilter.get)), JField("keyToKeyMatch", JBool(keyToKeyMatch)), JField("caseSensitive", JBool(caseSensitive))))
  }
))
abstract class ConfidenceMatrix(confidenceMatrix: Array[String]){
  def confidenceMap = confidenceMatrix
    .zip(Array.range(1,confidenceMatrix.length+1).reverse)
    .toMap
}
