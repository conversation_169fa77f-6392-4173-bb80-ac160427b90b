package ai.prevalent.entityinventory.preupgrade.configs

import ai.prevalent.sdspecore.jobbase.DefaultJobArgs
import picocli.CommandLine.Option

class ConfigSnapshotArgs extends DefaultJobArgs{
    @Option(names = Array("--current-timestamp"), description = Array("Current Timestamp"), required = true)
    var currentTimestamp: Long = _

    @Option(names = Array("--table-name"), description = Array("Table name"), required = true)
    var tableName: String = _

    @Option(names = Array("--version"), description = Array("Version"))
    var version: String = "new"

    @Option(names = Array("--write-mode"), description = Array("Iceberg write mode"), required = false)
    var writeMode: String = "overwrite"

  }
