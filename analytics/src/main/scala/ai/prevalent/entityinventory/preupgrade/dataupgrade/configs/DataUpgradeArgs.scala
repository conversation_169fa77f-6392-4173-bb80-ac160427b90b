package ai.prevalent.entityinventory.preupgrade.dataupgrade.configs

import ai.prevalent.sdspecore.configbase.ConfigurableJobArgs
import ai.prevalent.sdspecore.jobbase.DefaultJobArgs
import picocli.CommandLine.Option

class DataUpgradeArgs extends DefaultJobArgs{
  @Option(names = Array("--data-upgrade-interval-start"), description = Array("start value of data upgrade in timestamp"), required = false)
  var dataUpgradeIntervalStart: Long = _

  @Option(names = Array("--data-upgrade-interval-end"), description = Array("end value of data upgrade in timestamp"), required = false)
  var dataUpgradeIntervalEnd: Long = _

  @Option(names = Array("--input-table-name"), description = Array("schema of table located"), required = true)
  var inputTableName: String = _

  @Option(names = Array("--output-schema-name"), description = Array("schema of upgraded table"), required = false)
  var outputSchemaName: String = _

  @Option(names = Array("--table-type"), description = Array("Type of table whether resolver,fragments or resolution"), required = true)
  var tableType: String = _

}
