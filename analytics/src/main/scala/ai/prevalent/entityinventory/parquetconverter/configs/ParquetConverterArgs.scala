package ai.prevalent.entityinventory.parquetconverter.configs

import ai.prevalent.sdspecore.jobbase.DefaultJobArgs
import picocli.CommandLine.Option

class ParquetConverterArgs extends DefaultJobArgs{
  @Option(names = Array("--iceberg_table_name"), description = Array("Iceberg Table Name"), required = true)
  var icebergTableName: String = null

  @Option(names = Array("--output_path"), description = Array("Output parquet location"), required = true)
  var outputPath: String = null

  @Option(names = Array("--filter_expression"), description = Array("Filter expression to filter out the input table"), required = false)
  var filterExpression: String= "true"

  @Option(names = Array("--write-mode"), description = Array("Parquet write mode"), required = false)
  var writeMode:String = "overwrite"

}