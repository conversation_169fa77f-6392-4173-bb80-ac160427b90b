package ai.prevalent.entityinventory.common.configs

import ai.prevalent.entityinventory.delta.{Change, RelationshipDictionaryDelta}

case class RelationshipDictionary(caption:String = "", description:String="",
                                  entity_attributes:Map[String, DictionaryAttribute] = Map.empty,
                                  relationship_attributes:Map[String, DictionaryAttribute] = Map.empty)
  extends EIConfig[RelationshipDictionary, RelationshipDictionaryDelta] {
  override def getConfigDelta(prevConfig: RelationshipDictionary): RelationshipDictionaryDelta = RelationshipDictionaryDelta(prevConfig= prevConfig, newConfig = this)
  override def getConfigDelta(deltas: Seq[Change]): RelationshipDictionaryDelta = RelationshipDictionaryDelta()
}
