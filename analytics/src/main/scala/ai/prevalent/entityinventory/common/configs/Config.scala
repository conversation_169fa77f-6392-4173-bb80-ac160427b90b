package ai.prevalent.entityinventory.common.configs

import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{EVENT_TIMESTAMP_TS, PARSED_INTERVAL_TIMESTAMP_TS, UUID}
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.write

case class FieldsSpec(persistNonNullValue: Option[Boolean],
                      isInventoryDerived: Boolean = false,
                      convertEmptyToNull:Boolean =  true,
                      aggregateFunction: Option[String],
                      replaceExpression:Option[<PERSON>olean],
                      caseSensitiveExpression: Boolean = false
                     ) {
  override def hashCode(): Int = {
    val state = Seq(persistNonNullValue, isInventoryDerived, convertEmptyToNull, aggregateFunction, replaceExpression, caseSensitiveExpression)
    state.map(_.hashCode()).foldLeft(0)((a, b) => 31 * a + b)
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case that: FieldsSpec =>
        (that canEqual this) &&
          this.persistNonNullValue == that.persistNonNullValue &&
          this.isInventoryDerived == that.isInventoryDerived &&
          this.convertEmptyToNull == that.convertEmptyToNull &&
          this.aggregateFunction == that.aggregateFunction &&
          this.replaceExpression == that.replaceExpression &&
          this.caseSensitiveExpression == that.caseSensitiveExpression
      case _ => false
    }
  }

  def canEqual(other: Any): Boolean = other.isInstanceOf[FieldsSpec]

  override def toString: String = write(this)(DefaultFormats)
}


case class Property(colName:String, colExpr:String,
                    fieldsSpec:FieldsSpec = FieldsSpec(aggregateFunction = Option.empty[String], persistNonNullValue = Option.empty[Boolean], replaceExpression = Option.empty[Boolean])){
  override def hashCode(): Int =  colName.hashCode

  override def equals(o: Any): Boolean = if (o.isInstanceOf[Property]) colName.equals(o.asInstanceOf[Property].colName) else false
}

object Property {
  def apply(colName:String) = new Property(colName,colName)
  def apply(colName:String, fieldsSpec: FieldsSpec) = new Property(colName,colName,fieldsSpec)
  def apply(colName:String,colExpr: String) = new Property(colName,colExpr)
}

case class Entity(fieldSpec: FieldsSpec = FieldsSpec(aggregateFunction = Option.empty[String], persistNonNullValue = Some(true),replaceExpression=Some(true)),
                  name: String, lastUpdateFields:List[String] = List.empty, commonProperties: Array[Property]= Array.empty[Property],entitySpecificProperties: Array[Property]= Array.empty[Property],sourceSpecificProperties: Array[Property]= Array.empty[Property])
object  Entity {
  def defaultEntityConfig = Entity(fieldSpec =  null,"Dummy Report an issue to EI team if you ever see this")
}

case class OutputTableInfo(outputTableName:String=null,outputWrittenMode:String="tableType")
case class DataSource(name: String=null, feedName: String=null, srdm: String=null,
                      dataIntervalTimestampKey: String=PARSED_INTERVAL_TIMESTAMP_TS,dataEventTimestampKey: String=EVENT_TIMESTAMP_TS,uniqueRecordIdentifierKey:String=UUID){
  override def equals(obj: Any): Boolean = obj match {
    case that: DataSource =>
      (this.name == that.name || (this.name == null && that.name == null)) &&
        (this.feedName == that.feedName || (this.feedName == null && that.feedName == null)) &&
        (this.srdm == that.srdm || (this.srdm == null && that.srdm == null))&&
        (this.dataIntervalTimestampKey == that.dataIntervalTimestampKey || (this.dataIntervalTimestampKey == PARSED_INTERVAL_TIMESTAMP_TS && that.dataIntervalTimestampKey == PARSED_INTERVAL_TIMESTAMP_TS))&&
        (this.dataEventTimestampKey == that.dataEventTimestampKey || (this.dataEventTimestampKey == EVENT_TIMESTAMP_TS && that.dataEventTimestampKey == EVENT_TIMESTAMP_TS))&&
        (this.uniqueRecordIdentifierKey == that.uniqueRecordIdentifierKey || (this.uniqueRecordIdentifierKey == UUID && that.uniqueRecordIdentifierKey == UUID))


    case _ => false
  }

  override def hashCode(): Int = {
    val prime = 31
    var result = 1
    result = prime * result + (if (name != null) name.hashCode else 0)
    result = prime * result + (if (feedName != null) feedName.hashCode else 0)
    result = prime * result + (if (srdm != null) srdm.hashCode else 0)
    result
  }

  override def toString: String = write(this)(DefaultFormats)
}