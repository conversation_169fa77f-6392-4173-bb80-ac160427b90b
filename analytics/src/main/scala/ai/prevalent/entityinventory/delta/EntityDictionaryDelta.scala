package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.EntityDictionary

case class EntityDictionaryDelta(description:Option[Change] = Option.empty,
                                 attrChanges:Seq[Change] = Seq.empty) extends Delta

object EntityDictionaryDelta extends Delta {

  def apply(prevConfig:EntityDictionary, newConfig:EntityDictionary): EntityDictionaryDelta ={
    val description = checkDescriptionChange(prevDescription = prevConfig.description, newDescription = newConfig.description)
    RelationshipDictionaryDelta(description = description)
    val attrChanges = checkDictionaryAttributeChanges(oldAttributes = prevConfig.attributes, newAttributes =  newConfig.attributes)
    EntityDictionaryDelta(description, attrChanges = attrChanges)
  }

  def checkDescriptionChange(prevDescription:String, newDescription:String):Option[Change] ={
    if (!prevDescription.equals(newDescription)) {
      Some(Change("Entity Description", "Entity Description",
        Option.empty, Map("Old Value" -> prevDescription, "New Value" -> newDescription), "Entity"))
    }
    else Option.empty
  }
}
