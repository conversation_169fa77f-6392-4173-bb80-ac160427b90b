package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.Property
import ai.prevalent.entityinventory.disambiguator.configs.specs.{Config, FieldLevelConfidenceMatrix, ValueConfidence}
import ai.prevalent.entityinventory.utils.SparkUtil

case class DisambiguationConfigDelta(modelInputUpdate: Seq[Change] = Seq.empty,
                                     filterByChange: Option[Change] = Option.empty,
                                     candidateKeyChange: Seq[Change] = Seq.empty,
                                     confidenceMatrixUpdate: Option[Change] = Option.empty,
                                     strategyChange: Seq[Change] = Seq.empty,
                                     temporalConfidenceMatrixUpdate: Option[Change] = Option.empty,
                                     excludeValuesUpdate: Option[Change] = Option.empty,
                                     aggregationStrategyChange: Seq[Change] = Seq.empty,
                                     derivedPropertiesChange: Seq[Change]) extends Delta {
}

object DisambiguationConfigDelta extends Delta {

  private val UPDATED_INV_MODEL_INPUT = "Updated Inventory Model Input"
  private val TEMPORAL_CONFIDENCE_MATRIX = "Temporal Confidence Matrix"
  private val INV_MODEL_INPUT = "Inventory Model Input"
  private val AGG_STRATEGY_UPDATE = "Updated Aggregation Strategy"
  private val ATTR = "Attribute"
  private val CANDIDATE_KEY = "Candidate Key"

  def apply(prevConfig: Config, config: Config): DisambiguationConfigDelta = {
    val modelInputUpdate = checkModelInputUpdate(prevConfig, config)
    val filter = checkOutputFilterChange(prevConfig, config)
    val candidateKeyListChange = checkCandidateKeyListChange(prevConfig, config)
    val candidateKeyUpdate = checkCandidateKeyUpdate(prevConfig, config)
    val confidenceMatrixUpdate = checkConfidenceMatrixUpdate(prevConfig, config)
    val temporalConfidenceMatrixUpdate = checkTemporalConfidenceMatrixUpdate(prevConfig, config)
    val exludeValuesUpdate = checkExludeValuesUpdate(prevConfig, config)
    val fieldLevelConfidenceMatrixStrategyChange = checkfieldLevelConfidenceMatrixStrategyChange(prevConfig, config)
    val valueConfidenceStrategyChange = checkvalueConfidenceStrategyChange(prevConfig, config)
    val rollingUpFieldsStrategyChange = checkEntityRollingUpFieldsStrategyChange(prevConfig, config)
    val aggregationStrategyChange = checkEntityAggregationStrategyChange(prevConfig, config)

    val props1 = prevConfig.derivedProperties.map((prop => (prop.colName, prop))).toMap
    val props2 = config.derivedProperties.map((prop => (prop.colName, prop))).toMap
    val expression = checkDerivedPropertyExpressionChange(props1, props2)
    val newFields = checkNewDerivedProperties(props1, props2)
    val removedProps = checkRemovedProperties(props1, props2)
    DisambiguationConfigDelta(
      modelInputUpdate,
      filter,
      candidateKeyListChange ++ candidateKeyUpdate,
      confidenceMatrixUpdate,
      fieldLevelConfidenceMatrixStrategyChange ++ valueConfidenceStrategyChange ++ rollingUpFieldsStrategyChange,
      temporalConfidenceMatrixUpdate,
      exludeValuesUpdate,
      aggregationStrategyChange,
      expression ++ newFields ++ removedProps
    )
  }

  def apply(clientConfig: Config, deltas: Seq[Change]): DisambiguationConfigDelta = {
    val derivedProps = clientConfig.derivedProperties.map((prop => (prop.colName, prop))).toMap
    val derivedPropsInheritedChanges = checkPropertyInheritedChanges(deltas.toSeq, derivedProps)
    DisambiguationConfigDelta(
      derivedPropertiesChange = derivedPropsInheritedChanges
    )
  }

  /**
   * Checks for update in inventory model input like change in
   * name, table name etc.
   *
   * @param prevConfig
   * @param newConfig
   * @return
   */
  def checkModelInputUpdate(prevConfig: Config, newConfig: Config): Seq[Change] = {

    val prevInvModelInput = prevConfig.inventoryModelInput.map(input => (input.path, input)).toMap
    val currentInvModelInput = newConfig.inventoryModelInput.map(input => (input.path, input)).toMap
    val elementsWithFilterChange: Seq[Change] = prevInvModelInput
      .filter(prevInp => currentInvModelInput.isDefinedAt(prevInp._1))
      .map(prevInp => {
        val prevFilter = prevInp._2.filter
        val matchEle = currentInvModelInput(prevInp._1)
        if (!prevFilter.equals(matchEle.filter)) {
          Some(Change(UPDATED_INV_MODEL_INPUT, matchEle.name, Option.empty, Map("Old Filter" -> prevFilter, "New Filter" -> matchEle.filter), INV_MODEL_INPUT))
        } else {
          Option.empty
        }
      }).filter(_.isDefined).map(_.get).toSeq

    val elementsWithNameChange: Seq[Change] = prevInvModelInput
      .filter(prevInp => currentInvModelInput.isDefinedAt(prevInp._1))
      .map(prevInp => {
        val prevName = prevInp._2.name
        val matchEle = currentInvModelInput(prevInp._1)
        if (prevName != matchEle.name) {
          Some(Change(UPDATED_INV_MODEL_INPUT, matchEle.name, Option.empty, Map("Old Name" -> prevName, "New Name" -> matchEle.name), INV_MODEL_INPUT))
        } else {
          Option.empty
        }
      }).filter(_.isDefined).map(_.get).toSeq
    val elementsWithEnrichStatusChange: Seq[Change] = prevInvModelInput
      .filter(prevInp => currentInvModelInput.isDefinedAt(prevInp._1))
      .map(prevInp => {
        val prevStatus = prevInp._2.isEnrichSource
        val matchEle = currentInvModelInput(prevInp._1)
        if (prevStatus != matchEle.isEnrichSource) {
          Some(Change(UPDATED_INV_MODEL_INPUT, matchEle.name, Option.empty, Map("Old Is-Enrich-Source Status" -> prevStatus.toString, "New Is-Enrich-Source Status" -> matchEle.isEnrichSource.toString), INV_MODEL_INPUT))
        } else {
          Option.empty
        }
      }).filter(_.isDefined).map(_.get).toSeq
    val elementsWithUpdatedRemoveFieldsProp: Seq[Change] = prevInvModelInput
      .filter(prevInp => currentInvModelInput.isDefinedAt(prevInp._1))
      .map(prevInp => {
        val prevRmList = prevInp._2.removeFields
        val matchEle = currentInvModelInput(prevInp._1)
        if (!(prevRmList.diff(matchEle.removeFields) ++ matchEle.removeFields.diff(prevRmList)).isEmpty) {
          Some(Change(UPDATED_INV_MODEL_INPUT, matchEle.name, Option.empty, Map("Old Fields Remove List" -> prevRmList.mkString(","), "New Fields Remove List" -> matchEle.removeFields.mkString(",")), INV_MODEL_INPUT))
        } else {
          Option.empty
        }
      }).filter(_.isDefined).map(_.get).toSeq
    val newElementsAddedChange: Seq[Change] = currentInvModelInput
      .filter(prevInp => !prevInvModelInput.isDefinedAt(prevInp._1))
      .map(prevInp => {
        val keyValuePairs = prevInp._2.productElementNames.zip(prevInp._2.productIterator)
        Some(Change(UPDATED_INV_MODEL_INPUT, prevInp._1, Option.empty, Map("Newly Added Inventory Model Input Info" -> prevInp._2.toString), INV_MODEL_INPUT))

      }).filter(_.isDefined).map(_.get).toSeq
    val existingElementsRemovedChange: Seq[Change] = prevInvModelInput
      .filter(prevInp => !currentInvModelInput.isDefinedAt(prevInp._1))
      .map(prevInp => {
        Some(Change(UPDATED_INV_MODEL_INPUT, prevInp._1, Option.empty, Map("Removed Inventory Model Input" -> prevInp._1), INV_MODEL_INPUT))

      }).filter(_.isDefined).map(_.get).toSeq

    elementsWithFilterChange ++ elementsWithNameChange ++ elementsWithEnrichStatusChange ++ elementsWithUpdatedRemoveFieldsProp ++ newElementsAddedChange ++ existingElementsRemovedChange
  }

  /**
   * Checks if any output filter expression is changed
   *
   * @param prevConfig
   * @param newConfig
   * @return
   */
  def checkOutputFilterChange(prevConfig: Config, newConfig: Config): Option[Change] = {
    if (!prevConfig.output.filter.equals(newConfig.output.filter)) {
      Some(Change("Filter", "Output Filter Expression Changes",
        Option.empty, Map("Old Output Filter" -> prevConfig.output.filter, "New Output Filter" -> newConfig.output.filter), ATTR))
    }
    else Option.empty
  }

  /**
   * Checks if any candidate is removed or added
   *
   * @param prevConfig
   * @param newConfig
   * @return
   */
  def checkCandidateKeyListChange(prevConfig: Config, newConfig: Config): Seq[Change] = {
    val oldConfigCandidateKeys = prevConfig.disambiguation.candidateKeys.map(_.name)
    val newConfigCandidateKeys = newConfig.disambiguation.candidateKeys.map(_.name)
    val candidateKeyDiff = checkStringListChanges(oldConfigCandidateKeys, newConfigCandidateKeys, CANDIDATE_KEY)
    if (candidateKeyDiff.nonEmpty) candidateKeyDiff else Seq.empty
  }

  /**
   * Checks if any of the attributes of existing candidate key is updated
   *
   * @param prevConfig
   * @param newConfig
   * @return
   */
  def checkCandidateKeyUpdate(prevConfig: Config, newConfig: Config): Seq[Change] = {
    val prevConfigCandidateKeys = prevConfig.disambiguation.candidateKeys
    val newConfigCandidateKeys = newConfig.disambiguation.candidateKeys

    def checkExceptionFilterChange(): Seq[Change] = {
      newConfigCandidateKeys.flatMap(key => prevConfigCandidateKeys.filter(_.name == key.name).map(prevKey => {
        val prevExceptionFilter = prevKey.exceptionFilter.getOrElse("false")
        val newExceptionFilter = key.exceptionFilter.getOrElse("false")
        if (!prevExceptionFilter.equals(newExceptionFilter)) {
          Some(Change("Candidate Key Filter Change", key.name,
            Option.empty, Map("Old Exception Filter" -> prevExceptionFilter, "New Exception Filter" -> newExceptionFilter), CANDIDATE_KEY))
        } else Option.empty
      }).filter(_.isDefined).map(_.get)).toSeq
      Seq.empty
    }

    def checkKeyToKeyMatchChange(): Seq[Change] = {
      newConfigCandidateKeys
        .flatMap(key => prevConfigCandidateKeys
          .filter(_.name == key.name)
          .map(prevKey => {
            if (!prevKey.keyToKeyMatch.equals(key.keyToKeyMatch)) {
              Some(Change("Candidate Key Key-to-Key Match Updated", key.name,
                Option.empty, Map("Old Key-to-Key Match Status" -> prevKey.exceptionFilter.get, "New Key-to-Key Match Status" -> key.exceptionFilter.get), CANDIDATE_KEY))
            } else Option.empty
          }).filter(_.isDefined).map(_.get).toSeq
        )
    }

    def checkCaseSensitiveChange(): Seq[Change] = {
      newConfigCandidateKeys.flatMap(key => prevConfigCandidateKeys.filter(_.name == key.name).map(prevKey => {
        if (!prevKey.caseSensitive.equals(key.caseSensitive)) {
          Some(Change("Candidate Key Case Sensitive Updated", key.name,
            Option.empty, Map("Old Case Sensitive Status" -> prevKey.caseSensitive.toString, "New Case Sensitive Status" -> key.caseSensitive.toString), CANDIDATE_KEY))
        } else Option.empty
      }).filter(_.isDefined).map(_.get)).toSeq
    }

    if (prevConfigCandidateKeys.nonEmpty && newConfigCandidateKeys.nonEmpty) {
      checkExceptionFilterChange() ++ checkKeyToKeyMatchChange() ++ checkCaseSensitiveChange()
    } else throw new Exception("Either the new or the old configs have no candidate keys")

  }

  /**
   * Checks if confidence matrix is updated. This check is done considering their order in the list
   *
   * @param prevConfig
   * @param newConfig
   * @return
   */
  def checkConfidenceMatrixUpdate(prevConfig: Config, newConfig: Config): Option[Change] = {
    val prevConfidenceMatrix = prevConfig.disambiguation.confidenceMatrix
    val newConfidenceMatrix = newConfig.disambiguation.confidenceMatrix
    if (prevConfidenceMatrix.isEmpty && newConfidenceMatrix.isEmpty) {
      Option.empty
    } else if (prevConfidenceMatrix.isEmpty) {
      Some(Change(
        "Confidence Matrix", f"Confidence Matrix Defined", Option.empty,
        Map("Confidence Matrix Defined" -> newConfidenceMatrix.mkString(",")), ATTR
      ))
    }
    else if (newConfidenceMatrix.isEmpty) {
      Some(Change(
        "Confidence Matrix", f"Confidence Matrix Removed", Option.empty,
        Map("Removed Confidence Matrix" -> prevConfidenceMatrix.mkString(",")), ATTR
      ))
    } else if (!(prevConfidenceMatrix sameElements newConfidenceMatrix)) {
      Some(Change(
        "Updated Confidence Matrix", "Updated Confidence Matrix", Option.empty,
        Map("Old Confidence Matrix" -> prevConfidenceMatrix.mkString(","), "New Confidence Matrix" -> newConfidenceMatrix.mkString(",")), ATTR
      ))
    }
    else Option.empty
  }

  def checkTemporalConfidenceMatrixUpdate(prevConfig: Config, newConfig: Config): Option[Change] = {
    val prevTemporalConfidenceMatrix = prevConfig.disambiguation.temporalConfidenceMatrix
    val newTemporalConfidenceMatrix = newConfig.disambiguation.temporalConfidenceMatrix
    if (prevTemporalConfidenceMatrix.isEmpty && newTemporalConfidenceMatrix.isEmpty) {
      Option.empty
    }
    else if (prevTemporalConfidenceMatrix.isEmpty) {
      Some(Change(
        TEMPORAL_CONFIDENCE_MATRIX, f"Temporal Confidence Matrix Defined", Option.empty,
        Map("New Temporal Confidence Matrix Defined" -> newTemporalConfidenceMatrix.mkString(",")), "Temporal Confidence"
      ))
    }
    else if (newTemporalConfidenceMatrix.isEmpty) {
      Some(Change(
        TEMPORAL_CONFIDENCE_MATRIX, f"Temporal Confidence Matrix Removed}", Option.empty,
        Map("Removed Existing Temporal Confidence Matrix" -> prevTemporalConfidenceMatrix.mkString(",")), "Temporal Confidence"
      ))
    }
    else if (!(prevTemporalConfidenceMatrix sameElements newTemporalConfidenceMatrix)) {
      Some(Change(
        TEMPORAL_CONFIDENCE_MATRIX, f"Updated Temporal Confidence Matrix", Option.empty,
        Map("Old Temporal Confidence Matrix" -> prevTemporalConfidenceMatrix.mkString(","), "New Temporal Confidence Matrix" -> newTemporalConfidenceMatrix.mkString(",")), "Temporal Confidence"
      ))
    }
    else Option.empty
  }

  def checkExludeValuesUpdate(prevConfig: Config, newConfig: Config): Option[Change] = {
    val prevExcludeValues = prevConfig.disambiguation.excludeValues
    val newExcludeValues = newConfig.disambiguation.excludeValues
    if (!checkStringListChanges(prevExcludeValues, newExcludeValues, "Exclude Values").isEmpty) {
      Some(Change("Exclude Values", "Exclude Values Updated",
        Option.empty, Map("Old Exclude Values" -> prevExcludeValues.mkString(","), "New Exclude Values" -> newExcludeValues.mkString(",")), "Exclude Values"))
    } else Option.empty
  }

  def checkDerivedPropertyExpressionChange(prevConfigProps: Map[String, Property], newConfigProps: Map[String, Property]): Seq[Change] = {
    prevConfigProps
      .filter(p => newConfigProps.isDefinedAt(p._1))
      .map(p => {
        val expr2 = newConfigProps.get(p._1).get.colExpr
        if (!p._2.colExpr.equals(expr2)) {
          Some(Change("Updated Derived Field Expression", p._1, Option.empty, Map("Old Expression" -> p._2.colExpr, "New Expression" -> expr2), ATTR))
        } else Option.empty
      })
      .filter(_.isDefined)
      .map(_.get)
      .toSeq
  }

  def checkNewDerivedProperties(prevConfigProps: Map[String, Property], newConfigProps: Map[String, Property]): Seq[Change] = {
    newConfigProps
      .filter(p => !prevConfigProps.isDefinedAt(p._1))
      .map(p => {
        Some(Change("New Derived Field", p._1, Option.empty, Map("Expression" -> p._2.colExpr), ATTR))
      })
      .filter(_.isDefined)
      .map(_.get)
      .toSeq
  }

  def checkRemovedProperties(prevConfigProps: Map[String, Property], newConfigProps: Map[String, Property]): Seq[Change] = {
    prevConfigProps
      .filter(p => !newConfigProps.isDefinedAt(p._1))
      .map(p => {
        Some(Change("Deleted Derived Field", p._1, Option.empty, Map("Expression" -> p._2.colExpr), ATTR))
      })
      .filter(_.isDefined)
      .map(_.get)
      .toSeq
  }

  def checkfieldLevelConfidenceMatrixStrategyChange(prevConfig: Config, newConfig: Config): Seq[Change] = {
    val prevStrategy = if (prevConfig.disambiguation.strategy.get.fieldLevelConfidenceMatrix.isDefined) {
      prevConfig.disambiguation.strategy.get.fieldLevelConfidenceMatrix.get.map((prop => (prop.field, prop))).toMap
    } else Map.empty[String, FieldLevelConfidenceMatrix]
    val newStrategy = if (newConfig.disambiguation.strategy.get.fieldLevelConfidenceMatrix.isDefined) {
      newConfig.disambiguation.strategy.get.fieldLevelConfidenceMatrix.get.map((prop => (prop.field, prop))).toMap
    } else Map.empty[String, FieldLevelConfidenceMatrix]

    def checkTemporalConfidenceMatrixChange(): Seq[Change] = {
      prevStrategy.filter(p => newStrategy.isDefinedAt(p._1))
        .map(p => {
          val newTemporalConfidenceMatrix = newStrategy.get(p._1).get.temporalConfidenceMatrix.getOrElse(newConfig.disambiguation.temporalConfidenceMatrix)
          val oldTemporalConfidenceMatrix = p._2.temporalConfidenceMatrix.getOrElse(newConfig.disambiguation.temporalConfidenceMatrix)
          if (oldTemporalConfidenceMatrix.isEmpty && newTemporalConfidenceMatrix.isEmpty) {
            Option.empty
          }
          else if (oldTemporalConfidenceMatrix.isEmpty) {
            Some(Change(
              "Field Level Confidence Matrix", f"Temporal Confidence Matrix of ${p._1}", Option.empty,
              Map("Temporal Confidence Matrix Defined" -> newTemporalConfidenceMatrix.mkString(",")), "Field Level Confidence Matrix"
            ))
          }
          else if (newTemporalConfidenceMatrix.isEmpty) {
            Some(Change(
              "Field Level Confidence Matrix", f"Temporal Confidence Matrix of ${p._1}", Option.empty,
              Map("Removed Existing Temporal Confidence Matrix" -> oldTemporalConfidenceMatrix.mkString(",")), "Field Level Confidence Matrix"
            ))
          }
          else if (!(oldTemporalConfidenceMatrix sameElements newTemporalConfidenceMatrix)) {
            Some(Change(
              "Field Level Confidence Matrix", f"Temporal Confidence Matrix of ${p._1}", Option.empty,
              Map("Old Temporal Confidence Matrix" -> oldTemporalConfidenceMatrix.mkString(","), "New Temporal Confidence Matrix" -> newTemporalConfidenceMatrix.mkString(",")), "Field Level Confidence Matrix"
            ))
          }
          else Option.empty
        })
        .filter(_.isDefined)
        .map(_.get)
        .toSeq
    }

    def checkConfidenceMatrixChange(): Seq[Change] = {
      prevStrategy.filter(p => newStrategy.isDefinedAt(p._1))
        .map(p => {
          val newConfidenceMatrix = newStrategy.get(p._1).get.confidenceMatrix
          val oldConfidenceMatrix = p._2.confidenceMatrix
          if (oldConfidenceMatrix.isEmpty && newConfidenceMatrix.isEmpty) {
            Option.empty
          } else if (oldConfidenceMatrix.isEmpty) {
            Some(Change(
              "Field Level Confidence Matrix", f"Confidence Matrix of ${p._1}", Option.empty,
              Map("Value Confidence Matrix Defined" -> newConfidenceMatrix.mkString(",")), "Field Level Confidence Matrix"
            ))
          }
          else if (newConfidenceMatrix.isEmpty) {
            Some(Change(
              "Field Level Confidence Matrix", f"Confidence Matrix of ${p._1}", Option.empty,
              Map("Removed Value Confidence Matrix" -> oldConfidenceMatrix.mkString(",")), "Field Level Confidence Matrix"
            ))
          } else if (!(p._2.confidenceMatrix sameElements newConfidenceMatrix)) {
            Some(Change(
              "Field Level Confidence Matrix", f"Confidence Matrix of ${p._1}", Option.empty,
              Map("Old Confidence Matrix" -> p._2.confidenceMatrix.mkString(","), "New Confidence Matrix" -> newConfidenceMatrix.mkString(",")), "Field Level Confidence Matrix"
            ))
          }
          else Option.empty
        })
        .filter(_.isDefined)
        .map(_.get)
        .toSeq
    }

    def checkFieldSpecChange(): Seq[Change] = {
      val persistanceSpecChange = prevStrategy.filter(p => newStrategy.isDefinedAt(p._1))
        .map(p => {
          val persistNonNullValue = newStrategy.get(p._1).get.persistNonNullValue.getOrElse(true)
          if (!p._2.persistNonNullValue.getOrElse(true).equals(persistNonNullValue)) {
            Some(Change(
              "FieldSpec Updated", f"${p._1} persistNonNullValue Updated", Option.empty,
              Map("Old FieldSpec" -> p._2.persistNonNullValue.getOrElse(true).toString, "New FieldSpec" -> persistNonNullValue.toString), "Field Level Confidence Matrix"
            ))
          } else Option.empty
        })

      val confidenceMatrixRestrictionSpecChange = prevStrategy.filter(p => newStrategy.isDefinedAt(p._1))
        .map(p => {
          val confidenceMatrixRestriction = newStrategy.get(p._1).get.restrictToConfidenceMatrix
          if (!p._2.restrictToConfidenceMatrix.equals(confidenceMatrixRestriction)) {
            Some(Change(
              "Field Level Confidence Matrix", f"${p._1} restrictToConfidenceMatrix Updated", Option.empty,
              Map("Old FieldSpec" -> p._2.restrictToConfidenceMatrix.toString, "New FieldSpec" -> confidenceMatrixRestriction.toString), "Field Level Confidence Matrix"
            ))
          } else Option.empty
        })
      (persistanceSpecChange ++ confidenceMatrixRestrictionSpecChange).filter(_.isDefined)
        .map(_.get)
        .toSeq


    }

    if (!(prevStrategy.isEmpty && newStrategy.isEmpty)) {
      checkTemporalConfidenceMatrixChange() ++ checkConfidenceMatrixChange() ++ checkFieldSpecChange()
    } else Seq.empty
  }

  def checkvalueConfidenceStrategyChange(prevConfig: Config, newConfig: Config): Seq[Change] = {
    val prevStrategy = if (prevConfig.disambiguation.strategy.get.valueConfidence.isDefined) {
      prevConfig.disambiguation.strategy.get.valueConfidence.get.map((prop => (prop.field, prop))).toMap
    } else Map.empty[String, ValueConfidence]

    val newStrategy = if (newConfig.disambiguation.strategy.get.valueConfidence.isDefined) {
      newConfig.disambiguation.strategy.get.valueConfidence.get.map((prop => (prop.field, prop))).toMap
    } else Map.empty[String, ValueConfidence]

    def checkValueConfidenceUpdate(): Seq[Change] = {
      prevStrategy.filter(p => newStrategy.isDefinedAt(p._1))
        .map(p => {
          val newValueConfidence = newStrategy.get(p._1).get.confidenceMatrix
          val oldValueConfidence = p._2.confidenceMatrix
          if (!(newValueConfidence sameElements p._2.confidenceMatrix)) {
            Some(Change(
              "Value Confidence Updated", p._1, Option.empty,
              Map("Old Value Confidence" -> oldValueConfidence.mkString(","), "New Value Confidence" -> newValueConfidence.mkString(",")), "Value Confidence"
            ))
          } else if (newValueConfidence.isEmpty) {
            Some(Change(
              "Value Confidence Deleted", p._1, Option.empty,
              Map(f"Existing Value Confidence" -> oldValueConfidence.mkString(",")), "Value Confidence"
            ))
          }
          else if (oldValueConfidence.isEmpty) {
            Some(Change(
              "Value Confidence Defined", p._1, Option.empty,
              Map("New Value Confidence Defined" -> newValueConfidence.mkString(",")), "Value Confidence"
            ))
          } else Option.empty
        }).filter(_.isDefined).map(_.get).toSeq
    }

    def checkNewAddedValueConfidence(): Seq[Change] = {
      newStrategy
        .filter(p => !prevStrategy.isDefinedAt(p._1))
        .map(p => {
          Some(Change(
            "New Value Confidence", p._1, Option.empty,
            Map("New Value" -> p._2.confidenceMatrix.mkString(",")), "Value Confidence"
          ))
        }
        ).filter(_.isDefined).map(_.get).toSeq
    }

    def checkRemovedValueConfidence(): Seq[Change] = {
      prevStrategy
        .filter(p => !newStrategy.isDefinedAt(p._1))
        .map(p => {
          Some(Change(
            "Deleted Value Confidence", p._1, Option.empty,
            Map("Old Value Confidence" -> p._2.confidenceMatrix.mkString(",")), "Value Confidence"
          ))
        })
        .filter(_.isDefined)
        .map(_.get)
        .toSeq
    }

    if (!(prevStrategy.isEmpty && newStrategy.isEmpty)) {
      checkValueConfidenceUpdate() ++ checkNewAddedValueConfidence() ++ checkRemovedValueConfidence()
    } else Seq.empty
  }

  def checkEntityRollingUpFieldsStrategyChange(prevConfig: Config, newConfig: Config): Seq[Change] = {
    val prevStrategy = prevConfig.disambiguation.strategy
    val prevRollUp = if (prevStrategy.isDefined) prevStrategy.get.rollingUpFields else Set.empty[String]
    val newStrategy = newConfig.disambiguation.strategy
    val newRollUp = if (newStrategy.isDefined) newStrategy.get.rollingUpFields else Set.empty[String]

    val additionChange = if (!newRollUp.diff(prevRollUp).isEmpty) {
      Some(Change(
        "New Rolling Up Fields", "Additional Rolling Up Fields", Option.empty,
        Map("New Rolling Up Field" -> newRollUp.diff(prevRollUp).mkString(", ")), "Rolling Up Fields"
      ))
    } else Option.empty

  val removalChange = if(!prevRollUp.diff(newRollUp).isEmpty){
    Some(Change(
    "Deleted Rolling Up Fields", "Existing Rolling Up Fields", Option.empty, Map("Deleted Rolling Up Field" -> prevRollUp.diff(newRollUp).mkString(", ")), "Rolling Up Fields"
  ))
  } else Option.empty
  Seq(additionChange, removalChange).filter(_.isDefined).map(_.get)
}

def checkEntityAggregationStrategyChange(prevConfig: Config, newConfig: Config): Seq[Change] = {
  val prevConfigProps = prevConfig.disambiguation.strategy.get.aggregation.map((prop => (prop.field, prop))).toMap
  val newConfigProps = newConfig.disambiguation.strategy.get.aggregation.map((prop => (prop.field, prop))).toMap
  val emptyAggCheckChange = if (prevConfigProps.isEmpty) {
    newConfigProps.map(p => Some(Change(AGG_STRATEGY_UPDATE, p._1, Option.empty, Map("New Aggregation Field" -> p._1), ATTR))).filter(_.isDefined).map(_.get)
  } else if (newConfigProps.isEmpty) {
    prevConfigProps.map(p => Some(Change(AGG_STRATEGY_UPDATE, p._1, Option.empty, Map("Deleted Aggregation Field" -> p._1), ATTR))).filter(_.isDefined).map(_.get)
  } else {
    Seq.empty
  }
  val aggFieldsRemoveChange = prevConfigProps.filter(p => !newConfigProps.isDefinedAt(p._1))
    .map(p => {
      Some(Change(AGG_STRATEGY_UPDATE, p._1, Option.empty, Map("Deleted Aggregation Field" -> p._1), "Aggregation Strategy"))
    }).filter(_.isDefined).map(_.get).toSeq
  val aggFieldsAddedChange = newConfigProps.filter(p => !prevConfigProps.isDefinedAt(p._1))
    .map(p => {
      Some(Change(AGG_STRATEGY_UPDATE, p._1, Option.empty, Map("Newly Added Aggregation Field" -> p._1), "Aggregation Strategy"))
    }).filter(_.isDefined).map(_.get).toSeq
  val aggFieldUpdatedChange = prevConfigProps.filter(p => newConfigProps.isDefinedAt(p._1))
    .map(p => {
      val newPropFunc = newConfigProps.get(p._1).get.function
      if (!p._2.function.equals(newPropFunc)) {
        Some(Change(AGG_STRATEGY_UPDATE, p._1, Option.empty, Map("Old Aggregation Function" -> p._2.function, "New Aggregation Function" -> newPropFunc), "Aggregation Strategy"))
      } else Option.empty
    }).filter(_.isDefined).map(_.get).toSeq

  aggFieldsRemoveChange ++ aggFieldsAddedChange ++ aggFieldUpdatedChange ++ emptyAggCheckChange
}

}
