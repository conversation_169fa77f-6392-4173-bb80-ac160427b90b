package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.publisher.configs.Config

case class PublisherConfigDelta(properties: Seq[Change] = Seq.empty,
                                transformSpec: Seq[Change] = Seq.empty
                               ) extends Delta {
}

object PublisherConfigDelta extends Delta {

  def apply(clientConfig: Config, deltas: Seq[Change]): PublisherConfigDelta = {
    val derivedProps = clientConfig.derivedProperties.map((prop => (prop.colName, prop))).toMap
    val derivedPropsInheritedChanges = checkPropertyInheritedChanges(deltas, derivedProps)
    PublisherConfigDelta(properties = derivedPropsInheritedChanges)
  }

  /**
   * For finding delta between two solution versions
   *
   * @param prevConfig
   * @param config
   * @return
   */
  def apply(prevConfig: Config, config: Config): PublisherConfigDelta = {
    val derivedPropsChange = checkPropertyChanges(prevConfig.derivedProperties.toSeq, config.derivedProperties.toSeq)
    val transformSpecChange = checkTransformSpecChange(prevConfig, config)
    PublisherConfigDelta(transformSpec = transformSpecChange, properties = derivedPropsChange)

  }

  def checkTransformSpecChange(prevConfig: Config, config: Config): Seq[Change] = {
    config.transformSpec.getConfigDelta(prevConfig.transformSpec).changes()
  }
}
