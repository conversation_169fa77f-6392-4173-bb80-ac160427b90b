package ai.prevalent.entityinventory.publisher

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.publisher.configs._
import ai.prevalent.entityinventory.utils.DataHubUtils
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.entityinventory.utils.LineageUtils.createColumnLineage
import ai.prevalent.sdspecore.sparkbase.SDSSparkBaseConfigurable
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReaderFactory, SDSTableWriterFactory}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{col, dayofweek, days, expr}
import org.json4s.Formats
import org.json4s.jackson.Serialization



object Publisher extends SDSSparkBaseConfigurable [EIJobArgs, Config]{

  override def getInitParams: EIJobArgs = new EIJobArgs()
  override def getConfigManifest: Manifest[Config] = manifest[Config]

  override def execute(jobArgs:EIJobArgs, config: Config):Unit ={
    config.configValidator()
    val reader = SDSTableReaderFactory.getDefault(spark)
    val writer = SDSTableWriterFactory.getDefault(spark)

    val transformedModels = config.transformSpec.transform(reader, jobArgs.currentUpdateDate, spark, config)
    val derivedFieldsDF = modelWithDerivedProperties(transformedModels, config)
      .filter(config.outputTableInfo.outputFilter)
    val partitionColumns = config.outputTableInfo.partitionColumns.getOrElse(Array.empty[String])

    val outDf = removeNullFields(derivedFieldsDF)
    writer.overwritePartition(outDf, config.outputTableInfo.outputTableName,
        Array(days(col(SDSProperties.schema.UPDATED_AT_TS))) ++ partitionColumns.map(col)
    )
    val sourceTable = config.transformSpec.asInstanceOf[AttributeWriteBack].tableInfo.tableName
    val individualColumnDefinitions = createColumnLineage(sourceTable = sourceTable, targetTable = config.outputTableInfo.outputTableName, Left(transformedModels.columns))
    val derivedColumnDefinitions = createColumnLineage(sourceTable = config.outputTableInfo.outputTableName, targetTable = config.outputTableInfo.outputTableName, Right(config.derivedProperties))
    DataHubUtils.emitColumnLineageToDataHub(lineageDefinitions = Seq(individualColumnDefinitions, derivedColumnDefinitions))
    if(!config.isOLAPTable){
      val properties = Array("graph.vertex.name","graph.edge.name","graph.edge.source.name","graph.edge.target.name")
        .map( p => s"'$p'").mkString(",")
      spark.sql(s"""ALTER TABLE ${config.outputTableInfo.outputTableName} UNSET TBLPROPERTIES IF EXISTS  ( $properties )""")
    }
  }

  def modelWithDerivedProperties(modelDF: DataFrame, config: Config): DataFrame = {
    val df = modelDF.withColumn("updated_at_day_of_week", dayofweek(col(UPDATED_AT_TS)))
      .withColumn("updated_at_month_end",expr("CASE WHEN (unix_timestamp(last_day(updated_at_ts), 'yyyy-MM-dd') * 1000 + 86399999)=updated_at THEN true else false end"))
    config.derivedProperties
      .foldLeft(df)((df, property) => {
        df.withColumn(property.colName, expr(property.colExpr))
      })
  }

  override def configFormats: Formats = Serialization.formats(ConfigShortTypeHints(List(classOf[AttributeWriteBack])))

}
