package ai.prevalent.entityinventory.publisher.configs

import ai.prevalent.entityinventory.delta.Change
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.{P_ID, RELATION_ID}
import ai.prevalent.entityinventory.exceptions.InvalidDataException
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.entityinventory.publisher.configs.util.PublisherConfigReadUtil
import ai.prevalent.entityinventory.utils.SparkUtil.unionByName
import ai.prevalent.entityinventory.utils.{EILOGGER, EIUtil, SparkUtil}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.SDSConf
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
import ai.prevalent.sdspecore.utils.TableConnectUtil
import org.apache.iceberg.exceptions.NoSuchNamespaceException
import org.apache.spark.sql.functions.{col, expr, lit, regexp_replace, sha2, to_json, transform,size}
import org.apache.spark.sql.types.{ArrayType, DecimalType, StringType, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession, functions}

import scala.collection.parallel.CollectionConverters._

case class AttributeWriteBack(postSchemas: String, tableInfo: TableType) extends TransformSpec with LoggerBase {

  override def transform(reader: SDSTableReader, endEpoch: Long, spark:SparkSession, config:Config): DataFrame = {
    LOGGER.info(s"Reading knowledge graph table from  ${tableInfo.tableName}")
    val entityRelIdentifierRegex = if (tableInfo.inputTableIdentifierRegex.isDefined)
      tableInfo.inputTableIdentifierRegex.get
    else if (tableInfo.tableType == "entity")
      "^\\w++[.]sds_ei__((?>[a-z0-9]++(?:_(?=[a-z0-9]))?)++)__enrich"
    else
      "^\\w++[.]sds_ei__rel__((?>[a-z0-9]++_?)++$)"
    val entityRelName = entityRelIdentifierRegex.r.findFirstMatchIn(tableInfo.tableName).get.group(1)
    val df = EIUtil.safeReadEntity(tableInfo.tableName, expr(s" $UPDATED_AT_TS = to_timestamp($endEpoch/1000)"), reader)
      .filter(s"$KG_CONTENT_TYPE='data' OR $KG_CONTENT_TYPE IS NULL")

    val schemaEvolvedDataframe=if (config.isOLAPTable) {
      val listPublisherConfigs=PublisherConfigReadUtil.getAllpublisherConfig(spark)
      val tableNames = listPublisherConfigs.flatMap {
        eachconfig =>
          eachconfig.transformSpec match {
            case attributeWriteBack: AttributeWriteBack =>
              if (eachconfig.isOLAPTable) {
                Some(attributeWriteBack.tableInfo.tableName)
              }
              else None
            case _ => EILOGGER.jsonMiniPrint("Unsupported Config type", eachconfig)
              throw new IllegalArgumentException(s"Expected AttributeWriteBack but found " +
                s"${eachconfig.transformSpec.getClass.getSimpleName} for config: ${eachconfig}")
          }
      }
      val namedPublisherData = readPublisherData(tableNames.diff(tableInfo.tableName),reader, endEpoch, spark)
      val commonSchemaData = AttributeWriteBack.findCommonColumnNames(df, namedPublisherData)
      val commonUnionSchemaData = unionByName(commonSchemaData.map(_.df): _*)
      val commonSchemaDataframe = spark.createDataFrame(
        spark.sparkContext.emptyRDD[Row],
        commonUnionSchemaData.schema
      )
      unionByName(df, commonSchemaDataframe)
    }
    else df
    val finalDf=if(tableInfo.tableType == "entity") {
      if(schemaEvolvedDataframe.schema(ORIGIN).dataType.isInstanceOf[ArrayType])
      schemaEvolvedDataframe.withColumn(s"count_of_${ORIGIN}", size(col(ORIGIN)))
        .withColumn(s"${ORIGIN}_contribution_type",expr(s"CASE WHEN count_of_${ORIGIN} ==1 THEN 'Unique' WHEN count_of_${ORIGIN} >1 THEN 'Corroborated' ELSE NULL END"))
    else
      schemaEvolvedDataframe.withColumn(s"count_of_${ORIGIN}", lit(1))
        .withColumn(s"${ORIGIN}_contribution_type",lit("Unique"))
    } else schemaEvolvedDataframe
    LOGGER.info(s"Getting enrich tables from  $postSchemas")
    val enrichTableIdentifierRegex = if (tableInfo.enrichTableIdentifierRegex.isDefined)
      tableInfo.enrichTableIdentifierRegex.get
    else if (tableInfo.tableType == "entity")
      s"^\\w++.sds_[a-z_]+?__entity__${entityRelName}__enrich"
    else
      s"^\\w++[.]sds_[a-z_]+?__rel__${entityRelName}__enrich"

    val enrichTableSpecs = AttributeWriteBack.getTables(reader, postSchemas, enrichTableIdentifierRegex, endEpoch)

    LOGGER.info(s"Enrich tables from schemas $postSchemas are $enrichTableSpecs ")
    AttributeWriteBack.validator(enrichTableSpecs, tableInfo)

    val writeBackDF = AttributeWriteBack.writeBack(finalDf, enrichTableSpecs,tableInfo)
    populateGraphDetails(config, writeBackDF, finalDf)
  }

  private def readPublisherData(tableNames: List[String], reader: SDSTableReader, endEpoch: Long, spark: SparkSession): List[TableSpec] = {

    val publisherData = tableNames.map(tableName => {
      val df = EIUtil.safeReadEntity(tableName, expr(s" $UPDATED_AT_TS = to_timestamp($endEpoch/1000)"), reader)
      TableSpec(tableName, df)
    })
    publisherData.toList

  }

  def populateGraphDetails(config: Config, outDataFrame: DataFrame, baseDataFrame:DataFrame) = {
    if (config.isOLAPTable) {
      val olapDF = if (tableInfo.tableType == "entity") {
        val entityName = baseDataFrame.select("class").first().getString(0)
        val tablProps = Map("graph.vertex.name" -> entityName)
        tablProps.foreach(prop => baseDataFrame.sparkSession.conf.set(s"spark.sds.iceberg.table.${prop._1}", prop._2))

        outDataFrame.withColumn("graph_id", sha2(functions.concat(col(P_ID), col("updated_at_ts")), 256))
      } else {
        val sourceClass = baseDataFrame.select("source_entity_class").first().getString(0)
        val targetClass = baseDataFrame.select("target_entity_class").first().getString(0)
        val relName = baseDataFrame.select("relationship_name").first().getString(0)

        val tablProps = Map("graph.edge.name" -> relName, "graph.edge.source.name" -> sourceClass, "graph.edge.target.name" -> targetClass)
        tablProps.foreach(prop => baseDataFrame.sparkSession.conf.set(s"spark.sds.iceberg.table.${prop._1}", prop._2))

        outDataFrame.withColumn("graph_id", sha2(functions.concat(col(RELATION_ID), col("updated_at_ts")), 256))
          .withColumn("source_graph_id", sha2(functions.concat(col("source_p_id"), col("updated_at_ts")), 256))
          .withColumn("target_graph_id", sha2(functions.concat(col("target_p_id"), col("updated_at_ts")), 256))
      }
      EIUtil.updateOLAPDataTypes(olapDF)
    }
    else
      outDataFrame
  }

  override def getConfigDelta(otherConfig: TransformSpec): TransformSpecDelta = {
    otherConfig match {
      case other: AttributeWriteBack =>
        val postSchemaChangeCheck = if (other.postSchemas != this.postSchemas) {
          Some(Change("Publisher Config", f" Post Schema Change", Option.empty, Map("Old Post Schema" -> other.postSchemas, "New Post Schema" -> this.postSchemas), "Attribute"))
        } else Option.empty
        TransformSpecDelta(Seq( postSchemaChangeCheck).filter(_.isDefined).map(_.get))

      case _ =>  TransformSpecDelta(
        Seq(Some(Change("Publisher Config", f"Publisher Config Type Changed to ${otherConfig.getClass.getSimpleName}", Option.empty, Map("Old Publisher Type" -> "Denorm", "New Grouping Type" -> this.getClass.getSimpleName), "Attribute"))
      ).filter(_.isDefined).map(_.get))
    }
  }

  override def getConfigDelta(deltas: Seq[Change]): TransformSpecDelta = TransformSpecDelta(deltas)
}

object AttributeWriteBack extends LoggerBase {

  def validator(enrichTableSpecs: Seq[TableSpec], tableInfo: TableType) = {

    val columnsTableMap = enrichTableSpecs.flatMap(tableSpec => {
        tableSpec.df.columns
          .diff(tableInfo.commonColumns)
          .map((_, tableSpec.tableFullName))
      })
      .groupBy(_._1).mapValues(_.map(_._2))

    val columnsInMultiTable = columnsTableMap.filter(col => {
      val sameEntityCheck = if(tableInfo.tableType == "entity"){
        val entityList = col._2.map(_.split("__")(2))
        entityList.diff(entityList.distinct).size >= 1
      } else if(tableInfo.tableType == "rel"){
        val relList = col._2.map(_.split("__")(2))
        relList.diff(relList.distinct).size >= 1
      } else true
      col._2.size > 1 && sameEntityCheck
    })
    if (columnsInMultiTable.nonEmpty) {
      val message = columnsInMultiTable
        .map(columnInfo => s"${columnInfo._1} -> ${columnInfo._2.mkString("[", ", ", "]")}")
        .mkString("Some of the columns are present in multiple inputs, ", ", ", "")
      throw new InvalidDataException(message)
    }

    val duplicatePIDTables = enrichTableSpecs
      .map(tableSpec => {
        val isDuplicatePID = tableSpec.df.groupBy(tableInfo.uniqCol).count().filter("count >1").count() > 0
        (tableSpec, isDuplicatePID)
      })
      .filter(_._2)
    if (duplicatePIDTables.nonEmpty) {
      val message = s"Following tables contains duplicate ${tableInfo.uniqCol}, " +
        s"${duplicatePIDTables.map(_._1.tableFullName).mkString(", ")}"
      throw new InvalidDataException(message)
    }
  }

  def getTables(reader: SDSTableReader, schemas: String,
                tableIdentifierRegex: String, endEpoch: Long): List[TableSpec] = {

    val schemasList = schemas.split(",")
    val allTables = schemasList.flatMap(schema => {
      try{
        reader.listTables(schema)
          .map(tableName => s"$schema.$tableName")
      }catch {
        case e:NoSuchNamespaceException => LOGGER.warn(e.getMessage); Seq.empty
      }
    })

    val tables = allTables.filter(_.matches(tableIdentifierRegex))
    val unmatchedTables = allTables.diff(tables)
    if (unmatchedTables.length > 0) {
      LOGGER.info(s"The following tables doesn't satisfy the regex " + s"${tableIdentifierRegex}:" +
        s" ${unmatchedTables.mkString("[ ", ", ", " ]")}")
    }
    val namedGroupedTable = tables
      .map(tableName => {
        val df = EIUtil.safeReadEntity(tableName, expr(s" $UPDATED_AT_TS = to_timestamp($endEpoch/1000)"), reader).drop(KG_CONTENT_TYPE,"kg_config")
        TableSpec(tableName, df)
      }).toList
    namedGroupedTable
  }

  def writeBack(baseDF: DataFrame, enrichTableSpecs: Seq[TableSpec],
                tableInfo: TableType): DataFrame  = {
    val enrichedDF = enrichTableSpecs.foldLeft(baseDF)((baseDF, enrichSpec) => {
      val enrichDF = enrichSpec.df
      val enrichColumns = enrichDF.columns.diff(baseDF.columns)

      val invColsInEnrich = enrichDF.columns.diff(tableInfo.commonColumns).intersect(baseDF.columns)
      if (!invColsInEnrich.isEmpty) {
        LOGGER.warn(s"Ignoring inventory columns [${invColsInEnrich.mkString(",")}] in ${enrichSpec.tableFullName}")
      }

      val finalColumns = baseDF.columns.map(baseDF(_)) ++ enrichColumns.map(enrichDF(_))
      baseDF.join(enrichDF, baseDF(tableInfo.uniqCol) === enrichDF(tableInfo.uniqCol), "left")
        .select(finalColumns: _*)
    })

    enrichedDF
  }
  def findCommonColumnNames(baseTable: DataFrame, listTables: List[TableSpec]): List[TableSpec] = {
    val baseColumns = baseTable.schema.filter(!_.dataType.isInstanceOf[StructType]).map(_.name).toSet
    listTables.map { tableSpec =>
      val dfColumns = tableSpec.df.columns.toSet
      val commonColumns = baseColumns.intersect(dfColumns).toArray
      val newDF = tableSpec.df.select(commonColumns.map(col): _*)
      TableSpec(tableSpec.tableFullName, newDF)
    }
  }
}

case class TableSpec(tableFullName: String, df: DataFrame) {
  override def toString: String = s"Table Spec (tableFullName = $tableFullName)"
}

case class TableType(tableName: String, tableType: String, inputTableIdentifierRegex: Option[String], enrichTableIdentifierRegex: Option[String], commonColumns: Array[String], uniqCol: String)