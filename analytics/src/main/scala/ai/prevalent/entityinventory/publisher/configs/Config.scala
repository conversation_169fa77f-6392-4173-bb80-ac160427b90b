package ai.prevalent.entityinventory.publisher.configs

import ai.prevalent.entityinventory.common.configs.{EIConfig, Property}
import ai.prevalent.entityinventory.delta.{Change, Delta, PublisherConfigDelta}
import ai.prevalent.entityinventory.exceptions.UnsupportedConfig
import ai.prevalent.entityinventory.relationship.disambiguation.config.{DisambiguationGrouping, DisambiguationGroupingDelta}
import ai.prevalent.entityinventory.utils.EILOGGER
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.json4s.TypeHints

case class Config (transformSpec: TransformSpec = null, derivedProperties: Array[Property] = Array.empty,
                   outputTableInfo: OutputTableType, isOLAPTable:Boolean = true)
  extends EIConfig[Config, PublisherConfigDelta] {
  def configValidator(): Unit = {
  }

  override def getConfigDelta(otherConfig: Config): PublisherConfigDelta = PublisherConfigDelta(otherConfig, this)

  override def getConfigDelta(deltas: Seq[Change]): PublisherConfigDelta = PublisherConfigDelta(this, deltas)
}

trait TransformSpec extends EIConfig[TransformSpec, TransformSpecDelta]{
  def transform(reader: SDSTableReader, endEpoch:Long, spark:SparkSession, config:Config): DataFrame
}

case class TransformSpecDelta(
                             Changes: Seq[Change] = Seq.empty,
                             ) extends Delta

case class OutputTableType( partitionColumns: Option[Array[String]], outputTableName: String = "", outputFilter: String = "true")

case class ConfigShortTypeHints(hints: List[Class[_]], override val typeHintFieldName: String = "type")
  extends TypeHints {
  def hintFor(clazz: Class[_]) =
    Some(clazz.getName.substring(clazz.getName.lastIndexOf(".") + 1))
  def classFor(hint: String, parent: Class[_]) = hints find (hintFor(_).exists(_ == hint))
}