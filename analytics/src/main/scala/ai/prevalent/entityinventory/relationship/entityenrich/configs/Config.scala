package ai.prevalent.entityinventory.relationship.entityenrich.configs

import ai.prevalent.entityinventory.common.configs.{EIConfig, Property}
import ai.prevalent.entityinventory.delta.{Change, EntityEnrichConfigDelta}
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.P_ID
import ai.prevalent.entityinventory.multihop.configs.EntityEnrichment

case class Config(entityTableName: String = null, filter: String = "true", countEnriches: List[CountEnrich] = List.empty, inheritedPropertyEnrichment: Seq[EntityEnrichment]=Seq.empty,
                  derivedProperties: Array[Property] = Array.empty, output: Output = null) extends EIConfig[Config,EntityEnrichConfigDelta] {
  override def getConfigDelta(otherConfig: Config): EntityEnrichConfigDelta = EntityEnrichConfigDelta(otherConfig, this)

  override def getConfigDelta(deltas: Seq[Change]): EntityEnrichConfigDelta = EntityEnrichConfigDelta(this, deltas)
}

case class Output(filter: String = "true", outputTableName: String=null, writeOnlyEnrichedFields: Boolean=false, partitionColumns:Array[String]=Array.empty)

case class CountEnrich(colName: String=null, relationshipTableName: String = null, filter: String = "true")
