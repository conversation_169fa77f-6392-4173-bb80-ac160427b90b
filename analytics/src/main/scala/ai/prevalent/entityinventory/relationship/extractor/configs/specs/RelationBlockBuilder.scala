package ai.prevalent.entityinventory.relationship.extractor.configs.specs

import ai.prevalent.entityinventory.common.configs.Property
import ai.prevalent.entityinventory.delta.RelationshipConfigDelta
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.P_ID
import ai.prevalent.entityinventory.loader.LoaderUtils
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.entityinventory.relationship.extractor.Extractor
import ai.prevalent.entityinventory.utils.StructFieldReplaceUtils.{getNonStructFields, replaceStructProperties}
import ai.prevalent.entityinventory.utils.{EILOGGER, SparkUtil}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.utils.ConfigUtils
import ai.prevalent.entityinventory.utils.{EILOGGER, SparkUtil}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions._

trait RelationBlockBuilder extends LoggerBase {
  def build(inputSources: Seq[InputSource], prevMiniSDM: DataFrame, resolvers: DisambiguationResolvers, optionalAttributes: Array[Attribute], currentUpdatedAt: Long, config: Config, prevConfigStr: Option[String]): RelationBuildInfo = {

    val sourceDF = generateSourceDF(inputSources, config, prevConfigStr, currentUpdatedAt)
    val sourceEntityClass = if (inputSources.last.sourceLoader.nonEmpty) {
      inputSources.last.sourceLoader.last.entity.name
    } else {
      sourceDF.head.select("source_entity_class").first().getString(0)
    }

    val targetEntityClass = if (inputSources.last.targetLoader.nonEmpty) {
      inputSources.last.targetLoader.last.entity.name
    } else {
      sourceDF.head.select("target_entity_class").first().getString(0)
    }
    val prevMiniSDMProcessed =prevMiniSDM.withColumn("source_entity_class", lit(sourceEntityClass))
      .withColumn("target_entity_class", lit(targetEntityClass))
      .withColumn(UPDATED_AT, expr("cast(null as bigint)"))
    val unionedDF = SparkUtil.unionByName(sourceDF :+ prevMiniSDMProcessed :_*)
    val checkPointedUnion = checkpointIfEnabled(unionedDF)
    val (hasSourceJoin, hasTargetJoin) = config.inputSourceInfo.headOption.map(info => (
      Option(info.sourceMappingInfo.joinCondition).isDefined,
      Option(info.targetMappingInfo.joinCondition).isDefined
    )).getOrElse((false, false))

    val sourceResolvedDF = if (!hasSourceJoin && config.interSourcePath != null) {
      LOGGER.info("Applying source resolution")
      val srcResolver = getEntityResolver(resolvers, sourceEntityClass)
      resolvePID(checkPointedUnion, srcResolver, "source")
    } else {
      LOGGER.info("Skipping source resolution - using join condition or no resolver")
      checkPointedUnion
    }

    val finalResolvedDF = if (!hasTargetJoin && config.interTargetPath != null) {
      LOGGER.info("Applying target resolution")
      val targetResolver = getEntityResolver(resolvers, targetEntityClass)
      resolvePID(sourceResolvedDF, targetResolver, "target")
    } else {
      LOGGER.info("Skipping target resolution - using join condition or no resolver")
      sourceResolvedDF
    }

    val blockBuiltDF = buildBlock(finalResolvedDF)
    val newMiniSDM = createMiniSDM(checkPointedUnion,config, optionalAttributes)

    RelationBuildInfo(blockBuiltDF, newMiniSDM)
  }

  private def getEntityResolver(resolvers: DisambiguationResolvers, entityClass: String): DisambiguationResolvers = {
    DisambiguationResolvers(
      resolvers.intraSourceDF.filter(s"class = '$entityClass'"),
      resolvers.interSourceDF.filter(s"class = '$entityClass'")
    )
  }

  private def checkpointIfEnabled(df: DataFrame): DataFrame = {
    if (df.sparkSession.sparkContext.getCheckpointDir.isDefined) df.checkpoint(true)
    else df
  }

  def resolvePIDs(resolvers: DisambiguationResolvers) : DataFrame = {
    val intra = resolvers.intraSourceDF
    val inter = resolvers.interSourceDF
    inter.as("inter")
      .join(intra.as("intra"),expr(s"intra.disambiguated_p_id = inter.$P_ID"),"LEFT")
      .select(expr(s"coalesce(intra.$P_ID,inter.$P_ID)").as(P_ID),col("inter.disambiguated_p_id"))
  }

  def generateSourceDF(inputSources: Seq[InputSource], currentConfig: Config, prevConfigStr: Option[String], currentUpdatedAt: Long) = {

    val deltaInputSources = inputSources.map(p => p.copy(dataFrame = p.dataFrame.filter("is_incremental_delta")))
    val incrementalSrdm = generateSource(deltaInputSources, currentConfig.optionalAttributesProcessed, currentUpdatedAt)
    if (prevConfigStr.isDefined) {
      val deltaConfig = getConfigDelta(currentConfig, prevConfigStr.get)
      LOGGER.info(s"Delta Config - ${deltaConfig}")
      if(deltaConfig.isDefined){
        val historicalSrdm = generateSource(inputSources, deltaConfig.get.optionalAttributesProcessed, currentUpdatedAt)
        historicalSrdm ++ incrementalSrdm
      } else incrementalSrdm
    } else incrementalSrdm
  }

  def generateSource(inputSources: Seq[InputSource], optionalAttributes: Array[Attribute], currentUpdatedAt: Long) = {
    val sourceEntityClass = if (inputSources.last.sourceLoader.nonEmpty) {
      inputSources.last.sourceLoader.last.entity.name
    } else {
      inputSources.last.dataFrame.select("source_entity_class").first().getString(0)
    }

    val targetEntityClass = if (inputSources.last.targetLoader.nonEmpty) {
      inputSources.last.targetLoader.last.entity.name
    } else {
      inputSources.last.dataFrame.select("target_entity_class").first().getString(0)
    }

    val optionalAttributesRef = optionalAttributes.map(attr => expr(attr.exp).expr)
      .flatMap(_.references.map(_.name.split("[.]")(0)))
      .distinct
    val joinFields = inputSources.flatMap(_.joinCondition)
      .flatMap(expr(_).expr.references.map(_.name))
      .filter(_.startsWith("s.")).map(_.stripPrefix("s.")).toSet
    val sdmPrimaryKeyDF = inputSources.map(inputSource => {

      val tempAddedDF = inputSource.tempProperties.foldLeft(inputSource.dataFrame) { (tempDf, name) =>
        if(tempDf.columns.contains(name))
          tempDf.withColumnRenamed(name, s"temp__rel__$name")
        else
          tempDf
      }
      val inputSourcesDF = inputSource.sourceLoader.zipWithIndex.foldLeft(tempAddedDF)((df, source) => {

          val sourcePKey = LoaderUtils.generatePrimaryKey(df, source._1)
            .withColumn("class", lit(sourceEntityClass))
            .withColumn("origin", expr(source._1.origin))
            .withColumn("primary_key_attribute", lit(source._1.primaryKey))
            .withColumn(s"source_${source._2}_$P_ID", expr(SDSProperties.GUID_EXPR))
            .withColumn(s"source_${source._2}_$P_ID", expr(s"CASE WHEN  $PRIMARY_KEY IS NOT NULL AND (${source._1.filterBy}) THEN source_${source._2}_$P_ID END"))
          sourcePKey
        })
        .withColumn("source_entity_class", lit(sourceEntityClass))

      val inputSrcTargetDF = inputSource.targetLoader.zipWithIndex.foldLeft(inputSourcesDF)((df, target) => {
        val sourcePKey = LoaderUtils.generatePrimaryKey(df, target._1)
          .withColumn("class", lit(targetEntityClass))
          .withColumn("origin", expr(target._1.origin))
          .withColumn("primary_key_attribute", lit(target._1.primaryKey))
          .withColumn(s"target_${target._2}_$P_ID", expr(SDSProperties.GUID_EXPR))
          .withColumn(s"target_${target._2}_$P_ID", expr(s"CASE WHEN $PRIMARY_KEY IS NOT NULL AND (${target._1.filterBy}) THEN target_${target._2}_$P_ID END"))
        sourcePKey
      }).withColumn(ORIGIN, col(s"${ORIGIN}__temp"))
      val tempRemovedDF = inputSource.tempProperties.foldLeft(inputSrcTargetDF) { (tempDf, name) =>
        if (tempDf.columns.contains(name) && tempDf.columns.contains(s"temp__rel__$name")) {
          tempDf.drop(name).withColumnRenamed(s"temp__rel__$name", name)
        } else if (!tempDf.columns.contains(name) && tempDf.columns.contains(s"temp__rel__$name")) {
          tempDf.withColumnRenamed(s"temp__rel__$name", name)
        } else {
          tempDf  
        }
      }
      val pidGenDF = tempRemovedDF
        .filter(col(inputSource.dataEventTimestampKey) <= to_timestamp(lit(currentUpdatedAt).divide(1000)))
        .transform(df => addColumnIfNotExists(df, s"source_$P_ID", array(inputSource.sourceLoader.zipWithIndex.map(x => col(s"source_${x._2}_$P_ID")): _*)))
        .transform(df => addColumnIfNotExists(df, s"target_$P_ID", array(inputSource.targetLoader.zipWithIndex.map(x => col(s"target_${x._2}_$P_ID")): _*)))
        .withColumn(s"source_$P_ID", explode(filter(col(s"source_$P_ID"), x => x.isNotNull)))
        .withColumn(s"target_$P_ID", explode(filter(col(s"target_$P_ID"), x => x.isNotNull)))
        .withColumn("target_entity_class", lit(targetEntityClass))
        .withColumn("source_entity_class", lit(sourceEntityClass))

      val commonCols = Array(s"source_$P_ID", s"target_$P_ID", "target_entity_class", "source_entity_class", "event_timestamp_epoch", inputSource.dataEventTimestampKey, UUID)
      val selectCols = (commonCols ++ joinFields ++ optionalAttributesRef ++ inputSource.tempProperties).distinct.intersect(pidGenDF.columns).map(col(_))
      pidGenDF.select(selectCols: _*)
    }).toList

    sdmPrimaryKeyDF
  }
  def addColumnIfNotExists(df: DataFrame, columnName: String, columnExpr: => org.apache.spark.sql.Column): DataFrame = {
    if (!df.columns.contains(columnName)) {
      df.withColumn(columnName, columnExpr)
    } else {
      df.withColumn(columnName,expr(s"array($columnName)"))
    }
  }
  def getConfigDelta(config: Config, prevConfigStr: String): Option[Config] = {
    val prevConfig: Config = ConfigUtils.getConfigFromJSON(prevConfigStr, Extractor.configFormats)(Extractor.getConfigManifest)
    val delta: RelationshipConfigDelta = RelationshipConfigDelta(prevConfig, newConfig = config)
    val tempAttrNames = delta.properties
      .filter(_.category.eq("Attribute"))
      .map(_.name)
      .intersect(config.optionalAttributes.map(_.name))
    val changedTempAttr = config.optionalAttributes.filter(p => tempAttrNames.contains(p.name))
    if (!changedTempAttr.isEmpty ) {
      val deltaConfig = config.copy(optionalAttributes = changedTempAttr)
      Some(deltaConfig)
    } else Option.empty
  }

  def resolvePID(dataFrame: DataFrame,
                 resolvers: DisambiguationResolvers, primaryKeyPrefix: String) : DataFrame = {
    val resolver = resolvePIDs(resolvers)
    val sourceJoinKey = s"${primaryKeyPrefix}_$P_ID"
    dataFrame.as("int").join(resolver.as("res"), expr(s"$sourceJoinKey = res.$P_ID"))
      .select("int.*", "res.disambiguated_p_id")
      .withColumn(sourceJoinKey, col("disambiguated_p_id"))
      .drop("disambiguated_p_id")
  }

  def buildBlock(sourceDF: DataFrame):DataFrame

  def createMiniSDM(sourceDF: DataFrame,config: Config,optionalAttributes: Array[Attribute]):DataFrame
}

case class RelationBuildInfo(relationDF: DataFrame, miniSDM:DataFrame)
