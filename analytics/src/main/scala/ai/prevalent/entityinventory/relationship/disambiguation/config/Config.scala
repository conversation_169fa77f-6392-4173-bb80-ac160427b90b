package ai.prevalent.entityinventory.relationship.disambiguation.config

import ai.prevalent.entityinventory.common.configs.{EIConfig, Property}
import ai.prevalent.entityinventory.delta.{Change, Delta, RelationshipDisambiguationConfigDelta}
import ai.prevalent.entityinventory.disambiguator.configs.specs.ConfidenceMatrix
import ai.prevalent.entityinventory.relationship.disambiguation.Disambiguation
import org.apache.spark.sql.DataFrame
import org.json4s.jackson.Serialization.write
case class Config(
                   relationshipModels: List[RelationshipModel] = null,
                   disambiguation: RelDisambiguation = null,
                   derivedProperties: Array[Property] = Array.empty,
                   output: Output = null
                 ) extends EIConfig[Config, RelationshipDisambiguationConfigDelta] {

  override def toString: String = write(this)(Disambiguation.configFormats)

  override def getConfigDelta(otherConfig: Config): RelationshipDisambiguationConfigDelta = RelationshipDisambiguationConfigDelta(otherConfig, this)
  override def getConfigDelta(deltas: Seq[Change]): RelationshipDisambiguationConfigDelta = RelationshipDisambiguationConfigDelta(this, deltas)

}
case class RelationshipModel(tableName:String,name:String, filter:String = "true")
case class RelationshipModelDF(df:DataFrame, name:String,isTableExists:Boolean,path:String)

case class RelDisambiguation(
                           disambiguationGrouping: DisambiguationGrouping = null,
                           confidenceMatrix: Option[Array[String]] = None,
                           excludeValues: Array[String] = Array(""),
                           strategy: Option[Strategy] = None
                         ) extends ConfidenceMatrix(confidenceMatrix.getOrElse(Array.empty))

case class FieldLevelConfidenceMatrix(field: String,
                                      confidenceMatrix: Array[String], persistNonNullValue: Option[Boolean], restrictToConfidenceMatrix: Boolean = false) extends ConfidenceMatrix(confidenceMatrix)
case class ValueConfidence(field: String, confidenceMatrix: Array[String]) extends ConfidenceMatrix(confidenceMatrix)

case class SingleSource(field: String, source:String, persistNonNullValue:Boolean = true)

case class Aggregation(field: String, function:String)

case class Strategy(fieldLevelConfidenceMatrix: Option[Array[FieldLevelConfidenceMatrix]],
                    rollingUpFields: Array[String] = Array.empty,
                    valueConfidence: Option[Array[ValueConfidence]],
                    singleSource:List[SingleSource] = List.empty,
                    aggregation:List[Aggregation] = List.empty)

case class Output(filter: String = "true", disambiguatedModelLocation: String, resolverLocation: Option[String], fragmentLocation: String=null, isFragmentOLAPTable:Boolean = true)

object Strategy {
  def empty = Strategy(Option.empty, Array.empty, Option.empty)
}


trait DisambiguationGrouping extends EIConfig[DisambiguationGrouping, DisambiguationGroupingDelta] {
  def generateGroup(nonResolvedDF:DataFrame, config: Config): DataFrame
}

case class DisambiguationGroupingDelta(
  addedBlockVariables: Option[Change] =Option.empty,
  removedBlockVariables: Option[Change] = Option.empty,
  statusFieldChange: Option[Change] = Option.empty,
  groupingTypeChange: Option[Change] = Option.empty
) extends Delta