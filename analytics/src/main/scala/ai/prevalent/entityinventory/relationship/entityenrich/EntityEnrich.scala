package ai.prevalent.entityinventory.relationship.entityenrich

import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.{P_ID, RELATION_ID}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{KG_CONTENT_TYPE, UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.common.configs.{EIJobArgs, Property}
import ai.prevalent.entityinventory.lineage.ColumnLineageDefinition
import ai.prevalent.entityinventory.multihop.MultiHopEnrichment
import ai.prevalent.entityinventory.multihop.configs.EntityEnrichment
import ai.prevalent.entityinventory.relationship.entityenrich.configs.Config
import ai.prevalent.entityinventory.utils.DataHubUtils
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.entityinventory.utils.LineageUtils.createColumnLineage
import ai.prevalent.sdspecore.sparkbase.SDSSparkBaseConfigurable
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReaderFactory, SDSTableWriterFactory}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions._

object EntityEnrich extends SDSSparkBaseConfigurable[EIJobArgs, Config] {

  override def execute(params: EIJobArgs, config: Config): Unit = {

    try{
      spark.sparkContext.setCheckpointDir(spark.conf.get("spark.checkpoint.dir"))
    } catch {
      case _ =>
    }

    val reader = SDSTableReaderFactory.getDefault(spark)
    val writer = SDSTableWriterFactory.getDefault(spark)
    val entityDF = {
      val df = reader.read(config.entityTableName).filter(s"$UPDATED_AT_TS = to_timestamp(${params.currentUpdateDate}/1000) AND ${config.filter}")
      if(df.columns.contains(KG_CONTENT_TYPE))
        df.filter(s"$KG_CONTENT_TYPE='data' OR $KG_CONTENT_TYPE IS NULL")
      else df
    }

    val isEntityDF = entityDF.columns.contains("class")

    val enrichedEntity = if(isEntityDF )
      EntityEnrichUtil.build(config, entityDF,params)
    else entityDF

    val relEnrichedEntity = MultiHopEnrichment(reader,params.currentUpdateDate)
      .enrich(enrichedEntity, config.inheritedPropertyEnrichment, isEntityDF)
    val outputDf = modelWithDerivedProperties(relEnrichedEntity,config)
    val nullRemovedOutputDf = removeNullFields(outputDf)
    val columns: Seq[String] = if (config.output.writeOnlyEnrichedFields)
      (Seq(if(isEntityDF) P_ID else RELATION_ID, UPDATED_AT, UPDATED_AT_TS)++ config.derivedProperties.map(_.colName) ++ config.countEnriches.map(_.colName) ++ extractFromEnrichments(config.inheritedPropertyEnrichment)).distinct.filter(nullRemovedOutputDf.columns.toSet.contains)
    else
      nullRemovedOutputDf.columns.toSeq
    val individualColumnDefinitions = createColumnLineage(sourceTable = config.entityTableName, targetTable = config.output.outputTableName, Left(entityDF.columns))
    val inheritedColumnDefinitions = createAllInheritedColumnLineages(config.inheritedPropertyEnrichment,config.output.outputTableName)
    val derivedColumnDefinitions = createColumnLineage(sourceTable = config.output.outputTableName, targetTable = config.output.outputTableName, Right(config.derivedProperties))
    DataHubUtils.emitColumnLineageToDataHub(lineageDefinitions = (inheritedColumnDefinitions ++ Seq(individualColumnDefinitions, derivedColumnDefinitions)).toSeq)
    writer.overwritePartition(nullRemovedOutputDf.filter(expr(config.output.filter)).select(columns.map(col): _*), config.output.outputTableName, Array(days(col("updated_at_ts")))++ config.output.partitionColumns.map(col))
  }

  def extractFromEnrichments(enrichments: Seq[EntityEnrichment]): Set[String] = {
    enrichments.flatMap { e =>
      e.colEnrichments.map(_.colName) ++
        extractFromEnrichments(e.inheritedPropertyEnrichment)
    }.toSet
  }

  def modelWithDerivedProperties(inputDF: DataFrame, config: Config): DataFrame = {
    val spark = inputDF.sparkSession
    import spark.implicits._
    val deriveProperties=config.derivedProperties
    def hasMissingFields(exprStr: String, df: DataFrame): Boolean = {
      val columnNames = df.columns.toSet
      val parsedExpr = expr(exprStr)
      val referencedColumns = parsedExpr.expr.references.map(_.name.split("[.]")(0))
      !referencedColumns.forall(columnNames.contains)
    }
    deriveProperties.foldLeft(inputDF)((df, property) => {
      if(!hasMissingFields(property.colExpr, df)) {
        df.withColumn(property.colName, expr(property.colExpr))
      } else df
    })
  }

  def createAllInheritedColumnLineages(inheritedPropertyEnrichments: Seq[EntityEnrichment], targetTable: String): List[ColumnLineageDefinition] = {
    inheritedPropertyEnrichments.flatMap { inheritedEntity =>
      val sourceTable = inheritedEntity.tableName
      val properties = inheritedEntity.colEnrichments.map { colEnrich =>
        Property(colEnrich.colName, colEnrich.colExpr)
      }.toArray
      val currentLineage = createColumnLineage(sourceTable, targetTable, Right(properties))
      val nestedLineages = createAllInheritedColumnLineages(inheritedEntity.inheritedPropertyEnrichment, targetTable)
      currentLineage :: nestedLineages
    }.toList
  }

  /** Override this method in concrete class to specify job arguments class
   *
   * @example
   * override def getInitParams:JobArgs = new JobArgs
   */
  override def getInitParams: EIJobArgs = new EIJobArgs()

  /** Must be implemented in concrete class to specify Configuration class
   *
   * @example
   * override def getConfigManifest:Manifest[ConfigClass] = manifest[ConfigClass]
   */
  override def getConfigManifest: Manifest[Config] = manifest[Config]
}
