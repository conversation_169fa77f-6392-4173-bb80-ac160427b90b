package ai.prevalent.entityinventory.relationship.disambiguation.config.grouping

import ai.prevalent.entityinventory.delta.Change
import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils.DISAMBIGUATION_GROUP_ID
import ai.prevalent.entityinventory.relationship.disambiguation.config.{Config, DisambiguationGrouping, DisambiguationGroupingDelta}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.col

case class VariablesBased(blockVariables: List[String]) extends DisambiguationGrouping {

  override def generateGroup(nonResolvedDF: DataFrame, config: Config): DataFrame = {

    val selectVars = Array(col("relationship_last_seen_date"), col("relationship_id")) ++ blockVariables.map(col(_))
    val blockVariableDF = nonResolvedDF
      .select(selectVars: _*)

    val window = Window.partitionBy(blockVariables.map(col(_)): _*).orderBy(col("relationship_last_seen_date").asc)

    val groupedDF = blockVariableDF
      .withColumn("row_number", row_number().over(window))
      .withColumn("mono_id", when(expr("row_number=1"), monotonically_increasing_id())).checkpoint(true)
      .withColumn(DISAMBIGUATION_GROUP_ID, first(col("mono_id"), ignoreNulls = true).over(window))
    groupedDF.select(DISAMBIGUATION_GROUP_ID, "relationship_id")
  }

  override def getConfigDelta(otherConfig: DisambiguationGrouping): DisambiguationGroupingDelta = {
    otherConfig match {
      case other: VariablesBased => {
        val blockVariablesChanged = blockVariables != other.blockVariables
        val addedBlockVariables = blockVariables.diff(other.blockVariables)
        val removedBlockVariables = other.blockVariables.diff(blockVariables)
        if (blockVariablesChanged) {
          DisambiguationGroupingDelta(
            Some(Change("Disambiguation Grouping", "Variable Based Change", Option.empty, Map("Old variables" -> other.blockVariables.mkString(","), "Added variables" -> addedBlockVariables.mkString(",")), "Attribute"))
            , Some(Change("Disambiguation Grouping", "Variable Based Change", Option.empty, Map("Old variables" -> other.blockVariables.mkString(","), "Removed variables" -> removedBlockVariables.mkString(",")), "Attribute")))
        } else {
          DisambiguationGroupingDelta()
        }
      }
      case _ =>  DisambiguationGroupingDelta(
        groupingTypeChange = Some(Change("Disambiguation Grouping", f"Disambiguation Grouping Type Changed to ${otherConfig.getClass.getSimpleName}", Option.empty, Map("Old Grouping Type" -> "VariableBased", "New Grouping Type" -> this.getClass.getSimpleName), "Attribute"))
      )
    }
  }

  override def getConfigDelta(deltas: Seq[Change]): DisambiguationGroupingDelta = null
}