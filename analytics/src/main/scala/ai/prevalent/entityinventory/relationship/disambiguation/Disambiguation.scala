package ai.prevalent.entityinventory.relationship.disambiguation

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.RELATION_ID
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.entityinventory.publisher.configs.ConfigShortTypeHints
import ai.prevalent.entityinventory.relationship.disambiguation.DisambiguationUtils.enrichRelationGraphDetails
import ai.prevalent.entityinventory.relationship.disambiguation.config.grouping.{Union, VariablesBased, VulnerabilityFinding}
import ai.prevalent.entityinventory.relationship.disambiguation.config.{Config}
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.sdspecore.sparkbase.SDSSparkBaseConfigurable
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import org.apache.spark.sql.{DataFrame, functions}
import org.apache.spark.sql.functions._
import org.json4s.Formats
import org.json4s.jackson.Serialization

object Disambiguation extends SDSSparkBaseConfigurable[EIJobArgs,Config]{


  /** Override this method to implement execution logic
   *
   * @param params - Object of type P, which is the class defining job arguments
   * @param config - Case class defining configuration structure
   */
  override def execute(params: EIJobArgs, config: Config): Unit = {
    spark.sparkContext.setCheckpointDir(spark.conf.get("spark.checkpoint.dir"))
    val reader = SDSTableReaderFactory.getDefault(spark)
    val outputDF = DisambiguationUtils.build(config, params, reader, spark)
    val nullRemovedResolvedDf = removeNullFields(outputDF.resolvedDF)
    val relationshipName = nullRemovedResolvedDf.select("relationship_name").first().getString(0)
    getWriter().overwritePartition(nullRemovedResolvedDf, config.output.disambiguatedModelLocation, Array(days(col(UPDATED_AT_TS))))
    if (config.output.isFragmentOLAPTable) {
      val (nullRemovedResolverDf: DataFrame,nullRemovedNonResolvedDf: DataFrame)=enrichRelationGraphDetails(outputDF.resolverDF,relationshipName,outputDF.nonResolvedDF)
      val fragmentTableProps = Map("graph.vertex.name" -> s"Fragment $relationshipName", "graph.cache.enabled" -> "true")
      val resolverTableProps = Map("graph.edge.name" -> s"${relationshipName} Has Fragment", "graph.edge.source.name" -> relationshipName, "graph.edge.target.name" -> s"Fragment $relationshipName", "graph.cache.enabled" -> "true")
      getWriter(fragmentTableProps).overwritePartition(nullRemovedNonResolvedDf, config.output.fragmentLocation, Array(days(col(UPDATED_AT_TS))))
      getWriter(resolverTableProps).overwritePartition(nullRemovedResolverDf, config.output.resolverLocation.get, Array(days(col(UPDATED_AT_TS))))
    } else if(config.output.resolverLocation.isDefined){
      val nullRemovedResolverDf = removeNullFields(outputDF.resolverDF)
      getWriter().overwritePartition(nullRemovedResolverDf, config.output.resolverLocation.get, Array(days(col(UPDATED_AT_TS)), col("relationship_name"), col("data_source_name")))
    }
  }

  def getWriter(tableProps: Map[String, String] = Map.empty): SDSTableWriter = SDSTableWriterFactory.getDefault(
    spark, tableProperties = tableProps, options = Map("partitionOverwriteMode" -> "dynamic"))

  /** Override this method in concrete class to specify job arguments class
   *
   * @example
   * override def getInitParams:JobArgs = new JobArgs
   */
  override def getInitParams: EIJobArgs = new EIJobArgs

  /** Must be implemented in concrete class to specify Configuration class
   *
   * @example
   * override def getConfigManifest:Manifest[ConfigClass] = manifest[ConfigClass]
   */
  override def getConfigManifest: Manifest[Config] = manifest[Config]


  override def configFormats: Formats = Serialization.formats(ConfigShortTypeHints(List(classOf[VulnerabilityFinding], classOf[VariablesBased],classOf[Union])))
}
