package ai.prevalent.entityinventory.relationship.extractor.configs.specs

import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._

case class StartState(values: Array[String], occurrence: String)

case class EndState(values: Array[String], occurrence: String)

class ValueBasedRelationBuilderStrategySpec(val attribute: String,
                                            val startState: StartState,
                                            val endState: EndState) {

  def pkAttribute = s"pk__$attribute"

  def build(sourceDF: DataFrame): DataFrame = {
    val initialWinSpec = Window
      .partitionBy("source_p_id", "target_p_id")
      .orderBy(asc("event_timestamp_epoch"))

    val alwaysFalseCondition = when(expr("1=0"), lit(null).cast("int"))

    val startValueCondition = if(startState.occurrence=="FIRST") {
      startState.values.foldLeft(alwaysFalseCondition)((finalCondition, value)=> {
        finalCondition.when((col(pkAttribute) === lit(value)).and((lag(col(pkAttribute), 1).over(initialWinSpec) =!= lit(value)).or(lag(col(pkAttribute), 1).over(initialWinSpec).isNull)), col("row_number"))
      })
    } else {
      startState.values.foldLeft(alwaysFalseCondition)((finalCondition, value)=> {
        finalCondition.when((col(pkAttribute) === lit(value)).and((lead(col(pkAttribute), 1).over(initialWinSpec) =!= lit(value)).or(lead(col(pkAttribute), 1).over(initialWinSpec).isNull)), col("row_number"))
      })
    }

    val endValueCondition = if(endState.occurrence=="FIRST") {
      endState.values.foldLeft(alwaysFalseCondition)((finalCondition, value)=> {
        finalCondition.when((col(pkAttribute) === lit(value)).and((lag(col(pkAttribute), 1).over(initialWinSpec) === lit(value)).or(lag(col(pkAttribute), 1).over(initialWinSpec).isNull)), lit(null).cast("int")).otherwise(col("block_id"))
      })
    } else {
      col("block_id")
    }


    val outputDF = sourceDF
      .withColumn(pkAttribute, col(attribute))
      .withColumn("row_number", monotonically_increasing_id()).checkpoint(true)
      .withColumn("block_id", startValueCondition)
      .withColumn("block_id", last(col("block_id"), ignoreNulls = true).over(initialWinSpec.rowsBetween(Window.unboundedPreceding, Window.currentRow)))
      .withColumn("block_id", endValueCondition)

    outputDF
      .drop("row_number")
      .withColumn(s"pk__$attribute", last(pkAttribute, ignoreNulls = true).over(Window.partitionBy("block_id").orderBy(asc("event_timestamp_epoch")).rowsBetween(Window.unboundedPreceding, Window.unboundedFollowing)))
      .filter("block_id IS NOT NULL")

  }

}
