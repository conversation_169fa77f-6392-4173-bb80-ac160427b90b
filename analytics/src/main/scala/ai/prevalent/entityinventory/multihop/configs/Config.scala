package ai.prevalent.entityinventory.multihop.configs

import ai.prevalent.entityinventory.common.configs.Property
import org.apache.spark.sql.functions.expr
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization

case class ColumnEnrichment(
                             colName: String,
                             aggExpr: String,
                             aggFunction: String,
                             colExpr: String
                           ){
  override def toString: String = Serialization.write(this)(DefaultFormats)
}

object ColumnEnrichment{
  def apply(colName: String, aggExpr:String, aggFunction:String, colExpr:String) = new ColumnEnrichment(colName, aggExpr, aggFunction, colExpr)
  def apply(colName: String, aggExpr:String, aggFunction:String) = new ColumnEnrichment(colName, aggExpr, aggFunction, colName)
}



case class EntityEnrichment(
                             tableName: String,
                             alias: String,
                             filter: String = "true",
                             joinType:String = "LEFT",
                             preTransform: Seq[Property]= Seq.empty,
                             colEnrichments: Seq[ColumnEnrichment]=Seq.empty,
                             inheritedPropertyEnrichment: Seq[EntityEnrichment] = Seq.empty,
                             leftCol: Option[String] = Option.empty,
                             rightCol: Option[String] = Option.empty
                           ) {
  override def toString: String = Serialization.write(this)(DefaultFormats)
}

object ConfigOptimizer {

  def optimize(enrichments: Seq[EntityEnrichment]): Seq[EntityEnrichment] = {
    val withConvertedCounts = convertCountExpressions(enrichments)
    val optimized=optimizeEnrichmentsByTable(withConvertedCounts)
    val withUpdatedJoinTypes = updateJoinTypes(optimized)
    fixDuplicateAliases(withUpdatedJoinTypes)  
  }
  def updateJoinTypes(enrichments: Seq[EntityEnrichment]): Seq[EntityEnrichment] = {
    def isAllSinglePath(e: Seq[EntityEnrichment]): Boolean =
      e.isEmpty || (e.size == 1 && e.forall(x => isAllSinglePath(x.inheritedPropertyEnrichment)))
    if (!isAllSinglePath(enrichments)) enrichments
    else enrichments.map(e => e.copy(
      joinType = "INNER",
      inheritedPropertyEnrichment = if (e.inheritedPropertyEnrichment.nonEmpty) updateJoinTypes(e.inheritedPropertyEnrichment) else e.inheritedPropertyEnrichment
    ))
  }

  def convertCountExpressions(enrichments: Seq[EntityEnrichment]): Seq[EntityEnrichment] = {
    enrichments.map(enrichment => enrichment.copy(
      colEnrichments = enrichment.colEnrichments.map(col =>
        if (col.aggFunction.toLowerCase == "count") col.copy(aggExpr = s"CASE WHEN (${col.aggExpr}) IS NOT NULL THEN true END") else col
      ),
      inheritedPropertyEnrichment = if (enrichment.inheritedPropertyEnrichment.nonEmpty)
        convertCountExpressions(enrichment.inheritedPropertyEnrichment)
      else enrichment.inheritedPropertyEnrichment
    ))
  }

  def fixDuplicateAliases(enrichments: Seq[EntityEnrichment]): Seq[EntityEnrichment] = {
    def updateReferences(oldAlias: String, newAlias: String, text: String): String =
      if (text != "true") text.replaceAll(s"\\b$oldAlias\\.\\b", s"$newAlias.") else text

    def updateReferencesInTree(alias: String, newAlias: String)(e: EntityEnrichment): EntityEnrichment = e.copy(
      colEnrichments = e.colEnrichments.map(col => col.copy(aggExpr = updateReferences(alias, newAlias, col.aggExpr))),
      preTransform = e.preTransform.map(pt => pt.copy(colExpr = updateReferences(alias, newAlias, pt.colExpr))),
      filter = updateReferences(alias, newAlias, e.filter),
      inheritedPropertyEnrichment = e.inheritedPropertyEnrichment.map(updateReferencesInTree(alias, newAlias))
    )

    def processLevel(enrichments: Seq[EntityEnrichment]): Seq[EntityEnrichment] =
      if (enrichments.isEmpty) enrichments
      else enrichments.groupBy(_.alias).flatMap { case (alias, group) =>
        if (group.size == 1) Seq(group.head.copy(inheritedPropertyEnrichment = processLevel(group.head.inheritedPropertyEnrichment)))
        else group.zipWithIndex.map {
          case (e, 0) => e.copy(inheritedPropertyEnrichment = processLevel(e.inheritedPropertyEnrichment))
          case (e, idx) =>
            val newAlias = s"${alias}_$idx"
            val updated = updateReferencesInTree(alias, newAlias)(e.copy(alias = newAlias))
            updated.copy(inheritedPropertyEnrichment = processLevel(updated.inheritedPropertyEnrichment))
        }
      }.toSeq

    processLevel(enrichments)
  }

  def optimizeEnrichmentsByTable(enrichments: Seq[EntityEnrichment]): Seq[EntityEnrichment] = {
    enrichments.groupBy(_.tableName).flatMap { case (tableName, tableEnrichments) =>
      val byPreTransform = tableEnrichments.groupBy(_.preTransform.isEmpty)

      val withPreTransform = byPreTransform.getOrElse(false, Seq.empty).map { entry =>
        entry.copy(
          inheritedPropertyEnrichment =
            optimizeEnrichmentsByTable(entry.inheritedPropertyEnrichment)
        )
      }

      val withoutPreTransform = byPreTransform.getOrElse(true, Seq.empty)

      val processedWithoutPreTransform =
        if (canMerge(withoutPreTransform)) {
          val withFilteredExpressions = withoutPreTransform.map { enrichment =>
            if (enrichment.filter != "true") {
              val references = expr(enrichment.filter).expr.references.map(_.name)
              val filterExpression = references.foldLeft(enrichment.filter)((expr, ref) =>
                if (ref.startsWith(enrichment.alias + ".")) expr
                else expr.replace(ref, enrichment.alias + "." + ref)
              )
              applyFilterRecursively(enrichment, filterExpression)
            } else enrichment
          }
          val merged = mergeEnrichments(withFilteredExpressions)
          Seq(merged.copy(
            inheritedPropertyEnrichment =
              optimizeEnrichmentsByTable(merged.inheritedPropertyEnrichment)
          ))
        } else {
          withoutPreTransform.map { entry =>
            entry.copy(
              inheritedPropertyEnrichment =
                optimizeEnrichmentsByTable(entry.inheritedPropertyEnrichment)
            )
          }
        }

      val processedWithPreTransform = withPreTransform ++ processedWithoutPreTransform
      processedWithPreTransform
    }.toSeq
  }

  def applyFilterRecursively(enrichment: EntityEnrichment, filter: String): EntityEnrichment = {
    enrichment.copy(
      colEnrichments = enrichment.colEnrichments.map { col =>
        col.copy(aggExpr = s"CASE WHEN $filter THEN (${col.aggExpr}) ELSE NULL END")
      },
      inheritedPropertyEnrichment =
        enrichment.inheritedPropertyEnrichment.map(child => applyFilterRecursively(child, filter))
    )
  }

  def mergeEnrichments(enrichments: Seq[EntityEnrichment]): EntityEnrichment = {
    val base = enrichments.head
    val baseAlias = base.alias.replaceAll("\\d+$", "")

    def fixAlias(expr: String, oldAlias: String): String = {
      if (oldAlias == baseAlias) expr
      else {
        val oldBaseAlias = oldAlias.replaceAll("\\d+$", "")
        expr.replaceAll(s"$oldAlias\\.|$oldBaseAlias\\d+\\.", s"$baseAlias.")
      }
    }

    def updateAliasesRecursively(enrichment: EntityEnrichment, oldAlias: String): EntityEnrichment = {
      enrichment.copy(
        colEnrichments = enrichment.colEnrichments.map(col =>
          col.copy(aggExpr = fixAlias(col.aggExpr, oldAlias))
        ),
        filter = if (enrichment.filter != "true") fixAlias(enrichment.filter, oldAlias)
        else enrichment.filter,
        inheritedPropertyEnrichment = enrichment.inheritedPropertyEnrichment.map(child =>
          updateAliasesRecursively(child, oldAlias))
      )
    }

    EntityEnrichment(
      tableName = base.tableName,
      alias = baseAlias,
      filter = mergeFilters(enrichments.map(_.filter)),
      preTransform = Seq.empty,
      colEnrichments = enrichments.flatMap { e =>
        e.colEnrichments.map(col =>
          col.copy(aggExpr = fixAlias(col.aggExpr, e.alias)))
      }.groupBy(_.colName).map(_._2.head).toSeq,
      inheritedPropertyEnrichment = enrichments.flatMap { e =>
        e.inheritedPropertyEnrichment.map(child =>
          updateAliasesRecursively(child, e.alias))
      }
    )
  }

  def mergeFilters(filters: Seq[String]): String = filters match {
    case f if f.contains("true") => "true"
    case Seq(single) => single
    case multiple => multiple.filterNot(_ == "true").map(f => s"($f)").mkString(" OR ")
  }

  def canMerge(enrichments: Seq[EntityEnrichment]): Boolean =
    enrichments.size > 1 && enrichments.forall(e => e.leftCol.isEmpty && e.rightCol.isEmpty)
}
