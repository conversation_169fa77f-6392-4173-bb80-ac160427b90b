package ai.prevalent.entityinventory.grapholap

import ai.prevalent.entityinventory.grapholap.configs.{Config, GraphOlapJobArgs}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UPDATED_AT_TS
import ai.prevalent.entityinventory.publisher.Publisher
import ai.prevalent.entityinventory.utils.{ConfigUtil, EILOGGER}
import ai.prevalent.sdspecore.sparkbase.SDSSparkBase
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import org.apache.spark.sql.functions._

import scala.collection.mutable
import java.time.format.DateTimeFormatter
import java.time.{DayOfWeek, Instant, ZoneId, ZonedDateTime}



object GraphOlap extends  SDSSparkBase[GraphOlapJobArgs] {

  type PublisherConfig = ai.prevalent.entityinventory.publisher.configs.Config

  override def getInitParams: GraphOlapJobArgs = new GraphOlapJobArgs()


  override def execute(jobArgs: GraphOlapJobArgs): Unit = {
    val reader = SDSTableReaderFactory.getDefault(spark)
    val writer = SDSTableWriterFactory.getDefault(spark)
    val publisherTables = ConfigUtil.readAllConfigs[PublisherConfig](configListPath = spark.conf.get("spark.sds.publisher.writebackBasePath"),
        configArtifactURI = spark.conf.get("spark.sds.restapi.configArtifactoryUri",""),
        configEndpoint = spark.conf.get("spark.sds.restapi.eiSparkConfigsBasePath",""),
        Publisher.configFormats
      ).values
      .filter(_.isOLAPTable)
      .map(_.outputTableInfo.outputTableName)

    val olapConfigs=ConfigUtil.readAllConfigs[Config](configListPath = spark.conf.get("spark.sds.olap.writebackBasePath"),
      configArtifactURI = spark.conf.get("spark.sds.restapi.configArtifactoryUri",""),
      configEndpoint = spark.conf.get("spark.sds.restapi.eiSparkConfigsBasePath","")
      )
    createPublisherReplica(publisherTables,olapConfigs,jobArgs,writer)

  }

  private def findDays(config: Config, endEpochMillis: Long): Seq[ZonedDateTime] = {

    config.snapShotSpec.flatMap { spec =>
      val dayRange = spec.dayRange.split("-").map(_.toInt)
      val startDay = dayRange(0)
      val endDay = dayRange(1)
      val zone = ZoneId.of(spark.conf.get("spark.sql.session.timeZone", "UTC"))
      val startDateTime = Instant.ofEpochMilli(endEpochMillis).atZone(zone)

      spec.granularLevel.flatMap {
        case "DAILY" => Array.range(startDay - 1, endDay).map(startDateTime.minusDays(_))
        case "WEEKEND" => Array.range(startDay - 1, endDay).map(startDateTime.minusDays(_)).filter(_.getDayOfWeek.equals(DayOfWeek.valueOf("FRIDAY")))
        case "MONTH_END" => Array.range(startDay - 1, endDay).map(startDateTime.minusDays(_)).filter(d => d.getMonth != d.plusDays(1).getMonth)
      }.toSet
    }
  }
  def createPublisherReplica(publisherTables:Iterable[String],olapConfigs:mutable.Map[String,Config],jobArgs: GraphOlapJobArgs,writer:SDSTableWriter):Unit={
    val olapIdentifierRegex="^sds_(?:\\w+?)__(?:rel__)?(\\w+?)(?:__[^\\r\\n]*|$)"
    val knowledgeGraphOlapSchema = spark.conf.get("spark.sds.kg.olap.schema")
    publisherTables.foreach { isOlapTableName =>
      val tableName = isOlapTableName.split("[.]")(isOlapTableName.split("[.]").length-1)
      val olapConfigName = f"sds_ei__olap__${olapIdentifierRegex.r.findFirstMatchIn(tableName).get.group(1)}"
      val config:Config = olapConfigs.getOrElse(olapConfigName, olapConfigs("sds_ei__olap__global"))
      val customFilterExpr: Option[String] = if (config.filterSpec.filterExpr.nonEmpty)
        Some(config.filterSpec.filterExpr.mkString(" and "))
      else
        None
      EILOGGER.jsonMiniPrint(s"Creating filter expressions for $knowledgeGraphOlapSchema.$tableName from base $isOlapTableName with config", config)
      val days = findDays(config,jobArgs.endEpoch)
      val snapShotFilter = days.map(_.format(DateTimeFormatter.ISO_INSTANT))
        .map(time => expr(s"$UPDATED_AT_TS='$time'" + customFilterExpr.map(expr => s" and $expr").getOrElse("")).expr)
      LOGGER.info(s"Creating replica for $knowledgeGraphOlapSchema.$tableName from base $isOlapTableName with filter $snapShotFilter")

      writer.createReplica(isOlapTableName,s"$knowledgeGraphOlapSchema.$tableName",snapShotFilter)

    }
  }
}
