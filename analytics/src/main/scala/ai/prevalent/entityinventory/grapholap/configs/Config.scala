package ai.prevalent.entityinventory.grapholap.configs

case class Config (analysisPeriodSpec: AnalysisPeriodSpec,snapShotSpec:Array[RangeSpec],
                   filterSpec:FilterSpec)


case class AnalysisPeriodSpec(weekStart:String="MONDAY",weekEnd:String="FRIDAY",monthStart:String="CALENDAR_START",
                              monthEnd:String="CALENDAR_END")


case class RangeSpec(dayRange:String,granularLevel:Array[String])

case class FilterSpec(filterExpr:Array[String]=Array.empty)


