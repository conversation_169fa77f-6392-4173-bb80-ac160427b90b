{"inventoryModelInput": [{"path": "/dafa", "name": "sf"}, {"path": "/data2", "name": "ad"}, {"path": "/data2", "name": "source3"}], "disambiguation": {"temporalConfidenceMatrix": ["last_active_date", "first_seen_date"], "candidateKeys": [{"name": "fqdn", "exceptionFilter": "fqdn='fqdn343'"}, "azure_id", {"name": "case_sens_key", "caseSensitive": true}], "excludeValues": ["Unknown"], "confidenceMatrix": ["sf", "ad", "source3"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "persistcol1", "temporalConfidenceMatrix": ["last_active_date", "first_seen_date", "last_found_date"], "confidenceMatrix": ["source3", "ad"], "persistNonNullValue": true, "restrictToConfidenceMatrix": true}, {"field": "persistcol2", "confidenceMatrix": ["ad"], "persistNonNullValue": true, "restrictToConfidenceMatrix": true}, {"field": "persistcol3", "confidenceMatrix": ["source3"], "persistNonNullValue": true, "restrictToConfidenceMatrix": false}, {"field": "persistcol4", "confidenceMatrix": ["source3"], "persistNonNullValue": false, "restrictToConfidenceMatrix": false}, {"field": "persistcol5", "confidenceMatrix": ["source3"], "persistNonNullValue": false, "restrictToConfidenceMatrix": true}], "valueConfidence": [{"field": "status", "confidenceMatrix": ["Active", "InActive"]}], "rollingUpFields": ["origin"], "aggregation": [{"field": "last_active_date", "function": "max"}]}}, "output": {"disambiguatedModelLocation": "", "resolverLocation": "kg_test.host_resolver", "fragmentLocation": "kg_test.host_fragment", "isFragmentOLAPTable": true, "resolverGraphLocation": "graph.olap"}, "entityConfig": {"name": "Host", "defaultEntitySpec": {"fieldSpec": {}}, "fieldLevelSpec": [], "lastUpdateFields": ["fqdn"]}}