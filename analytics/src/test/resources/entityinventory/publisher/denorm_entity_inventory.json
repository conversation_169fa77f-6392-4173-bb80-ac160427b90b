{"transformSpec": {"type": "Denorm", "eiSchemas": "ei,ei2", "postSchemas": "ei,ei_post,ccm", "tableInfo": {"tableType": "entity", "inputTableIdentifierRegex": "^\\w++.sds_ei__([a-z]++)$", "enrichTableIdentifierRegex": "^\\w++.sds_[a-z_]+?__entity__([a-z]++)__enrich$", "commonColumns": ["p_id", "updated_at", "updated_at_ts"], "uniqCol": "p_id"}}, "outputTableInfo": {"isSingleOutput": true, "partitionColumns": ["class"], "outputTableName": "sds_ei__publish_transformer__entity_inventoryII", "outputFilter": "class='Person'"}}