{"inventoryModelInput": [{"path": "ei.sds_ei__person__savyint_iga", "name": "sds_ei__person__savyint_iga"}, {"path": "ei.sds_ei__person__successfactors_hr__employee_id", "name": "sds_ei__person__successfactors_hr__employee_id"}], "disambiguation": {"candidateKeys": ["employee_id", "azure_ad_user_id", "aad_id", "email_id", {"name": "name", "caseSensitive": true}], "confidenceMatrix": ["sds_ei__person__savyint_iga", "sds_ei__person__successfactors_hr__employee_id"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "display_label", "confidenceMatrix": ["sds_ei__person__savyint_iga", "sds_ei__person__successfactors_hr__employee_id"]}, {"field": "manager", "confidenceMatrix": ["sds_ei__person__savyint_iga", "sds_ei__person__successfactors_hr__employee_id"]}], "rollingUpFields": ["origin"], "valueConfidence": [{"field": "status", "confidenceMatrix": ["Terminated", "Active", "Unknown"]}]}}, "output": {"disambiguatedModelLocation": "ei.sds_ei__person", "resolverLocation": "ei.sds_ei_inter_source_resolver"}, "entityConfig": {"name": "Host", "defaultEntitySpec": {}, "fieldLevelSpec": [], "lastUpdateFields": ["fqdn"]}}