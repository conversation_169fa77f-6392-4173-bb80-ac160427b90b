{"inventoryModelInput": [{"path": "ei.sds_ei__host__active_directory__object_guid", "name": "sds_ei__host__active_directory__object_guid"}, {"path": "ei.sds_ei__host__ms_defender", "name": "sds_ei__host__ms_defender"}], "disambiguation": {"candidateKeys": ["fqdn", "azure_ad_device_id", "host_name", "serial_number", "cloud_resource_id", "primary_key"], "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_defender"], "exceptionsFilter": "(lower(host_name) LIKE '%iphone%' OR lower(host_name) LIKE '%android%' OR lower(host_name) LIKE '%ipad%' OR lower(host_name) LIKE '%macbook%' OR host_name RLIKE '(?i)pro([^a-zA-Z0-9]|$)' OR lower(host_name) LIKE '%galaxy%' OR lower(host_name) LIKE '%samsung%') OR lower(host_name)='wrk' OR (lower(os) LIKE '%android%' OR lower(os) LIKE '%appleios%' OR lower(os) LIKE '%tizen%' OR azure_ad_device_id = '00000000-0000-0000-0000-000000000000')", "strategy": {"fieldLevelConfidenceMatrix": [{"field": "host_type", "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_defender"]}, {"field": "display_label", "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_defender"]}], "rollingUpFields": ["origin"], "valueConfidence": [{"field": "status", "confidenceMatrix": ["Disabled", "Active"]}, {"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "output": {"disambiguatedModelLocation": "ei.sds_ei__host", "resolverLocation": "ei.sds_ei_inter_source_resolver"}, "entityConfig": {"name": "Host", "defaultEntitySpec": {}, "fieldLevelSpec": [], "lastUpdateFields": ["fqdn"]}}