{"entityTableName": "ei_edm_sit_v5.sds_ei__person", "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__rel__person_manages_person", "alias": "p_mg_p", "leftCol": "p_id", "rightCol": "source_p_id", "colEnrichments": [{"colName": "managed_person_count", "aggExpr": "p_mg_p.target_p_id", "aggFunction": "count", "colExpr": "managed_person_count"}], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__person", "alias": "person", "leftCol": "target_p_id", "colEnrichments": [{"colName": "manages_person_list", "aggExpr": "person.display_label", "aggFunction": "collect_set", "colExpr": "manages_person_list"}]}]}], "derivedProperties": [], "output": {"outputTableName": "ei_enrich.sds_ei__self_enrich"}}