{"entityTableName": "ei_edm_sit_v5.sds_ei__host", "countEnriches": [], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__rel__vulnerability_finding_on_host", "alias": "vfoh", "filter": "true", "joinType": "INNER", "preTransform": [], "colEnrichments": [{"colName": "has_open_vulnerability_count", "aggExpr": "CASE WHEN vfoh.current_status='Open' THEN (CASE WHEN (vfoh.source_p_id) IS NOT NULL THEN true END) ELSE NULL END", "aggFunction": "COUNT", "colExpr": "has_open_vulnerability_count"}, {"colName": "has_all_vulnerability_count", "aggExpr": "CASE WHEN (vfoh.source_p_id) IS NOT NULL THEN true END", "aggFunction": "COUNT", "colExpr": "has_all_vulnerability_count"}], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__vulnerability", "alias": "vul", "filter": "(severity='Medium') OR (severity='High') OR (severity='Low')", "joinType": "INNER", "preTransform": [], "colEnrichments": [{"colName": "count_non_patch_vul_all", "aggExpr": "CASE WHEN vul.severity='Low' THEN (CASE WHEN vfoh.patch_available = true THEN 1 ELSE 0 END) ELSE NULL END", "aggFunction": "SUM", "colExpr": "count_non_patch_vul_all"}, {"colName": "high_severity_count", "aggExpr": "CASE WHEN vul.severity='High' THEN (CASE WHEN vfoh.current_status='Open' THEN (CASE WHEN (vul.id) IS NOT NULL THEN true END) ELSE NULL END) ELSE NULL END", "aggFunction": "COUNT", "colExpr": "high_severity_count"}, {"colName": "count_non_patch_vul", "aggExpr": "CASE WHEN vul.severity='Medium' THEN (CASE WHEN vfoh.current_status='Open' THEN (CASE WHEN vul.patch_available = true THEN 1 ELSE 0 END) ELSE NULL END) ELSE NULL END", "aggFunction": "SUM", "colExpr": "count_non_patch_vul"}], "inheritedPropertyEnrichment": []}]}], "derivedProperties": [], "output": {"outputTableName": "ei_enrich.sds_ei__entity_enrich"}}