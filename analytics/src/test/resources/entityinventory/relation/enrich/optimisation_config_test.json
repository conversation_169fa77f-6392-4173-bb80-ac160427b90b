{"entityTableName": "ei_edm_sit_v5.sds_ei__host", "countEnriches": [], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__rel__vulnerability_finding_on_host", "alias": "vfoh", "filter": "current_status='Open'", "preTransform": [], "colEnrichments": [{"colName": "has_open_vulnerability_count", "aggExpr": "vfoh.source_p_id", "aggFunction": "COUNT", "colExpr": "has_open_vulnerability_count"}], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__vulnerability", "alias": "vul", "filter": "severity='Medium'", "preTransform": [], "colEnrichments": [{"colName": "count_non_patch_vul", "aggExpr": "CASE WHEN vul.patch_available = true THEN 1 ELSE 0 END", "aggFunction": "SUM", "colExpr": "count_non_patch_vul"}], "inheritedPropertyEnrichment": []}, {"tableName": "ei_edm_sit_v5.sds_ei__vulnerability", "alias": "vul2", "filter": "severity='High'", "preTransform": [], "colEnrichments": [{"colName": "high_severity_count", "aggExpr": "vul2.id", "aggFunction": "COUNT", "colExpr": "high_severity_count"}], "inheritedPropertyEnrichment": []}]}, {"tableName": "ei_edm_sit_v5.sds_ei__rel__vulnerability_finding_on_host", "alias": "vfoh2", "filter": "true", "preTransform": [], "colEnrichments": [{"colName": "has_all_vulnerability_count", "aggExpr": "vfoh2.source_p_id", "aggFunction": "COUNT", "colExpr": "has_all_vulnerability_count"}], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__vulnerability", "alias": "vul", "filter": "severity='Low'", "preTransform": [], "colEnrichments": [{"colName": "count_non_patch_vul_all", "aggExpr": "CASE WHEN vfoh2.patch_available = true THEN 1 ELSE 0 END", "aggFunction": "SUM", "colExpr": "count_non_patch_vul_all"}], "inheritedPropertyEnrichment": []}]}], "derivedProperties": [], "output": {"outputTableName": "ei_enrich.sds_ei__entity_enrich"}}