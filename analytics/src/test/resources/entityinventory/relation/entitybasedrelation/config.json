{"name": "PERSON_OWNS_HOST", "origin": "MS Intune", "inverseRelationshipName": "HOST_OWNED_BY_PERSON", "intraSourcePath": "eias.sds_ei_intra_source_resolver", "interSourcePath": "eias.sds_ei_inter_source_resolver", "output": {"outputTable": "eias.sds_ei__rel__ms_intunes__person_owns_host_ent", "prevMiniSDM": "eias.sds_ei__rel_mini_sdm__ms_intunes__person_owns_host_ent"}, "inputSourceInfo": [{"sdmPath": "sdm.microsoft__intune", "origin": "MS Intune", "sourceMappingInfo": ["%s/sds_ei__person__ms_intunes__user_id__job_config.json"], "targetMappingInfo": ["%s/sds_ei__host__ms_intunes__device_id__job_config.json"]}], "optionalAttributes": [{"name": "abc", "exp": "xyz", "occurrence": "FIRST"}, {"name": "status", "exp": "status", "occurrence": "FIRST"}], "entityBasedRelationBuilderStrategySpec": {"baseEntity": "targetEntity"}}