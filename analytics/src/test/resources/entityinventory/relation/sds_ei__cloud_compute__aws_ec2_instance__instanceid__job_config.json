{"primaryKey": "lower(InstanceId)", "origin": "'AWS EC2 Instance'", "commonProperties": [{"colName": "type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "EC2 Instance", "srdm": "read_catalog_dev.srdm_edm_sit.aws__ec2_describe_instances"}, "outputTableInfo": {"outputTableName": "kg_edm_sit.sds_ei__cloud_compute__aws_ec2_instance__instanceid"}, "entity": {"name": "Cloud Compute", "defaultEntitySpec": {}, "fieldLevelSpec": [], "lastUpdateFields": ["fqdn"]}}