{"primaryKey": "source_p_id", "origin": "'MS Intune'", "outputTable": "ei_temp.sds_ei__host__ms_intune__source_p_id", "dataSource": {"name": "Microsoft", "feedName": "sjdasjd", "srdm": "sdm.microsoft__intune"}, "entityConfig": {"name": "Person", "displayLabel": ["fqdn", "dns_name", "host_name", "azure_ad_device_id", "primary_key"], "entityTag": {"Host Type": "host_type", "OS": "os", "BU": "business_unit", "Cloud Provider": "cloud_provider"}, "fieldSpec": {"persistNonNullValue": true}}}