{"inventoryModelInput": [{"path": "/dafa", "name": "source1"}, {"path": "/data2", "name": "source2"}], "disambiguation": {"candidateKeys": ["azure_id"], "confidenceMatrix": ["source1", "source2", "source3"], "strategy": {"frequencyConfidence": [{"field": "os", "fallbackValueConfidence": ["Ub", "<PERSON>"]}]}}, "output": {"disambiguatedModelLocation": "", "resolverLocation": ""}, "entityConfig": {"entityClass": "Host", "defaultEntitySpec": {}, "fieldLevelSpec": [], "lastUpdateFields": ["fqdn"]}}