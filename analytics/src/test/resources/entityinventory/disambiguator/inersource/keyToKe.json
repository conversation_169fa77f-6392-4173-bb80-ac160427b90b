{"inventoryModelInput": [{"path": "/dafa", "name": "source1"}, {"path": "/data2", "name": "source2"}], "disambiguation": {"candidateKeys": ["account_name", {"name": "sam_account_name", "keyToKeyMatch": true}], "confidenceMatrix": ["source1"]}, "output": {"disambiguatedModelLocation": "", "resolverLocation": ""}, "entityConfig": {"name": "Host", "defaultEntitySpec": {}, "fieldLevelSpec": [], "lastUpdateFields": ["fqdn"]}}