{"output": {"resolverLocation": "ei_edm_sit_v5.sds_ei_inter_source_resolver", "disambiguatedModelLocation": "ei_edm_sit_v5.sds_ei__host"}, "disambiguation": {"strategy": {"aggregation": [{"field": "tenable_io_asset_updated_at", "function": "max"}], "rollingUpFields": ["origin", "defender_threat_name", "defender_action_type", "qualys_id", "defender_id", "qualys_asset_id"], "valueConfidence": [{"field": "type", "confidenceMatrix": ["Server", "Network", "Hypervisor", "Mobile", "Printer", "Workstation", "Other"]}, {"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}], "fieldLevelConfidenceMatrix": [{"field": "os", "confidenceMatrix": ["sds_ei__host__ms_defender", "sds_ei__host__ms_azure_ad", "host_project_lookup", "sds_ei__host__ms_azure", "sds_ei__host__active_directory__object_guid"]}, {"field": "av_block_malicious_code_status", "confidenceMatrix": ["confidence matrix"], "temporalConfidenceMatrix": ["last_active_date"]}]}, "candidateKeys": [{"name": "aad_device_id", "exceptionFilter": "aad_device_id = '00000000-0000-0000-0000-000000000000'"}, {"name": "host_name", "exceptionFilter": "(lower(host_name) LIKE '%iphone%' OR lower(host_name) LIKE '%android%' OR lower(host_name) LIKE '%ipad%' OR lower(host_name) LIKE '%macbook%' OR host_name RLIKE '(?i)pro([^a-zA-Z0-9]|$)' OR lower(host_name) LIKE '%galaxy%' OR lower(host_name) LIKE '%samsung%') OR lower(host_name) IN ('wrk','user deleted for this device') OR (lower(os) LIKE '%android%' OR lower(os) LIKE '%appleios%' OR lower(os) LIKE '%tizen%') OR (cloud_resource_id IS NOT NULL OR cloud_instance_id IS NOT NULL)"}, "fqdn"], "excludeValues": ["Unknown", "Other", "-"], "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_defender", "sds_ei__host__ms_intunes"]}, "derivedProperties": [{"colExpr": "CASE WHEN arrays_overlap(data_source_dev,array('EDM')) THEN array_except(array(trim(REGEXP_extract(qualys_tags, 'AC:([^,]+)'))),array('Unknown','None',null)) ELSE cast(null as array<string>) END", "colName": "asset_compliance_scope"}, {"colExpr": "CASE WHEN arrays_overlap(data_source_dev,array('EDM')) THEN array_except(array(IF (lower(qualys_tags) like '%domain controller%','Domain Controller',NULL), IF (lower(qualys_tags) like '%db server%','Database Server',NULL)),array(null)) ELSE array_except(array(IF(lower(cast(dns_name as string)) like ('%dc%.prevalent.com%'),'Domain Controller',NULL)), array(null)) END", "colName": "asset_purpose"}], "inventoryModelInput": [{"name": "sds_ei__host__active_directory__object_guid", "path": "ei_edm_sit_v5.sds_ei__host__active_directory__object_guid"}, {"name": "sds_ei__host__ms_defender", "path": "ei_edm_sit_v5.sds_ei__host__ms_defender"}, {"name": "old_table_removed", "path": "ei_edm_sit_v5.old_table_removed"}, {"name": "abc", "path": "ei_edm_sit_v5.sds_ei__host__tenable_io"}], "entityConfig": {"fieldSpec": {"persistNonNullValue": true}, "name": "Host", "lastUpdateFields": ["type", "first_seen_date", "account_enabled_status"]}, "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(cloud_instance_name,fqdn__resolved,dns_name__resolved,host_name__resolved,aad_device_id__resolved,hardware_serial_number__resolved,ip[0],primary_key))"}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END"}]}