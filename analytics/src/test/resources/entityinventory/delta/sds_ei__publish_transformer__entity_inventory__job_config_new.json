{"transformSpec": {"type": "Denorm", "eiSchemas": "<%EI_SCHEMA_NAME%>", "postSchemas": "<%EI_POST_SCHEMA_NAMES%>", "tableInfo": {"tableType": "entity", "inputTableIdentifierRegex": "^\\w++[.]sds_ei__((?>[a-z0-9]++_?)++$)", "enrichTableIdentifierRegex": "^\\w++.sds_[a-z_]+?__entity__((?:[a-z0-9]_?)+)__enrich$", "commonColumns": ["p_id", "updated_at_partition", "updated_at_ts"], "uniqCol": "p_id"}}, "outputTableInfo": {"isSingleOutput": true, "isSnapshotEnabled": false, "outputTableName": "sds_ei__publish_transformer__entity_inventory_temp"}}