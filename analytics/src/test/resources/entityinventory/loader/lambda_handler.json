{"primaryKey": "arn", "filterBy": "resourceType IN ('AWS::EC2::Instance','AWS::EC2::EC2Fleet','AWS::ECS::Cluster','AWS::ECS::Service','AWS::ECS::TaskDefinition','AWS::ECR::Repository','AWS::EKS::Addon','AWS::EKS::Cluster','AWS::Lambda::Function','AWS::AutoScaling::AutoScalingGroup')", "origin": "'AWS Resource Details'", "outputTable": "ei_temp.sds_ei__host__active_directory__object_guid", "dataSource": {"name": "Microsoft", "feedName": "sjdasjd", "srdm": "sdm.microsoft__active_directory"}, "commonProperties": [{"colName": "type", "colExpr": "SUBSTRING_INDEX(resourceType, '::', -2)"}], "entitySpecificProperties": [{"colName": "host_name_ip", "colExpr": "CASE WHEN (private_ip IS NOT NULL AND host_name IS NOT NULL) THEN TRANSFORM(SPLIT(private_ip, ','), ip_addr -> concat(host_name,'-', ip_addr)) WHEN private_ip IS NULL and host_name is not null THEN array(host_name) WHEN host_name IS NULL THEN SPLIT(private_ip, ',')  END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "private_ip", "colExpr": "concat_ws(',',FILTER(SPLIT(ip, ','), ip_addr -> ip_addr RLIKE '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "username", "colExpr": "transform(external_email_id, x -> concat(REGEXP_REPLACE(x, '\\\\\\\\..*', ''), '_', type))"}, {"colName": "external_email_id", "colExpr": "transform(filter(array_union(otherMails, backup_mails), em -> em is not null and em not rlike '^[ \\\\t]*+$'), em -> lower(em))"}], "sourceSpecificProperties": [{"colName": "aws_instance_private_ip_address", "colExpr": "PrivateIpAddress"}, {"colName": "aws_instance_public_ip_address", "colExpr": "PublicIpAddress"}, {"colName": "ip", "colExpr": "concat_ws(',', aws_instance_private_ip_address,aws_instance_public_ip_address)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "Ec2InstanceId"}, {"colName": "host_name", "colExpr": "cloud_instance_id", "fieldsSpec": {"isInventoryDerived": true}}], "entityConfig": {"fieldSpec": {"persistNonNullValue": true}, "name": "Host", "lastUpdateFields": ["os_family"]}}