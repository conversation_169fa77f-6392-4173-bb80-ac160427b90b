{"outputTableInfo": {"outputTableName": "ei_temp.sds_ei__host__active_directory__object_guid", "outputWrittenMode": "viewType"}, "entitySpecificProperties": [{"colName": "username", "colExpr": "CASE WHEN btrim(cast(transform(CASE WHEN (btrim(CAST(transform(filter(array_union(otherMails, backup_mails), em -> ((em IS NOT NULL) AND (NOT RLIKE(em, '^[ \\\\t]*+$')))), em -> lower(em)) AS STRING), '[\t ]') = '') THEN NULL ELSE transform(filter(array_union(otherMails, backup_mails), em -> ((em IS NOT NULL) AND (NOT RLIKE(em, '^[ \\\\t]*+$')))), em -> lower(em)) END, x -> concat(REGEXP_REPLACE(x, '\\\\\\\\..*', ''), '_', CASE WHEN (btrim(CAST(SUBSTRING_INDEX(resourceType, '::', -2) AS STRING), '[\t ]') = '') THEN NULL ELSE SUBSTRING_INDEX(resourceType, '::', -2) END)) as STRING),'[\\t ]') = '' THEN null ELSE transform(CASE WHEN (btrim(CAST(transform(filter(array_union(otherMails, backup_mails), em -> ((em IS NOT NULL) AND (NOT RLIKE(em, '^[ \\\\t]*+$')))), em -> lower(em)) AS STRING), '[\t ]') = '') THEN NULL ELSE transform(filter(array_union(otherMails, backup_mails), em -> ((em IS NOT NULL) AND (NOT RLIKE(em, '^[ \\\\t]*+$')))), em -> lower(em)) END, x -> concat(REGEXP_REPLACE(x, '\\\\\\\\..*', ''), '_', CASE WHEN (btrim(CAST(SUBSTRING_INDEX(resourceType, '::', -2) AS STRING), '[\t ]') = '') THEN NULL ELSE SUBSTRING_INDEX(resourceType, '::', -2) END)) END", "fieldsSpec": {"isInventoryDerived": false, "convertEmptyToNull": true, "caseSensitiveExpression": false}}, {"colName": "host_name_ip", "colExpr": "CASE WHEN btrim(cast(CASE WHEN ((CASE WHEN (btrim(CAST(concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) END IS NOT NULL) AND (CASE WHEN (btrim(CAST(CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END AS STRING), '[\t ]') = '') THEN NULL ELSE CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END END IS NOT NULL)) THEN TRANSFORM(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) END, ','), ip_addr -> concat(CASE WHEN (btrim(CAST(CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END AS STRING), '[\t ]') = '') THEN NULL ELSE CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END END, '-', ip_addr)) WHEN ((CASE WHEN (btrim(CAST(concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) END IS NULL) AND (CASE WHEN (btrim(CAST(CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END AS STRING), '[\t ]') = '') THEN NULL ELSE CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END END IS NOT NULL)) THEN array(CASE WHEN (btrim(CAST(CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END AS STRING), '[\t ]') = '') THEN NULL ELSE CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END END) WHEN (CASE WHEN (btrim(CAST(CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END AS STRING), '[\t ]') = '') THEN NULL ELSE CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END END IS NULL) THEN SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) END, ',') END as STRING),'[\\t ]') = '' THEN null ELSE CASE WHEN ((CASE WHEN (btrim(CAST(concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) END IS NOT NULL) AND (CASE WHEN (btrim(CAST(CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END AS STRING), '[\t ]') = '') THEN NULL ELSE CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END END IS NOT NULL)) THEN TRANSFORM(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) END, ','), ip_addr -> concat(CASE WHEN (btrim(CAST(CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END AS STRING), '[\t ]') = '') THEN NULL ELSE CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END END, '-', ip_addr)) WHEN ((CASE WHEN (btrim(CAST(concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) END IS NULL) AND (CASE WHEN (btrim(CAST(CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END AS STRING), '[\t ]') = '') THEN NULL ELSE CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END END IS NOT NULL)) THEN array(CASE WHEN (btrim(CAST(CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END AS STRING), '[\t ]') = '') THEN NULL ELSE CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END END) WHEN (CASE WHEN (btrim(CAST(CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END AS STRING), '[\t ]') = '') THEN NULL ELSE CASE WHEN (btrim(CAST(Ec2InstanceId AS STRING), '[\t ]') = '') THEN NULL ELSE Ec2InstanceId END END IS NULL) THEN SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', FILTER(SPLIT(CASE WHEN (btrim(CAST(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) AS STRING), '[\t ]') = '') THEN NULL ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END, ','), ip_addr -> RLIKE(ip_addr, '^10\\\\.|^192\\\\.168\\\\.|^172\\\\.(1[6-9]|2[0-9]|3[01])\\\\.'))) END, ',') END END", "fieldsSpec": {"isInventoryDerived": true, "convertEmptyToNull": true, "caseSensitiveExpression": false}}], "sourceSpecificProperties": [{"colName": "ip", "colExpr": "CASE WHEN btrim(cast(concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) as STRING),'[\\t ]') = '' THEN null ELSE concat_ws(',', CASE WHEN (btrim(CAST(PrivateIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PrivateIpAddress END, CASE WHEN (btrim(CAST(PublicIpAddress AS STRING), '[\t ]') = '') THEN NULL ELSE PublicIpAddress END) END", "fieldsSpec": {"isInventoryDerived": true, "convertEmptyToNull": true, "caseSensitiveExpression": false}}]}