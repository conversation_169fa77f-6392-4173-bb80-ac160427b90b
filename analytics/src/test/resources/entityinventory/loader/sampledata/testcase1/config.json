{"primaryKey": "id", "origin": "'activedirectory'", "entityConfig": {"fieldSpec": {"persistNonNullValue": true}, "name": "Host"}, "temporaryProperties": [{"colName": "temp_source_specific_col", "colExpr": "source_specific_col"}, {"colName": "temp_entity_specific_col", "colExpr": "entity_specific_col"}], "commonProperties": [{"colName": "class", "colExpr": "'Person'"}, {"colName": "display_label", "colExpr": "id"}], "entitySpecificProperties": [{"colName": "operating_system_version", "colExpr": "entity_specific_col", "fieldsSpec": {"persistNonNullValue": true}}], "sourceSpecificProperties": [{"colName": "location", "colExpr": "source_specific_col", "fieldsSpec": {"persistNonNullValue": true}}, {"colName": "first_name", "colExpr": "temp_source_specific_col", "fieldsSpec": {"isDerived": true}}, {"colName": "last_name", "colExpr": "temp_entity_specific_col", "fieldsSpec": {"isDerived": true}}, {"colName": "full_name", "colExpr": "CONCAT_WS(',',first_name, last_name)", "fieldsSpec": {"isDerived": true}}]}