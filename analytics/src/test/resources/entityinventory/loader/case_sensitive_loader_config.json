{"primaryKey": "arn", "filterBy": "resourceType IN ('AWS::EC2::Instance','AWS::EC2::EC2Fleet','AWS::ECS::Cluster','AWS::ECS::Service','AWS::ECS::TaskDefinition','AWS::ECR::Repository','AWS::EKS::Addon','AWS::EKS::Cluster','AWS::Lambda::Function','AWS::AutoScaling::AutoScalingGroup')", "origin": "'AWS Resource Details'", "outputTable": "ei_temp.sds_ei__host__active_directory__object_guid_replace", "dataSource": {"name": "Microsoft", "feedName": "sjdasjd", "srdm": "sdm.microsoft__active_directory"}, "commonProperties": [{"colName": "type", "colExpr": "SUBSTRING_INDEX(resourceType, '::', -2)"}, {"colName": "first_seen_date", "colExpr": "aws_resource_created_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "coalesce(resourceId,resourceName)"}, {"colName": "total_disk_size", "colExpr": "properties.storageProfile.osDisk.diskSizeGB", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties", "colExpr": "configuration.arn"}, {"colName": "relation_name_list", "colExpr": "transform(relationships, ele -> ele.relationshipName)"}, {"colName": "os_family", "colExpr": "configuration.Version is not null"}], "sourceSpecificProperties": [{"colName": "aws_account_id", "colExpr": "accountId"}, {"colName": "aws_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(resourceCreationTime)))"}], "entity": {"fieldSpec": {"persistNonNullValue": true}, "name": "Cloud Compute", "lastUpdateFields": ["os_family"]}}