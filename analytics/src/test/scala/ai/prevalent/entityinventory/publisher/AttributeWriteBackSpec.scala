package ai.prevalent.entityinventory.publisher

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.{P_ID, RELATION_ID}
import ai.prevalent.entityinventory.exceptions.InvalidDataException
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.publisher.configs.{AttributeWriteBack, TableSpec}
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
import ai.prevalent.entityinventory.publisher.configs.util.PublisherConfigReadUtil
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.SparkConf
import org.apache.spark.sql.{DataFrame, Row, functions}
import org.apache.spark.sql.functions.{col, expr, sha2, to_json}
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll}
import org.scalatest.flatspec.AnyFlatSpec
import ai.prevalent.entityinventory.publisher.configs.AttributeWriteBack.findCommonColumnNames
import ai.prevalent.entityinventory.utils.SparkUtil.unionByName

import java.io.File
import scala.io.Source
import scala.reflect.io.Directory
import scala.util.Using

class AttributeWriteBackSpec extends AnyFlatSpec with BeforeAndAfter with BeforeAndAfterAll with DataFrameSuiteBase with IcebergSparkTestWrapper{

  var writer : SDSTableWriter = _
  var reader : SDSTableReader = _
  val END_EPOCH = 1681516799999l
  var EIJobArgs: EIJobArgs = _
  val postSchema = "ei,ei_post,ccm"
  val eiSchema = "ei,ei2"
  val eiHostTable = "ei.sds_ei__host__enrich"
  val ei2PersonTable = "ei2.sds_ei__person"
  val eiIdentityTable = "ei.sds_ei__identity"
  val hostAMEnrich = "ei.sds_am__entity__host__enrich"
  val WIN10 = "Win 10"
  var hostExpectedDF: DataFrame = _
  val logFilePath = "target/customLogs.log"
  var relExpDF: DataFrame = _
  var publisherConfDir: String =_

  import spark.implicits._

  override def conf: SparkConf = super.conf.set("spark.sql.iceberg.merge-schema","true")
    .set("spark.sql.iceberg.set-all-nullable-field","true")
    .set("spark.sql.iceberg.check-ordering","false")
    .set("spark.sql.shuffle.partitions","1")

  override def warehousePath: String = {
    new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse")).createDirectory()
    getClass.getResource("/iceberg_warehouse").getPath
  }

  override def beforeAll : Unit = {
    super.beforeAll()
    writer = SDSTableWriterFactory.getDefault(spark)
    reader = SDSTableReaderFactory.getDefault(spark)
    publisherConfDir = getClass.getResource("/entityinventory/publisher/graph_entity_write_back_configs/").getPath

    val configPath = getClass.getResource("/entityinventory/publisher/host_entity_write_back/host_entity_write_back.json").getPath
    val EIJobArgsConfigArray = Array("--current-updated-date", s"$END_EPOCH",
      "--output-schema", "ei_post",
      "--config", s"$configPath"
    )
    EIJobArgs = Publisher.getParseCmdArgs(EIJobArgsConfigArray)
    spark.conf.set("spark.sds.publisher.writebackBasePath", getClass.getResource("/entityinventory/publisher/host_entity_write_back/").getPath)
    val hostEnrich = Seq(
      ("p_12", true, END_EPOCH)
    ).toDF("p_id", "critical", UPDATED_AT)
      .withColumn(UPDATED_AT_TS,expr(s"to_timestamp($UPDATED_AT/1000)"))
    val hostEnrich2 = Seq(
      ("p_12", false, END_EPOCH)
    ).toDF("p_id", "deactivated", UPDATED_AT)
      .withColumn(UPDATED_AT_TS,expr(s"to_timestamp($UPDATED_AT/1000)"))

    writer.overwritePartition(hostEnrich, hostAMEnrich)
    writer.overwritePartition(hostEnrich2, "ccm.sds_ccm__entity__host__enrich")

    val hostActiveDirectoryObjectGuid = Seq(
      ("p_12", "Host", "A", "8df00cad-guid1", 1234567890124l, Array("Fqdn1"), "azure_id1", WIN10, 1234567890121l, END_EPOCH, 0, 0),
      ("p_13", "Host", "A", "8df00cad-3434-guid2", 1234567890124l, Array("fQDn2"), "azure_id2", WIN10, 1234567890121l, END_EPOCH, 0, 0),
      ("p_14", "Host", "A", "8df00cad-23214-guid2", 1234567890124l, Array("FQDN3"), "azure_id3", WIN10, 1234567890121l, END_EPOCH, 0, 0)
    ).toDF("p_id", "class", "origin", "primary_key", "last_seen_date", "fqdn", "azure_id", "os", "first_seen_date", "updated_at", "lifetime", "recency")
      .withColumn("last_updated_attrs", expr("struct(cast(null as string))"))
      .withColumn(UPDATED_AT_TS,expr(s"to_timestamp($UPDATED_AT/1000)"))
      .withColumn("last_found_date", expr("1707523199999"))
      .withColumn("graph_id", sha2(functions.concat(col(P_ID), col("updated_at_ts")), 256))

    writer.overwritePartition(hostActiveDirectoryObjectGuid, eiHostTable)
    hostExpectedDF = hostActiveDirectoryObjectGuid
      .withColumn("critical", expr("case when p_id = 'p_12' then true end"))
      .withColumn("deactivated", expr("case when p_id = 'p_12' then false end"))
      .withColumnRenamed("last_updated_attrs","last_updated_attrs__struct")
      .withColumn("last_updated_attrs",to_json(col("last_updated_attrs__struct")))

    val vulnFindingsDF = Seq(
      ("1", "Active", END_EPOCH, "p_id45", "p_id54", "VULNERABILITY_FINDING_ON_HOST", "HOST_HAS_VULNERABILITY_FINDING","Vulnerability","Host"),
      ("2", "Active", END_EPOCH, "p_id65", "p_id56", "VULNERABILITY_FINDING_ON_HOST", "HOST_HAS_VULNERABILITY_FINDING","Vulnerability","Host")
    ).toDF("relationship_id", "active", UPDATED_AT, "source_p_id", "target_p_id", "relationship_name", "inverse_relationship_name","source_entity_class","target_entity_class")
      .withColumn(UPDATED_AT_TS,expr(s"to_timestamp($UPDATED_AT/1000)"))
      .withColumn("graph_id", sha2(functions.concat(col(RELATION_ID), col("updated_at_ts")), 256))
      .withColumn("source_graph_id", sha2(functions.concat(col("source_p_id"), col("updated_at_ts")), 256))
      .withColumn("target_graph_id", sha2(functions.concat(col("target_p_id"), col("updated_at_ts")), 256))

    val vulnFindingsEnrichDF = Seq(
      ("1", "High", END_EPOCH)
    ).toDF("relationship_id", "severity", UPDATED_AT)
      .withColumn(UPDATED_AT_TS,expr(s"to_timestamp($UPDATED_AT/1000)"))
    relExpDF = vulnFindingsDF.withColumn("severity",expr("case when relationship_id = '1' then 'High' end"))
    writer.overwritePartition(vulnFindingsDF, "ei.sds_ei__rel__vulnerability_finding_on_host")
    writer.overwritePartition(vulnFindingsEnrichDF, "ei.sds_am__rel__vulnerability_finding_on_host__enrich")
  }

  "AttributeWriteBack" should "be able to write back entity attributes" in {
    Publisher.spark = spark
    Publisher.execute(EIJobArgs)
    val outDF = reader.read("ei_publish_attr.sds_ei__host__publish")
    outDF.show(false)
    spark.sql("SHOW TBLPROPERTIES iceberg_catalog.ei_publish_attr.sds_ei__host__publish").show(false)
    assertDataFrameEquals(hostExpectedDF.orderBy("p_id"), spark.createDataFrame(outDF.select(hostExpectedDF.columns.map(col(_)): _*).rdd,
      hostExpectedDF.schema).orderBy("p_id"))
  }

  "it" should "reject entity and relationship tables that does match regex" in {
    Using(Source.fromFile(logFilePath)) { source =>
      val logFileContents = source.getLines().mkString("\n")
      val contentToFind = "The following tables doesn't satisfy the regex"
      val res = if (logFileContents.contains(contentToFind)) 1 else 0
      assertResult(1)(res)
      source.close()
    }
  }

  "it" should "throw exception when duplicate pid are present in enrich tables" in {
    val vmHostEnrichDupCol = Seq(
      ("p_12", false, END_EPOCH)
    ).toDF("p_id", "test_col", UPDATED_AT)
      .withColumn(UPDATED_AT_TS,expr(s"to_timestamp($UPDATED_AT/1000)"))
    writer.overwritePartition(vmHostEnrichDupCol, "temp.sds_am__entity__host__enrich")
    writer.append(vmHostEnrichDupCol, "temp.sds_am__entity__host__enrich")
    val caught = intercept[InvalidDataException] {
      Publisher.execute(EIJobArgs)
    }
    spark.sql("drop table iceberg_catalog.temp.sds_am__entity__host__enrich")
    assertResult(s"Following tables contains duplicate p_id, temp.sds_am__entity__host__enrich")(caught.getMessage)
  }

  "it" should "throw exception when duplicate columns are present in enrich tables" in {
        val vmHostEnrichDupCol = Seq(
          ("p_14",false,END_EPOCH)
        ).toDF("p_id","critical",UPDATED_AT)
          .withColumn(UPDATED_AT_TS,expr(s"to_timestamp($UPDATED_AT/1000)"))
        writer.overwritePartition(vmHostEnrichDupCol, "ei.sds_vm__entity__host__enrich")

        val caught = intercept[InvalidDataException]{Publisher.execute(EIJobArgs)}
        spark.sql("drop table iceberg_catalog.ei.sds_vm__entity__host__enrich")
        assert(caught.getMessage === "Some of the columns are present in multiple inputs, critical -> [ei.sds_vm__entity__host__enrich, ei.sds_am__entity__host__enrich]")
  }

  "AttributeWriteBack" should "be able to write back relation attributes" in {
    val configPath = getClass.getResource("/entityinventory/publisher/relation_write_back.json").getPath
    val EIJobArgsConfigArray = Array("--current-updated-date", s"$END_EPOCH",
      "--output-schema", "ei_post",
      "--config", s"$configPath"
    )
    EIJobArgs = Publisher.getParseCmdArgs(EIJobArgsConfigArray)
    Publisher.spark = spark
    Publisher.execute(EIJobArgs)
    val outDF = reader.read("ei_publish_attr.sds_ei__rel__vulnerability_finding_on_host__publish")
    outDF.show(false)
    spark.sql("SHOW TBLPROPERTIES iceberg_catalog.ei_publish_attr.sds_ei__rel__vulnerability_finding_on_host__publish").show(false)
    assertDataFrameEquals(relExpDF.orderBy("relationship_id"), spark.createDataFrame(outDF.select(relExpDF.columns.map(col(_)): _*).rdd,
      relExpDF.schema).orderBy("relationship_id"))
  }
  "AttributeWriteBack" should "have uniform type across all tables" in {
    val configPath = getClass.getResource("/entityinventory/publisher/graph_entity_write_back_configs/sds_ei__account__publish.json").getPath
    println(configPath)
    val EIJobArgsConfigArray = Array("--current-updated-date", s"$END_EPOCH",
      "--output-schema", "ei_post",
      "--config", s"$configPath"
    )
    EIJobArgs = Publisher.getParseCmdArgs(EIJobArgsConfigArray)
    Publisher.spark = spark
    Publisher.execute(EIJobArgs)
    val outDF = reader.read("ei_publish_attr.sds_ei__host__publish_uniform")
    outDF.show(false)

  }
}
