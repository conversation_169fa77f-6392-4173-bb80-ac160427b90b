package ai.prevalent.entityinventory.disambiguator

import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils.{DATA_SOURCE_SUBSET_NAME, InventoryModelSpec, build, normalizeInventoryModels}
import ai.prevalent.entityinventory.disambiguator.configs.specs.{Config, Strategy}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{LAST_UPDATED_DATE, ORIGIN, UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.utils.SchemaEvolutionUtil
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
import ai.prevalent.sdspecore.utils.ConfigUtils
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.SparkConf
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, StringType}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec

import java.io.File
import scala.reflect.io.Directory

 class DisambiguatorUtilsSpec extends AnyFlatSpec with BeforeAndAfter with DataFrameSuiteBase with IcebergSparkTestWrapper with LoggerBase {

   var source1: DataFrame = _
   var source2: DataFrame = _
   var source3: DataFrame = _
   var expectedDis: DataFrame = _
   var expectedResolv: DataFrame = _

   var config: Config = _

   import spark.implicits._

   val WIN10 = "Win 10"
   val JITHIN_display_label = "Jithin Odattu"
   var emptyPrevResolver: DataFrame = _
   var prevInv: DataFrame = _
   var writer: SDSTableWriter = _
   var reader: SDSTableReader = _

   val eiConfigFormat = Disambiguator.configFormats

   override def conf: SparkConf = super.conf.set("spark.sql.shuffle.partitions", "1").set("spark.driver.memory","5g").set("spark.sds.hive.read.catalog","iceberg_catalog")

   override def warehousePath: String = {
     new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse")).createDirectory()
     getClass().getResource("/iceberg_warehouse").getPath
   }

   before {
     writer = SDSTableWriterFactory.get(SDSIcebergConnect.name, spark)
     reader = SDSTableReaderFactory.get(SDSIcebergConnect.name, spark)

     spark.sql(s"""CREATE SCHEMA IF NOT EXISTS iceberg_catalog.ei""")
     emptyPrevResolver = DisambiguatorUtils.safeLoadPreviousResolve("ei.test", 898l, reader, spark)
     val source_config_path = "file:" + getClass.getResource("/entityinventory/disambiguator/inersource/config.json").getPath
     config = ConfigUtils.getConfig(spark, s"$source_config_path", manifest[Config], eiConfigFormat)
     prevInv = spark.emptyDataFrame
     val location_lookup = Seq(
       ("ap-south-1", "IND", "Mumbai")
     ).toDF("region", "location_c", "location_city")
     writer.overwritePartition(location_lookup,"sdm.location_lookup")
     source1 = Seq(
       ("p_11", "Person", "A", "jithinodattu", 1234567890123L, "fQDn1", "azure_id1", "Win", "Jithin", 1234567890121L, 1234567890123L, "Active", "source1_persist1", "source1_persist2", "source1_persist3","source1_persist4",null, 1234567890121L, 1234567890123L),
       ("p_12", "Person", "A", "atulkrishnan", 1234567890124L, "fqdn1", "azure_id235", WIN10, "Unknown", 1234567890121L, 1234567890123L, "InActive", "source1_persist1", "source1_persist2", "source1_persist3","source1_persist4",null, 1234567890121L, 1234567890124L),
       ("p_14", "Person", "A", "confluenceadmin", 1234567890126L, "fqdn2", "azure_id2", WIN10, "Confluence", 1234567890121L, 1234567890125L, "InActive", "source1_persist1", "source1_persist2", "Unknown","source1_persist4",null, 1234567890121L, 1234567890126L),
       ("p_15", "Person", "A", "lijamohan", 1234567890125L, "fqdn23", "azure_id3", "Unknown", "Lija Mohan", 1234567890121L, 1234567890125L, "InActive", "source1_persist1", "source1_persist2", "p_15source1_persist3","source1_persist4",null, 1234567890121L, 1234567890125L)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "persistcol1", "persistcol2", "persistcol3", "persistcol4","persistcol5", "first_seen_date", "last_active_date")


     source2 = Seq(
       ("p_21", "Person", "B", "jithinodattu", 1234567890123L, "fqdn11", "azure_id12", null, "Jithin", 1234567890121L, 1234567890123L, "InActive", "Unknown", "source2_persist2", "source2_persist3","source2_persist4",null, 1234567890121L, 1234567890123L),
       ("p_22", "Person", "B", "atulkrishnan", 1234567890124L, "fqdn12", "azure_id22", "Win", "Atul", 1234567890121L, 1234567890124L, "InActive", "source2_persist1", "source2_persist2", "source2_persist3","source2_persist4",null, 1234567890121L, 1234567890124L),
       ("p_24", "Person", "B", "confluenceadmin2", 1234567890125L, "fqdn2", "azure_id12", null, "Confluence", 1234567890121L, 1234567890125L, "InActive", "Unknown", "source2_persist2", "source2_persist3","source2_persist4",null, 1234567890121L, 1234567890125L),
       ("p_25", "Person", "B", "lijamohan2", 1234567890126L, "fqdn23", "azure_id23", "Unknown", "Lija", 1234567890121L, 1234567890125L, "InActive", "Unknown", "Unknown", "source2_persist3","source2_persist4",null, 1234567890121L, 1234567890126L),
       ("p_255", "Person", "B", "lijamohan3", 1234567890125L, "fqdnx", "azure_id3", null, "Lija", 1234567890121L, 1234567890125L, "InActive", "source2_persist1", "source2_persist2", "source2_persist3","source2_persist4",null, 1234567890121L, 1234567890125L)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "persistcol1", "persistcol2", "persistcol3", "persistcol4","persistcol5", "first_seen_date", "last_active_date")

     source3 = Seq(
       ("p_31", "Person", "C", "lij", 1234567890123L, "fqdn2", "azure_id3", "Win", "lij", 1234567890121L, 1234567890123L, "InActive", "source3_persist1", "source3_persist2", "source3_persist3","source3_persist4",null, 1234567890121L, 1234567890123L)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "person__fqdn", "person__sou_rce1__azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "persistcol1", "persistcol2", "persistcol3", "persistcol4","persistcol5", "first_seen_date", "last_active_date")
     writer.overwritePartition(source1.withColumn(UPDATED_AT, lit(1682035199999L)).withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)")).drop("persistcol3","persistcol4","persistcol5"),"ei.sds_ei__person__a",Array(col("updated_at_ts")))
     writer.overwritePartition(source2.withColumn(UPDATED_AT, lit(1682035199999L)).withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)")).drop("persistcol3","persistcol4","persistcol5"),"ei.sds_ei__person__b",Array(col("updated_at_ts")))
     expectedDis = Seq(
       ("p_12", "Person", Array("A"), "atulkrishnan", 1234567890124L, Array("fqdn1"), Array("azure_id1", "azure_id235"), WIN10, "Jithin", 1234567890121l, 1234567890123l, "Active", 0, 0, 2, "fqdn1", "azure_id235", null, null, "source1_persist3", "source1_persist4", null),
       ("p_14", "Person", Array("A", "B", "C"), "confluenceadmin", 1234567890126L, Array("fqdn11", "fqdn2", "fqdn23", "fqdn343", "fqdnx"), Array("azure_id12", "azure_id2", "azure_id23", "azure_id3"), WIN10, "Confluence", 1234567890121l, 1234567890125l, "InActive", 0, 0, 8, "fqdn2", "azure_id2", "p_30source3_persist1", "source2_persist2", "p_15source1_persist3", "Unknown", "Unknown"),
       ("p_22", "Person", Array("B"), "atulkrishnan", 1234567890124l, Array("fqdn12"), Array("azure_id22"), "Win", "Atul", 1234567890121l, 1234567890124l, "InActive", 0, 0, 1, "fqdn12", "azure_id22", "source2_persist1", "source2_persist2", "source2_persist3", "source2_persist4", null),
       ("p_39", "Person", Array("C"), "lij", 1234567890123L, Array("fqdn343"), Array("azure_id343"), "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", 0, 0, 1, "fqdn343", "azure_id343", "Unknown", null, "Unknown", "source3_persist4",null),
       ("p_34", "Person", Array("C"), "lij", 1234567890123L, Array("p_32", "p_34"), Array("p_32","p_34"), "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", 0, 0, 2, "p_34", "p_34", "Unknown", null, "Unknown", "source3_persist4",null),
       ("p_33", "Person", Array("C"), "lij", 1234567890123L, Array("p_33"), Array("p_33"), "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", 0, 0, 1, "p_33", "p_33", "Unknown", null, "Unknown", "source3_persist4",null),
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "lifetime", "recency", "fragments", "fqdn__resolved", "azure_id__resolved", "persistcol1", "persistcol2", "persistcol3", "persistcol4", "persistcol5")
       .withColumn(DATA_SOURCE_SUBSET_NAME,col(ORIGIN))
       .withColumn(s"count_of_${ORIGIN}", size(col(ORIGIN)))
       .withColumn(s"${ORIGIN}_contribution_type",expr(s"CASE WHEN count_of_${ORIGIN} ==1 THEN 'Unique' WHEN count_of_${ORIGIN} >1 THEN 'Corroborated' ELSE NULL END"))
       .withColumn("case_sens_key",expr("case when p_id='p_34' then array('case_Sens_key') when p_id='p_33' then array('case_sens_key') else array() end"))
       .withColumn("new_type",expr("case when p_id='p_34' then 'Server' else null end"))
     val ORIGIN_ABC = Array("A", "B", "C")
     expectedResolv = Seq(
       ("p_12", Array("A"), "p_11", "Jithin", "jithinodattu", Array("A"), "Person", 1234567890123L,3),
       ("p_12", Array("A"), "p_12", "Jithin", "atulkrishnan", Array("A"), "Person", 1234567890123L,3),
       ("p_14", ORIGIN_ABC, "p_14", "Confluence", "confluenceadmin", Array("A"), "Person", 1234567890125L,3),
       ("p_14", ORIGIN_ABC, "p_15", "Confluence", "lijamohan", Array("A"), "Person", 1234567890125L,3),
       ("p_14", ORIGIN_ABC, "p_21", "Confluence", "jithinodattu", Array("B"), "Person", 1234567890125L,2),
       ("p_14", ORIGIN_ABC, "p_24", "Confluence", "confluenceadmin2", Array("B"), "Person", 1234567890125L,2),
       ("p_14", ORIGIN_ABC, "p_25", "Confluence", "lijamohan2", Array("B"), "Person", 1234567890125L,2),
       ("p_14", ORIGIN_ABC, "p_255", "Confluence", "lijamohan3", Array("B"), "Person", 1234567890125L,2),
       ("p_14", ORIGIN_ABC, "p_30", "Confluence", "lij", Array("C"), "Person", 1234567890125L,1),
       ("p_14", ORIGIN_ABC, "p_31", "Confluence", "lij", Array("C"), "Person", 1234567890125L,1),
       ("p_22", Array("B"), "p_22", "Atul", "atulkrishnan", Array("B"), "Person", 1234567890124L,2),
       ("p_39", Array("C"), "p_39", "lij", "lij", Array("C"), "Person", 1234567890123L,1),
       ("p_34", Array("C"), "p_32", "lij", "lij", Array("C"), "Person", 1234567890123L,1),
       ("p_34", Array("C"), "p_34", "lij", "lij", Array("C"), "Person", 1234567890123L,1),
       ("p_33", Array("C"), "p_33", "lij", "lij", Array("C"), "Person", 1234567890123L,1),
     ).toDF("disambiguated_p_id", "disambiguated_origin", "p_id","disambiguated_display_label", "primary_key", "origin", "class", "updated_at","precedence_order")
   }

   "it" should "give correct disambiguated df following confidence matrix restriction and persistance" in {
     val source3 = Seq(
       ("p_31", "Person", Array("C"), "lij", 1234567890123L, "fqdn2", "azure_id3", null, "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_31source3_persist2", "Unknown","Unknown","Unknown", 1234567890121L, 1234567890123L),
       ("p_30", "Person", Array("C"), "lij", 1234567890123L, "fqdn343", "azure_id3", "abc", "lij", 1234567890121L, 1234567890123L, "InActive", "p_30source3_persist1", "p_30source3_persist2", "Unknown",null,"source3_persist5", 1234567890121L, 1234567890123L),
       ("p_39", "Person", Array("C"), "lij", 1234567890123L, "fqdn343", "azure_id343", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_32", "Person", Array("C"), "lij", 1234567890123L, "p_32", "p_32", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_34", "Person", Array("C"), "lij", 1234567890123L, "p_34", "p_34", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_33", "Person", Array("C"), "lij", 1234567890123L, "p_33", "p_33", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "persistcol1", "persistcol2", "persistcol3","persistcol4","persistcol5", "first_seen_date", "last_active_date")
       .withColumn("case_sens_key",expr("case when p_id in ('p_32','p_34') then 'case_Sens_key' when p_id='p_33' then 'case_sens_key' end"))
       .withColumn("new_type",expr("case when p_id='p_34' then 'Server' else null end"))
     val inventoryModelSpecTest1 = Array(InventoryModelSpec(source1, "sf"),
       InventoryModelSpec(source2, "ad"),
       InventoryModelSpec(source3, "source3")
     )
     val nomralizedSpec = normalizeInventoryModels(inventoryModelSpecTest1, spark, config.disambiguation)
     val artifacts = build(nomralizedSpec, prevInv, config, spark)
     val properties = expectedDis.columns.map(col(_))
     val invDF = artifacts.interSourceDisambiguatedInventoryModel.select(properties:_*)
     val resolver = artifacts.interSourceResolver.get.drop("data_source_name",UPDATED_AT_TS).orderBy("primary_key","disambiguated_p_id", "p_id")

     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expectedResolv,resolver.schema).orderBy("p_id"),
       SchemaEvolutionUtil.evolveSchema(resolver,resolver.schema).orderBy("p_id"))
     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expectedDis,invDF.schema).orderBy("p_id"),
       SchemaEvolutionUtil.evolveSchema(invDF,invDF.schema).orderBy("p_id"))
   }


   "it" should "give disambiguated output with temporal confidence matrix feature" in {
     expectedDis = Seq(
       ("p_12", "Person", Array("A"), "atulkrishnan", 1234567890124L, Array("fqdn1"), Array("azure_id1", "azure_id235"), WIN10, "Jithin", 1234567890121l, 1234567890123l, "Active", 0, 0, 2, "fqdn1", "azure_id235", null, null, "source1_persist3", "source1_persist4", null),
       ("p_14", "Person", Array("A", "B", "C"), "confluenceadmin", 1234567890126L, Array("fqdn11", "fqdn2", "fqdn23", "fqdn343", "fqdnx"), Array("azure_id12", "azure_id2", "azure_id23", "azure_id3"), WIN10, "Confluence", 1234567890121l, 1234567890125l, "InActive", 0, 0, 8, "fqdn2", "azure_id2", "Unknown", "source2_persist2", "p_15source1_persist3", "Unknown", "Unknown"),
       ("p_22", "Person", Array("B"), "atulkrishnan", 1234567890124l, Array("fqdn12"), Array("azure_id22"), "Win", "Atul", 1234567890121l, 1234567890124l, "InActive", 0, 0, 1, "fqdn12", "azure_id22", null, "source2_persist2", "source2_persist3", "source2_persist4", null),
       ("p_39", "Person", Array("C"), "lij", 1234567890123L, Array("fqdn343"), Array("azure_id343"), "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", 0, 0, 1, "fqdn343", "azure_id343", "Unknown", null, "Unknown", "source3_persist4", null),
       ("p_32", "Person", Array("C"), "lij", 1234567890123L, Array("p_32", "p_34"), Array("p_32", "p_34"), "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", 0, 0, 2, "p_32", "p_32", "p_34_persist1", null, "Unknown", "source3_persist4", null),
       ("p_33", "Person", Array("C"), "lij", 1234567890123L, Array("p_33"), Array("p_33"), "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", 0, 0, 1, "p_33", "p_33", "Unknown", null, "Unknown", "source3_persist4", null),
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "lifetime", "recency", "fragments", "fqdn__resolved", "azure_id__resolved", "persistcol1", "persistcol2", "persistcol3", "persistcol4", "persistcol5")
       .withColumn(DATA_SOURCE_SUBSET_NAME, col(ORIGIN))
       .withColumn(s"count_of_${ORIGIN}", size(col(ORIGIN)))
       .withColumn(s"${ORIGIN}_contribution_type",expr(s"CASE WHEN count_of_${ORIGIN} ==1 THEN 'Unique' WHEN count_of_${ORIGIN} >1 THEN 'Corroborated' ELSE NULL END"))
       .withColumn("case_sens_key", expr("case when p_id='p_32' then array('case_Sens_key') when p_id='p_33' then array('case_sens_key') else array() end"))
     val source3 = Seq(
       ("p_31", "Person", Array("C"), "lij", 1234567890123L, "fqdn2", "azure_id3", null, "lij", 1234567890121L, 1234567890123L, "InActive", null, "p_31source3_persist2", "Unknown", "Unknown", "Unknown", 1234567890121L, 1234567890125L),
       ("p_30", "Person", Array("C"), "lij", 1234567890123L, "fqdn343", "azure_id3", "abc", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_30source3_persist2", "Unknown", null, "source3_persist5", 1234567890121L, 1234567890123L),
       ("p_39", "Person", Array("C"), "lij", 1234567890123L, "fqdn343", "azure_id343", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown", "source3_persist4", null, 1234567890121L, 1234567890123L),
       ("p_32", "Person", Array("C"), "lij", 1234567890123L, "p_32", "p_32", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown", "source3_persist4", null, 1234567890121L, 1234567890123L),
       ("p_34", "Person", Array("C"), "lij", 1234567890125L, "p_34", "p_34", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "p_34_persist1", "p_39source3_persist2", "Unknown", "source3_persist4", null, 1234567890121L, 1234567890123L),
       ("p_33", "Person", Array("C"), "lij", 1234567890123L, "p_33", "p_33", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown", "source3_persist4", null, 1234567890121L, 1234567890123L)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "persistcol1", "persistcol2", "persistcol3", "persistcol4", "persistcol5", "first_seen_date", "last_active_date")
       .withColumn("case_sens_key", expr("case when p_id in ('p_32','p_34') then 'case_Sens_key' when p_id='p_33' then 'case_sens_key' end"))
       .withColumn("last_active_date", expr("case when p_id = 'p_34' then cast(null as long) else last_active_date end"))
       .withColumn("first_seen_date", expr("case when p_id = 'p_34' then cast(null as long) else first_seen_date end"))
       .withColumn("last_found_date", expr("case when p_id = 'p_34' then cast(null as long) else last_found_date end"))
     val inventoryModelSpecTest1 = Array(InventoryModelSpec(source1.withColumn("persistcol1", lit(null).cast(StringType)), "sf","sf"),
       InventoryModelSpec(source2.withColumn("persistcol1", lit(null).cast(StringType)), "ad","ad"),
       InventoryModelSpec(source3, "source3","source3"))
     val nomralizedSpec = normalizeInventoryModels(inventoryModelSpecTest1, spark, config.disambiguation)
     val artifacts = build(nomralizedSpec, prevInv, config, spark)
     val properties = expectedDis.columns.map(col(_))
     val invDF = artifacts.interSourceDisambiguatedInventoryModel.select(properties: _*)
     invDF.show(false)
     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expectedDis, invDF.schema).orderBy("p_id"),
       SchemaEvolutionUtil.evolveSchema(invDF, invDF.schema).orderBy("p_id"))
   }

//   "it" should "return exclude values only if exclude values and null are present and should return  null if only null is present" in {
//     expectedDis = Seq(
//       ("p_31", 1234567890123L, 1234567890121L,Array("C"),"source3_persist1",null,null),
//       ("p_14", 1234567890126L, 1234567890121L,Array("A", "B"),"Unknown",null,"Unknown"),
//       ("p_15", 1234567890126L, 1234567890121L,Array("A", "B"),"source2_persist1","Unknown","Unknown"),
//       ("p_12", 1234567890124L, 1234567890121L,Array("A"),null,null,null),
//       ("p_22", 1234567890124L, 1234567890121L,Array("B"),"source2_persist1",null,"Unknown")
//     ).toDF("p_id", "last_found_date", "first_found_date", "origin","persistcol1","persistcol2","persistcol3")
//       .withColumn(DATA_SOURCE_SUBSET_NAME,col(ORIGIN))
//       .withColumn(s"count_of_${ORIGIN}", size(col(ORIGIN)))
//       .withColumn(s"${ORIGIN}_contribution_type",expr(s"CASE WHEN count_of_${ORIGIN} ==1 THEN 'Unique' WHEN count_of_${ORIGIN} >1 THEN 'Corroborated' ELSE NULL END"))
//
//     source1 = source1.withColumn("persistcol3", expr("cast(null as string)"))
//     source2 = source2.withColumn("persistcol3", expr("'Unknown'"))
//       .withColumn("persistcol2", expr("case when primary_key != 'lijamohan2' then cast(null as string) else persistcol2 end"))
//     source3 = source3.withColumn("persistcol3", expr("cast(null as string)"))
//
//     val inventoryModelSpecTest1 = Array(InventoryModelSpec(source1, "sf","sf"),
//       InventoryModelSpec(source2, "ad","ad"),
//       InventoryModelSpec(source3, "source3","source3"))
//     val nomralizedSpec = normalizeInventoryModels(inventoryModelSpecTest1, spark, config.disambiguation)
//     val artifacts = build(nomralizedSpec, prevInv, config, spark)
//     val properties = expectedDis.columns.map(col(_))
//     val invDF = artifacts.interSourceDisambiguatedInventoryModel.select(properties:_*)
//     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expectedDis,invDF.schema).orderBy("p_id"),
//       SchemaEvolutionUtil.evolveSchema(invDF,invDF.schema).orderBy("p_id"))
//   }

//   "it"should "explode candidate keys of array type and apply disambiguation logic for each primary key" in {
//     source1 = source1.withColumn("fqdn", array(Array("fqdn1", "fqdn12").map(lit):_*))
//     source3 = source3.withColumn("fqdn", array(Array("fqdn12", "fqdn23").map(lit):_*))
//     source2 = source2.withColumn("fqdn", array(Array("fqdn1", "fqdn23").map(lit):_*))
//     val inventoryModelSpecTest1 = Array(InventoryModelSpec(source1, "sf","sf"),
//       InventoryModelSpec(source2, "ad","ad"),
//       InventoryModelSpec(source3, "source3","source3"))
//     val nomralizedSpec = normalizeInventoryModels(inventoryModelSpecTest1, spark, config.disambiguation)
//     val artifacts = build(nomralizedSpec, prevInv, config, spark)
//     val invDF = artifacts.interSourceDisambiguatedInventoryModel.drop("case_sens_key")
//
//     expectedDis = spark.read.schema(invDF.schema).json(getClass.getResource("/entityinventory/disambiguator/expected/").getPath)
//       .withColumn(DATA_SOURCE_SUBSET_NAME,col(ORIGIN))
//       .withColumn(s"count_of_${ORIGIN}", size(col(ORIGIN)))
//       .withColumn(s"${ORIGIN}_contribution_type",expr(s"CASE WHEN count_of_${ORIGIN} ==1 THEN 'Unique' WHEN count_of_${ORIGIN} >1 THEN 'Corroborated' ELSE NULL END"))
////     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expectedDis,invDF.schema).orderBy("p_id"),
//     invDF.show(false)
//     expectedDis.show(false)
//       SchemaEvolutionUtil.evolveSchema(invDF,invDF.schema).orderBy("p_id")
//   }

   "it" should "handle fields missing scenarios" in {
     val enrich_config_path = "file:" + getClass.getResource("/entityinventory/disambiguator/enrich_config.json").getPath
     source1 = Seq(
       ("p_11", "Person", "A", "jithinodattu", 1234567890125L, "fqdn1", "azure_id1", "Win", JITHIN_display_label, 1234567890121L, 1234567890125L, "status", 1234567890121L, 1234567890125L,"ap-south-1"),
       ("p_12", "Person", "A", "jithinodattu2", 1234567890123L, "fqdn1", "azure_id1", "Win", "Jithin", 1234567890121L, 1234567890125L, "status", 1234567890121L, 1234567890125L,"ap-south-1")
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdnn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "first_seen_date", "last_active_date","region")
     config = ConfigUtils.getConfig(spark, s"$enrich_config_path", manifest[Config], eiConfigFormat)
     val expected = Seq(
       ("p_11", "Person", Array("A"), "jithinodattu", 1234567890125L, "fqdn1", Array("azure_id1"), "Win", JITHIN_display_label, 1234567890121L, Array.empty[String], 1234567890125L, "status", 0, 0, 2, null, "azure_id1", null, null, 0, 1234567890121L)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdnn", "azure_id", "os", "display_label", "first_found_date", "fqdn", "updated_at", "status", "lifetime", "recent_activity", "fragments", "fqdn__resolved", "azure_id__resolved", "persistcol1", "persistcol2", "observed_lifetime", "first_seen_date")
       .withColumn("persistcol3", expr("cast(null as string)"))
       .withColumn("persistcol4", expr("cast(null as string)"))
       .withColumn("persistcol5", expr("cast(null as string)"))
       .withColumn(DATA_SOURCE_SUBSET_NAME,col(ORIGIN))
       .withColumn("region",lit("ap-south-1"))
       .withColumn("location_c",lit("IND"))
       .withColumn("location_city",lit("Mumbai"))
       .withColumn("location_country",lit("India"))
       .withColumn("origin_lookup_enrich",array(lit(null)))
       .withColumn(s"count_of_${ORIGIN}", size(col(ORIGIN)))
       .withColumn(s"${ORIGIN}_contribution_type",expr(s"CASE WHEN count_of_${ORIGIN} ==1 THEN 'Unique' WHEN count_of_${ORIGIN} >1 THEN 'Corroborated' ELSE NULL END"))
     val inventoryModelSpec = Array(InventoryModelSpec(source1, "source1","source1"),
       InventoryModelSpec(spark.emptyDataFrame, "source1","source1"))
     val normalizedModelSpec = normalizeInventoryModels(inventoryModelSpec, spark, config.disambiguation)
     val artifacts = build(normalizedModelSpec, prevInv, config, spark)

     val invDF = artifacts.interSourceDisambiguatedInventoryModel.drop("fqdn").drop("last_updated_attrs", LAST_UPDATED_DATE, "last_active_date", "recency", "exclude__temp_precedence", "temp_precedence", "case_sens_key")
     invDF.show(false)
     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expected,invDF.schema).orderBy("p_id"),
       SchemaEvolutionUtil.evolveSchema(invDF,invDF.schema).orderBy("p_id"))
   }

   "it" should "treat entities with precedence key null as separate entities when disambiguating" in {
     source1 = Seq(
       ("p_11", "Person", "A", "asdb", 1234567890125L, null, "azure_id1", "Win", "ASDB", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_12", "Person", "A", "sadsad", 1234567890123L, null, null, "Win", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "last_active_date", "first_seen_date", "persistcol3")

     source2 = Seq(
       ("p_112", "Person", "B", "jithinodattu", 1234567890125L, "", "azure_id12323", "Win", JITHIN_display_label, 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "last_active_date", "first_seen_date", "persistcol3")

     val expected = Seq(
       ("p_11", "Person", Array("A"), "asdb", 1234567890125L, Array.empty[String], Array("azure_id1"), "Win", "ASDB", 1234567890121L, "status", 1234567890125L, 1, null, "azure_id1", null, null, 0, 0, 0, 0, 1234567890121L),
       ("p_12", "Person", Array("A"), "sadsad", 1234567890123L, Array.empty[String], Array.empty[String], "Win", "SADSAD", 1234567890121L, "status", 1234567890125L, 1, null, null, null, null, 0, 0, 0, 0, 1234567890121L),
       ("p_112", "Person", Array("B"), "jithinodattu", 1234567890125L, Array.empty[String], Array("azure_id12323"), "Win", JITHIN_display_label, 1234567890121L, "status", 1234567890125L, 1, null, "azure_id12323", null, null, 0, 0, 0, 0, 1234567890121L)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "status", "updated_at", "fragments", "fqdn__resolved", "azure_id__resolved", "persistcol1", "persistcol2", "lifetime", "recency", "observed_lifetime", "recent_activity", "first_seen_date")
       .withColumn("persistcol3", expr("cast(null as string)"))
       .withColumn("persistcol4", expr("cast(null as string)"))
       .withColumn("persistcol5", expr("cast(null as string)"))
       .withColumn(DATA_SOURCE_SUBSET_NAME,col(ORIGIN))
       .withColumn(s"count_of_${ORIGIN}", size(col(ORIGIN)))
       .withColumn(s"${ORIGIN}_contribution_type",expr(s"CASE WHEN count_of_${ORIGIN} ==1 THEN 'Unique' WHEN count_of_${ORIGIN} >1 THEN 'Corroborated' ELSE NULL END"))
     val inventoryModelSpec = Array(InventoryModelSpec(source1, "source1","source1"),
       InventoryModelSpec(source2, "source1","source1"))
     val normalizeInventoryModel = normalizeInventoryModels(inventoryModelSpec, spark, config.disambiguation)
     val artifacts = build(normalizeInventoryModel, prevInv, config, spark)
     val invDF = artifacts.interSourceDisambiguatedInventoryModel.drop("last_updated_attrs", LAST_UPDATED_DATE, "last_active_date", "exclude__temp_precedence", "temp_precedence","case_sens_key")
     val resolver = artifacts.interSourceResolver.get.drop("data_source_name",UPDATED_AT_TS)
     invDF.show(false)
     resolver.show(false)

     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expected,invDF.schema).orderBy("p_id"),
       SchemaEvolutionUtil.evolveSchema(invDF,invDF.schema).orderBy("p_id"))
     assertResult(3)(resolver.count())
   }

   "it" should "handle single candidate key" in {
     val config2 = configRead("/entityinventory/disambiguator/inersource/singleKeyConfig.json")
     source1 = Seq(
       ("p_11", "Person", "A", "jithinodattu", 1234567890125L, "fqdn1", "azure_id1", "Win", JITHIN_display_label, 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L),
       ("p_12", "Person", "A", "jithinodattu2", 1234567890123L, "fqdn1", "azure_id1", "Win", "Jithin", 1234567890121L, 1234567890125L, "status", 1234567890123L, 1234567890121L)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdnn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "last_active_date", "first_seen_date")
       .withColumn("persistcol3", expr("cast(null as string)"))
     val expected = Seq(
       ("p_11", "Person", Array("A"), "jithinodattu", 1234567890125L, "fqdn1", Array("azure_id1"), "Win", JITHIN_display_label, 1234567890121L, null, 1234567890125L, "status", 0, 0, 0, 0, 2, "azure_id1", 1234567890121L)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdnn", "azure_id", "os", "display_label", "first_found_date", "fqdn", "updated_at", "status", "lifetime", "recency", "recent_activity", "observed_lifetime", "fragments", "azure_id__resolved", "first_seen_date")
       .withColumn("persistcol3", expr("cast(null as string)"))
       .withColumn(DATA_SOURCE_SUBSET_NAME,col(ORIGIN))
       .withColumn(s"count_of_${ORIGIN}", size(col(ORIGIN)))
       .withColumn(s"${ORIGIN}_contribution_type",expr(s"CASE WHEN count_of_${ORIGIN} ==1 THEN 'Unique' WHEN count_of_${ORIGIN} >1 THEN 'Corroborated' ELSE NULL END"))

     val inventoryModelSpec = Array(InventoryModelSpec(source1, "source1","source1"),
       InventoryModelSpec(spark.emptyDataFrame, "source1","source1"))
     val normalizationModelSpec = normalizeInventoryModels(inventoryModelSpec, spark, config2.disambiguation)
     val artifacts = build(normalizationModelSpec, prevInv, config2, spark)
     val invDF = artifacts.interSourceDisambiguatedInventoryModel.drop("last_updated_attrs", LAST_UPDATED_DATE, "last_active_date", "exclude__temp_precedence", "temp_precedence")
     val resolver = artifacts.interSourceResolver.get.drop("data_source_name",UPDATED_AT_TS)

     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expected,invDF.schema).orderBy("p_id"),
       SchemaEvolutionUtil.evolveSchema(invDF,invDF.schema).orderBy("p_id"))
     assertResult(2)(resolver.count())
   }

   "it" should "handle key to key match candidate key" in {
     val config2 = configRead("/entityinventory/disambiguator/inersource/keyToKe.json")
     source1 = Seq(
       ("p_11", "Person", "A", "jithinodattu", 1234567890125L, "name1", "name2", "Win", JITHIN_display_label, 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L),
       ("p_12", "Person", "A", "jithinodattu2", 1234567890123L, "name2", "name1", "Win", "Jithin", 1234567890121L, 1234567890125L, "status", 1234567890123L, 1234567890121L)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "account_name", "sam_account_name", "os", "display_label", "first_found_date", "updated_at", "status", "last_active_date", "first_seen_date")
       .withColumn("persistcol3", expr("cast(null as string)"))
     expectedResolv = Seq(
       ("p_11", "A", "p_11", JITHIN_display_label, "jithinodattu", "A", "Person", 1234567890125L,1),
       ("p_12", "A", "p_12", "Jithin", "jithinodattu2", "A", "Person", 1234567890125L,1)
     ).toDF("disambiguated_p_id", "disambiguated_origin", "p_id", "disambiguated_display_label", "primary_key", "origin", "class", "updated_at","precedence_order")

     val inventoryModelSpec = Array(InventoryModelSpec(source1, "source1","source1"))
     val normalizationModelSpec = normalizeInventoryModels(inventoryModelSpec, spark, config2.disambiguation)
     val artifacts = build(normalizationModelSpec, prevInv, config2, spark)
     val resolver = artifacts.interSourceResolver.get.drop("data_source_name",UPDATED_AT_TS)

     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expectedResolv,resolver.schema).orderBy("p_id"),
       SchemaEvolutionUtil.evolveSchema(resolver,resolver.schema).orderBy("p_id"))
     assertResult(2)(artifacts.interSourceDisambiguatedInventoryModel.count())

   }

   "it" should "validate whether all the sources mentioned in the InventoryModelInput is mentioned in the default confidence matrix" in {
     val config1 = configRead("/entityinventory/disambiguator/inersource/validatorCheck.json")
     val caught = intercept[Exception] {
       config1.configValidator
     }

     assert("InvalidConfidenceMatrix:confidenceMatrix entries do not match inventoryModelInput list for source3", caught.getMessage)
   }

   "it" should "work with the candidate key with match attribute feature" in {
     val config = configRead("/entityinventory/disambiguator/inersource/config_match_key.json")
     val resolverInThistest = Seq(
       ("p_333", Array("C"), "p_333", "lij", "lij", Array("C"), "Person", 1234567890123L,1),
       ("p_334", Array("C"), "p_334", "lij", "lij", Array("C"), "Person", 1234567890123L,1),
       ("p_336", Array("C"), "p_335", "lij", "lij", Array("C"), "Person", 1234567890123L,1),
       ("p_336", Array("C"), "p_336", "lij", "lij", Array("C"), "Person", 1234567890123L,1),
       ("p_338", Array("C"), "p_337", "lij", "lij", Array("C"), "Person", 1234567890123L,1),
       ("p_338", Array("C"), "p_338", "lij", "lij", Array("C"), "Person", 1234567890123L,1),
       ("p_332", Array("C"), "p_331", "lij", "lij", Array("C"), "Person", 1234567890123L,1),
       ("p_332", Array("C"), "p_332", "lij", "lij", Array("C"), "Person", 1234567890123L,1)
     ).toDF("disambiguated_p_id", "disambiguated_origin", "p_id","disambiguated_display_label", "primary_key", "origin", "class", "updated_at","precedence_order")
     val source3 = Seq(
       ("p_31", "Person", Array("C"), "lij", 1234567890123L, "fqdn2", "azure_id3", null, "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_31source3_persist2", "Unknown","Unknown","Unknown", 1234567890121L, 1234567890123L),
       ("p_30", "Person", Array("C"), "lij", 1234567890123L, "fqdn343", "azure_id3", "abc", "lij", 1234567890121L, 1234567890123L, "InActive", "p_30source3_persist1", "p_30source3_persist2", "Unknown",null,"source3_persist5", 1234567890121L, 1234567890123L),
       ("p_39", "Person", Array("C"), "lij", 1234567890123L, "fqdn343", "azure_id343", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_32", "Person", Array("C"), "lij", 1234567890123L, "p_32", "p_32", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_34", "Person", Array("C"), "lij", 1234567890123L, "p_34", "p_34", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_33", "Person", Array("C"), "lij", 1234567890123L, "p_33", "p_33", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_333", "Person", Array("C"), "lij", 1234567890123L, "p_333_1", "p_333_1", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_334", "Person", Array("C"), "lij", 1234567890123L, "p_334_1", "p_334_1", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_335", "Person", Array("C"), "lij", 1234567890123L, "p_335_1", "p_335_1", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_336", "Person", Array("C"), "lij", 1234567890123L, "p_336_1", "p_336_1", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_337", "Person", Array("C"), "lij", 1234567890123L, "p_337_1", "p_337_1", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_338", "Person", Array("C"), "lij", 1234567890123L, "p_338_1", "p_337_1", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_331", "Person", Array("C"), "lij", 1234567890123L, "p_331_1", "p_331_1", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L),
       ("p_332", "Person", Array("C"), "lij", 1234567890123L, "p_332_1", "p_332_1", "Unknown", "lij", 1234567890121L, 1234567890123L, "InActive", "Unknown", "p_39source3_persist2", "Unknown","source3_persist4",null, 1234567890121L, 1234567890123L)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "persistcol1", "persistcol2", "persistcol3","persistcol4","persistcol5", "first_seen_date", "last_active_date")
       .withColumn("case_sens_key",expr("case when p_id in ('p_32','p_34') then 'case_Sens_key' when p_id='p_33' then 'case_sens_key' end"))
       .withColumn("host_scenario_match_key", expr("CASE WHEN p_id in ('p_333','p_334','p_335','p_336') THEN 'h1_match_test' WHEN p_id IN ('p_337','p_338') THEN 'h2_match_test' when p_id IN ('p_331','p_332') THEN 'h3_match_test' END"))
       .withColumn("fqdn_scenario_match_key", expr("CASE WHEN p_id = 'p_333' THEN 'f1_match_test' WHEN p_id = 'p_334' THEN 'f2_match_test' WHEN p_id = 'p_337' THEN 'f3_match_test' END"))


     val inventoryModelSpecTest1 = Array(InventoryModelSpec(source1, "sf","sf"),
       InventoryModelSpec(source2, "ad","ad"),
       InventoryModelSpec(source3, "source3","source3"))
     val nomralizedSpec = normalizeInventoryModels(inventoryModelSpecTest1, spark, config.disambiguation)
     val artifacts = build(nomralizedSpec, prevInv, config, spark)
     val resolver = artifacts.interSourceResolver.get.drop("data_source_name",UPDATED_AT_TS).orderBy("primary_key","disambiguated_p_id", "p_id")
     resolver.show(100,false)
     SchemaEvolutionUtil.evolveSchema(resolver,resolver.schema).orderBy("disambiguated_p_id").show(false)
     val finalResolver = expectedResolv.unionByName(resolverInThistest)
     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(finalResolver,resolver.schema).orderBy("p_id"),
       SchemaEvolutionUtil.evolveSchema(resolver,resolver.schema).orderBy("p_id"))
   }

   "it" should "return fragments with graph details" in {
     spark.conf.set("spark.checkpoint.dir", "/tmp/spark-checkpoint")
     spark.sql("create namespace if not exists iceberg_catalog.ei_frag")
     val configPath = getClass.getResource("/entityinventory/disambiguator/person_inter_config.json").getPath
     val jobArgs: Array[String] = Array(
       "--current-updated-date", "1682035199999",
       "--previous-updated-date", "1681935199999",
       "--config", configPath
     )
     val args = Disambiguator.getParseCmdArgs(jobArgs)
     val config = Disambiguator.getConfig(spark, args)
     Disambiguator.spark = spark
     Disambiguator.execute(args, config)
     val outDF = reader.read("ei_frag.sds_ei__person")
     val outfrag = reader.read("ei_frag.sds_ei__fragment__person")
     val outresol = reader.read("ei_frag.sds_ei__resolver_fragment__person")
     import spark.implicits._

     val expFragDF = spark.createDataFrame(Seq(
       ("p_12", "Person", "A", "atulkrishnan", 1234567890124L, "fqdn1", "azure_id235", "Win 10", "Unknown", 1234567890121L, 1682035199999L, "InActive", "source1_persist1", "source1_persist2", 1234567890121L, 1234567890124L, "2023-04-21 05:29:59.999", null, "p_12", 1, 1, "e323dc24fa495fc817f3b8775638fe065d70c3bc32400f4853d99945c508978e"),
       ("p_21", "Person", "B", "jithinodattu", 1234567890123L, "fqdn11", "azure_id12", null, "Jithin", 1234567890121L, 1682035199999L, "InActive", "Unknown", "source2_persist2", 1234567890121L, 1234567890123L, "2023-04-21 05:29:59.999", null, "p_14", 2, 2, "de86e582804ad42b0b885fd6ba1a7639b98b97090e90287706779b13f96951cd"),
       ("p_15", "Person", "A", "lijamohan", 1234567890125L, "fqdn23", "azure_id3", "Unknown", "Lija Mohan", 1234567890121L, 1682035199999L, "InActive", "source1_persist1", "source1_persist2", 1234567890121L, 1234567890125L, "2023-04-21 05:29:59.999", null, "p_15", 1, 1, "448482adc101ff6de534aa63f7180b68eaf9fd6a4168c88908f21f8489e4be5e"),
       ("p_25", "Person", "B", "lijamohan2", 1234567890126L, "fqdn23", "azure_id23", "Unknown", "Lija", 1234567890121L, 1682035199999L, "InActive", "Unknown", "Unknown", 1234567890121L, 1234567890126L, "2023-04-21 05:29:59.999", null, "p_15", 2, 1, "07a7b6a65b83982e466c3dc0fa4616997512fed69ac2057fda55ed066b3d9386"),
       ("p_11", "Person", "A", "jithinodattu", 1234567890123L, "fqdn1", "azure_id1", "Win", "Jithin", 1234567890121L, 1682035199999L, "Active", "source1_persist1", "source1_persist2", 1234567890121L, 1234567890123L, "2023-04-21 05:29:59.999", null, "p_12", 1, 2, "1dc313dbf7b19ba07e3cf3a2a9bf0e2877d3e2ea919082d1a9f2188a24d7e578"),
       ("p_14", "Person", "A", "confluenceadmin", 1234567890126L, "fqdn2", "azure_id2", "Win 10", "Confluence", 1234567890121L, 1682035199999L, "InActive", "source1_persist1", "source1_persist2", 1234567890121L, 1234567890126L, "2023-04-21 05:29:59.999", null, "p_14", 1, 1, "1c089a8382d5f997fcee4c9c9ea71c39d0ca4634f997dbe4d3436916bdb2ae78"),
       ("p_24", "Person", "B", "confluenceadmin2", 1234567890125L, "fqdn2", "azure_id12", null, "Confluence", 1234567890121L, 1682035199999L, "InActive", "Unknown", "source2_persist2", 1234567890121L, 1234567890125L, "2023-04-21 05:29:59.999", null, "p_14", 2, 1, "8c02e5273138a46d30705b546d2c8e2ca9375d7318abb8ac9d78269bdac1afc1"),
       ("p_255", "Person", "B", "lijamohan3", 1234567890125L, "fqdnx", "azure_id3", null, "Lija", 1234567890121L, 1682035199999L, "InActive", "source2_persist1", "source2_persist2", 1234567890121L, 1234567890125L, "2023-04-21 05:29:59.999", null, "p_15", 2, 2, "2c951836351588e43c429b375ae9e5d0fc8d94dfc68fff1a7092c976afe21743"),
       ("p_22", "Person", "B", "atulkrishnan", 1234567890124L, "fqdn12", "azure_id22", "Win", "Atul", 1234567890121L, 1682035199999L, "InActive", "source2_persist1", "source2_persist2", 1234567890121L, 1234567890124L, "2023-04-21 05:29:59.999", null, "p_22", 1, 1, "0d8fa41064948f9a9498c46f178b38598b087b85210dea07fa6f3d1089b64832")
     )).toDF(
       "p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "persistcol1", "persistcol2", "first_seen_date", "last_active_date", "updated_at_ts", "origin_entity_enrich", "disambiguated_p_id", "origin_index", "origin_fragment_index", "graph_id")

     val possibleArray = config.disambiguation.candidateKeys.map(_.name) ++ config.disambiguation.strategy.getOrElse(Strategy.empty).rollingUpFields
     val updDFFragDF = possibleArray.foldLeft(expFragDF)((dfUpd,col) => {
       if(expFragDF.columns.contains(col) && !expFragDF.schema(col).dataType.isInstanceOf[ArrayType])
         dfUpd.withColumn(col, array(col))
       else dfUpd
     })

     val expResolDF = spark.createDataFrame(Seq(
       ("p_14", "p_14", Array("A", "B"), "Confluence", "confluenceadmin", "A", "Person", 1682035199999L, 2, "ei_frag.sds_ei__person", "2023-04-21 05:29:59.999", "Person Has Fragment", "Resolved To Person", "851249e45adc23e4e78316c00ec85df7725e0be36f3735f846643573fec67866", "402b3d51cc37afdd9a2c937be06613f0b5aebd8c6813e16de142bd7e06421362", "1c089a8382d5f997fcee4c9c9ea71c39d0ca4634f997dbe4d3436916bdb2ae78"),
       ("p_21", "p_14", Array("A", "B"), "Confluence", "jithinodattu", "B", "Person", 1682035199999L, 1, "ei_frag.sds_ei__person", "2023-04-21 05:29:59.999", "Person Has Fragment", "Resolved To Person", "942fd89bcb7ed432f6bfd075f84d0b8c874c4e3242d099b8c26ca5ca2d46d2d3", "402b3d51cc37afdd9a2c937be06613f0b5aebd8c6813e16de142bd7e06421362", "de86e582804ad42b0b885fd6ba1a7639b98b97090e90287706779b13f96951cd"),
       ("p_24", "p_14", Array("A", "B"), "Confluence", "confluenceadmin2", "B", "Person", 1682035199999L, 1, "ei_frag.sds_ei__person", "2023-04-21 05:29:59.999", "Person Has Fragment", "Resolved To Person", "c7b3b073ba6e7498b7c7685afe6af3b68745c7ce0bd2330a6cab4283968e34d9", "402b3d51cc37afdd9a2c937be06613f0b5aebd8c6813e16de142bd7e06421362", "8c02e5273138a46d30705b546d2c8e2ca9375d7318abb8ac9d78269bdac1afc1"),
       ("p_25", "p_15", Array("A", "B"), "Lija Mohan", "lijamohan2", "B", "Person", 1682035199999L, 1, "ei_frag.sds_ei__person", "2023-04-21 05:29:59.999", "Person Has Fragment", "Resolved To Person", "7f7cb108abd45833671907ec785d293289961ff4575c3c2f9ed5b2dfc92caf54", "5f7f83ea776ac4a8fdc5e0f54d00c9774276ac89898c502338f8cc4c58083ddf", "07a7b6a65b83982e466c3dc0fa4616997512fed69ac2057fda55ed066b3d9386"),
       ("p_15", "p_15", Array("A", "B"), "Lija Mohan", "lijamohan", "A", "Person", 1682035199999L, 2, "ei_frag.sds_ei__person", "2023-04-21 05:29:59.999", "Person Has Fragment", "Resolved To Person", "df9997889bbc748c8e774536d0ab808f2668f0723fd8f192eed07643ebf839bd", "5f7f83ea776ac4a8fdc5e0f54d00c9774276ac89898c502338f8cc4c58083ddf", "448482adc101ff6de534aa63f7180b68eaf9fd6a4168c88908f21f8489e4be5e"),
       ("p_255", "p_15", Array("A", "B"), "Lija Mohan", "lijamohan3", "B", "Person", 1682035199999L, 1, "ei_frag.sds_ei__person", "2023-04-21 05:29:59.999", "Person Has Fragment", "Resolved To Person", "5efe962f50847062b127dbf58848b49f55315a1be007dd62e7fca6691a035aa5", "5f7f83ea776ac4a8fdc5e0f54d00c9774276ac89898c502338f8cc4c58083ddf", "2c951836351588e43c429b375ae9e5d0fc8d94dfc68fff1a7092c976afe21743"),
       ("p_22", "p_22", Array("B"), "Atul", "atulkrishnan", "B", "Person", 1682035199999L, 1, "ei_frag.sds_ei__person", "2023-04-21 05:29:59.999", "Person Has Fragment", "Resolved To Person", "3aaeb279b70a12280da2d7ca808b773f805c4f9e4388e63222150da2f39b5dbf", "f346d972959341fe10b20d4a35080939b4c82335205670eb05ec3518d698ed4c", "0d8fa41064948f9a9498c46f178b38598b087b85210dea07fa6f3d1089b64832"),
       ("p_11", "p_12", Array("A"), "Jithin", "jithinodattu", "A", "Person", 1682035199999L, 2, "ei_frag.sds_ei__person", "2023-04-21 05:29:59.999", "Person Has Fragment", "Resolved To Person", "4fd366136b3474215470aa05a00ce69ce881a5384c00f788409c6aa8e48d05a9", "8d9ad154723b969565a9bf1eec19532784391ec8586a57aed63ef30db44c6122", "1dc313dbf7b19ba07e3cf3a2a9bf0e2877d3e2ea919082d1a9f2188a24d7e578"),
       ("p_12", "p_12", Array("A"), "Jithin", "atulkrishnan", "A", "Person", 1682035199999L, 2, "ei_frag.sds_ei__person", "2023-04-21 05:29:59.999", "Person Has Fragment", "Resolved To Person", "b210661cd2e00b8ec79504c0915eb2b42a1f15637c45b9a03a74ffefb7fea93e", "8d9ad154723b969565a9bf1eec19532784391ec8586a57aed63ef30db44c6122", "e323dc24fa495fc817f3b8775638fe065d70c3bc32400f4853d99945c508978e")
     )).toDF("p_id", "disambiguated_p_id", "disambiguated_origin", "disambiguated_display_label", "primary_key", "origin", "class", "updated_at", "precedence_order", "data_source_name", "updated_at_ts", "relationship_name", "inverse_relationship_name", "graph_id", "source_graph_id", "target_graph_id")
     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(outfrag, updDFFragDF.schema).orderBy("p_id"),updDFFragDF.orderBy("p_id"))
     assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(outresol, expResolDF.schema).orderBy("p_id"),expResolDF.orderBy("p_id"))

     val resolTableProps = spark.sql("SHOW TBLPROPERTIES iceberg_catalog.ei_frag.sds_ei__resolver_fragment__person").collect()
     val resolPropertiesMap = resolTableProps.map(row => row.getString(0) -> row.getString(1)).toMap
//     resolPropertiesMap.toSeq.sortBy(_._1).foreach { case (key, value) =>
//       println(s"$key = $value")
//     }
     assert(resolPropertiesMap("graph.cache.enabled")=="true")
     assert(resolPropertiesMap("graph.edge.name")=="Person Has Fragment")
     assert(resolPropertiesMap("graph.edge.source.name")=="Person")
     assert(resolPropertiesMap("graph.edge.target.name")=="Fragment Person")

     val fragTableProps = spark.sql("SHOW TBLPROPERTIES iceberg_catalog.ei_frag.sds_ei__fragment__person").collect()
     val fragPropertiesMap = fragTableProps.map(row => row.getString(0) -> row.getString(1)).toMap
//     fragPropertiesMap.toSeq.sortBy(_._1).foreach { case (key, value) =>
//              println(s"$key = $value")
//            }
     assert(fragPropertiesMap("graph.cache.enabled") == "true")
     assert(fragPropertiesMap("graph.vertex.name") == "Fragment Person")
   }

   "it" should "return union df" in {
     spark.conf.set("spark.checkpoint.dir", "/tmp/spark-checkpoint")
     spark.sql("create namespace if not exists iceberg_catalog.ei_frag")
     val configPath = getClass.getResource("/entityinventory/disambiguator/union.json").getPath
     val jobArgs: Array[String] = Array(
       "--current-updated-date", "1682035199999",
       "--previous-updated-date", "1681935199999",
       "--config", configPath
     )
     val args = Disambiguator.getParseCmdArgs(jobArgs)
     val config = Disambiguator.getConfig(spark, args)
     Disambiguator.spark = spark
     Disambiguator.execute(args, config)
     val outDF = reader.read("ei_frag.sds_ei__person_union")

     import spark.implicits._


   }

   "it" should "correctly resolve freq attributes" in {
     val config = configRead("/entityinventory/disambiguator/inersource/config_freq_attr.json")
     source1 = Seq(
       ("p_11", "Person", "A", "asdb", 1234567890125L, null, "azure_id1", "Win", "ASDB", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_12", "Person", "A", "sadsad1", 1234567890123L, null, "azure_id1", "Win", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_13", "Person", "A", "sadsad2", 1234567890123L, null, "azure_id1", "Lin", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_14", "Person", "A", "sadsad3", 1234567890123L, null, "azure_id2", "Win", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_15", "Person", "A", "sadsad4", 1234567890123L, null, "azure_id2", "Win", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_16", "Person", "A", "sadsad5", 1234567890123L, null, "azure_id2", "Lin", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_17", "Person", "A", "sadsad6", 1234567890123L, null, "azure_id2", "Lin", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_18", "Person", "A", "sadsad7", 1234567890123L, null, "azure_id2", "Ub", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_20", "Person", "A", "sadsad8", 1234567890123L, null, "azure_id3", "Ubuntu", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_21", "Person", "A", "sadsad9", 1234567890123L, null, "azure_id3", "Ubuntu", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_22", "Person", "A", "sadsad9", 1234567890123L, null, "azure_id3", "Ubuntuuu", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null),
       ("p_23", "Person", "A", "sadsad9", 1234567890123L, null, "azure_id3", "Ubuntuuu", "SADSAD", 1234567890121L, 1234567890125L, "status", 1234567890125L, 1234567890121L, null)
     ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status", "last_active_date", "first_seen_date", "persistcol3")

     val inventoryModelSpec = Array(InventoryModelSpec(source1, "source1"))
     val normalizationModelSpec = normalizeInventoryModels(inventoryModelSpec, spark, config.disambiguation)
     val artifacts = build(normalizationModelSpec, prevInv, config, spark)
     artifacts.interSourceDisambiguatedInventoryModel.show(false)
     val expDF = Seq(
       ("p_11", "Person", Array("A"), "asdb", "Win"),
       ("p_18", "Person", Array("A"), "sadsad7", "Lin"),
       ("p_23", "Person", Array("A"), "sadsad9", "Ubuntuuu"),
     ).toDF("p_id", "class", "origin", "primary_key", "os")


     assertDataFrameDataEquals(expDF.orderBy("p_id"),
       artifacts.interSourceDisambiguatedInventoryModel.select(expDF.columns.map(col):_*).orderBy("p_id"))

   }
   def configRead(location:String): Config = {
     val confPath = getClass.getResource(location).getPath
     ConfigUtils.getConfig(spark, s"file:$confPath", manifest[Config], eiConfigFormat)
   }
 }