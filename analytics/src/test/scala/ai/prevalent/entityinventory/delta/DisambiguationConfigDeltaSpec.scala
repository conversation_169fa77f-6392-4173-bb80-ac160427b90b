package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.disambiguator.Disambiguator
import ai.prevalent.entityinventory.disambiguator.configs.specs.Config
import ai.prevalent.sdspecore.utils.ConfigUtils
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.scalatest.BeforeAndAfterAll
import org.scalatest.flatspec.AnyFlatSpec

class DisambiguationConfigDeltaSpec  extends AnyFlatSpec with BeforeAndAfterAll with DataFrameSuiteBase{
    var idConfigOld:Config = _
  var idConfigNew:Config = _


  override def beforeAll : Unit = {
    super.beforeAll()
    val newPath = "file:" + getClass.getResource("/entityinventory/delta/sds_ei__host__job_config_new.json").getPath
    val oldPath = "file:" + getClass.getResource("/entityinventory/delta/sds_ei__host__job_config_old.json").getPath
    idConfigOld = ConfigUtils.getConfig(spark, oldPath , manifest[Config], Disambiguator.configFormats)
    idConfigNew = ConfigUtils.getConfig(spark, newPath, manifest[Config], Disambiguator.configFormats)
  }

  "it" should "should show changes in inv model input" in {
    val modelChange = DisambiguationConfigDelta.checkModelInputUpdate(idConfigOld, idConfigNew)
    println(modelChange.mkString("Array(", ", ", ")"))
  }

  "it" should "should show changes in output filter" in {
    val filterChange = DisambiguationConfigDelta.checkOutputFilterChange(idConfigOld, idConfigNew)
    println(filterChange.mkString("Array(", ", ", ")"))
  }

  "it" should "show changes in candidate key list" in {
    val candidateKeyListChange = DisambiguationConfigDelta.checkCandidateKeyListChange(idConfigOld, idConfigNew)
    val candidateKeyUpdateChange = DisambiguationConfigDelta.checkCandidateKeyUpdate(idConfigOld, idConfigNew)
    candidateKeyListChange.foreach(key => {
      println(key.name + " " + key .reason+ " " + key.category + " " + key.changeType)
    })
    candidateKeyUpdateChange.map(key => {
      println(key.name + " " + key .reason+ " " + key.category + " " + key.changeType)
    })
  }

  "it" should "show changes in confidence matrix" in {
    val confidenceMatrixChange = DisambiguationConfigDelta.checkConfidenceMatrixUpdate(idConfigOld, idConfigNew)
    println(confidenceMatrixChange.get)
  }

  "it" should "show changes in exclude list" in {
    val excludeListChange = DisambiguationConfigDelta.checkExludeValuesUpdate(idConfigOld, idConfigNew)
    println(excludeListChange.mkString("Array(", ", ", ")"))
  }

//  "it" should "show changes in rollup" in {
//    val rollupChange = DisambiguationConfigDelta.checkRollingUpFieldsStrategyChange(idConfigOld, idConfigNew)
//    println(rollupChange.mkString("Array(", ", ", ")"))
//  }

  "it" should "show changes in value confidence strategy" in {
    val valueConfidenceChange = DisambiguationConfigDelta.checkvalueConfidenceStrategyChange(idConfigOld, idConfigNew)
    println("********Value confidence ***********")
    println(valueConfidenceChange.mkString("Array(", ", ", ")"))
    println("********Value confidence ***********")
  }


  "it" should "show changes in temporal confidence matrix" in {
    val temporalChange = DisambiguationConfigDelta.checkfieldLevelConfidenceMatrixStrategyChange(idConfigOld, idConfigNew)
        println("********Value confidence ***********")
    println(temporalChange.mkString("Array(", ", ", ")"))
    println("********Value confidence ***********")

  }



}
