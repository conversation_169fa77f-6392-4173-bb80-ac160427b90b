package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.sdspecore.utils.ConfigUtils
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.scalatest.BeforeAndAfterAll
import org.scalatest.flatspec.AnyFlatSpec

class LoaderConfigDeltaSpec extends AnyFlatSpec with BeforeAndAfterAll with DataFrameSuiteBase{

  var idConfigOld:Config = _
  var idConfigNew:Config = _


  override def beforeAll : Unit = {
    super.beforeAll()
    idConfigOld = ConfigUtils.getConfig(spark, getClass.getResource("/entityinventory/delta/sds_ei__identity__active_directory__sam_account_name_with_domain__job_config_old.json").getPath, manifest[Config])
    idConfigNew = ConfigUtils.getConfig(spark, getClass.getResource("/entityinventory/delta/sds_ei__identity__active_directory__sam_account_name_with_domain__job_config_new.json").getPath, manifest[Config])
  }

  "checkPrimaryKeyDelta" should "be able to identify the primary key changes" in {
    val changes = LoaderConfigDelta.checkPrimaryKeyChange(idConfigOld,idConfigNew)
    println("***************** Primary Key Changes ****************")
    println(changes)
  }

  "checkFilterChange" should "be able to identify the filter changes" in {
    val changes = LoaderConfigDelta.checkFilterChange(idConfigOld,idConfigNew)
    println("***************** Filter Changes ****************")
    println(changes)
  }

  "checkOriginChange" should "be able to identify the origin changes" in {
    val changes = LoaderConfigDelta.checkOriginChange(idConfigOld,idConfigNew)
    println("***************** Origin Changes ****************")
    println(changes)
  }

  "checkPropertyExpressionChange" should " be able to identify the property expression changes" in {
    val changes = propertyExpressionChanges
    println("***************** Expression Spec Changes ****************")
    changes.foreach(println)
  }

  "checkFieldSpecChange" should "be able to identify the field spec changes" in {
    val propsOld = idConfigOld.allProperties(true).map(( prop => (prop.colName,prop))).toMap
    val propsNew = idConfigNew.allProperties(true).map(( prop => (prop.colName,prop))).toMap
    val changes = LoaderConfigDelta.checkFieldSpecChange(propsOld,propsNew)
    println("**************** Field Spec Changes ****************")
    changes.foreach(println)
  }

  "checkInheritedChanges" should "be able to identify indirect changes happened to a propert" in {
    val changes = propertyExpressionChanges
    val propsNew = idConfigNew.allProperties(true).map(( prop => (prop.colName,prop))).toMap
    val inheritedChanges = LoaderConfigDelta.checkPropertyInheritedChanges(changes,propsNew)
    println("**************** Cascaded Changes ****************")
    inheritedChanges.foreach(println)
  }



  def propertyExpressionChanges  ={
    val propsOld = idConfigOld.allProperties(true).map(( prop => (prop.colName,prop))).toMap
    val propsNew = idConfigNew.allProperties(true).map(( prop => (prop.colName,prop))).toMap
    LoaderConfigDelta.checkPropertyExpressionChange(propsOld,propsNew)
  }

}
