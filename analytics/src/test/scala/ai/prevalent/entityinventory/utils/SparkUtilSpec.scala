package ai.prevalent.entityinventory.utils

import ai.prevalent.entityinventory.common.configs.{DataSource, Entity, FieldsSpec, OutputTableInfo, Property}
import ai.prevalent.entityinventory.loader.LoaderUtils.extractIdenticalColumnSequences
import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.entityinventory.utils.SparkUtil.unionByName
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.scalatest.flatspec.AnyFlatSpec

class SparkUtilSpec extends AnyFlatSpec with DataFrameSuiteBase {

  import spark.implicits._

  "union" should "return dataframe" in {
    val source2 = Seq(
      ("p_13", "Person", <PERSON><PERSON><PERSON>("B"), <PERSON><PERSON><PERSON>("lija"), 1234567890123L, "fqdn1", "azure_id1", "Win", "lija", 1234567890121L )
    ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date")

    val source1 = Seq(
      ("p_11", "Person", "A", Array("jithinodattu"), 1234567890123L, "fqdn1", "azure_id1", "Win", "Jithin", 1234567890121L, 1234567890123L, "Active")
    ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status")

    val source3 = Seq(
      ("p_31", "Host", Array("C","A"),12323, 1234567890123L, "fqdn2", "azure_id3", "Win", "Host1", 1234567890121L, Array(1234567890123L), "InActive"))
      .toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status")

    val union = unionByName(source2, source1,source3)

    val expectedDf = Seq(
      ("p_13", "Person", Array("B"), Array("lija"), 1234567890123L, "fqdn1", "azure_id1", "Win", "lija", 1234567890121L, null,null),
      ("p_11", "Person", Array("A"), Array("jithinodattu"), 1234567890123L, "fqdn1", "azure_id1", "Win", "Jithin", 1234567890121L, Array(1234567890123L), "Active"),
      ("p_31", "Host", Array("C","A"),Array("12323"), 1234567890123L, "fqdn2", "azure_id3", "Win", "Host1", 1234567890121L, Array(1234567890123L), "InActive")
    ).toDF("p_id", "class", "origin", "primary_key", "last_found_date", "fqdn", "azure_id", "os", "display_label", "first_found_date", "updated_at", "status")

    assertDataFrameEquals(union.select(expectedDf.columns.map(col(_)):_*),expectedDf)
  }

  "union" should "evolve struct colmn" in {
    val df1 = spark.sql("SELECT true as kk,array(1) as a, false as k, from_json('{\"fqdn\":{\"prev\":{\"value\":\"oldfq\",\"updated_at\":4321,\"last_found_date\":1234},\"last_changed\":{\"value\":\"fqdn1\",\"updated_at\":321,\"last_found_date\":123}}}','STRUCT<`fqdn`: STRUCT<`prev`: STRUCT<`value`: STRING, `updated_at`: INT, `last_seen_date`: INT>, `last_changed`: STRUCT<`value`: STRING, `updated_at`: INT, `last_seen_date`: INT>>>') as last_updated_attrs")
    val df2 = spark.sql("SELECT 'trule' as kk,2 as a, from_json('{\"is_active\":{\"prev\":{\"value\":\"oldfq\",\"updated_at\":4321,\"last_found_date\":1234},\"last_changed\":{\"value\":\"fqdn1\",\"updated_at\":321,\"last_found_date\":123}}}','STRUCT<`is_active`: STRUCT<`prev`: STRUCT<`value`: STRING, `updated_at`: INT, `last_seen_date`: INT>, `last_changed`: STRUCT<`value`: STRING, `updated_at`: INT, `last_seen_date`: INT>>>') as last_updated_attrs")
    df2.printSchema()
    val newDF = unionByName(df1,df2)
    newDF.show()
    newDF.printSchema()
  }

  "caseSensitivePropertyExpressionReplace" should "replace case sensitive properties" in {
    val schema = StructType(Seq(StructField("city", StringType, true), StructField("Code", StringType, true), StructField("country", StringType, true), StructField("resource", StructType(Seq(
        StructField("name", StructType(Seq(StructField("id", StringType, true))), true), StructField("Name", StructType(Seq(StructField("id", StringType, true))), true))), true)))
    val data = Seq(Row("New York", "NY", "USA", null), Row("San Francisco", "CA", "USA", null), Row("London", null, "UK", null))
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), schema)
    val identical_cols=extractIdenticalColumnSequences(df)
    val expected_identical_cols = List(
      List("resource.name.id", "resource.Name.id"),
      List("city"), List("country"),
      List("resource.name", "resource.Name"),
      List("Code"), List("resource")
    )
    val orderingMap: Map[String, Int] = expected_identical_cols.map(_.mkString(",")).zipWithIndex.toMap
    val sorted_identical_cols = identical_cols.sortBy(cols => orderingMap.getOrElse(cols.mkString(","), Int.MaxValue))
    assert(sorted_identical_cols == expected_identical_cols)
    val config= Config(
      primaryKey = "object_guid",
      entity = Entity(name = "Host"),
      origin = "Active Directory",
      outputTableInfo=OutputTableInfo(outputTableName = "ei_temp.sds_ei__host__active_directory__object_guid",outputWrittenMode = "tableType"),
      dataSource = Some(DataSource(name = "AD", feedName = "abc", srdm = "path/to/srdm")),
        entitySpecificProperties = Array(Property("first_name", "s_name"),Property("country_name", "COuntry"),Property("resource_id", "resource.name.id"),Property("resource_name", "resource.NAME"))
    )
    val replacedCasesensitiveProperties = CasePropertyExpressionReplacer.caseSensitivePropertyExpressionReplace(config.allProperties,identical_cols)
    val expected_property = List(
      Property("first_found_date", "event_timestamp_epoch", FieldsSpec(None, false, true, None, None, false)),
      Property("last_found_date", "event_timestamp_epoch", FieldsSpec(None, false, true, None, None, false)),
      Property("resource_name", "COALESCE(resource.name, resource.Name)", FieldsSpec(None, false, true, None, None, false)),
      Property("resource_id", "COALESCE(resource.name.id, resource.Name.id)", FieldsSpec(None, false, true, None, None, false)),
      Property("country_name", "COALESCE(country)", FieldsSpec(None, false, true, None, None, false)),
      Property("first_name", "s_name", FieldsSpec(None, false, true, None, None, false)),
      Property("class", "'Host'", FieldsSpec(None, false, true, None, None, false)),
      Property("recent_activity", "CASE WHEN (last_active_date IS NOT NULL) THEN datediff(from_unixtime((updated_at / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime((last_active_date / 1000), 'yyyy-MM-dd HH:mm:ss')) ELSE CAST(NULL AS INT) END", FieldsSpec(None, true, true, None, None, false)),
      Property("observed_lifetime", "datediff(from_unixtime((last_found_date / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime((first_found_date / 1000), 'yyyy-MM-dd HH:mm:ss'))", FieldsSpec(None, true, true, None, None, false)),
      Property("recency", "datediff(from_unixtime((updated_at / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime((last_found_date / 1000), 'yyyy-MM-dd HH:mm:ss'))", FieldsSpec(None, true, true, None, None, false)),
      Property("lifetime", "CASE WHEN ((last_active_date IS NOT NULL) AND (first_seen_date IS NOT NULL)) THEN datediff(from_unixtime((last_active_date / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime((first_seen_date / 1000), 'yyyy-MM-dd HH:mm:ss')) ELSE CAST(NULL AS INT) END", FieldsSpec(None, true, true, None, None, false)))
      assert(replacedCasesensitiveProperties==expected_property)
  }
}
