package ai.prevalent.entityinventory.utils



// package ai.prevalent.entityinventory.utils

// import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils
// import ai.prevalent.entityinventory.loader.Loader
// import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
// import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
// import com.holdenkarau.spark.testing.DataFrameSuiteBase
// import org.apache.spark.sql.functions._
// import org.scalatest.BeforeAndAfter
// import org.scalatest.flatspec.AnyFlatSpec

// import java.io.File
// import scala.reflect.io.Directory

// class EIUtilSpec extends AnyFlatSpec with DataFrameSuiteBase with BeforeAndAfter with IcebergSparkTestWrapper{

//   var writer:SDSTableWriter = _
//   var reader:SDSTableReader = _

//   before {
//     writer = SDSTableWriterFactory.getDefault(spark)
//     reader = SDSTableReaderFactory.getDefault(spark)
//   }
//   after {
//     new Directory(new File(warehousePath)).deleteRecursively()
//   }

//   override def warehousePath: String = {
//     new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse")).createDirectory()
//     getClass().getResource("/iceberg_warehouse").getPath
//   }

//   "EIUtil" should "find last updated " in {
//     val confPath = "file:" + getClass.getResource("/entityinventory/loader/microsoft_azure__defender_device_list/sds_ei__host__ms_defender_device_list__id__job_config.json").getPath
//     val sdmPath = "file:" + getClass.getResource("/entityinventory/loader/microsoft_azure__defender_device_list/microsoft_azure__defender_device_list").getPath
//     val sdmName = "sdm.azurefoutil"
//     val eiName = "ei_temp.azure_tuiltest"
//     val sdmDF = spark.read.parquet(sdmPath)
//     writer.overwritePartition(sdmDF, sdmName)
//     spark.sql("create namespace if not exists iceberg_catalog.ei_temp")

//     val args = Array(
//       "--parsed-interval-start", "0000000000000",
//       "--parsed-interval-end", "1681862399999",
//       "--current-updated-date", "1681862399999",
//       "--config-path", confPath,
//       "--previous-updated-date", "-1"
//     )
//     Loader.spark = spark
//     Loader.execute(Loader.getParseCmdArgs(args))

//     val outDF = reader.read(eiName).drop("recency")
//     assertResult(1)(outDF.filter("p_id='00343efb7aba3d2025461619fc1a492eebcbe77d90a3d732718a73462e44c605' and last_updated_attrs.fqdn.isChangedInCurrentRun = false AND last_updated_attrs.fqdn.prev IS NULL AND last_updated_attrs.fqdn.last_changed IS NULL").count())

//     val newInv1 = outDF
//       .withColumn("fqdn", expr("case when p_id='00343efb7aba3d2025461619fc1a492eebcbe77d90a3d732718a73462e44c605' then 'new fw' else fqdn end"))
//       .withColumn("last_found_date", expr("last_found_date+223233"))
//       .withColumn("updated_at", expr("updated_at+223233"))

//     val lastUpDF1 = DisambiguatorUtils.findLatestUpdatedFields(newInv1, outDF, Loader.getConfig(spark, Loader.getParseCmdArgs(args)).entity)
//     val updInv1 = lastUpDF1
//       .filter("p_id='00343efb7aba3d2025461619fc1a492eebcbe77d90a3d732718a73462e44c605'")
//       .select("last_updated_attrs")
//     val schema = updInv1.schema.fields.filter(_.name.equals("last_updated_attrs")).last.dataType.sql
//     assertDataFrameDataEquals(spark.sql(s"""select from_json('{"fqdn":{"isChangedInCurrentRun":true,"prev":{"value":"fqdn7-alecha-mob.corp.xyz.edu","updated_at":1681862399999,"last_found_date":1672820000000},"last_changed":{"value":"new fw","updated_at":1681862623232,"last_found_date":1672820223233}},"test1":{"isChangedInCurrentRun":false,"prev":null,"last_changed":null},"test2":{"isChangedInCurrentRun":false,"prev":null,"last_changed":null},"test3":{"isChangedInCurrentRun":false,"prev":null,"last_changed":null}}','$schema') as last_updated_attrs"""), updInv1)
//     assertResult(1)(updInv1.filter("last_found_date=1672820223233").count())
//     val newInv2 = lastUpDF1
//       .withColumn("last_found_date", expr("last_found_date+223233"))
//       .withColumn("updated_at", expr("updated_at+223233"))
//       .withColumn("test1", expr("Array('a')"))
//       .withColumn("test2", expr("Struct('b')"))
//       .filter("p_id='00343efb7aba3d2025461619fc1a492eebcbe77d90a3d732718a73462e44c605'")

//     val lastUpDF2 = DisambiguatorUtils.findLatestUpdatedFields(newInv2, lastUpDF1, Loader.getConfig(spark, Loader.getParseCmdArgs(args)).entity)
//     val updInv2 = lastUpDF2.filter("p_id='00343efb7aba3d2025461619fc1a492eebcbe77d90a3d732718a73462e44c605'")
//     assertDataFrameDataEquals(spark.sql(s"""select from_json('{"fqdn":{"isChangedInCurrentRun":false,"prev":{"value":"fqdn7-alecha-mob.corp.xyz.edu","updated_at":1681862399999,"last_found_date":1672820000000},"last_changed":{"value":"new fw","updated_at":1681862623232,"last_found_date":1672820223233}},"test1":{"isChangedInCurrentRun":true,"prev":{"updated_at":1681862623232,"last_found_date":1672820223233},"last_changed":{"value":"a","updated_at":1681862846465,"last_found_date":1672820446466}},"test2":{"isChangedInCurrentRun":true,"prev":{"updated_at":1681862623232,"last_found_date":1672820223233},"last_changed":{"value":{"col1":"b"},"updated_at":1681862846465,"last_found_date":1672820446466}},"test3":{"isChangedInCurrentRun":false,"prev":null,"last_changed":null}}','$schema') as last_updated_attrs"""), updInv2.select("last_updated_attrs"))
//     assertResult(1)(updInv1.filter("last_found_date=1672820223233").count())
//   }
// }
