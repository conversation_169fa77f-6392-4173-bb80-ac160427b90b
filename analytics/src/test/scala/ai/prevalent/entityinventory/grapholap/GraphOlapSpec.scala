package ai.prevalent.entityinventory.grapholap

import ai.prevalent.entityinventory.grapholap.configs.{Config, GraphOlapJobArgs}
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.sdspecore.sparkbase.table.iceberg.IcebergUtil
import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
import ai.prevalent.sdspecore.utils.ConfigUtils
import ai.prevalent.entityinventory.grapholap.GraphOlap
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.sql.catalyst.expressions.Expression
import org.apache.spark.sql.functions.{col, days, expr}
import org.json4s.DefaultFormats
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll}

import java.io.File
import java.time.format.DateTimeFormatter
import scala.reflect.io.Directory
import scala.collection.mutable


class GraphOlapSpec extends AnyFlatSpec with BeforeAndAfter with BeforeAndAfterAll with DataFrameSuiteBase with IcebergSparkTestWrapper{

  var writer : SDSTableWriter = _
  var reader : SDSTableReader = _
  val END_EPOCH = 1721260799999L
  var graphOlapJobArgs: GraphOlapJobArgs = _
  val hostpublish = "kg_publish.sds_ei__host__publish"
  val vulnpublish = "kg_publish.sds_ei__vulnerability__publish"


  import spark.implicits._

  override def conf() = super.conf
    .set("spark.sds.kg.olap.schema","kg_olap")
    .set("spark.sds.kg.olap.fragment.schema","kg_fragment_olap")
    .set( "spark.sql.session.timeZone", "UTC")
    .set("spark.sds.publisher.writebackBasePath", getClass.getResource("/entityinventory/grapholap/publisher/").getPath)
    .set("spark.sds.inter.writebackBasePath", getClass.getResource("/entityinventory/grapholap/inter/").getPath)
    .set("spark.sds.olap.writebackBasePath", getClass.getResource("/entityinventory/grapholap/olap_configs/").getPath)
  override def warehousePath: String = {
    new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse")).createDirectory()
    getClass.getResource("/iceberg_warehouse").getPath
  }
  override def beforeAll : Unit = {
    super.beforeAll()
    GraphOlap.spark = spark
    writer = SDSTableWriterFactory.getDefault(spark)
    reader = SDSTableReaderFactory.getDefault(spark)
    val graphJobArgsConfigArray = Array("--current-updated-date", s"$END_EPOCH"
        )
    graphOlapJobArgs = GraphOlap.getParseCmdArgs(graphJobArgsConfigArray)
    val df = Array.range(0,389).map( l => END_EPOCH-(l*86400000l))
      .map { i =>
        Seq(
          ("p_12", true, i)
        ).toDF("p_id", "critical", UPDATED_AT)
          .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
          .withColumn("status",expr("CASE WHEN updated_at_ts >= '2024-07-01 23:59:59.999' THEN 'active' ELSE 'inactive' END"))
      }.reduce( _ union _)
    writer.overwritePartition(df, hostpublish, Array(days(col(SDSProperties.schema.UPDATED_AT_TS))))
    writer.overwritePartition(df, vulnpublish, Array(days(col(SDSProperties.schema.UPDATED_AT_TS))))
    writer.overwritePartition(df, "kg_test.host_resolver", Array(days(col(SDSProperties.schema.UPDATED_AT_TS))))
    writer.overwritePartition(df, "kg_test.host_fragment", Array(days(col(SDSProperties.schema.UPDATED_AT_TS))))
  }
  def configRead(location: String): Config = {
    val confPath = getClass.getResource(location).getPath
    ConfigUtils.getConfig(spark, s"file:$confPath", manifest[Config], DefaultFormats)
  }
  "Graph olap write" should "fragment" in {
    GraphFragmentOlap.spark = spark
    GraphFragmentOlap.execute(graphOlapJobArgs)
    val df1  = reader.read("kg_fragment_olap.host_resolver")
    val df2  = reader.read("kg_fragment_olap.host_fragment")
    df1.show()
    df2.show()
  }
  "Graph olap write" should "create replica" in {
    spark.conf.set("spark.sds.olap.writebackBasePath",getClass.getResource("/entityinventory/grapholap/olap_configs/").getPath)
    GraphOlap.execute(graphOlapJobArgs)
    assertResult(17)(reader.read("kg_olap.sds_ei__host__publish").filter("status='active'").count())
  }
  "Graph olap write" should "create replica from global config" in {
    val globalconfig = configRead("/entityinventory/grapholap/olap_configs/sds_ei__olap__global.json")
    val publisherTables : Iterable[String] = Iterable("kg_publish.sds_ei__vulnerability__publish")
    val olapConfigs : mutable.Map[String,Config]=mutable.Map("sds_ei__olap__global"-> globalconfig)
    GraphOlap.createPublisherReplica(publisherTables,olapConfigs,graphOlapJobArgs,writer)
    assertResult(49)(reader.read("kg_olap.sds_ei__vulnerability__publish").count())

  }




}
