package ai.prevalent.entityinventory.relation.relationextractor

import ai.prevalent.entityinventory.loader.configs.specs.{Config => LoaderConfig}
import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.relationship.extractor.Extractor
import ai.prevalent.entityinventory.relationship.extractor.Extractor.{readInputSources, spark}
import ai.prevalent.entityinventory.relationship.extractor.configs.specs.{Config, DisambiguationResolvers, InputSource, VariablesBasedRelationBuilderStrategySpec}
import ai.prevalent.entityinventory.utils.{EIUtil, SchemaEvolutionUtil, SparkUtil}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
import ai.prevalent.sdspecore.utils.ConfigUtils
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.hadoop.util.JsonSerialization.writer
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import org.apache.spark.sql.types._
import org.json4s._
import org.json4s.jackson.JsonMethods._
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.apache.spark.sql.functions._

import java.io.File
import scala.io.Source
import scala.reflect.io.Directory

class RelationExtractorUtilsSpec extends AnyFlatSpec with BeforeAndAfter with DataFrameSuiteBase with IcebergSparkTestWrapper with LoggerBase {
  var writer: SDSTableWriter = _
  var reader: SDSTableReader = _
  override def warehousePath: String = {
    new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse")).createDirectory()
    getClass().getResource("/iceberg_warehouse").getPath
  }
  import spark.implicits._
  before {
    writer = SDSTableWriterFactory.get(SDSIcebergConnect.name, spark)
    reader = SDSTableReaderFactory.get(SDSIcebergConnect.name, spark)
    val interDF = Seq(
      ("a1af5436849f8b36e97e4ee6dbbb8750ebba56aa239ad726f96bf8e101f924da", "Cloud Account", "************", "PRENT-TELS", "2024-07-17 23:59:59.999"),
      ("36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53", "Cloud Account", "************", "PENTAI-SOIONS", "2024-07-17 23:59:59.999"),
      ("af9a518133cab617aca7629de41cbb3021bf06d57f7ef659c834334fbef10cd3", "Cloud Account", "************", "OPT", "2024-07-17 23:59:59.999"),
      ("227c73d350e0497d23ce43c5cbd47d40c5717c9943d14db1a4d981289ab8c340", "Cloud Account", "************", "PRET-MTER", "2024-07-17 23:59:59.999"),
      ("12081af9cb2c3e409fd8bb3a0303e2869a73f8445b9a5c94229228ca0fd7028f", "Cloud Account", "************", "PRAI-OTIONS", "2024-07-17 23:59:59.999")
    ).toDF("p_id", "class", "primary_key", "display_label", "updated_at_ts")
      .withColumn("updated_at_ts", to_timestamp($"updated_at_ts"))

    val intraResolverDF = Seq(
      ("1607050e7db8204c6fbee31b5b9678fa6fc4092624be1cd6631e774b95eb0772", "cacbdb8ccac4d81d6837513c4decc87f3f9f00ed0d9587814384344ddc8e028e", "Cloud Compute", "SOL-S70BE", "2024-07-17 23:59:59.999"),
      ("929f91243de5026ec976fd0fd0e738553d7463ca2f86b6091c51e406f17ee178", "4cad3ced8b1f7a9a297e87da519ffdc1316331dcbe152903e469f10224de2053", "Cloud Compute", "SOL-SULT", "2024-07-17 23:59:59.999"),
      ("1aa46e5bf5a02e35ce9b5f2d5d801fd77518e54eaf9921dbac9c65be14014c8d", "7402cd6400e31ff2060d4f5828d629ab607beb8a86027c965011bc0bf0cd9fba", "Cloud Compute", "SO3-PAC", "2024-07-17 23:59:59.999"),
      ("b1f2ade6158a99b7b0f8d5ddd65c41200a7f03d5baeba8852119165fff543538", "b1f2ade6158a99b7b0f8d5ddd65c41200a7f03d5baeba8852119165fff543538", "Cloud Compute", "NOTEBOOK", "2024-07-17 23:59:59.999"),
      ("529ed37431f35e9dc9f70ab235fb662556929afddb4493a2690618f592150d25", "90cae9c9a0f05c4910742da0ca3b6015740c36cbdc3b579a2540081c0b8bd260", "Cloud Compute", "PAI23", "2024-07-17 23:59:59.999")
    ).toDF("p_id", "disambiguated_p_id", "class", "disambiguated_display_label", "updated_at_ts")
      .withColumn("updated_at_ts", to_timestamp($"updated_at_ts"))

    val interResolverDF = Seq(
      ("cacbdb8ccac4d81d6837513c4decc87f3f9f00ed0d9587814384344ddc8e028e", "cacbdb8ccac4d81d6837513c4decc87f3f9f00ed0d9587814384344ddc8e028e", "Cloud Compute", "SOL-S70BE", "2024-07-17 23:59:59.999"),
      ("4cad3ced8b1f7a9a297e87da519ffdc1316331dcbe152903e469f10224de2053", "4cad3ced8b1f7a9a297e87da519ffdc1316331dcbe152903e469f10224de2053", "Cloud Compute", "SOL-SULT", "2024-07-17 23:59:59.999"),
      ("7402cd6400e31ff2060d4f5828d629ab607beb8a86027c965011bc0bf0cd9fba", "7402cd6400e31ff2060d4f5828d629ab607beb8a86027c965011bc0bf0cd9fba", "Cloud Compute", "SO3-PAC", "2024-07-17 23:59:59.999"),
      ("b1f2ade6158a99b7b0f8d5ddd65c41200a7f03d5baeba8852119165fff543538", "b1f2ade6158a99b7b0f8d5ddd65c41200a7f03d5baeba8852119165fff543538", "Cloud Compute", "NOTEBOOK", "2024-07-17 23:59:59.999"),
      ("90cae9c9a0f05c4910742da0ca3b6015740c36cbdc3b579a2540081c0b8bd260", "90cae9c9a0f05c4910742da0ca3b6015740c36cbdc3b579a2540081c0b8bd260", "Cloud Compute", "PAI23", "2024-07-17 23:59:59.999")
    ).toDF("p_id", "disambiguated_p_id", "class", "disambiguated_display_label", "updated_at_ts")
      .withColumn("updated_at_ts", to_timestamp($"updated_at_ts"))
      .withColumn(UPDATED_AT, expr(s"UNIX_MILLIS($UPDATED_AT_TS)"))


    val srdmDF = Seq(
      ("************", "i-0f7d561f1588ed257", "2024-07-07 23:59:59.999", "2024-07-07 23:59:59.999"),
      ("************", "i-0197e5f701d506f9b", "2024-07-07 23:59:59.999", "2024-07-07 23:59:59.999"),
      ("************", "i-0cccb4ba57cae3e55", "2024-07-07 23:59:59.999", "2024-07-07 23:59:59.999"),
      ("************", "i-0d926c69a4e0585e7", "2024-07-07 23:59:59.999", "2024-07-07 23:59:59.999"),
      ("************", "i-002b8ecceced3837b", "2024-07-07 23:59:59.999", "2024-07-07 23:59:59.999")
    ).toDF("ownerid", "lower_instance_id", "parsed_interval_timestamp_ts", "event_timestamp_ts")
      .withColumn("parsed_interval_timestamp_ts", to_timestamp($"parsed_interval_timestamp_ts"))
      .withColumn("event_timestamp_ts", to_timestamp($"event_timestamp_ts"))
      .withColumn("event_timestamp_epoch", unix_timestamp($"event_timestamp_ts"))
    spark.sql(s"""CREATE SCHEMA IF NOT EXISTS iceberg_catalog.ei""")
    writer.overwritePartition(srdmDF, "sdm.aws__ec2_describe_instances", Array(col("parsed_interval_timestamp_ts"),col("event_timestamp_ts")))
    writer.overwritePartition(interResolverDF, "rel_test.sds_ei_inter_source_resolver", Array(col("updated_at_ts")))
    writer.overwritePartition(intraResolverDF, "rel_test.sds_ei_intra_source_resolver", Array(col("updated_at_ts")))
    writer.overwritePartition(interDF, "rel_test.sds_ei__cloud_account", Array(col("updated_at_ts")))

  }

  val relArgs = Array("--parsed-interval-start", "0",
    "--parsed-interval-end", "*************",
    "--current-updated-date", "*************",
    "--config-path", parentPath + "/config_upd.json",
    "--output-path", "eias.sds_ei__rel__intunes__person_owns_host",
    "--prev-mini-sdm", "eias.ei.sds_ei__rel_mini_sdm__intunes__person_owns_host",
    "--previous-updated-date", "-1"
  )
  val parentPath = "file:" + getClass.getResource("/entityinventory/relation/entitybasedrelation").getPath
  val confPath = parentPath + "/config.json"
  val configPath = "file:" + getClass.getResource("/entityinventory/relation/entitybasedrelation/config.json").getPath
  val args = Extractor.getParseCmdArgs(relArgs)
  val eiConfigFormat = Extractor.configFormats
  val eiLoaderConfigFormat=Loader.configFormats


  "config" should "follow the correct format" in {
    val path = "file:" + getClass.getResource("/entityinventory/relation/entitybasedrelation/config.json").getPath
    val expectedDF = spark.read.option("multiline", "true").json(path)
    val expectedColumns = Array(
      "name", "origin", "inverseRelationshipName", "intraSourcePath", "interSourcePath", "inputSourceInfo")
    assert(expectedColumns.forall(expectedDF.columns.contains) &&
      (expectedDF.columns.contains("variablesBasedRelationBuilderStrategySpec") || expectedDF.columns.contains("entityBasedRelationBuilderStrategySpec")))
  }

  "resolvePID" should "resolve pid" in {
    val resolverSchema = StructType(Array(StructField("disambiguated_p_id", StringType, nullable = true), StructField("p_id", StringType, nullable = true), StructField("disambiguated_display_label", StringType, nullable = true), StructField("class", StringType, nullable = true), StructField("updated_at", LongType, nullable = true), StructField("updated_at_ts", TimestampType, nullable = true)))

    val emptyIntraSourceDF: DataFrame = spark.createDataFrame(spark.emptyDataFrame.rdd, resolverSchema)
    val emptyInterSourceDF: DataFrame = spark.createDataFrame(spark.emptyDataFrame.rdd, resolverSchema)
    val inputPath = "file:" + getClass.getResource("/entityinventory/relation/entitybasedrelation/entity.csv").getPath
    val inputDF = spark.read.option("header", "true").option("inferSchema", "true").csv(inputPath)
    val resolvers = DisambiguationResolvers(emptyIntraSourceDF, emptyInterSourceDF)
    val primaryKeyPrefix = "source"
  }

  "exec" should "return a valid RelationBuildInfo" in {
    val configPath = getClass.getResource("/entityinventory/relation/entitybasedrelation/config.json").getPath
    val jsonContent = Source.fromFile(configPath).mkString
    implicit val formats = DefaultFormats
    val configJson = parse(jsonContent).extract[Config]
    val resolverSchema = StructType(Array(StructField("disambiguated_p_id", StringType, nullable = true), StructField("p_id", StringType, nullable = true), StructField("disambiguated_display_label", StringType, nullable = true), StructField("class", StringType, nullable = true), StructField("updated_at", LongType, nullable = true), StructField("updated_at_ts", TimestampType, nullable = true)))
    val emptyIntraSourceDF: DataFrame = spark.createDataFrame(spark.emptyDataFrame.rdd, resolverSchema)
    val emptyInterSourceDF: DataFrame = spark.createDataFrame(spark.emptyDataFrame.rdd, resolverSchema)
    val resolvers = DisambiguationResolvers(emptyIntraSourceDF, emptyInterSourceDF)

  }




  "RelationshipBlockBuilder" should "return df with block_id" in {
    val rel_config=configRead("/entityinventory/relation/sds_ei__rel__aws_ec2_instance__cloud_compute_resource_belongs_to_cloud_account__job_config.json")
    val sourceConfig=loaderConfigRead("/entityinventory/relation/sds_ei__cloud_compute__aws_ec2_instance__instanceid__job_config.json")
    print(rel_config)
    println(sourceConfig)
    val jobArgs: Array[String] =Array("--spark-service"
    , "spark"
    , "--parsed-interval-start"
    , "*************"
    , "--parsed-interval-end"
    , "*************"
    , "--current-updated-date"
    , "*************"
    , "--previous-updated-date"
    , "-1"
    , "--config-path"
    , ""
    )
    val args = Extractor.getParseCmdArgs(jobArgs)
//    val inputsource=readInputSources(rel_config.inputSourceInfo,reader:SDSTableReader,args)
    val enricheddf = Seq(
      ("************", "i-0f7d561f1588ed257", "2024-07-07 23:59:59.999", "2024-07-07 23:59:59.999", *************L, "36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53", "AWS", *************L, true, "2024-07-18 05:29:59.999","Cloud Account"),
      ("************", "i-0197e5f701d506f9b", "2024-07-07 23:59:59.999", "2024-07-07 23:59:59.999", *************L, "36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53", "AWS", *************L, true, "2024-07-18 05:29:59.999","Cloud Account"),
      ("************", "i-0cccb4ba57cae3e55", "2024-07-07 23:59:59.999", "2024-07-07 23:59:59.999", *************L, "36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53", "AWS", *************L, true, "2024-07-18 05:29:59.999","Cloud Account"),
      ("************", "i-0d926c69a4e0585e7", "2024-07-07 23:59:59.999", "2024-07-07 23:59:59.999", *************L, "36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53", "AWS", *************L, true, "2024-07-18 05:29:59.999","Cloud Account"),
      ("************", "i-002b8ecceced3837b", "2024-07-07 23:59:59.999", "2024-07-07 23:59:59.999", *************L, "36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53", "AWS", *************L, true, "2024-07-18 05:29:59.999","Cloud Account")
    ).toDF("ownerid", "InstanceId", "parsed_interval_timestamp_ts",
      "event_timestamp_ts", "event_timestamp_epoch", "target_p_id", "origin__temp", "updated_at", "is_incremental_delta", "updated_at_ts","target_entity_class")
      .withColumn("event_timestamp_ts", expr("to_timestamp(event_timestamp_epoch / 1000)"))
      .withColumn("uuid", expr("'a2bb0b3a-bb6d-41d6-97b1'"))
    val inputSource=InputSource(enricheddf,"event_timestamp_ts",Array.empty,List(sourceConfig),List.empty)
    val intraResolverDF= reader.read("rel_test.sds_ei_intra_source_resolver")
    val interResolverDF=reader.read("rel_test.sds_ei_inter_source_resolver")
    val resolvers=DisambiguationResolvers(intraResolverDF,interResolverDF)
    val prevMinSDM=spark.emptyDataFrame.withColumn(UPDATED_AT_TS,lit(null))
    val blockVariables=Array("source_p_id","target_p_id")
    val builder = new VariablesBasedRelationBuilderStrategySpec(blockVariables)
    val out= builder.build(
      inputSources = Seq(inputSource),
      prevMiniSDM = prevMinSDM,
      resolvers = resolvers,
      optionalAttributes = rel_config.optionalAttributes,
      currentUpdatedAt = 1731260799999L,
      config = rel_config,
      prevConfigStr = None
    )
    val expectedData = spark.createDataFrame(Seq(
      ("4cad3ced8b1f7a9a297e87da519ffdc1316331dcbe152903e469f10224de2053", *************L, "a2bb0b3a-bb6d-41d6-97b1", "Cloud Account",  null, "Cloud Compute", "AWS", "36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53", null,  "4cad3ced8b1f7a9a297e87da519ffdc1316331dcbe152903e469f10224de2053#36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53"),
      ("7402cd6400e31ff2060d4f5828d629ab607beb8a86027c965011bc0bf0cd9fba", *************L, "a2bb0b3a-bb6d-41d6-97b1", "Cloud Account",  null, "Cloud Compute", "AWS", "36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53", null,  "7402cd6400e31ff2060d4f5828d629ab607beb8a86027c965011bc0bf0cd9fba#36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53"),
      ("90cae9c9a0f05c4910742da0ca3b6015740c36cbdc3b579a2540081c0b8bd260", *************L, "a2bb0b3a-bb6d-41d6-97b1", "Cloud Account",  null, "Cloud Compute", "AWS", "36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53", null,  "90cae9c9a0f05c4910742da0ca3b6015740c36cbdc3b579a2540081c0b8bd260#36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53"),
      ("b1f2ade6158a99b7b0f8d5ddd65c41200a7f03d5baeba8852119165fff543538", *************L, "a2bb0b3a-bb6d-41d6-97b1", "Cloud Account",  null, "Cloud Compute", "AWS", "36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53", null,  "b1f2ade6158a99b7b0f8d5ddd65c41200a7f03d5baeba8852119165fff543538#36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53"),
      ("cacbdb8ccac4d81d6837513c4decc87f3f9f00ed0d9587814384344ddc8e028e", *************L, "a2bb0b3a-bb6d-41d6-97b1", "Cloud Account",  null, "Cloud Compute", "AWS", "36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53", null,  "cacbdb8ccac4d81d6837513c4decc87f3f9f00ed0d9587814384344ddc8e028e#36a816e92780fd7f83acac702ce6426ef0ac5cc06035f2ca2de54bce864e4a53")
    )).toDF("source_p_id", "event_timestamp_epoch", "uuid", "target_entity_class", "updated_at_ts", "source_entity_class", "origin", "target_p_id", "updated_at", "block_id")
    assertDataFrameEquals(spark.createDataFrame(out.relationDF.select(expectedData.columns.map(col(_)): _*).rdd,
      expectedData.schema).orderBy("block_id"),expectedData.orderBy("block_id"))
  }

  def configRead(location: String): Config = {
    val confPath = getClass.getResource(location).getPath
    ConfigUtils.getConfig(spark, s"file:$confPath", manifest[Config], eiConfigFormat)
  }

  def loaderConfigRead(location: String): LoaderConfig = {
    val confPath = getClass.getResource(location).getPath
    ConfigUtils.getConfig(spark, s"file:$confPath", manifest[LoaderConfig], eiLoaderConfigFormat)
  }

}
