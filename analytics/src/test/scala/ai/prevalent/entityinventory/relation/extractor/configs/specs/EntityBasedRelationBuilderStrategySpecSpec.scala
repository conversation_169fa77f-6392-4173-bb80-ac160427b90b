package ai.prevalent.entityinventory.relation.extractor.configs.specs



//package ai.prevalent.entityinventory.relation.extractor.configs.specs
//
//import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{UPDATED_AT, UPDATED_AT_TS}
//import ai.prevalent.entityinventory.relationship.extractor.Extractor
//import ai.prevalent.entityinventory.relationship.extractor.configs.specs.{Attribute, Config, EntityBasedRelationBuilderStrategySpec}
//import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
//import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
//import ai.prevalent.sdspecore.utils.ConfigUtils
//import com.holdenkarau.spark.testing.DataFrameSuiteBase
//import org.apache.hadoop.fs.{FileSystem, Path}
//import org.apache.spark.sql.functions.{col, expr}
//import org.json4s.DefaultFormats
//import org.json4s.jackson.JsonMethods.parse
//import org.scalatest.BeforeAndAfter
//import org.scalatest.flatspec.AnyFlatSpec
//
//import java.io.{File, PrintWriter}
//import java.net.URI
//import java.util.Random
//import scala.io.Source
//import scala.reflect.io.Directory
//
//class EntityBasedRelationBuilderStrategySpecSpec extends AnyFlatSpec with DataFrameSuiteBase with BeforeAndAfter with IcebergSparkTestWrapper{
//
//  var writer:SDSTableWriter = _
//  var reader:SDSTableReader = _
//
//  override def warehousePath: String = {
//    val rand = new Random().nextInt(1000)
//    new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse__$rand")).createDirectory()
//    getClass().getResource(s"/iceberg_warehouse__$rand").getPath
//  }
//
//  before {
//    writer = SDSTableWriterFactory.getDefault(spark)
//    reader = SDSTableReaderFactory.getDefault(spark)
//    import spark.implicits._
//
//    val resolDF = Seq(
//      ("0f18077e8b1cffdc285e5a3057590a7ae5bab69538c26c9492488a2c24d088d9","0f18077e8b1cffdc285e5a3057590a7ae5bab69538c26c9492488a2c24d088d9","v1","Host",1682035199999l),
//      ("223e848b4ca38f51565e167b395299d8fce597be8c739fce28cbae013a3cb9f2","223e848b4ca38f51565e167b395299d8fce597be8c739fce28cbae013a3cb9f2","v3","Host",1682035199999l),
//      ("771e4a6161d2b153221bfa8addcd64f7ded5e641ec7bbf693a66e7c6b0c6d484","771e4a6161d2b153221bfa8addcd64f7ded5e641ec7bbf693a66e7c6b0c6d484","v2","Host",1682035199999l),
//      ("b6228b1bc3e6343f77c8b6e25d73c61a85246c1af6ff71d40a82c7517df4f804","b6228b1bc3e6343f77c8b6e25d73c61a85246c1af6ff71d40a82c7517df4f804","h6","Host",1682035199999l),
//      ("6caf0ac4873fafec75b23d9dc0ae40b4e51859bd7e207cfac4fba18c2066c62a","6caf0ac4873fafec75b23d9dc0ae40b4e51859bd7e207cfac4fba18c2066c62a","h4","Host",1682035199999l),
//      ("d3ec3343c360c4dde4498ceba07e9b7af791b803e4d67da4c588b57a4a794a68","d3ec3343c360c4dde4498ceba07e9b7af791b803e4d67da4c588b57a4a794a68","h5","Host",1682035199999l),
//      ("3996802b4381541c9d6d988d8017eeb874562b01a4ccb123970fa1da76a6a40e","3996802b4381541c9d6d988d8017eeb874562b01a4ccb123970fa1da76a6a40e","h1","Host",1682035199999l),
//      ("2e1ac6ba0b552f95d32c11873cd2e551bdd358803f8b27a51ec24017cbe1f2c4","2e1ac6ba0b552f95d32c11873cd2e551bdd358803f8b27a51ec24017cbe1f2c4","h2","Host",1682035199999l),
//      ("e022a02554831bfd6a713a1de7eac17da8a37a1e562c8182972021d4c27f37b4","e022a02554831bfd6a713a1de7eac17da8a37a1e562c8182972021d4c27f37b4","h3","Host",1682035199999l),
//    ).toDF("disambiguated_p_id","p_id","disambiguated_display_label","class","updated_at")
//      .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
//
//    writer.overwritePartition(resolDF,"eias.sds_ei_intra_source_resolver")
//    writer.overwritePartition(resolDF,"eias.sds_ei_inter_source_resolver")
//    writer.overwritePartition(resolDF, "ei_util.sds_ei_inter_source_resolver")
//  }
//  "build" should "return blocked relation table" in {
//    val sourcepath = "file:" + getClass.getResource("/entityinventory/relation/entitybasedrelation/entity.csv").getPath
//    val sourceDF = spark.read.option("header", "true").option("inferSchema", "true").csv(sourcepath)
//    val entityBasedRelationBuilderStrategySpec = new EntityBasedRelationBuilderStrategySpec("targetEntity")
//    val outputDF = entityBasedRelationBuilderStrategySpec.buildBlock(sourceDF)
//    val expPath = "file:" + getClass.getResource("/entityinventory/relation/entitybasedrelation/blockbuild.csv").getPath
//    val expectedDF = spark.read.option("header", "true").option("inferSchema", "true").csv(expPath)
//    assertDataFrameEquals(outputDF, spark.createDataFrame(expectedDF.select(outputDF.columns.map(col(_)): _*).rdd,
//      outputDF.schema))
//  }
//
//  "RelationExtractor" should "build entity based relation" in{
//    val parentPath = "file:" + getClass.getResource("/entityinventory/relation/entitybasedrelation").getPath
//    val confPath = parentPath+"/config.json"
//    val uri= new URI(confPath)
//    val fs = FileSystem.get(uri, spark.sparkContext.hadoopConfiguration)
//    val config = Source.fromInputStream(fs.open(new Path(uri))).mkString.format(parentPath,parentPath)
//    new PrintWriter(parentPath.replaceAll("file:","")+"/config_upd.json") { write(config); close()}
//
//    val relArgs = Array("--parsed-interval-start", "0",
//      "--parsed-interval-end", "1682046000000",
//      "--current-updated-date","1682035199999",
//      "--config-path",parentPath+"/config_upd.json",
//      "--output-path","eias.sds_ei__rel__ms_intunes__person_owns_host_ent",
//      "--prev-mini-sdm","ei.sds_ei__rel_mini_sdm__ms_intunes__person_owns_host_ent",
//      "--previous-updated-date","-1"
//    )
//    val args = Extractor.getParseCmdArgs(relArgs)
//    val sourcepath = "file:" + getClass.getResource("/entityinventory/relation/entitybasedrelation/entity.csv").getPath
//
//    val sourceDF = spark.read.option("header", "true").option("inferSchema", "true").csv(sourcepath)
//      .withColumn("parsed_interval_timestamp_ts",expr("to_timestamp(1682045000)"))
//      .withColumn("event_timestamp_ts",expr("to_timestamp(event_timestamp_epoch/1000)"))
//    writer.overwritePartition(sourceDF,"sdm.microsoft__intune")
//    val con = ConfigUtils.getConfig(spark,parentPath+"/config_upd.json",Extractor.getConfigManifest).asInstanceOf[Config]
//    Extractor.spark =  spark
//    Extractor.execute(args,con)
//    reader.read("eias.sds_ei__rel__ms_intunes__person_owns_host_ent").show(false)
//  }
//
//  "createMiniSDM" should "return mini sdm " in {
//    val configPath = getClass.getResource("/entityinventory/relation/variablebasedrelation/config.json").getPath
//    val jsonContent = Source.fromFile(configPath).mkString
//    implicit val formats = DefaultFormats
//    val configJson = parse(jsonContent).extract[Config]
//    val optionalAttributes: Array[Attribute] = configJson.optionalAttributes
//    val sourcepath = "file:" + getClass.getResource("/entityinventory/relation/entitybasedrelation/entity.csv").getPath
//    val sourceDF = spark.read.option("header", "true").option("inferSchema", "true").csv(sourcepath)
//    val entityBasedRelationBuilderStrategySpec = new EntityBasedRelationBuilderStrategySpec("targetEntity")
//    val outputDF = entityBasedRelationBuilderStrategySpec.createMiniSDM(sourceDF,optionalAttributes)
//    val expPath = "file:" + getClass.getResource("/entityinventory/relation/entitybasedrelation/minisdm.csv").getPath
//    val expectedDF = spark.read.option("header", "true").option("inferSchema", "true").csv(expPath)
//    assertDataFrameEquals(outputDF, spark.createDataFrame(expectedDF.select(outputDF.columns.map(col(_)): _*).rdd,
//      outputDF.schema))
//  }
//
//}
