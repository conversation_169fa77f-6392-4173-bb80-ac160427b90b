*#*#
*.#*
*.iml
*.ipr
*.iws
*.pyc
*.pyo
*.swp
*~
.java-version
.DS_Store
.ammonite
.bloop
.bsp/
.cache
.classpath
.ensime
.ensime_cache/
.ensime_lucene
.generated-mima*
.idea/
.idea_modules/
.metals
.project
.pydevproject
.scala_dependencies
.settings
.vscode
/lib/
build/*.jar
build/apache-maven*
build/scala*
cache
checkpoint
conf/*.cmd
conf/*.conf
conf/*.properties
conf/*.sh
conf/*.xml
conf/java-opts
conf/slaves
dependency-reduced-pom.xml
derby.log
dev/create-release/*final
dev/create-release/*txt
dev/pr-deps/
dist/
docs/_site/
docs/api
docs/.local_ruby_bundle
sql/docs
sql/site
lib_managed/
lint-r-report.log
lint-js-report.log
log/
logs/
metals.sbt
scalastyle-on-compile.generated.xml
scalastyle-output.xml
scalastyle.txt
spark-*-bin-*.tgz
spark-tests.log
src_managed/
streaming-tests.log
target/
unit-tests.log
work/
docs/.jekyll-metadata
docs/.jekyll-cache

# For Hive
TempStatsStore/
metastore/
metastore_db/
sql/hive-thriftserver/test_warehouses
warehouse/
spark-warehouse/

# For R session data
.RData
.RHistory
.Rhistory
*.Rproj
*.Rproj.*

.Rproj.user

# For SBT
.jvmopts

# For Node.js
node_modules