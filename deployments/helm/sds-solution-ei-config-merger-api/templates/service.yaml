apiVersion: v1
kind: Service
metadata:
  labels:
    app: ei-config-merger-api
    env: {{ .Release.Namespace }}
    {{- include "eiConfigMergerApi.labels" . | nindent 4 }}
  name: ei-config-merger-api
spec:
  type: {{ .Values.service.type }}
  ports:
  - port: {{ .Values.service.port }}
    protocol: TCP
    targetPort: {{ .Values.service.port }}
    name: http
  selector:
    app: ei-config-merger-api
    env: {{ .Release.Namespace }}
    {{- include "eiConfigMergerApi.selectorLabels" . | nindent 4 }}
