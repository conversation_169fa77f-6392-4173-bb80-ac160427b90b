apiVersion: apps/v1
kind: Deployment
metadata:
  name: eiconfig-merger-{{ .Release.Name }}
  labels:
    app: ei-config-merger-api
    env: {{ .Release.Namespace }}
    {{- include "eiConfigMergerApi.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: ei-config-merger-api
      env: {{ .Release.Namespace }}
      {{- include "eiConfigMergerApi.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        app: ei-config-merger-api
        env: {{ .Release.Namespace }}
        application_name: eiConfigMergerApi
        sds_app_type: application
        {{- include "eiConfigMergerApi.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecret }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "eiConfigMergerApi.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      automountServiceAccountToken: false
      containers:
        - name: ei-config-merger-api
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ include "eiConfigMergerApi.getImageTag" . }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          resources:
            {{- toYaml .Values.resources | nindent 12  }}
          env:
          {{- include "custom_ei_config_merger_api_env" . | indent 8 }}
          volumeMounts:
            {{- include "extra_volumes_mounts" . | indent 10 }}
      volumes:
        {{- include "extra_volumes" . | indent 6 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}