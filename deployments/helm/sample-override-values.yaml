# =======================================================================
# =======================================================================
# This section contains values for the Deployer Helm chart itself.
# This values control the Release bundleversion and manages its 
# modules based on the Release bundle version.
#
# Key configurations in this section include:
# - Release bundle version of the mono repository
#
# =======================================================================

# bundle-version: 1.2.2-20250117-085607111728107


# =======================================================================
# Individual Module Configuration Section
# =======================================================================
# This section contains override values for each module managed by the 
# Deployer. Each modules values should be placed directly under
# its chart name. All the possible module names are listed in this values file below.
# We jus need to uncomment the same and override the configurations as you need.
#
# =======================================================================

# sds-solution-ei-expression-validator-api:
#   bundle-version: 1.2.2-20250117-101337580983808
  # enabled:
  # image:
  #   repository: prevalentai/sds-solution-ei-validator-api
  #   tag: ""
  # extraEnv:
  #   - name: REDIS_DATABASE
  #     value: "4"

# sds-solution-ei-config-merger-api:
#   bundle-version:
#   enabled:
#   image:
#     repository: prevalentai/sds-solution-ei-config-merger-api
#     tag: ""

# ========================================================================
# Individual jobs configuration over rides section
# =======================================================================

# jobOverrides:
#   sds_ei_jar_copy_job:
#     enabled:
#     bundle-version: 1.2.2-20250117-092610474027479
#     init_job_command:
#       aws: []
#       azure: []
#     job_command:
#       aws: []
#       azure: []
#     post_job_command:
#       aws: []
#       azure: []
#     env:
#       common: []
#       aws: []
#       azure: []
#     extraEnv: []
#     resources: []

#   sds_ei_dag_deploy_job:
#     enabled:
#     bundle-version:
#     image:
#       repository: prevalentai/sds-solution-ei-orchestration
#       tag:
#       pullPolicy: IfNotPresent
#     command:
#       aws: []
#       azure: []
#     env:
#       common: []
#       aws: []
#       azure: []
#     extraEnv: []
#     resources: []

#   sds_ei_profiling_copy_job:
#     enabled:
#     bundle-version:
#     init_job_command:
#       aws: []
#       azure: []
#     job_command:
#       aws: []
#       azure: []
#     post_job_command:
#       aws: []
#       azure: []
#     env:
#       common: []
#       aws: []
#       azure: []
#     extraEnv: []
#     resources: []
