{{- if index .Values "sds-solution-ei" "enabled" }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  annotations:
    notifications.argoproj.io/subscribe.on-deleted.teams: channelName
    notifications.argoproj.io/subscribe.on-health-degraded.teams: channelName
    notifications.argoproj.io/subscribe.on-sync-failed.teams: channelName
    notifications.argoproj.io/subscribe.on-sync-status-unknown.teams: channelName
    notifications.argoproj.io/subscribe.on-deleted.email: {{ $.Values.global.ARGOCD_NOTIFICATION_EMAIL }}
    notifications.argoproj.io/subscribe.on-health-degraded.email: {{ $.Values.global.ARGOCD_NOTIFICATION_EMAIL }}
    notifications.argoproj.io/subscribe.on-sync-failed.email: {{ $.Values.global.ARGOCD_NOTIFICATION_EMAIL }}
    notifications.argoproj.io/subscribe.on-sync-status-unknown.email: {{ $.Values.global.ARGOCD_NOTIFICATION_EMAIL }}
  name: {{ default (printf "%s-%s-%s" (index .Values "sds-solution-ei" "name") $.Values.global.PROJECT_NAME $.Values.global.ENVIRONMENT) (index .Values "sds-solution-ei" "nameOverride")}}
  namespace: argocd
spec:
  project: {{ default (printf "sds-%s-%s" $.Values.global.PROJECT_NAME $.Values.global.ENVIRONMENT) $.Values.projectOverride }}
  destination:
    server: {{ $.Values.global.CLUSTER_ENDPOINT }}
    namespace: {{ $.Values.global.DEPLOY_NAMESPACE }}
  sources:
    - repoURL: {{ $.Values.global.HELM_REPO }}
      path: sds-solution-ei/
      targetRevision: {{ include "sdsSolutionEi.targetRevision" . }}
      helm:
        valueFiles:
          - $values/{{ $.Values.global.COMMON_FILES_LOCATION }}
          - {{ default (printf "$values1/%s/sds-solution-ei/values.yaml" $.Values.global.OVERRIDE_FILES_LOCATION ) (.Values.overRideFilePath) }}
      chart: sds-solution-ei
    - repoURL: {{ $.Values.global.GIT_REPOSITORY_URL }}
      targetRevision: {{ default $.Values.global.GIT_BRANCHNAME ( .Values.gitBranch) }}
      ref: values
    - repoURL: {{ $.Values.global.GIT_APP_REPOSITORY_URL }}
      targetRevision: {{ default $.Values.global.GIT_APP_BRANCHNAME (.Values.gitBranch) }}
      ref: values1
  syncPolicy:
    automated:
      selfHeal: true 
{{- end }}
