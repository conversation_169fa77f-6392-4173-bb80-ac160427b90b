# Default values for sds-solution-ei-jobs
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

backoffLimit: 1
activeDeadlineSeconds: 7200
ttlSecondsAfterFinished: 86400

secretName: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"

restartPolicy: Never
labelJob: sds-solution-ei-job
jobAnnotations: 
  argocd.argoproj.io/hook: Sync
  argocd.argoproj.io/hook-delete-policy: HookSucceeded
  argocd.argoproj.io/sync-options: Force=true,Replace=true

jobLabels: {}

podAnnotations: {}

podLabels: 
  cronterminate: "false"

imagePullSecrets:
  - name: docker-secret
image:
  pullPolicy: IfNotPresent

jFrogArtifactURL: &defaultJfrogURL
  name: ARTIFACT_URL
  value: https://prevalentai.jfrog.io/ 

##The current image tag is hardcoded, sourced from the mono repository sds-pe-deployment-utils. We need to expand the Jenkins pipeline to fetch the image details from the platform apps mono repository. 
initImage:
  repository: prevalentai/devops-utils
  tag: kubectl1.27.0-awscliv2-azcpv10-azcli-curl-jq-bookworm-12.5-********-slim

image:
    repository: prevalentai/sds-platform-apps-deployment-utils
    tag: 3-6-1-********-**********-APPS

resources:
  limits:
    cpu: 100m
    memory: 3000Mi
  requests:
    cpu: 50m
    memory: 1024Mi

nodeSelector: {}

tolerations: []

affinity: {}

volumes: {}

volumeMounts: {}

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

# EI bundle version, This version will be updated by the Jenkins during the chart creation. During the deploymenttime, if you want to override individual components during the deployment, then specisy the same in the below componentOverride section.
bundleVersion:
  solutionEi: ""
  
# ## This section be it like this, if any overrise then update it in the override yaml file.
# bundle-version: "<BUNDLE_VERSION>"

jobOverrides:
  sds_ei_jar_copy_job:
    enabled: true
    init_job_command:
      aws: ["bash", "-c", "artifact_checksum_s3=$(/tmp/aws/bin/aws s3api get-object-tagging --bucket {{ .Values.global.S3_APPS_BUCKET_NAME }} --key $ARTIFACT_PATH/$ARTIFACT_NAME --query 'TagSet[?Key==`artifactchecksum`].Value' --output text 2>/dev/null); artifactchecksum_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_jar_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-ei-analytics\")) | .checksum'); echo $artifactchecksum_jf; if [ \"$artifact_checksum_s3\" == \"$artifactchecksum_jf\" ]; then echo \"Artifact exists in datalake, skipping the artifact copy...\"; echo \"true\" > /tmp/result/result.txt; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; echo \"false\" > /tmp/result/result.txt; fi"]
      azure: ["bash","-c","az login --output none --only-show-errors --federated-token \"$(cat $AZURE_FEDERATED_TOKEN_FILE)\" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID;artifact_checksum_blob=$(az storage blob metadata show --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --auth-mode login --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name $ARTIFACT_PATH/$ARTIFACT_NAME 2>/dev/null | jq -r '.artifactchecksum' ); artifactchecksum_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_jar_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-ei-analytics\")) | .checksum');  if [ \"$artifact_checksum_blob\" == \"$artifactchecksum_jf\" ]; then echo \"Artifact exists in datalake, skipping the artifact copy...\"; echo \"true\" > /tmp/result/result.txt; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; echo \"false\" > /tmp/result/result.txt; fi"]
    job_command:
      aws: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then echo \"Artifact exists in datalake, Skipping artifact copy...\"; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; python /opt/sds-pe-deployment-utils/artifact-object-store-deploy.py --bundle_name sds-solution-ei --sol_name sds-solution-ei --comp_name sds-ei-analytics --obj_store_path s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/$ARTIFACT_PATH/ --artifact_name $ARTIFACT_NAME && mkdir /tmp/result/ready; fi"]
      azure: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then echo \"Artifact already exists in datalake, Skipping the artifact copy...\"; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; python /opt/sds-pe-deployment-utils/artifact-object-store-deploy.py --bundle_name sds-solution-ei --sol_name sds-solution-ei --comp_name sds-ei-analytics --obj_store_path az://{{ .Values.global.BLOB_APPS_CONTAINER_NAME }}/$ARTIFACT_PATH/ --artifact_name $ARTIFACT_NAME && mkdir /tmp/result/ready; fi"]
    post_job_command:
      aws: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then sleep 10; echo \"Jar already exists, skipped tagging the artifact...\"; else echo \"Tagging the artifact in datalake....\"; echo \"Getting Artifact checksum from Jfrog...\"; artifactCheckSum=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_jar_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-ei-analytics\")) | .checksum'); until [ -e \"/tmp/result/ready\" ]; do echo \"Waiting for S3 upload to be completed...\"; sleep 3; done; tags='{\"TagSet\":[{\"Key\":\"artifactchecksum\",\"Value\":\"'\"$artifactCheckSum\"'\"}]}'; /tmp/aws/bin/aws s3api put-object-tagging --bucket {{ .Values.global.S3_APPS_BUCKET_NAME }} --key $ARTIFACT_PATH/$ARTIFACT_NAME --tagging \"$tags\" > /dev/null 2>&1; fi; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
      azure: ["bash","-c","until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\";az login --output none --only-show-errors --federated-token \"$(cat $AZURE_FEDERATED_TOKEN_FILE)\" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then sleep 10; echo \"Artifact exists in datalake and artifact checksum is same, skipping artifact tagging...\"; else echo \"Artifact doesn't exist in datalake, proceeding with tagging artifact in datalake...\"; artifactCheckSum=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_jar_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-ei-analytics\")) | .checksum'); until [ -e \"/tmp/result/ready\" ]; do echo \"Waiting for Artifact upload to be completed...\"; sleep 3; done; az storage blob metadata update --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --auth-mode login --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name $ARTIFACT_PATH/$ARTIFACT_NAME --metadata artifactchecksum=$artifactCheckSum ; fi; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]

    env:
      common:
        - <<: *defaultJfrogURL
        - name: ARTIFACT_NAME
          value: sds-ei-analytics_2.13.jar
        - name: ARTIFACT_PATH
          value: "{{ .Values.global.DEPLOY_NAMESPACE}}/sds/data-analytics/lib/latest"
      aws: []
      azure: []   
    extraEnv: []

  sds_ei_validator_jar_copy_job:
    enabled: true
    init_job_command:
      aws: ["bash", "-c", "artifact_checksum_s3=$(/tmp/aws/bin/aws s3api get-object-tagging --bucket {{ .Values.global.S3_APPS_BUCKET_NAME }} --key $ARTIFACT_PATH/$ARTIFACT_NAME --query 'TagSet[?Key==`artifactchecksum`].Value' --output text 2>/dev/null); artifactchecksum_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_validator_jar_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"validator\")) | .checksum'); echo $artifactchecksum_jf; if [ \"$artifact_checksum_s3\" == \"$artifactchecksum_jf\" ]; then echo \"Artifact exists in datalake, skipping the artifact copy...\"; echo \"true\" > /tmp/result/result.txt; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; echo \"false\" > /tmp/result/result.txt; fi"]
      azure: ["bash","-c","az login --output none --only-show-errors --federated-token \"$(cat $AZURE_FEDERATED_TOKEN_FILE)\" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID;artifact_checksum_blob=$(az storage blob metadata show --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --auth-mode login --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name $ARTIFACT_PATH/$ARTIFACT_NAME 2>/dev/null | jq -r '.artifactchecksum' ); artifactchecksum_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_validator_jar_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"validator\")) | .checksum');  if [ \"$artifact_checksum_blob\" == \"$artifactchecksum_jf\" ]; then echo \"Artifact exists in datalake, skipping the artifact copy...\"; echo \"true\" > /tmp/result/result.txt; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; echo \"false\" > /tmp/result/result.txt; fi"]
    job_command:
      aws: [ "bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then echo \"Artifact exists in datalake, Skipping artifact copy...\"; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; python /opt/sds-pe-deployment-utils/artifact-object-store-deploy.py --bundle_name sds-solution-ei --sol_name sds-solution-ei --comp_name validator --obj_store_path s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/$ARTIFACT_PATH/ --artifact_name $ARTIFACT_NAME && kubectl rollout restart statefulset expression-validator-sds-ei-{{ .Values.global.PROJECT_NAME }}-{{ .Values.global.ENVIRONMENT }} -n {{ .Values.global.DEPLOY_NAMESPACE }} && mkdir /tmp/result/ready; fi"]    
      azure: [ "bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then echo \"Artifact already exists in datalake, Skipping the artifact copy...\"; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; python /opt/sds-pe-deployment-utils/artifact-object-store-deploy.py --bundle_name sds-solution-ei --sol_name sds-solution-ei --comp_name validator --obj_store_path az://{{ .Values.global.BLOB_APPS_CONTAINER_NAME }}/$ARTIFACT_PATH/ --artifact_name $ARTIFACT_NAME && kubectl rollout restart statefulset expression-validator-sds-ei-{{ .Values.global.PROJECT_NAME }}-{{ .Values.global.ENVIRONMENT }} -n {{ .Values.global.DEPLOY_NAMESPACE }} && mkdir /tmp/result/ready; fi" ]
    post_job_command:
      aws: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then sleep 10; echo \"Jar already exists, skipped tagging the artifact...\"; else echo \"Tagging the artifact in datalake....\"; echo \"Getting Artifact checksum from Jfrog...\"; artifactCheckSum=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_validator_jar_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"validator\")) | .checksum'); until [ -e \"/tmp/result/ready\" ]; do echo \"Waiting for S3 upload to be completed...\"; sleep 3; done; tags='{\"TagSet\":[{\"Key\":\"artifactchecksum\",\"Value\":\"'\"$artifactCheckSum\"'\"}]}'; /tmp/aws/bin/aws s3api put-object-tagging --bucket {{ .Values.global.S3_APPS_BUCKET_NAME }} --key $ARTIFACT_PATH/$ARTIFACT_NAME --tagging \"$tags\" > /dev/null 2>&1; fi; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
      azure: ["bash","-c","until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\";az login --output none --only-show-errors --federated-token \"$(cat $AZURE_FEDERATED_TOKEN_FILE)\" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then sleep 10; echo \"Artifact exists in datalake and artifact checksum is same, skipping artifact tagging...\"; else echo \"Artifact doesn't exist in datalake, proceeding with tagging artifact in datalake...\"; artifactCheckSum=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_validator_jar_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"validator\")) | .checksum'); until [ -e \"/tmp/result/ready\" ]; do echo \"Waiting for Artifact upload to be completed...\"; sleep 3; done; az storage blob metadata update --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --auth-mode login --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name $ARTIFACT_PATH/$ARTIFACT_NAME --metadata artifactchecksum=$artifactCheckSum ; fi; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
    env:
      common:
        - <<: *defaultJfrogURL     
        - name: ARTIFACT_URL
          value: https://prevalentai.jfrog.io/
        - name: ARTIFACT_NAME
          value: sds-ei-validator.jar
        - name: ARTIFACT_PATH
          value: "{{ .Values.global.DEPLOY_NAMESPACE}}/sds/data-analytics/lib/latest"          
      aws: [ ]
      azure: [ ]
    extraEnv: []
    resources:
      limits:
        cpu: "800m"
        memory: "18Gi"
      requests:
        cpu: "600m"
        memory: "15Gi"


  sds_ei_dag_deploy_job:
    enabled: true
    # backoffLimit: 1
    # activeDeadlineSeconds: 6000
    # ttlSecondsAfterFinished: 30
    image:
      repository: prevalentai/sds-solution-ei-orchestration
      tag:
      pullPolicy: IfNotPresent
    command:
      aws: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; /tmp/aws/bin/aws s3 cp /opt/airflow/sds/dags/ei.tgz s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/orchestration/lib/latest/dags/; /tmp/aws/bin/aws s3 cp /opt/airflow/sds/commons/ei.tgz s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/orchestration/lib/latest/commons/; /tmp/aws/bin/aws s3 cp /opt/airflow/sds/plugins/ei.tgz s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/orchestration/lib/latest/plugins/; kubectl rollout restart deployment --selector=component=scheduler -n {{ .Values.global.DEPLOY_NAMESPACE }}; kubectl rollout restart deployment --selector=component=triggerer -n {{ .Values.global.DEPLOY_NAMESPACE }}; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
      azure: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; az login --output none --only-show-errors --federated-token \"$(cat $AZURE_FEDERATED_TOKEN_FILE)\" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID;echo \"Copying DAGS...\" && az storage blob upload --output none --only-show-errors --overwrite --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name {{ .Values.global.DEPLOY_NAMESPACE}}/sds/orchestration/lib/latest/dags/ei.tgz --file \"/opt/airflow/sds/dags/ei.tgz\" --auth-mode login;echo \"Copying plugins...\" && az storage blob upload --output none --only-show-errors --overwrite --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name {{ .Values.global.DEPLOY_NAMESPACE}}/sds/orchestration/lib/latest/plugins/ei.tgz --file \"/opt/airflow/sds/plugins/ei.tgz\" --auth-mode login;echo \"Copying commons...\" && az storage blob upload --output none --only-show-errors --overwrite --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name {{ .Values.global.DEPLOY_NAMESPACE}}/sds/orchestration/lib/latest/commons/ei.tgz --file \"/opt/airflow/sds/commons/ei.tgz\" --auth-mode login; kubectl rollout restart deployment --selector=component=scheduler -n {{ .Values.global.DEPLOY_NAMESPACE }}; kubectl rollout restart deployment --selector=component=triggerer -n {{ .Values.global.DEPLOY_NAMESPACE }}; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
    resources:
      limits:
        cpu: 100m
        memory: 2500Mi
      requests:
        cpu: 50m
        memory: 1500Mi
    env:
      common: []     
      aws: []
      azure: []   
    extraEnv: []

  sds_ei_profiling_copy_job:
    enabled: true
    init_job_command:
      aws: ["bash", "-c", "artifact_checksum_apps_s3=$(/tmp/aws/bin/aws s3api get-object-tagging --bucket {{ .Values.global.S3_APPS_BUCKET_NAME }} --key $ARTIFACT_PATH/$ARTIFACT_NAME_1 --query 'TagSet[?Key==`artifactchecksum`].Value' --output text 2>/dev/null); artifact_checksum_deps_s3=$(/tmp/aws/bin/aws s3api get-object-tagging --bucket {{ .Values.global.S3_APPS_BUCKET_NAME }} --key $ARTIFACT_PATH/$ARTIFACT_NAME_2 --query 'TagSet[?Key==`artifactchecksum`].Value' --output text 2>/dev/null); artifact_checksum_apps_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_profiling_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-solution-ei-profiling-application\")) | .checksum'); artifact_checksum_deps_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_profiling_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-solution-ei-profiling-dependencies\")) | .checksum'); if [[ \"$artifact_checksum_apps_s3\" == \"$artifact_checksum_apps_jf\" && \"$artifact_checksum_deps_s3\" == \"$artifact_checksum_deps_jf\" ]]; then echo \"Artifact exists in datalake, skipping the artifact copy...\"; echo \"true\" > /tmp/result/result.txt; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; echo \"false\" > /tmp/result/result.txt; fi"]
      azure: ["bash", "-c", "az login --output none --only-show-errors --federated-token \"$(cat $AZURE_FEDERATED_TOKEN_FILE)\" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID;artifact_checksum_apps_blob=$(az storage blob metadata show --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --auth-mode login --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name $ARTIFACT_PATH/$ARTIFACT_NAME_1 2>/dev/null | jq -r '.artifactchecksum' ); artifact_checksum_deps_blob=$(az storage blob metadata show --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --auth-mode login --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name $ARTIFACT_PATH/$ARTIFACT_NAME_2 2>/dev/null | jq -r '.artifactchecksum' ); artifact_checksum_apps_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_profiling_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-solution-ei-profiling-application\")) | .checksum'); artifact_checksum_deps_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_profiling_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-solution-ei-profiling-dependencies\")) | .checksum'); if [[ \"$artifact_checksum_apps_blob\" == \"$artifact_checksum_apps_jf\" && \"$artifact_checksum_deps_blob\" == \"$artifact_checksum_deps_jf\" ]]; then echo \"Artifact exists in datalake, skipping the artifact copy...\"; echo \"true\" > /tmp/result/result.txt; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; echo \"false\" > /tmp/result/result.txt; fi"]
    job_command:
      aws: ["bash","-c","until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then echo \"Artifact exists in datalake, Skipping artifact copy...\"; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; python /opt/sds-pe-deployment-utils/artifact-object-store-deploy.py --bundle_name sds-solution-ei --sol_name sds-solution-ei --comp_name sds-ei-profiling --obj_store_path s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/$ARTIFACT_PATH/ --artifact_identifier sds-solution-ei-profiling-application --artifact_name application.zip && python /opt/sds-pe-deployment-utils/artifact-object-store-deploy.py --bundle_name sds-solution-ei --sol_name sds-solution-ei --comp_name sds-ei-profiling --obj_store_path s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/$ARTIFACT_PATH/ --artifact_identifier sds-solution-ei-profiling-dependencies --artifact_name dependencies.tar.gz && python /opt/sds-pe-deployment-utils/pyspark-zip-artifact-deploy.py --bundle_name sds-solution-ei --sol_name sds-solution-ei --comp_name sds-ei-profiling --obj_store_path s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/$ARTIFACT_PATH/ --artifact_identifier sds-solution-ei-profiling-application --artifact_name dummy && mkdir /tmp/result/ready; fi"]
      azure: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then echo \"Artifact exists in datalake, Skipping artifact copy...\"; else echo \"Artifact doesn't exist in datalake, proceeding with artifact copy...\"; python /opt/sds-pe-deployment-utils/artifact-object-store-deploy.py --bundle_name sds-solution-ei --sol_name sds-solution-ei --comp_name sds-ei-profiling --obj_store_path az://{{ .Values.global.BLOB_APPS_CONTAINER_NAME }}/$ARTIFACT_PATH/ --artifact_identifier sds-solution-ei-profiling-application --artifact_name application.zip && python /opt/sds-pe-deployment-utils/artifact-object-store-deploy.py --bundle_name sds-solution-ei --sol_name sds-solution-ei --comp_name sds-ei-profiling --obj_store_path az://{{ .Values.global.BLOB_APPS_CONTAINER_NAME }}/$ARTIFACT_PATH/ --artifact_identifier sds-solution-ei-profiling-dependencies --artifact_name dependencies.tar.gz && python /opt/sds-pe-deployment-utils/pyspark-zip-artifact-deploy.py --bundle_name sds-solution-ei --sol_name sds-solution-ei --comp_name sds-ei-profiling --obj_store_path az://{{ .Values.global.BLOB_APPS_CONTAINER_NAME }}/$ARTIFACT_PATH/ --artifact_identifier sds-solution-ei-profiling-application --artifact_name dummy && mkdir /tmp/result/ready; fi"]
    post_job_command:
      aws: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\"; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then sleep 10; echo \"Jar already exists, skipped tagging the artifact...\"; else echo \"Tagging the artifact in datalake....\"; echo \"Getting Artifact checksum from Jfrog...\"; artifact_checksum_apps_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_profiling_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-solution-ei-profiling-application\")) | .checksum'); artifact_checksum_deps_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_profiling_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-solution-ei-profiling-dependencies\")) | .checksum'); until [ -e \"/tmp/result/ready\" ]; do echo \"Waiting for artifact upload to be completed...\"; sleep 3; done; tagsApps='{\"TagSet\":[{\"Key\":\"artifactchecksum\",\"Value\":\"'\"$artifact_checksum_apps_jf\"'\"}]}'; tagsDeps='{\"TagSet\":[{\"Key\":\"artifactchecksum\",\"Value\":\"'\"$artifact_checksum_deps_jf\"'\"}]}'; /tmp/aws/bin/aws s3api put-object-tagging --bucket {{ .Values.global.S3_APPS_BUCKET_NAME }} --key $ARTIFACT_PATH/$ARTIFACT_NAME_1 --tagging \"$tagsApps\" > /dev/null 2>&1; /tmp/aws/bin/aws s3api put-object-tagging --bucket {{ .Values.global.S3_APPS_BUCKET_NAME }} --key $ARTIFACT_PATH/$ARTIFACT_NAME_2 --tagging \"$tagsDeps\" > /dev/null 2>&1; fi; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
      azure: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\";az login --output none --only-show-errors --federated-token \"$(cat $AZURE_FEDERATED_TOKEN_FILE)\" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID; if [ \"$(cat /tmp/result/result.txt)\" == \"true\" ]; then sleep 10; echo \"Jar already exists, skipped tagging the artifact...\"; else echo \"Tagging the artifact in datalake....\"; echo \"Getting Artifact checksum from Jfrog...\"; artifact_checksum_apps_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_profiling_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-solution-ei-profiling-application\")) | .checksum'); artifact_checksum_deps_jf=$(curl -u $ARTIFACT_USERNAME:$ARTIFACT_PASSWORD -H \"Content-Type: application/json\" \"${ARTIFACT_URL}lifecycle/api/v2/release_bundle/records/sds-solution-ei/{{ or (index .Values \"jobOverrides\" \"sds_ei_profiling_copy_job\" \"bundle-version\") .Values.bundleVersion.solutionEi }}\" | jq -r '.artifacts[] | select(.path | startswith(\"sds-solution-ei-profiling-dependencies\")) | .checksum'); until [ -e \"/tmp/result/ready\" ]; do echo \"Waiting for artifact upload to be completed...\"; sleep 3; done; az storage blob metadata update --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --auth-mode login --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name $ARTIFACT_PATH/$ARTIFACT_NAME_1 --metadata artifactchecksum=$artifact_checksum_apps_jf; az storage blob metadata update --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --auth-mode login --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name $ARTIFACT_PATH/$ARTIFACT_NAME_2 --metadata artifactchecksum=$artifact_checksum_deps_jf; fi; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
    env:
      common:
        - <<: *defaultJfrogURL
        - name: ARTIFACT_NAME_1
          value: application.zip
        - name: ARTIFACT_NAME_2
          value: dependencies.tar.gz
        - name: ARTIFACT_PATH
          value: "{{ .Values.global.DEPLOY_NAMESPACE}}/sds/data-analytics/lib/latest"
      aws: []
      azure: []   
    extraEnv: []



