apiVersion: v1
kind: ConfigMap
metadata:
  name: dependencies-configmap-{{ .Release.Name }}
data:
  dependencies.yaml: |
    "sds-solution-ei":
      bundle-version: {{ .Values.bundleVersion.solutionEi | quote }}
      component-override:
        {{- $bundleVersion := index .Values "bundle-version" }}
        {{- if index .Values "jobOverrides" "sds_ei_jar_copy_job" "bundle-version" }}
        "sds-ei-analytics": {{ upper (index  .Values "jobOverrides" "sds_ei_jar_copy_job" "bundle-version") }}
        {{- else }}
        "sds-ei-analytics": {{ if $bundleVersion }}{{ upper $bundleVersion }}{{ else }}""{{ end }}
        {{- end }}
        {{- if index .Values "jobOverrides" "sds_ei_validator_jar_copy_job" "bundle-version" }}
        "validator": {{ upper (index  .Values "jobOverrides" "sds_ei_validator_jar_copy_job" "bundle-version") }}
        {{- else }}
        "validator": {{ if $bundleVersion }}{{ upper $bundleVersion }}{{ else }}""{{ end }}
        {{- end }}
        {{- if index .Values "jobOverrides" "sds_ei_profiling_copy_job" "bundle-version" }}
        "sds-ei-profiling": {{ upper (index  .Values "jobOverrides" "sds_ei_profiling_copy_job" "bundle-version") }}
        {{- else }}
        "sds-ei-profiling": {{ if $bundleVersion }}{{ upper $bundleVersion }}{{ else }}""{{ end }}
        {{- end }}
        


  job-overrides: |
    {{ tpl (toYaml .Values.jobOverrides) . | nindent 6 }}