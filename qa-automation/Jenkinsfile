
@Library('sds-common') _

pipeline {

  agent any
  environment {
        repository                      = 'qa-automation'
        registryCredential              = 'dockerhub'
        repoName                        = 'sds-qa-solutions-automation'
        COSIGN_PASSWORD                 = credentials('cosign-password')
        COSIGN_PRIVATE_KEY              = credentials('cosign-private-key')
        COSIGN_PUBLIC_KEY               = credentials('cosign-public-key')
  }
  
  stages{

    stage('Setup parameters') {
            steps {
                script { 
                    properties([
                        parameters([
                            choice(
                                choices: ['selenium/standalone-chrome:4.9.1', 'selenium/standalone-firefox:4.9.1'], 
                                name: 'BASE_IMAGE'
                            )
                        ])
                    ])
                }
            }
        }


    stage ('version') {
      steps {
        script {
            env.buildNumber = getDockerBuildVersion()
        }
      }
    } 

    stage ('ImageTag') {
      steps {
        script {
            if (params.BASE_IMAGE == 'selenium/standalone-chrome:4.9.1') {
                env.tag = 'selenium-standalone-chrome-4-9-1'
            } else if (params.BASE_IMAGE == 'selenium/standalone-firefox:4.9.1') {
                env.tag = 'selenium-standalone-firefox-4-9-1'
            }
        }
      }
    } 


    stage('buildingDockerImage') {
      steps{
        script {
          def buildTag = sh(script: "echo ${buildNumber} | sed \"s/+/-/g\" | sed \"s/\\./-/g\"", returnStdout: true)
          imageName = "${docker_registery}/"+"${repository}:"+"${env.tag}-"+"${buildTag}"
          docker.withRegistry( '', registryCredential ) {
            sh "docker build . --label \"${repoName}.Branch=${env.GIT_BRANCH}\" --label \"${repoName}.BuildVersion=${buildNumber}\" --build-arg BASE_IMAGE=${params.BASE_IMAGE} -t ${imageName}"
          } 
        }
      }
    }

    stage('pushDockerImage') {
      steps{
        script {
          docker.withRegistry( '', registryCredential ) {
              sh "docker push ${imageName}"
          }
        }
      }
    }

    stage('sign the container image') {
          steps {
              script{
                  docker.withRegistry( '', registryCredential ){
                      cosign.signImage(imageName: "${imageName}")
                  }
              }
          }
      }

        stage('verify container image') {
            steps {
                script{
                    docker.withRegistry( '', registryCredential ){
                        cosign.verifyImage(imageName:"${imageName}")
                    }
                }
            }
        }

    stage('cleaningUp') { 
      steps { 
        sh "docker rmi ${imageName}" 
      }
    }

    stage ('notification'){
      steps{
        script{
          deploy.notifyOnBuild(buildVersion:"${buildNumber}")
        }
      }
    }
  }
}