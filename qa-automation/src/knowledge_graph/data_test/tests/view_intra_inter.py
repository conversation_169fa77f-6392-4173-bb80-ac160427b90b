from pyspark.sql.functions import col
import pandas as pd
import numpy as np
from src.utilities.data_test.common import Common
from core.common.logging_utils import setup_logger
import logging
import os, sys
import shutil
import tempfile
from pyspark.sql.types import TimestampType
from pyspark.sql import functions as F
import hashlib
from pyspark.sql import Row
from io import StringIO

logger = setup_logger(__name__)


class ViewInterComparison:
    """
    A class to handle snapshot comparison between views.
    """

    def __init__(self, data):
        try:
            self.output_table = data['output']['disambiguatedModelLocation']
            logger.info(f"[INIT] Initialized ViewInterComparison for table: {self.output_table}")
            self.input_table = data['inventoryModelInput'][0]['path']
            logger.info(f"[INIT] Initialized ViewInterComparison for table: {self.input_table}")
        except KeyError as e:
            logger.error(f"[ERROR] Missing required configuration key: {e}")
            raise ValueError(f"Invalid configuration data: missing {e}")

    def snapshot_read(self, spark):
        """Read current snapshots from inter/intra table."""
        try:
            logger.info(f"[READ] Reading snapshots for table: {self.output_table}")
            
            # Handle the real Iceberg table - get latest snapshot
            inter_snapshots_df = spark.sql(f"SELECT * FROM iceberg_catalog.{self.output_table}.snapshots")
            latest_inter_snapshot_df = inter_snapshots_df.orderBy(F.col("committed_at").desc()).limit(1)
            inter_intra_snapshot_id_df = latest_inter_snapshot_df.select(F.col("snapshot_id").alias("inter_intra_snapshot_id"))

            inter_intra_snapshot_id = inter_intra_snapshot_id_df.collect()[0]["inter_intra_snapshot_id"]
            logger.info(f"[SRDM] Latest srdm_inv snapshot_id: {inter_intra_snapshot_id}")

            return inter_intra_snapshot_id_df
            
        except Exception as e:
            logger.error(f"[ERROR] Error reading snapshots: {str(e)}")
            raise

    def snapshot_write(self, df):
        """Write snapshot DataFrame to persistent storage."""
        try:
            logger.info("[WRITE] Writing snapshot to iceberg_catalog.ei_new_out_kg.previous_inter_intra_snapshot")
            df.writeTo("iceberg_catalog.ei_new_out_kg.previous_inter_intra_snapshot").createOrReplace()
            logger.info("[SUCCESS] Data written to iceberg_catalog.ei_new_out_kg.previous_inter_intra_snapshot")
        except Exception as e:
            logger.error(f"[ERROR] Error writing snapshot: {str(e)}")
            raise

    def snapshot_compare(self, spark, latest_snapshot_df):
        """Compare latest snapshot with previously stored snapshot."""
        try:
            logger.info("[COMPARE] Comparing current and previous snapshots")
            
            prev_snapshot_df = spark.sql("SELECT * FROM iceberg_catalog.ei_new_out_kg.previous_inter_intra_snapshot")
            
            # Rename previous snapshot columns to avoid conflicts
            for c in prev_snapshot_df.columns:
                prev_snapshot_df = prev_snapshot_df.withColumnRenamed(c, c + "_prev")
            
            # Step 5: Compare old and new snapshot IDs
            comparison_df = latest_snapshot_df.crossJoin(prev_snapshot_df)
            comparison_df = comparison_df.withColumn("is_snap_same", F.expr("inter_intra_snapshot_id = inter_intra_snapshot_id_prev"))
            
            # Log the comparison values for debugging
            first_row = comparison_df.first()
            logger.info(f"[DEBUG] Current SRDM snapshot: {first_row['inter_intra_snapshot_id']}")
            logger.info(f"[DEBUG] Previous SRDM snapshot: {first_row['inter_intra_snapshot_id_prev']}")
            logger.info(f"[DEBUG] Snapshot same: {first_row['is_snap_same']}")
            
            logger.info("[SUCCESS] Snapshot comparison completed")
            return comparison_df
            
        except Exception as e:
            logger.error(f"[ERROR] Error comparing snapshots: {str(e)}")
            raise

    def check_no_change(self, comparison_df):
        try:
            logger.info("[VALIDATE] Checking for changes in snapshot")

            is_snap_same = comparison_df.select("is_snap_same").first()[0]
            
            logger.info(f"[STATUS] Snapshot same: {is_snap_same}")
            
            if is_snap_same :
                logger.info("[PASS] Validation passed: Snapshot is unchanged")
                mismatch_flag = False
            else:
                logger.error("[FAIL] Validation failed:")
                logger.error("[FAIL] - Expected: Snapshot should not have changed, but it changed")
                mismatch_flag = True
            return mismatch_flag
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking single change: {str(e)}")
            raise

    def check_change(self, comparison_df):

        try:
            logger.info("[VALIDATE] Checking there is no changes in Inter output")

            is_snap_same = comparison_df.select("is_snap_same").first()[0]
            
            logger.info(f"[STATUS]  snapshot same: {is_snap_same}")

            if is_snap_same:
                logger.error("[FAIL] Validation failed:")  
                logger.error("[FAIL] - Expected: Snapshot should have changed, but it is unchanged")
                mismatch_flag = True

            else:
                logger.info("[PASS] Validation passed: Snapshot is changed")
                mismatch_flag = False
            
            logger.info(f"[RESULT] mismatch_flag: {mismatch_flag}")
            return mismatch_flag
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking both changes: {str(e)}")
            raise
        
        
    def inp_row_addition(self, spark):
        """Write extra record to loader."""
        try:
            logger.info(f"[WRITE] Write extra record to Intra {self.input_table}")
            intra =spark.sql(f"SELECT * FROM iceberg_catalog.{self.input_table}")
            intra_one = intra.limit(1)
            intra_one = intra_one.withColumn("primary_key", F.lit('test'))
            intra = intra.union(intra_one)
            intra.writeTo(f"iceberg_catalog.{self.input_table}").createOrReplace()
            logger.info(f"[SUCCESS] Data written to {self.input_table}")
        except Exception as e:
            logger.error(f"[ERROR] Error writing {self.input_table}: {str(e)}")
            raise

    def inp_removal(self, spark):
        """Remove extra record from loader."""
        try:
            logger.info(f"Remove extra record from Intra {self.input_table}")
            intra =spark.sql(f"SELECT * FROM iceberg_catalog.{self.input_table}")
            intra = intra.filter("primary_key!='test'")
            intra.writeTo(f"iceberg_catalog.{self.input_table}").createOrReplace()
            logger.info(f"[SUCCESS] Data removed from{ self.input_table}")
        except Exception as e:
            logger.error(f"[ERROR] Error removing from  {self.input_table}: {str(e)}")
            raise