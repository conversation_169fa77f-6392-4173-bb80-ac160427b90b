from pyspark.sql.functions import expr, lit, split, to_timestamp, unix_timestamp

class NonDisambiguatedRelationshipValidation:

    def __init__(self, spark, rel_config):
        block_variables = [item + '_exp' for item in rel_config['block_variables']]
        block_variables = ", ".join(block_variables)

        if(len(rel_config['srdm']['columns_to_select'])==0):
            columns_to_select = '*'
        else:
            columns_to_select = [item for item in rel_config['srdm']['columns_to_select']]
            columns_to_select = ", ".join(columns_to_select)

        if(len(rel_config['override_columns']) != 0):
            srdm = spark.sql(f"select {columns_to_select} from {rel_config['srdm']['iceberg_catalog_name']}.{rel_config['srdm']['schema_name']}.{rel_config['srdm']['table_name']}")
            for i in (rel_config['override_columns']):
                srdm = srdm.withColumn(f"{i}",expr(f"{rel_config['override_columns'][i]}"))
            srdm = srdm.filter(f"event_timestamp_ts <='{rel_config['srdm']['end_event_timestamp_ts']}' and event_timestamp_ts >='{rel_config['srdm']['start_event_timestamp_ts']}' and parsed_interval_timestamp_ts <='{rel_config['srdm']['end_parsed_interval_timestamp_ts']}'")
        else:
            srdm = spark.sql(f"select {columns_to_select} from {rel_config['srdm']['iceberg_catalog_name']}.{rel_config['srdm']['schema_name']}.{rel_config['srdm']['table_name']} where event_timestamp_ts <='{rel_config['srdm']['end_event_timestamp_ts']}' and event_timestamp_ts >='{rel_config['srdm']['start_event_timestamp_ts']}' and parsed_interval_timestamp_ts <='{rel_config['srdm']['end_parsed_interval_timestamp_ts']}'")

        if(len(rel_config['temporary_properties']) != 0):
            for i in (rel_config['temporary_properties']):
                srdm = srdm.withColumn(f"{i}",expr(f"{rel_config['temporary_properties'][i]}"))

        src_loader = spark.sql(f"select p_id, primary_key from {rel_config['iceberg_catalog_name']}.{rel_config['schema_name']}.{rel_config['loader']['source_table_name']} where   updated_at_ts='{rel_config['srdm']['end_event_timestamp_ts']}'")
        tgt_loader = spark.sql(f"select p_id, primary_key from {rel_config['iceberg_catalog_name']}.{rel_config['schema_name']}.{rel_config['loader']['target_table_name']} where   updated_at_ts='{rel_config['srdm']['end_event_timestamp_ts']}'")

        src_loader = src_loader.toDF(*(c.replace(c, 'src_' + c) for c in src_loader.columns))
        tgt_loader = tgt_loader.toDF(*(c.replace(c, 'tgt_' + c) for c in tgt_loader.columns))

        srdm = srdm.withColumn("src_primary_key_exp",expr(f"{rel_config['loader']['source_primary_key_logic']}"))
        srdm = srdm.withColumn("tgt_primary_key_exp",expr(f"{rel_config['loader']['target_primary_key_logic']}"))

        srdm__src_ldr = srdm.join(src_loader,srdm.src_primary_key_exp == src_loader.src_primary_key,"left")
        srdm__src_ldr__tgt_ldr = srdm__src_ldr.join(tgt_loader,srdm__src_ldr.tgt_primary_key_exp == tgt_loader.tgt_primary_key,"left")

        if(len(rel_config['intra_resolver_table_name']) != 0):
            intra_resolver = spark.sql(f"select * from {rel_config['iceberg_catalog_name']}.{rel_config['schema_name']}.{rel_config['intra_resolver_table_name']} where updated_at_ts='{rel_config['srdm']['end_event_timestamp_ts']}'")
            intra_resolver = intra_resolver.toDF(*(c.replace(c, 'intra_res_src_' + c) for c in intra_resolver.columns))

        inter_resolver = spark.sql(f"select * from {rel_config['iceberg_catalog_name']}.{rel_config['schema_name']}.{rel_config['inter_resolver_table_name']} where updated_at_ts='{rel_config['srdm']['end_event_timestamp_ts']}'")
        inter_resolver = inter_resolver.toDF(*(c.replace(c, 'inter_res_src_' + c) for c in inter_resolver.columns))

        if(rel_config['is_source_intra_resolver_present'] == 'true'):
            srdm__src_ldr__tgt_ldr__src_intra_res = srdm__src_ldr__tgt_ldr.join(intra_resolver,srdm__src_ldr__tgt_ldr.src_p_id == intra_resolver.intra_res_src_p_id,"left")
            srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res = srdm__src_ldr__tgt_ldr__src_intra_res.join(inter_resolver,srdm__src_ldr__tgt_ldr__src_intra_res. intra_res_src_disambiguated_p_id == inter_resolver.inter_res_src_p_id, "left")
        else:
            srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res = srdm__src_ldr__tgt_ldr.join(inter_resolver,srdm__src_ldr__tgt_ldr.src_p_id == inter_resolver.inter_res_src_p_id, "left")

        if(len(rel_config['intra_resolver_table_name']) != 0):
            intra_resolver = spark.sql(f"select * from {rel_config['iceberg_catalog_name']}.{rel_config['schema_name']}.{rel_config['intra_resolver_table_name']} where updated_at_ts='{rel_config['srdm']['end_event_timestamp_ts']}'")
            intra_resolver = intra_resolver.toDF(*(c.replace(c, 'intra_res_tgt_' + c) for c in intra_resolver.columns))

        inter_resolver = spark.sql(f"select * from {rel_config['iceberg_catalog_name']}.{rel_config['schema_name']}.{rel_config['inter_resolver_table_name']} where updated_at_ts='{rel_config['srdm']['end_event_timestamp_ts']}'")
        inter_resolver = inter_resolver.toDF(*(c.replace(c, 'inter_res_tgt_' + c) for c in inter_resolver.columns))

        if(rel_config['is_target_intra_resolver_present'] == 'true'):
            srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res = srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res.join(intra_resolver, srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res.tgt_p_id == intra_resolver.intra_res_tgt_p_id,"left")
            srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res__tgt_inter_res = srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res.join (inter_resolver,srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res.intra_res_tgt_disambiguated_p_id == inter_resolver.inter_res_tgt_p_id, "left")
        else:
            srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res__tgt_inter_res = srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res.join(inter_resolver,srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res.tgt_p_id == inter_resolver.inter_res_tgt_p_id, "left")

        srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res__tgt_inter_res = srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res__tgt_inter_res.filter("inter_res_src_disambiguated_p_id is not null and inter_res_tgt_disambiguated_p_id is not null")

        src_inter = spark.sql(f"select p_id, display_label from {rel_config['iceberg_catalog_name']}.{rel_config['schema_name']}.{rel_config['source_inter_table_name']} where updated_at_ts='{rel_config['srdm']['end_event_timestamp_ts']}'")
        tgt_inter = spark.sql(f"select p_id, display_label from {rel_config['iceberg_catalog_name']}.{rel_config['schema_name']}.{rel_config['target_inter_table_name']} where updated_at_ts='{rel_config['srdm']['end_event_timestamp_ts']}'")

        src_inter = src_inter.toDF(*(c.replace(c, 'inter_src_' + c) for c in src_inter.columns))
        tgt_inter = tgt_inter.toDF(*(c.replace(c, 'inter_tgt_' + c) for c in tgt_inter.columns))

        srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res__tgt_inter_res__src_inter = srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res__tgt_inter_res.join(src_inter,srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res__tgt_inter_res.inter_res_src_disambiguated_p_id == src_inter.inter_src_p_id, "left")
        srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res__tgt_inter_res__src_inter__tgt_inter = srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res__tgt_inter_res__src_inter.join(tgt_inter,srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res__tgt_inter_res__src_inter.inter_res_tgt_disambiguated_p_id == tgt_inter.inter_tgt_p_id, "left")

        rel_exp = srdm__src_ldr__tgt_ldr__src_intra_res__src_inter_res__tgt_intra_res__tgt_inter_res__src_inter__tgt_inter.withColumnRenamed('inter_res_src_disambiguated_p_id','source_p_id_exp').withColumnRenamed('inter_res_tgt_disambiguated_p_id','target_p_id_exp').withColumnRenamed('inter_src_display_label','source_display_label_exp'). withColumnRenamed('inter_tgt_display_label','target_display_label_exp')

        rel_exp = rel_exp.withColumn("source_entity_class_exp",lit(f"{rel_config['common_attributes']['source_entity_class']}")).withColumn("target_entity_class_exp",lit(f"{rel_config ['common_attributes']['target_entity_class']}"))
        rel_exp = rel_exp.withColumn("start_epoch_exp",expr(f"min(event_timestamp_epoch) over (partition by {block_variables})")).withColumn("end_epoch_exp",expr(f"max (event_timestamp_epoch) over (partition by {block_variables})"))
        rel_exp = rel_exp.withColumn("relationship_name_exp",lit(f"{rel_config['common_attributes']['relationship_name']}")).withColumn("inverse_relationship_name_exp",lit(f"{rel_config['common_attributes']['inverse_relationship_name']}")).withColumn("relationship_origin_exp",lit(f"{rel_config['common_attributes']['relationship_origin']}"))
        rel_exp = rel_exp.withColumn("relationship_origin_exp",split('relationship_origin_exp',","))
        rel_exp = rel_exp.withColumn("relationship_first_seen_date_exp",expr(f"{rel_config['common_attributes']['relationship_first_seen_date']} over (partition by {block_variables}) ")).withColumn("relationship_last_seen_date_exp",expr(f"{rel_config['common_attributes']['relationship_last_seen_date']} over (partition by {block_variables})"))
        rel_exp = rel_exp.withColumn("updated_at_ts_epoch",lit(f"{rel_config['srdm']['end_event_timestamp_ts']}"))
        rel_exp = rel_exp.withColumn("updated_at_ts_epoch",to_timestamp("updated_at_ts_epoch"))
        rel_exp = rel_exp.withColumn("updated_at_ts_epoch", unix_timestamp(to_timestamp("updated_at_ts_epoch", "yyyy-MM-dd HH:mm:ss")))
        rel_exp = rel_exp.withColumn("lifetime_relationship_exp",expr("datediff(to_date((to_timestamp(relationship_last_seen_date_exp/1000))),to_date((to_timestamp (relationship_first_seen_date_exp/1000))))")).withColumn("recency_relationship_exp",expr("datediff(to_date((to_timestamp(updated_at_ts_epoch))),to_date((to_timestamp(relationship_last_seen_date_exp/1000))))"))

        if(len(rel_config['optional_attributes']) != 0):
            for i in (rel_config['optional_attributes']):
                if('occurence' not in i):
                    if(rel_config['optional_attributes'][(i+'_occurence')] == 'last'):
                        rel_exp = rel_exp.withColumn(f"{i}_exp",expr(f"first({rel_config['optional_attributes'][i]}) over (partition by {block_variables} order by event_timestamp_epoch desc)"))
                    elif(rel_config['optional_attributes'][(i+'_occurence')] == 'first'):
                        rel_exp = rel_exp.withColumn(f"{i}_exp",expr(f"first({rel_config['optional_attributes'][i]}) over (partition by {block_variables} order by event_timestamp_epoch asc)"))

        block_variables = [item + '_exp' for item in rel_config['block_variables']]
        self.rel_exp = rel_exp.dropDuplicates([*block_variables])

        self.rel = spark.sql(f"select * from {rel_config['iceberg_catalog_name']}.{rel_config['schema_name']}.{rel_config['relationship_table_name']} where updated_at_ts='{rel_config['srdm']['end_event_timestamp_ts']}'")

        comp = self.rel.join(rel_exp, (self.rel.source_p_id == rel_exp.source_p_id_exp) & (self.rel.target_p_id == rel_exp.target_p_id_exp), "full")

        self.comp = comp.withColumn("relationship_name_compare",expr("case when relationship_name==relationship_name_exp or (relationship_name is null and relationship_name_exp is null) then 'matching' else 'not matching' end")).withColumn("inverse_relationship_name_compare",expr("case when inverse_relationship_name==inverse_relationship_name_exp or (inverse_relationship_name is null and inverse_relationship_name_exp is null) then 'matching' else 'not matching' end")).withColumn("relationship_origin_compare",expr("case when relationship_origin==relationship_origin_exp or (relationship_origin is null and relationship_origin_exp is null) then 'matching' else 'not matching' end")).withColumn("relationship_first_seen_date_compare",expr("case when relationship_first_seen_date==relationship_first_seen_date_exp or (relationship_first_seen_date is null and relationship_first_seen_date_exp is null) then 'matching' else 'not matching' end")).withColumn("relationship_last_seen_date_compare",expr("case when relationship_last_seen_date==relationship_last_seen_date_exp or (relationship_last_seen_date is null and relationship_last_seen_date_exp is null) then 'matching' else 'not matching' end")).withColumn("source_entity_class_compare",expr("case when source_entity_class==source_entity_class_exp or (source_entity_class is null and source_entity_class_exp is null) then 'matching' else 'not matching' end")).withColumn("source_display_label_compare",expr("case when source_display_label==source_display_label_exp or (source_display_label is null and source_display_label_exp is null) then 'matching' else 'not matching' end")).withColumn("source_p_id_compare",expr("case when source_p_id==source_p_id_exp or (source_p_id is null and source_p_id_exp is null) then 'matching' else 'not matching' end")).withColumn("target_entity_class_compare",expr("case when target_entity_class==target_entity_class_exp or (target_entity_class is null and target_entity_class_exp is null) then 'matching' else 'not matching' end")).withColumn("target_display_label_compare",expr("case when target_display_label==target_display_label_exp or (target_display_label is null and target_display_label_exp is null) then 'matching' else 'not matching' end")).withColumn("target_p_id_compare",expr("case when target_p_id==target_p_id_exp or (target_p_id is null and target_p_id_exp is null) then 'matching' else 'not matching' end")).withColumn("start_epoch_compare",expr("case when start_epoch==start_epoch_exp or (start_epoch is null and start_epoch_exp is null) then 'matching' else 'not matching' end")).withColumn("end_epoch_compare",expr("case when end_epoch==end_epoch_exp or (end_epoch is null and end_epoch_exp is null) then 'matching' else 'not matching' end")).withColumn("lifetime_relationship_compare",expr("case when lifetime_relationship==lifetime_relationship_exp or (lifetime_relationship is null and lifetime_relationship_exp is null) then 'matching' else 'not matching' end")).withColumn("recency_relationship_compare",expr("case when recency_relationship==recency_relationship_exp or (recency_relationship is null and recency_relationship_exp is null) then 'matching' else 'not matching' end"))

        if(len(rel_config['optional_attributes']) != 0):
            for i in (rel_config['optional_attributes']):
                if('occurence' not in i):
                    self.comp = self.comp.withColumn(f"{i}_compare",expr(f"case when {i}=={i}_exp or ({i} is null and {i}_exp is null) then 'matching' else 'not matching' end"))

    def relationship_id_uniqueness_check(self):
        total_count = self.rel.count()
        distinct_count = self.rel.select("relationship_id").distinct().count()
        if(total_count == 0 or distinct_count == 0):
            print("Not Executed")
        elif(total_count == distinct_count):
            print('Passed')
        else:
            print("Failed")

        return total_count, distinct_count

    def non_negative_values_for_recency_and_lifetime_check(self):
        neg_counter = self.rel.filter("recency<0 or lifetime<0").count()
        if(neg_counter == 0):
            print("Passed")
        else:
            print('Failed')

        return neg_counter

    def block_variables_uniqueness_check(self, rel_config):
        block_variables = [item + '_exp' for item in rel_config['block_variables']]
        exp_distinct_count = self.rel_exp.select(*block_variables).distinct().count()
        block_variables = [item for item in rel_config['block_variables']]
        rel_distinct_count = self.rel.select(*block_variables).distinct().count()
        total_count = self.rel.count()
        if(total_count == exp_distinct_count == rel_distinct_count):
            print("Passed")
        else:
            print('Failed')

        return total_count, exp_distinct_count, rel_distinct_count

    def rel_name_and_inv_rel_name_check(self):
        not_matching_count = self.comp.filter("relationship_name_compare = 'not matching' or inverse_relationship_name_compare = 'not matching'").count()
        if(not_matching_count == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_count

    def rel_name_and_inv_rel_name_check(self):
        not_matching_count = self.comp.filter("relationship_name_compare = 'not matching' or inverse_relationship_name_compare = 'not matching'").count()
        if(not_matching_count == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_count
    
    def first_seen_and_last_seen_check(self):
        not_matching_count = self.comp.filter("relationship_first_seen_date_compare = 'not matching' or relationship_last_seen_date_compare = 'not matching'").count()
        if(not_matching_count == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_count
    
    def relationship_origin_source_entity_class_and_target_entity_class_check(self):
        not_matching_count = self.comp.filter("relationship_origin_compare = 'not matching' or source_entity_class_compare = 'not matching' or target_entity_class_compare = 'not matching'").count()
        if(not_matching_count == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_count
    
    def source_display_label_target_display_label_check(self):
        not_matching_count = self.comp.filter("source_display_label_compare = 'not matching' or target_display_label_compare = 'not matching'").count()
        if(not_matching_count == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_count
    
    def common_relationship_fields_check(self):
        not_matching_count = self.comp.filter("start_epoch_compare = 'not matching' or end_epoch_compare = 'not matching' or lifetime_relationship_compare = 'not matching' or recency_relationship_compare = 'not matching'").count()
        if(not_matching_count == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_count

    def optional_attributes_check(self, rel_config):
        master_not_matching_count = 0
        optional_attributes_present = False

        if(len(rel_config['optional_attributes']) != 0):
            for i in (rel_config['optional_attributes']):
                if('occurence' not in i):
                    not_matching_count = self.comp.filter(f"{i}_compare = 'not matching'").count()
                    master_not_matching_count = master_not_matching_count + int(not_matching_count)
            optional_attributes_present = True

        if(optional_attributes_present == False):
            print("Not Executed")
        elif(master_not_matching_count == 0):
            print("Passed")
        else:
            print('Failed')

        return master_not_matching_count, optional_attributes_present
