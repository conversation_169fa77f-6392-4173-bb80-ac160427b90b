from pyspark.sql.functions import col
import pandas as pd
import numpy as np
from src.utilities.data_test.common import Common
from core.common.logging_utils import setup_logger
import logging
import os, sys
import shutil
import tempfile
from pyspark.sql.types import TimestampType
from pyspark.sql import functions as F
import hashlib
from pyspark.sql import Row
from io import StringIO

logger = setup_logger(__name__)


class ViewSnapshotComparison:
    """
    A class to handle snapshot comparison between views and SRDM inventory tables.
    """

    def __init__(self, data):
        try:
            self.output_table = data['outputTableInfo']['outputTableName']
            self.input_table = data['dataSource']['srdm']
            logger.info(f"[INIT] Initialized ViewSnapshotComparison for table: {self.output_table}")
        except KeyError as e:
            logger.error(f"[ERROR] Missing required configuration key: {e}")
            raise ValueError(f"Invalid configuration data: missing {e}")

    def snapshot_read(self, spark):
        """Read current snapshots from both view and SRDM inventory table."""
        try:
            logger.info(f"[READ] Reading snapshots for table: {self.output_table}")
            
            # Handle the view - get CREATE TABLE statement and hash it
            view_create_stmt_df = spark.sql(f"SHOW CREATE TABLE spark_catalog.{self.output_table}")
            view_create_stmt = view_create_stmt_df.collect()[0][0]  # Extract SQL string

            # Compute hash of the view SQL
            view_hash_id = hashlib.sha256(view_create_stmt.encode("utf-8")).hexdigest()
            logger.info(f"[VIEW] View hash_id (SHA256): {view_hash_id}")

            # Convert to DataFrame
            view_hash_df = spark.createDataFrame([Row(view_hash_id=view_hash_id)])

            # Handle the real Iceberg table - get latest snapshot
            srdm_snapshots_df = spark.sql(f"SELECT * FROM iceberg_catalog.{self.output_table}__srdm_inv.snapshots")
            latest_srdm_snapshot_df = srdm_snapshots_df.orderBy(F.col("committed_at").desc()).limit(1)
            srdm_snapshot_id_df = latest_srdm_snapshot_df.select(F.col("snapshot_id").alias("srdm_inv_snapshot_id"))

            srdm_snapshot_id = srdm_snapshot_id_df.collect()[0]["srdm_inv_snapshot_id"]
            logger.info(f"[SRDM] Latest srdm_inv snapshot_id: {srdm_snapshot_id}")

            # Combine hash and snapshot ID into single row
            combined_snapshot_df = view_hash_df.crossJoin(srdm_snapshot_id_df)
            logger.info("[SUCCESS] Successfully combined view and SRDM snapshots")
            
            return combined_snapshot_df
            
        except Exception as e:
            logger.error(f"[ERROR] Error reading snapshots: {str(e)}")
            raise

    def snapshot_write(self, df):
        """Write snapshot DataFrame to persistent storage."""
        try:
            logger.info("[WRITE] Writing snapshot to iceberg_catalog.ei_new_out_kg.previous_snapshot")
            df.writeTo("iceberg_catalog.ei_new_out_kg.previous_snapshot").createOrReplace()
            logger.info("[SUCCESS] Data written to iceberg_catalog.ei_new_out_kg.previous_snapshot")
        except Exception as e:
            logger.error(f"[ERROR] Error writing snapshot: {str(e)}")
            raise

    def snapshot_compare(self, spark, latest_snapshot_df):
        """Compare latest snapshot with previously stored snapshot."""
        try:
            logger.info("[COMPARE] Comparing current and previous snapshots")
            
            prev_snapshot_df = spark.sql("SELECT * FROM spark_catalog.ei_new_out_kg.previous_snapshot")
            
            # Rename previous snapshot columns to avoid conflicts
            for c in prev_snapshot_df.columns:
                prev_snapshot_df = prev_snapshot_df.withColumnRenamed(c, c + "_prev")
            
            # Step 5: Compare old and new snapshot IDs
            comparison_df = latest_snapshot_df.crossJoin(prev_snapshot_df)
            comparison_df = comparison_df.withColumn("is_view_same", F.expr("view_hash_id = view_hash_id_prev"))
            comparison_df = comparison_df.withColumn("is_srdm_inv_same", F.expr("srdm_inv_snapshot_id = srdm_inv_snapshot_id_prev"))
            
            # Log the comparison values for debugging
            first_row = comparison_df.first()
            logger.info(f"[DEBUG] Current view hash: {first_row['view_hash_id']}")
            logger.info(f"[DEBUG] Previous view hash: {first_row['view_hash_id_prev']}")
            logger.info(f"[DEBUG] Current SRDM snapshot: {first_row['srdm_inv_snapshot_id']}")
            logger.info(f"[DEBUG] Previous SRDM snapshot: {first_row['srdm_inv_snapshot_id_prev']}")
            logger.info(f"[DEBUG] View same: {first_row['is_view_same']}")
            logger.info(f"[DEBUG] SRDM same: {first_row['is_srdm_inv_same']}")
            
            logger.info("[SUCCESS] Snapshot comparison completed")
            return comparison_df
            
        except Exception as e:
            logger.error(f"[ERROR] Error comparing snapshots: {str(e)}")
            raise

    def check_change_in_one(self, comparison_df):
        """
        Check if only the view has changed while SRDM inventory remains unchanged.
        Expected: View changed (is_view_same = False) AND SRDM unchanged (is_srdm_inv_same = True)
        Returns: True if validation FAILS, False if validation PASSES
        """
        try:
            logger.info("[VALIDATE] Checking for changes in view only (SRDM should be unchanged)")
            
            is_view_same = comparison_df.select("is_view_same").first()[0]
            is_srdm_inv_same = comparison_df.select("is_srdm_inv_same").first()[0]
            
            logger.info(f"[STATUS] View same: {is_view_same}, SRDM same: {is_srdm_inv_same}")
            
            # Expected scenario: View changed (False) AND SRDM unchanged (True)
            view_changed = not is_view_same
            srdm_unchanged = is_srdm_inv_same
            
            if view_changed and srdm_unchanged:
                logger.info("[PASS] Validation passed: View changed, SRDM inventory unchanged")
                mismatch_flag = False
            else:
                logger.error("[FAIL] Validation failed:")
                if not view_changed:
                    logger.error("[FAIL] - Expected: View should have changed, but it didn't")
                if not srdm_unchanged:
                    logger.error("[FAIL] - Expected: SRDM should be unchanged, but it changed")
                mismatch_flag = True
            
            logger.info(f"[RESULT] mismatch_flag: {mismatch_flag}")
            return mismatch_flag
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking single change: {str(e)}")
            raise

    def check_no_change_in_both(self, comparison_df):
        """
        Check if both view and SRDM inventory have changed.
        Expected: Both should not changed (is
        _view_same = True AND is_srdm_inv_same = True)
        Returns: True if validation FAILS, False if validation PASSES
        """
        try:
            logger.info("[VALIDATE] Checking for changes in both view and SRDM inventory")
            
            is_view_same = comparison_df.select("is_view_same").first()[0]
            is_srdm_inv_same = comparison_df.select("is_srdm_inv_same").first()[0]
            
            logger.info(f"[STATUS] View same: {is_view_same}, SRDM same: {is_srdm_inv_same}")
            
            # Expected scenario: Both changed (both False)
            view_changed = is_view_same
            srdm_changed = is_srdm_inv_same
            
            if view_changed and srdm_changed:
                logger.info("[PASS] Validation passed: Both view and SRDM inventory not changed")
                mismatch_flag = False
            else:
                logger.error("[FAIL] Validation failed:")
                if not view_changed:
                    logger.error("[FAIL] - Expected: View should have not changed, but it chnaged")
                if not srdm_changed:
                    logger.error("[FAIL] - Expected: SRDM should have not changed, but it chnaged")
                mismatch_flag = True
            
            logger.info(f"[RESULT] mismatch_flag: {mismatch_flag}")
            return mismatch_flag
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking both changes: {str(e)}")
            raise8
        
    
    
    def check_change_in_both(self, comparison_df):
        """
        Check if both view and SRDM inventory have changed.
        Expected: Both changed (is
        _view_same = False AND is_srdm_inv_same = False)
        Returns: True if validation FAILS, False if validation PASSES
        """
        try:
            logger.info("[VALIDATE] Checking for changes in both view and SRDM inventory")
            
            is_view_same = comparison_df.select("is_view_same").first()[0]
            is_srdm_inv_same = comparison_df.select("is_srdm_inv_same").first()[0]
            
            logger.info(f"[STATUS] View same: {is_view_same}, SRDM same: {is_srdm_inv_same}")
            
            # Expected scenario: Both changed (both False)
            view_changed = not is_view_same
            srdm_changed = not is_srdm_inv_same
            
            if view_changed and srdm_changed:
                logger.info("[PASS] Validation passed: Both view and SRDM inventory changed")
                mismatch_flag = False
            else:
                logger.error("[FAIL] Validation failed:")
                if not view_changed:
                    logger.error("[FAIL] - Expected: View should have changed, but it didn't")
                if not srdm_changed:
                    logger.error("[FAIL] - Expected: SRDM should have changed, but it didn't")
                mismatch_flag = True
            
            logger.info(f"[RESULT] mismatch_flag: {mismatch_flag}")
            return mismatch_flag
            
        except Exception as e:
            logger.error(f"[ERROR] Error checking both changes: {str(e)}")
            raise8
        
       
    
    
    def srdm_addition(self, spark):
        """Write extra record to SRDM."""
        try:
            logger.info(f"[WRITE] Write extra record to SRDM {self.input_table}")
            srdm =spark.sql(f"SELECT * FROM iceberg_catalog.{self.input_table}")
            row_add = srdm.filter("QID ='983252'")
            row_add = row_add.withColumn("QID", F.lit('test'))
            srdm = srdm.union(row_add)
            srdm.writeTo(f"iceberg_catalog.{self.input_table}").createOrReplace()
            logger.info(f"[SUCCESS] Data written to {self.input_table}")
        except Exception as e:
            logger.error(f"[ERROR] Error writing {self.input_table}: {str(e)}")
            raise

    def srdm_removal(self, spark):
        """Remove extra record from SRDM."""
        try:
            logger.info(f"Remove extra record from SRDM {self.input_table}")
            srdm =spark.sql(f"SELECT * FROM iceberg_catalog.{self.input_table}")
            srdm = srdm.filter("QID!='test'")
            srdm.writeTo(f"iceberg_catalog.{self.input_table}").createOrReplace()
            logger.info(f"[SUCCESS] Data removed from{ self.input_table}")
        except Exception as e:
            logger.error(f"[ERROR] Error removing from  {self.input_table}: {str(e)}")
            raise