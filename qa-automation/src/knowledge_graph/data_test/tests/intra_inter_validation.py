from prettytable import PrettyTable
from pyspark.sql.window import Window
from pyspark.sql.functions import lit, expr, col, lag, lead, explode_outer
from io import StringIO
import tempfile
import sys
import pyspark.sql.functions as F
import pandas as pd
import numpy as np
import csv
from src.utilities.data_test.common import Common
from core.common.logging_utils import setup_logger
import logging
import os, re
import shutil
logger = setup_logger(__name__)

class IntraInterValidation:
    def __init__(self, spark, data, type, context):
        self.spark = spark
        self.data = data
        self.type = type
        self.context = context
        self.logger = logger
        self._init_candidate_keys_and_exceptions(data)
        self._load_and_union_tables()
        self._apply_exception_filters()
        self._add_lag_lead_columns()
        self._rename_and_join_dataframes()
        self._finalize_class_attributes()

    def _init_candidate_keys_and_exceptions(self, data):
        self.updated_at = Common.UPDATED_AT
        self.table_list = data["inventoryModelInput"]
        self.candidate_key_list = [
            item["name"] if isinstance(item, dict) and "name" in item else item
            for item in data["disambiguation"]["candidateKeys"]
        ]
        candidate_keys_with_exception = [
            item for item in data["disambiguation"]["candidateKeys"]
            if isinstance(item, dict) and "name" in item and "exceptionFilter" in item
        ]
        self.exception_filter = len(candidate_keys_with_exception) >= 1
        self.exception_keys = []
        if self.exception_filter:
            for item in candidate_keys_with_exception:
                field_name = item["name"]
                expression = item["exceptionFilter"]
                self.exception_keys.append(field_name)
        self.candidate_keys_with_exception = candidate_keys_with_exception

    def _load_and_union_tables(self):
        spark = self.spark
        data = self.data
        type = self.type
        context = self.context
        updated_at = self.updated_at
        table_list = self.table_list
        self.df = spark.createDataFrame([(1, "test")], ["id_test", "label"])
        self.df = self.df.filter(self.df.id_test != self.df.first().id_test)
        if type == 'inter':
            intra_table = context.deployement_config['config_value']['spark_job_configs']['source_models']['intrasource_disambiguated_models']
            loader_table = context.deployement_config['config_value']['spark_job_configs']['source_models']['inventory_models']
            self.fragment_table = data['output']['fragmentLocation']
            for item in table_list:
                self._process_table_item(item, intra_table, loader_table, updated_at, inter=True)
        else:
            loader_table = context.deployement_config['config_value']['spark_job_configs']['source_models']['inventory_models']
            for item in table_list:
                self._process_table_item(item, loader_table, loader_table, updated_at, inter=False)

    def _process_table_item(self, item, intra_table, loader_table, updated_at, inter):
        spark = self.spark
        logger = self.logger
        table_name = item['path']
        table_name_without_schema = table_name.split('.')[-1]
        schema = table_name.split('.')[0]
        
        # Early validation checks
        if not self._validate_table_availability(table_name_without_schema, schema, intra_table, loader_table):
            return
        
        # Read and process table data
        df_name = self._read_and_process_table_data(spark, table_name, table_name_without_schema, schema, updated_at, logger)
        
        # Resolve conflicts and union
        df_name = self._resolve_conflicts_and_union(df_name, table_name_without_schema)

    def _validate_table_availability(self, table_name_without_schema, schema, intra_table, loader_table):
        """Validate if table exists in deployment config and schema"""
        # Check deployment config
        if table_name_without_schema not in intra_table and table_name_without_schema not in loader_table:
            self.logger.info(f" ❌ Table {table_name_without_schema} doesn't exist in deployment config")
            return False
        
        # Check if table exists in schema
        existing_table = self.spark.sql(f"show tables in iceberg_catalog.{schema}")
        existing_tables_list = [row['tableName'] for row in existing_table.select("tableName").distinct().collect()]
        
        if table_name_without_schema not in existing_tables_list:
            self.logger.info(f"❌ Table '{table_name_without_schema}' does not exist in schema '{schema}'")
            return False
        
        return True

    def _read_and_process_table_data(self, spark, table_name, table_name_without_schema, schema, updated_at, logger):
        """Read table data and process array columns"""
        # Determine table type and read data
        table_type = spark.sql(f"SHOW CREATE TABLE {table_name}")
        stmt = table_type.collect()[0]["createtab_stmt"]
        table_type = " ".join(stmt.split()[:2])
        
        if table_type == "CREATE TABLE":
            df_name = spark.sql(f"SELECT * FROM iceberg_catalog.{table_name} WHERE updated_at = {updated_at}")
            logger.info(f"✅ Read Table '{table_name_without_schema}' from schema '{schema}'")
        else:
            df_name = spark.sql(f"SELECT * FROM spark_catalog.{table_name} WHERE updated_at = {updated_at}")
            logger.info(f"✅ Read View '{table_name_without_schema}' from schema '{schema}'")
        
        # Process array columns
        for key_name in self.candidate_key_list:
            is_array_column = (key_name in df_name.schema.fieldNames() and 
                            df_name.schema[key_name].dataType.typeName() == "array")
            
            if is_array_column:
                column_names = df_name.columns
                array_column_names = [key_name]
                selected_columns = [col(column_name) for column_name in column_names if column_name not in array_column_names]
                df_name = df_name.select(*selected_columns, explode_outer(col(key_name)).alias(key_name))
        
        return df_name

    def _resolve_conflicts_and_union(self, df_name, table_name_without_schema):
        """Resolve column conflicts and union with main dataframe"""
        # Resolve column name conflicts
        fields_1 = set(self.df.columns)
        fields_2 = set(df_name.columns)
        common_fields = fields_1.intersection(fields_2)
        
        for field in common_fields:
            data_type_1 = self.df.schema[field].dataType
            data_type_2 = df_name.schema[field].dataType
            if data_type_1 != data_type_2:
                df_name = df_name.withColumnRenamed(field, f"{field}_from_{table_name_without_schema}")
        
        # Union with main dataframe
        self.df = self.df.unionByName(df_name, allowMissingColumns=True)

    def _apply_exception_filters(self):
        if not self.exception_filter:
            return
        
        df = self.df
        for item in self.candidate_keys_with_exception:
            field_name = item["name"]
            expression = item["exceptionFilter"]
            new_column = expr(expression)
            df = df.withColumn(f'ex_{field_name}', new_column)
        self.df = df

    def _add_lag_lead_columns(self):
        df = self.df
        for key_name in self.candidate_key_list:
            window_spec = Window.orderBy(f"{key_name}")
            df = df.withColumn(f"lag_{key_name}", lag(f"{key_name}", 1).over(window_spec))
            df = df.withColumn(f"lead_{key_name}", lead(f"{key_name}", 1).over(window_spec))
            new_column = expr(f"CASE WHEN ({key_name}!= ' ' and {key_name}!= '' and {key_name} == lag_{key_name}) or ({key_name}!= ' ' and {key_name} == lead_{key_name}) THEN True ELSE False END")
            df = df.withColumn(f"is_{key_name}_match", new_column)
        self.df = df

    def _rename_and_join_dataframes(self):
        spark = self.spark
        data = self.data
        type = self.type
        updated_at = self.updated_at
        df = self.df
        rename_list_bri = ['origin', 'primary_key']
        table_name_bri = data['output']['resolverLocation']
        self.table_name_inter = data['output']['disambiguatedModelLocation']
        df = df.toDF(*(c.replace(c, c + '_inp') for c in df.columns))
        if type == "inter":
            table_name_frag = data['output']['fragmentLocation']
            self.df_frag = spark.sql(f"select * from iceberg_catalog.{table_name_frag} where updated_at= {updated_at}")
            self.df_bri = spark.sql(f"select * from iceberg_catalog.{table_name_bri} where updated_at= {updated_at}")
        else:
            self.df_bri = spark.sql(f"select * from iceberg_catalog.{table_name_bri} where updated_at= {updated_at} and data_source_name = '{self.table_name_inter}'")
        for field_name in rename_list_bri:
            self.df_bri = self.df_bri.withColumnRenamed(f"{field_name}", f"{field_name}_bri")
        self.rf = df.join(self.df_bri, [df.p_id_inp == self.df_bri.p_id])

    def _finalize_class_attributes(self):
        data = self.data
        type = self.type
        updated_at = self.updated_at
        self.table_name_inter = data['output']['disambiguatedModelLocation']
        if type == "inter":
            self.class_name = self.table_name_inter.split("__")[1]
        else:
            self.class_name = self.table_name_inter
        self.df_inter = self.spark.sql(f"select * from iceberg_catalog.{self.table_name_inter} where updated_at= {updated_at}")
        rename_list = ['origin', 'primary_key', 'first_seen', 'last_seen', 'p_id', 'analysis_period_end','analysis_period_start', 'class', 'display_label', 'lifetime', 'recency', 'type','updated_at']
        for field_name in rename_list:
            self.df_inter = self.df_inter.withColumnRenamed(f"{field_name}", f"{field_name}_inter")
        self.rf = self.rf.join(self.df_inter, [self.rf.disambiguated_p_id == self.df_inter.p_id_inter])
        window_spec = (
            Window
            .partitionBy("disambiguated_p_id")
            .rowsBetween(Window.unboundedPreceding, Window.unboundedFollowing)
        )

        self.rf = self.rf.withColumn("count", F.count("p_id_inp").over(window_spec))
        self.x = PrettyTable()
        self.x.field_names = {"CLASS", 'TEST_CASE', "STATUS"}

    def _report_mismatch(self,  desc_resolver, p_id_resolver, desc_union, p_id, additional_rows, class_name, test_case, table_label):
        print(desc_resolver, p_id_resolver)
        print(desc_union, p_id)
        print(f"******************************{class_name}*****************************")
        print(test_case)
        print(f"Additional rows in {table_label}:")
        additional_rows.show(truncate=False)

    def intra_inter_out_resolver_count_comp(self, f2):
        logger.info(" ✅ start of intra_inter_out_resolver_count_comp of ✅" , self.class_name)
        mismatch_flag =False
        temp_file_path2 = f2.name
        disamb_p_id_resolver = self.df_bri.select("disambiguated_p_id").distinct().count()
        logger.info('Count of Disambguited p_id in Resolver =', disamb_p_id_resolver)
        disamb_p_id_inter = self.df_inter.select("p_id_inter").count()
        TEST_CASE = "Validation of Count between Resolved Output and Resolver"
        desc_disamb = "Count of Disambguited p_id in final out"

        if disamb_p_id_resolver == disamb_p_id_inter:
            self.x.add_row([self.class_name, TEST_CASE, "PASS"])
        else:
            self.x.add_row([self.class_name, TEST_CASE, "FAIL"])
            mismatch_flag = True
            original_stdout = sys.stdout
            sys.stdout = f2 
            try :
                if disamb_p_id_resolver > disamb_p_id_inter:
                    print(desc_disamb, disamb_p_id_inter)
                    print('Count of Disambguited p_id in Reolver =', disamb_p_id_resolver)
                    additional_rows_resolver = (self.df_bri.select("disambiguated_p_id").distinct()).subtract(self.df_inter.select("p_id_inter"))
                    print(f"******************************{self.class_name}*****************************")
                    print(TEST_CASE)
                    print("Additional rows in Resolver Table:")
                    additional_rows_resolver.show(truncate=False)
                elif disamb_p_id_resolver < disamb_p_id_inter:
                    print(desc_disamb, disamb_p_id_inter)
                    print('Count of Disambguited p_id in Reolver =', disamb_p_id_resolver)
                    additional_rows_inter_out = self.df_inter.select("p_id_inter").subtract(self.df_bri.select("disambiguated_p_id").distinct())
                    print(f"******************************{self.class_name}*****************************")
                    print(TEST_CASE)
                    print("Additional rows in Inter Source Table:")
                    additional_rows_inter_out.show(truncate=False)  
            finally:
                sys.stdout = original_stdout                 
        logger.info(" ✅ end of intra_inter_out_resolver_count_comp of ✅", self.class_name)
        return temp_file_path2, mismatch_flag

    def intra_inter_union_resolver_count_comp(self, data, type, f2):
        mismatch_flag = False
        logger.info("✅ start of intra_inter_union_resolver_count_comp of ✅ " , self.class_name)
        rename_list = ['origin', 'primary_key', 'first_seen', 'last_seen', 'p_id', 'analysis_period_end','analysis_period_start', 'class', 'display_label', 'lifetime', 'recency', 'type','updated_at']
        updated_filter = None
        if 'filter' in data.get('output', {}):
            filter_expr = data['output']['filter']
            def update_filter_expression(expr, rename_fields):
                for field in rename_fields:
                    expr = re.sub(rf'\b{field}\b', f'{field}_inter', expr)
                return expr
            updated_filter = update_filter_expression(filter_expr, rename_list)
            logger.info(f" ✅Updated filter: {updated_filter}")
        if updated_filter:
            p_id = self.rf.filter(updated_filter).select("p_id_inp").distinct().count()
        else:
            p_id = self.rf.select("p_id_inp").distinct().count()
        if type == "inter":
            temp_file_path2, mismatch_flag = self._compare_fragments_and_union(f2, p_id)
        else:
            temp_file_path2, mismatch_flag = self._compare_resolver_and_union(f2, p_id)
        logger.info(" ✅ end of intra_inter_union_resolver_count_comp of ✅ " , self.class_name)
        return  temp_file_path2, mismatch_flag

    def _compare_fragments_and_union(self, f2, p_id):
        temp_file_path2 = f2.name
        mismatch_flag = False
        TEST_CASE = "Validation of Count between Fragments table and with Expected fragments output"
        p_id_frag = self.df_frag.select("p_id").distinct().count()
        desc_union = "Count of p_id in Union table"
        desc_resolver = "Count of p_id in Resolver table"
        if p_id_frag == p_id:
            self.x.add_row([self.class_name, TEST_CASE, "PASS"])
        else:
            mismatch_flag = True
            original_stdout = sys.stdout
            sys.stdout = f2
            self.x.add_row([self.class_name, TEST_CASE, "FAIL"])
            try:
                if p_id_frag > p_id:
                    additional_rows_resolver = (self.df_frag.select("p_id").distinct()).subtract(self.rf.select("p_id_inp").distinct())
                    self._report_mismatch( desc_resolver, p_id_frag, desc_union, p_id, additional_rows_resolver, self.class_name, TEST_CASE, "Fragments Table")
                elif p_id_frag < p_id:
                    additional_rows_union_out = self.rf.select("p_id_inp").distinct().subtract(self.df_frag.select("p_id").distinct())
                    self._report_mismatch( desc_resolver, p_id_frag, desc_union, p_id, additional_rows_union_out, self.class_name, TEST_CASE, "Expected Fragments Table")
            finally:
                sys.stdout = original_stdout
        return temp_file_path2, mismatch_flag

    def _compare_resolver_and_union(self, f2, p_id):
        temp_file_path2 = f2.name
        mismatch_flag = False
        TEST_CASE = "Validation of Count between Resolver table and with union of input table"
        p_id_resolver = self.df_bri.select("p_id").distinct().count()
        desc_union = "Count of p_id in Union table"
        desc_resolver = "Count of p_id in Resolver table"
        if p_id_resolver == p_id:
            self.x.add_row([self.class_name, TEST_CASE, "PASS"])
        else:
            self.x.add_row([self.class_name, TEST_CASE, "FAIL"])
            mismatch_flag = True
            original_stdout = sys.stdout
            sys.stdout = f2
            try:
                if p_id_resolver > p_id:
                    additional_rows_resolver = (self.df_bri.select("disambiguated_p_id").distinct()).subtract(self.rf.select("p_id_inp").distinct())
                    self._report_mismatch( desc_resolver, p_id_resolver, desc_union, p_id, additional_rows_resolver, self.class_name, TEST_CASE, "Resolver Table")
                elif p_id_resolver < p_id:
                    additional_rows_union_out = self.rf.select("p_id_inp").distinct().subtract(self.df_bri.select("disambiguated_p_id").distinct())
                    self._report_mismatch( desc_resolver, p_id_resolver, desc_union, p_id, additional_rows_union_out, self.class_name, TEST_CASE, "Union input Table")
            finally:
                sys.stdout = original_stdout
        return temp_file_path2, mismatch_flag

    def intra_inter_exception_other_matching_keys(self, f2):
        logger.info("✅ start of intra_inter_exception_other_matching_keys of  ✅", self.class_name)
        mismatch_flag = False
        temp_file_path2 = f2.name
        
        if not self.exception_filter:
            self.x.add_row([self.class_name, f"There is no exception filter for Class {self.class_name}", "PASS"])
            logger.info("✅ end of intra_inter_exception_other_matching_keys of ✅", self.class_name)
            return temp_file_path2, mismatch_flag
        
        for ex_key_name in self.exception_keys:
            condition_value = self._build_condition_for_exception_key(ex_key_name)
            temp_df = self.rf.filter(condition_value).select("count").distinct().sort("count")
            
            if temp_df.select(col("*")).count() == 0:
                self._add_pass_result(ex_key_name)
                continue
                
            mismatch_flag = self._process_exception_records(temp_df, ex_key_name, condition_value, f2, mismatch_flag)
        
        logger.info("✅ end of intra_inter_exception_other_matching_keys of ✅", self.class_name)
        return temp_file_path2, mismatch_flag
    
    def _build_condition_for_exception_key(self, ex_key_name):
        """Build the filter condition for a specific exception key"""
        # Build matching keys condition
        matching_conditions = []
        for key_name in self.candidate_key_list:
            if key_name != ex_key_name:
                matching_conditions.append(f"is_{key_name}_match_inp == True")
        
        matching_part = "(" + " or ".join(matching_conditions) + ")"
        
        # Build exception keys condition
        exception_conditions = []
        for ex_key_name_inner in self.exception_keys:
            if ex_key_name == ex_key_name_inner:
                exception_conditions.append(f"ex_{ex_key_name_inner}_inp == True")
            else:
                exception_conditions.append(f"ex_{ex_key_name_inner}_inp != True")
        
        exception_part = " and ".join(exception_conditions)
        
        # Combine conditions
        full_condition = f"{matching_part} and {exception_part}"
        return full_condition

    def _process_exception_records(self, temp_df, ex_key_name, condition_value, f2, mismatch_flag):
        """Process exception records and determine pass/fail status"""
        for index, row_iterator in temp_df.toPandas().iterrows():
            count_temp = (row_iterator[0], row_iterator[0])
            
            if count_temp == (1, 1):
                mismatch_flag = True
                self._add_fail_result_and_display(ex_key_name, condition_value, f2)
            else:
                self._add_pass_result(ex_key_name)
                break
        
        return mismatch_flag
    
    def _add_pass_result(self, ex_key_name):
        """Add a PASS result for the given exception key"""
        self.x.add_row([
            self.class_name,
            f"Records with exception filter for {ex_key_name} but disambiguated with other matching keys",
            "PASS"
        ])
    
    def _add_fail_result_and_display(self, ex_key_name, condition_value, f2):
        """Add a FAIL result and display the problematic records"""
        self.x.add_row([
            self.class_name,
            f"Records with exception filter for {ex_key_name} but disambiguated with other matching keys",
            "FAIL"
        ])
        
        self._display_exception_records(condition_value, f2)
    
    def _display_exception_records(self, condition_value, f2):
        """Display the exception records that failed disambiguation"""
        original_stdout = sys.stdout
        sys.stdout = f2
        
        try:
            exception_with_other_matching_keys = (
                self.rf.filter(condition_value)
                .filter("count = 1")
                .select("disambiguated_p_id", "p_id_inp")
                .sort("disambiguated_p_id")
            )
            
            print(f"******************************{self.class_name}*****************************")
            print("Records with an exception key but also have other matching candidate keys, but didn't get disambiguated")
            exception_with_other_matching_keys.show(100, truncate=False)
        finally:
            sys.stdout = original_stdout
            

    def intra_inter_exception_no_other_matching_keys(self, f2):
        logger.info(" ✅ start of intra_inter_exception_no_other_matching_keys of ✅", self.class_name)
        mismatch_flag = False
        temp_file_path2 = f2.name
        
        if not self.exception_filter:
            logger.info("There is no exception filter for Class")
            self.x.add_row([self.class_name, f"There is no exception filter for Class {self.class_name}", "PASS"])
            logger.info(" ✅ end of intra_inter_exception_no_other_matching_keys of ✅", self.class_name)
            return temp_file_path2, mismatch_flag
        
        for ex_key_name in self.exception_keys:
            condition_value = self._build_no_other_matching_condition(ex_key_name)
            
            temp_df = self.rf.filter(condition_value).select("count").distinct().sort("count")
            
            if temp_df.select(col("*")).count() == 0:
                self._add_no_other_matching_pass_result(ex_key_name)
                continue
                
            mismatch_flag = self._process_no_other_matching_records(temp_df, ex_key_name, condition_value, f2, mismatch_flag)
        
        logger.info(" ✅ end of intra_inter_exception_no_other_matching_keys of ✅", self.class_name)
        return temp_file_path2, mismatch_flag

    def _build_no_other_matching_condition(self, ex_key_name):
        """Build the filter condition for exception key with no other matching keys"""
        # Build non-matching keys condition
        non_matching_conditions = []
        for key_name in self.candidate_key_list:
            if key_name != ex_key_name:
                non_matching_conditions.append(f"is_{key_name}_match_inp != True")
        
        non_matching_part = "(" + " AND ".join(non_matching_conditions) + ")"
        
        # Build exception condition (only for the current exception key)
        exception_condition = f"ex_{ex_key_name}_inp == True"
        
        # Combine conditions
        full_condition = f"{non_matching_part} and {exception_condition}"
        return full_condition

    def _process_no_other_matching_records(self, temp_df, ex_key_name, condition_value, f2, mismatch_flag):
        """Process records with no other matching keys and determine pass/fail status"""
        for index, row_iterator in temp_df.toPandas().iterrows():
            count_temp = (row_iterator[0], row_iterator[0])
            
            if count_temp != (1, 1):
                mismatch_flag = True
                self._add_no_other_matching_fail_result_and_display(ex_key_name, condition_value, f2)
            else:
                self._add_no_other_matching_pass_result(ex_key_name)
                break
        
        return mismatch_flag

    def _add_no_other_matching_pass_result(self, ex_key_name):
        """Add a PASS result for exception key with no other matching keys"""
        self.x.add_row([
            self.class_name,
            f"Exception filter {ex_key_name} and no other matching keys is not disambiguated",
            "PASS"
        ])

    def _add_no_other_matching_fail_result_and_display(self, ex_key_name, condition_value, f2):
        """Add a FAIL result and display the problematic records"""
        self.x.add_row([
            self.class_name,
            f"Exception filter {ex_key_name} and no other matching keys is not disambiguated",
            "FAIL"
        ])
        
        self._display_no_other_matching_records(ex_key_name, condition_value, f2)

    def _display_no_other_matching_records(self, ex_key_name, condition_value, f2):
        """Display the exception records with no other matching keys that failed"""
        original_stdout = sys.stdout
        sys.stdout = f2
        
        try:
            exception_with_no_other_matching_keys = (
                self.rf.filter(condition_value)
                .filter("count != 1")
                .select("disambiguated_p_id", "p_id_inp")
                .sort("disambiguated_p_id")  # Fixed typo: "sor" -> "sort"
            )
            
            print(f"******************************{self.class_name}*****************************")
            print(f"Exception filter {ex_key_name} and no other matching keys is not disambiguated")
            exception_with_no_other_matching_keys.show(100, truncate=False)
            print(exception_with_no_other_matching_keys.collect())
        finally:
            sys.stdout = original_stdout
            
        
    def intra_inter_disamb_samekeys_one_p_id(self, f2):
        logger.info(" ✅ start of intra_inter_disamb_samekeys_one_p_id of ✅", self.class_name)
        mismatch_flag = False
        temp_file_path2 = f2.name
        
        original_stdout = sys.stdout
        try:
            sys.stdout = f2
            
            for key_name in self.candidate_key_list:
                key_mismatch_flag = self._process_candidate_key(key_name)
                if key_mismatch_flag:
                    mismatch_flag = True
                    break
            
            logger.info(" ✅end of intra_inter_disamb_samekeys_one_p_id of ✅", self.class_name)
        finally:
            sys.stdout = original_stdout
        
        return temp_file_path2, mismatch_flag

    def _process_candidate_key(self, key_name):
        """Process a single candidate key and return mismatch flag"""
        if self.exception_filter and key_name in self.exception_keys:
            return self._process_exception_key(key_name)
        else:
            return self._process_regular_key(key_name)

    def _process_exception_key(self, key_name):
        """Process a key that is in exception_keys"""
        filter_condition = f"ex_{key_name}_inp != True and {key_name}_inp is not null and {key_name}_inp != '' and length(trim({key_name}_inp)) > 0"
        message_suffix = f"in exception_keys: {key_name}"
        
        return self._check_key_disambiguation(key_name, filter_condition, message_suffix)

    def _process_regular_key(self, key_name):
        """Process a regular key (not in exception_keys or no exception filter)"""
        filter_condition = f"{key_name}_inp is not null and {key_name}_inp != '' and length(trim({key_name}_inp)) > 0"
        
        if self.exception_filter:
            message_suffix = f"not in exception_keys: {key_name}"
        else:
            message_suffix = f"exception filter condition false: {key_name}"
        
        return self._check_key_disambiguation(key_name, filter_condition, message_suffix)

    def _check_key_disambiguation(self, key_name, filter_condition, message_suffix):
        """Check if records with matching candidate keys are disambiguated to a single group"""
        filter_df = self._create_grouped_dataframe(filter_condition, key_name)
        
        for index, row_iterator in filter_df.toPandas().iterrows():
            count_temp = (row_iterator[1], row_iterator[1])
            
            if count_temp != (1, 1):
                self._add_fail_result_and_display(key_name, message_suffix, filter_condition)
                return True  # mismatch found
            else:
                self._add_pass_result(key_name, message_suffix)
                break
        
        return False  # no mismatch

    def _create_grouped_dataframe(self, filter_condition, key_name):
        """Create grouped dataframe with disambiguation count"""
        return (self.rf.filter(filter_condition)
                .groupby(f"{key_name}_inp")
                .agg(F.countDistinct("disambiguated_p_id").alias("con"))
                .sort("con", ascending=False)
                .distinct())

    def _add_pass_result(self, key_name, message_suffix):
        """Add a PASS result for the key disambiguation check"""
        self.x.add_row([
            self.class_name,
            f"Records with matching candidate keys are disambiguated to a single group for candidate key for {message_suffix}",
            "PASS"
        ])

    def _add_fail_result_and_display(self, key_name, message_suffix, filter_condition):
        """Add a FAIL result and display the problematic records"""
        self.x.add_row([
            self.class_name,
            f"Records with matching candidate keys are disambiguated to a single group for candidate key for {message_suffix}",
            "FAIL"
        ])
        
        self._display_disambiguation_results(filter_condition, key_name)

    def _display_disambiguation_results(self, filter_condition, key_name):
        """Display the disambiguation results for failed cases"""
        result = (self.rf.filter(filter_condition)
                .groupby(f"{key_name}_inp")
                .agg(F.countDistinct("disambiguated_p_id").alias("con"))
                .sort("con", ascending=False)
                .distinct())
        
        result.show(100, truncate=False) 


    def intra_inter_aggregate(self, data, f2):
        logger.info(" ✅ start pf intra_inter_aggregate of  ✅ " , self.class_name)            
        mismatch_flag = False
        w = Window.partitionBy("p_id_inter")
        aggregation = data["disambiguation"]["strategy"]["aggregation"]
        min_agg_fields = [item["field"] for item in aggregation if item["function"] == "min"]
        max_agg_fields = [item["field"] for item in aggregation if item["function"] == "max"]

        def validate_fields(agg_fields, df_cols):
            valid_fields = []
            for field in agg_fields:
                if f"{field}_inp" in df_cols:
                        valid_fields.append(field)
                else:
                        logger.info(field , " is not present")
            return valid_fields
        df_cols = self.rf.columns

        min_agg_fields = validate_fields(min_agg_fields, df_cols)
        max_agg_fields = validate_fields(max_agg_fields, df_cols)    

        logger.info("Min Aggregation Fields: after", min_agg_fields)
        logger.info("Max Aggregation Fields: after", max_agg_fields)
        case_desc_min = "Min of Aggregation field"
        temp_file_path2 = f2.name
        original_stdout = sys.stdout
        try:
            sys.stdout = f2   
            for key_name in min_agg_fields:
                self.rf = self.rf.withColumn(f'{key_name}_test', F.min(f'{key_name}_inp').over(w))
                filter_df= self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter")
                if filter_df.select(col("*")).count() != 0:
                    self.x.add_row([self.class_name,f"{case_desc_min} {key_name}","FAIL"])
                    mismatch_flag = True
                    self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}").show(truncate=False)
                    result = self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}")
                    result.show(100,truncate=False)
                else:
                    self.x.add_row([self.class_name,f"{case_desc_min} {key_name}","PASS"])
                    self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}").show(truncate=False)
            case_desc_max = "Max of Aggregation field"
            for key_name in max_agg_fields:
                self.rf = self.rf.withColumn(f'{key_name}_test', F.max(f'{key_name}_inp').over(w))
                filter_df= self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter")
                if filter_df.select(col("*")).count() != 0:
                    self.x.add_row([self.class_name,f"{case_desc_max} {key_name}","FAIL"])
                    mismatch_flag = True
                    self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}").show(truncate=False)
                    result = self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}")
                    result.show(100,truncate=False)
                else:
                    self.x.add_row([self.class_name,f"{case_desc_max} {key_name}","PASS"])  
                    logger.info(f"{case_desc_max} {key_name}")               
                    self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}").show(truncate=False)
            logger.info(" ✅end of intra_inter_aggregate of ✅" , self.class_name)
        finally:
            sys.stdout = original_stdout
        return  temp_file_path2,  mismatch_flag

    def intra_inter_out_p_id_check(self, f2):
        logger.info(" ✅ start of p_id check of ✅" , self.class_name)
        temp_file_path2 = f2.name
        original_stdout = sys.stdout
        try:
            sys.stdout = f2   
            mismatch_flag = False
            logger.info("start of p_id check")
            inter_out_count = self.df_inter.count()
            inter_p_id_count = self.df_inter.select("p_id_inter").distinct().count()
            TEST_CASE_NAME = "Validation of p_id Uniqueness"
            if inter_out_count == inter_p_id_count:
                self.x.add_row([ self.class_name, TEST_CASE_NAME, "PASS"])
            else:
                self.x.add_row([ self.class_name, TEST_CASE_NAME, "FAIL"])
                mismatch_flag = True
                if inter_out_count != inter_p_id_count:
                    duplicate_p_id_rows_inter = (self.df_inter.groupby("p_id_inter").agg(F.count("p_id_inter").alias("con")).filter("con!=1").sort("con",ascending=False).show(truncate=False))
                    print(f"******************************{self.class_name}*****************************")
                    print("Validation of p_id Uniqueness")
                    print("Duplicate p_id's in Intra output:")
                    duplicate_p_id_rows_inter.show(truncate=False)
            logger.info(" ✅ end of p_id check of ✅ " , self.class_name)
        finally:
            sys.stdout = original_stdout
        return  temp_file_path2,  mismatch_flag

    def intra_inter_out_p_id_primary_key_display_label_check(self, f2):
        logger.info(" ✅ start of  intra_inter_out_p_id_primary_key_display_label_check ✅" , self.class_name)
        mismatch_flag = False
        mismatch_flag_disp_label = False
        mismatch_flag_primary_key = False
        mismatch_flag_p_id = False
        temp_file_path2 = f2.name
        original_stdout = sys.stdout
        try:
            sys.stdout = f2   
            # Validate p_id is not null and Validate that the primary_key is not null#
            logger.info("start of p_id check")
            filter_val = "p_id_inter is null or trim(p_id_inter)=='' or p_id_inter RLIKE '^\\s*$'"
            p_id_is_null = self.df_inter.filter(filter_val).select("p_id_inter").count()
            TEST_CASE_NAME = "Validate that p_id is not null"
            if p_id_is_null == 0:
                self.x.add_row([ self.class_name, TEST_CASE_NAME, "PASS"])
            else:
                self.x.add_row([ self.class_name, TEST_CASE_NAME, "FAIL"])
                mismatch_flag_p_id = True
                self.df_inter.filter(filter_val).select("p_id_inter").show(truncate=False)
                p_id_null = (self.df_inter.filter(filter_val).select("p_id_inter", "class","primary_key","origin"))
                print(f"******************************{self.class_name}*****************************")
                print("Validate that p_id is not null")
                print(" Blank p_id's:")
                p_id_null.show(truncate=False)
            # Validate primary_key is not null
            logger.info("✅ start of primary key  check ✅")
            primary_key_is_null = self.df_inter.filter("primary_key is null or trim(primary_key)=='' or primary_key RLIKE '^\\s*$'").select("p_id_inter").count()
            TEST_CASE_NAME = "Validate that primary_key is not null"
            if primary_key_is_null == 0:
                self.x.add_row([ self.class_name, TEST_CASE_NAME, "PASS"])
            else:
                self.x.add_row([ self.class_name, TEST_CASE_NAME, "FAIL"])
                mismatch_flag_primary_key = True
                p_id_null = (self.df_inter.filter("primary_key is null or trim(primary_key)=='' or primary_key RLIKE '^\\s*$'").select("p_id_inter", "class", "primary_key", "origin"))
                print(f"****************************** {self.class_name}*****************************")
                print("Validation of primary_key is not null")
                print("Blank primary_key's:")
                p_id_null.show(truncate=False)
            # Validate display_label is not null
            logger.info("start of display label check")
            display_label_is_null = self.df_inter.filter("display_label is null or trim(display_label)=='' or display_label RLIKE '^\\s*$'").select("p_id_inter").count()
            TEST_CASE_NAME = "Validate that display_label is not null"
            if display_label_is_null == 0:
                self.x.add_row([ self.class_name, TEST_CASE_NAME, "PASS"])
            else:
                self.x.add_row([ self.class_name, TEST_CASE_NAME, "FAIL"])
                mismatch_flag_disp_label = True
                p_id_null = (self.df_inter.filter("display_label is null or trim(display_label)=='' or display_label RLIKE '^\\s*$'").select("p_id_inter", "class", "display_label", "origin"))
                print(f"****************************** {self.class_name}*****************************")
                print("Validation of display_label is not null")
                print("Blank display_label's:")
                p_id_null.show(truncate=False)
            if(mismatch_flag_disp_label == mismatch_flag_primary_key == mismatch_flag_p_id == False):   
                mismatch_flag = False
            else:
                mismatch_flag = True
            logger.info(" ✅end of intra_inter_out_p_id_primary_key_display_label_check ✅", self.class_name)
        finally:
            sys.stdout = original_stdout
        return  temp_file_path2, mismatch_flag

    def lifetime_recency_recent_activity_observed_lifetime_negative_check(self, f2):
        logger.info("✅ start of lifetime_recency_recent_activity_observed_lifetime_negative_check ✅" , self.class_name)
        temp_file_path2 = f2.name
        original_stdout = sys.stdout
        try:
            sys.stdout = f2   
            mismatch_flag = False
            neg_counter = self.df_inter.filter("recency_inter<0 or lifetime_inter<0 or recent_activity<0 or observed_lifetime<0").count()
            TEST_CASE_NAME = "Validation of lifetime recency recent_activity and observed_lifetime negative values"
            if neg_counter == 0:
                self.x.add_row([ self.class_name, TEST_CASE_NAME, "PASS"])
                logger.info(TEST_CASE_NAME, "PASS")
            else:
                self.x.add_row([ self.class_name, TEST_CASE_NAME, "FAIL"])
                mismatch_flag = True
                negative_values = (self.df_inter.groupby("p_id_inter").agg(F.count("p_id_inter").alias("con")).filter("recency_inter<0 or lifetime_inter<0 or recent_activity_inter<0 or observed_lifetime_inter<0").sort("con",ascending=False).show(truncate=False))
                print(f"******************************{self.class_name}*****************************")
                print("Validation of lifetime, recency, recent_activity and observed_lifetime negative values")
                print("Negative values in output:")
                negative_values.show(truncate=False)
            logger.info(" ✅end of lifetime_recency_recent_activity_observed_lifetime_negative_check ✅", self.class_name)
        finally:
            sys.stdout = original_stdout
        return temp_file_path2, mismatch_flag

    def intra_inter_disamb_at_least_matching_key_in_a_group(self, f2):
        logger.info("✅start of intra_inter_disamb_at_least_matching_key_in_a_group ✅", self.class_name)
        mismatch_flag = False
        temp_file_path2 = f2.name
        
        original_stdout = sys.stdout
        try:
            sys.stdout = f2
            
            # Add window functions for all candidate keys
            self._add_window_functions_for_keys()
            
            # Build and apply the join condition
            join_condition = self._build_join_condition()
            self.rf = self.rf.withColumn("is_join", expr(join_condition))
            
            # Check for disambiguation failures
            mismatch_flag = self._check_disambiguation_failures()
            
            logger.info(" ✅ end of intra_inter_disamb_at_least_matching_key_in_a_group ✅", self.class_name)
        finally:
            sys.stdout = original_stdout
        
        
        return temp_file_path2, mismatch_flag

    def _add_window_functions_for_keys(self):
        """Add lag and lead window functions for all candidate keys"""
        for key_name in self.candidate_key_list:
            window_spec = Window.partitionBy("disambiguated_p_id").orderBy(f"{key_name}_inp", "last_found_date_inp")
                # Add lag and lead columns
            self.rf = self.rf.withColumn(f"lag_{key_name}_joined", lag(f"{key_name}_inp", 1).over(window_spec))
            self.rf = self.rf.withColumn(f"lead_{key_name}_joined", lead(f"{key_name}_inp", 1).over(window_spec))
                # Add joined indicator column
            join_condition = self._create_key_join_condition(key_name)
            self.rf = self.rf.withColumn(f"is_{key_name}_joined", expr(join_condition))

    def _create_key_join_condition(self, key_name):
        """Create the join condition for a specific key"""
        return (f"CASE WHEN (length(trim({key_name}_inp))>0 and {key_name}_inp is not null and "
                f"{key_name}_inp == lag_{key_name}_joined) or "
                f"(length(trim({key_name}_inp))>0 and {key_name}_inp is not null and "
                f"{key_name}_inp == lead_{key_name}_joined) THEN True ELSE False END")

    def _build_join_condition(self):
        """Build the overall join condition for all candidate keys"""
        join_conditions = []
        for key_name in self.candidate_key_list:
            if self.exception_filter and key_name in self.exception_keys:
                condition = f"(is_{key_name}_joined == True and ex_{key_name}_inp != True)"
            else:
                condition = f"is_{key_name}_joined == True"
            join_conditions.append(condition)
        # Combine all conditions with OR
        combined_condition = " or ".join(join_conditions)
        return combined_condition

    def _check_disambiguation_failures(self):
        """Check for records that don't belong to a disambiguated group"""
        temp_df = self.rf.filter("count!=1").select("is_join").sort("is_join").distinct()
        test_case = "No records found which doesn't belong to a disambiguated group"
        
        if temp_df.select(col("*")).count() == 0:
            self.x.add_row([self.class_name, test_case, "PASS"])
            return False
        
        return self._process_disambiguation_results(temp_df, test_case)

    def _process_disambiguation_results(self, temp_df, test_case):
        """Process the disambiguation results and determine pass/fail"""
        for index, row_iterator in temp_df.toPandas().iterrows():
            count_temp = (row_iterator[0], row_iterator[0])
            
            if count_temp != (True, True):
                self.rf.filter("count!=1 and is_join=False").select("disambiguated_p_id", "p_id_inp").sort("disambiguated_p_id").distinct().show(100, truncate=False)
                self.x.add_row([self.class_name, test_case, "FAIL"])
                return True  # mismatch found
            else:
                self.x.add_row([self.class_name, test_case, "PASS"])
                break
        
        return False  # no mismatch


    def generate_report(self, temp_file_path2):
        logger.info("Entered report generation function")
        current_directory = os.getcwd()
        folder_name = 'output'
        folder_path = os.path.join(current_directory, folder_name)
        os.makedirs(folder_path, exist_ok=True)

        pass_file = os.path.join(folder_path, f'disambuigaution_status_{self.class_name}.txt')
        fail_file = os.path.join(folder_path, f'disambuigaution_failed_out_{self.class_name}.txt')

        logger.info("✅ Report generation started ✅")
        logger.info(str(self.x))
        # Write the PrettyTable summary to pass_file
        with open(pass_file, "w") as f:
            f.write(str(self.x))

        # Copy the detailed failure output (captured in temp_file_path2) to fail_file
                    
        if temp_file_path2:
            shutil.copy(temp_file_path2, fail_file)
        
        if temp_file_path2 and os.path.exists(temp_file_path2):
        # 🔹 Show contents in logger
            with open(temp_file_path2, "r") as f:
                contents = f.read()
                logger.info("📄 Detailed mismatch output\n" + contents)

        
        logger.info("✅ Report generation completed ✅")

                

