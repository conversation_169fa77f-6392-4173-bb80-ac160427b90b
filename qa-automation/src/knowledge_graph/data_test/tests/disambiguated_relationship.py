from pyspark.sql.functions import expr, lit, split, to_timestamp, unix_timestamp, explode_outer, col, lag, lead

class DisambiguatedRelationshipValidation:

    def __init__(self, spark, config):

        table_list = config['inp_table_list']
        schema_name = config['schema_name']
        updated_at = config['updated_at']

        for df_name, table_name in table_list.items():
            df_name = spark.sql(f"select * from iceberg_catalog.{schema_name}.{table_name}")

        df = df.unionByName(df_name, allowMissingColumns=True)

    def relationship_id_uniqueness_check(self):
        total_cnt = self.rel.count()
        distinct_count = self.rel.select("relationship_id").distinct().count()
        if(total_cnt == 0 or distinct_count == 0):
            print("Not-Executed")
        elif(total_cnt == distinct_count):
            print('Passed')
        else:
            print("Failed")

        return total_cnt, distinct_count

    def non_negative_values_for_recency_and_lifetime_check(self):
        neg_counters = self.rel.filter("recency<0 or lifetime<0").count()
        if(neg_counters == 0):
            print("Passed")
        else:
            print('Failed')

        return neg_counters

    def block_variable_uniqueness_check(self, rel_config):
        block_variable = [item + '_exp' for item in rel_config['block_variable']]
        exp_distinct_count = self.rel_exp.select(*block_variable).distinct().count()
        block_variable = [item for item in rel_config['block_variable']]
        rel_distinct_counts = self.rel.select(*block_variable).distinct().count()
        total_cnt = self.rel.count()
        if(total_cnt == exp_distinct_count == rel_distinct_counts):
            print("Passed")
        else:
            print('Failed')

        return total_cnt, exp_distinct_count, rel_distinct_counts

    def rel_name_and_inv_rel_name_check(self):
        not_matching_counts = self.comp.filter("relationship_name_compare = 'not matching' or inverse_relationship_name_compare = 'not matching'").count()
        if(not_matching_counts == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_counts

    def rel_name_and_inv_rel_name_check(self):
        not_matching_counts = self.comp.filter("relationship_name_compare = 'not matching' or inverse_relationship_name_compare = 'not matching'").count()
        if(not_matching_counts == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_counts
    
    def first_seen_and_last_seen_check(self):
        not_matching_counts = self.comp.filter("relationship_first_seen_date_compare = 'not matching' or relationship_last_seen_date_compare = 'not matching'").count()
        if(not_matching_counts == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_counts
    
    def relationship_origin_source_entity_class_and_target_entity_class_check(self):
        not_matching_counts = self.comp.filter("relationship_origin_compare = 'not matching' or source_entity_class_compare = 'not matching' or target_entity_class_compare = 'not matching'").count()
        if(not_matching_counts == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_counts
    
    def source_display_label_target_display_label_check(self):
        not_matching_counts = self.comp.filter("source_display_label_compare = 'not matching' or target_display_label_compare = 'not matching'").count()
        if(not_matching_counts == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_counts
    
    def common_relationship_fields_check(self):
        not_matching_counts = self.comp.filter("start_epoch_compare = 'not matching' or end_epoch_compare = 'not matching' or lifetime_relationship_compare = 'not matching' or recency_relationship_compare = 'not matching'").count()
        if(not_matching_counts == 0):
            print("Passed")
        else:
            print('Failed')

        return not_matching_counts

    def optional_attributes_check(self, rel_config):
        master_not_matching_counts = 0
        optional_attributes_present = False

        if(len(rel_config['optional_attributes']) != 0):
            for i in (rel_config['optional_attributes']):
                not_matching_counts = self.comp.filter(f"{i}_compare = 'not matching'").count()
                master_not_matching_counts = master_not_matching_counts + int(not_matching_counts)
            optional_attributes_present = True

        if(optional_attributes_present == False):
            print("Not-Executed")
        elif(master_not_matching_counts == 0):
            print("Passed")
        else:
            print('Failed')

        return master_not_matching_counts, optional_attributes_present
