from prettytable import PrettyTable
from pyspark.sql.window import Window
from pyspark.sql.functions import lit, expr, col, concat_ws, sha2, concat, date_format
from pyspark.sql.types import StructType
from io import StringIO
import tempfile
import sys
import pyspark.sql.functions as F
import pandas as pd
import numpy as np
import re
from src.utilities.data_test.common import Common
from src.utilities.common.common_utilities import CommonUtilities
from pyspark.sql import Row
import os
import sys
from datetime import datetime, timedelta, time, timezone
import pytz
import fnmatch
from src.utilities.common.readProperties import ReadConfig 
from src.utilities.common.api_utils import APIUtils 
from core.api.api_client import APIClient
from core.common.logging_utils import setup_logger
import traceback
import logging
import json
import requests
logger = setup_logger(__name__)

collector = CommonUtilities()
sys.stdout = collector
readConfig = ReadConfig()
api_utils= APIUtils()
apiclient = APIClient()


class KnowledgeGraphValidation:

    def handle_webdriver_exception(self, exception):
        error_msg = str(exception).split('\n')[0]  # Get just the first line of error
        tb = traceback.format_exc().split('Stacktrace:')[0]  # Get Python traceback without Selenium stacktrace
        logger.error(f"Error occurred: {error_msg}\nTraceback:\n{tb}")
        raise exception
        
        
       
    def fragment_count_comp(self, spark, deployement_config,inter_config):
        with tempfile.NamedTemporaryFile(mode='a', delete=False) as f1, tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
            mismatch_flag = False
            original_stdout = sys.stdout            
            schema_frag =  inter_config['output']['fragmentLocation'].split('.')[0]
            inter_table = deployement_config['config_value']['spark_job_configs']['source_models']['intersource_disambiguated_models']        
            logger.info(f"Retrieved fragment schema: {schema_frag}")
            logger.info(f"Retrieved inter_table list: {inter_table}")
            TEST_CASE = "Validation of Count between Union of Loader/Intra and the Fragment output"
            updated_at = Common.UPDATED_AT
            #updated_at= '1723118399999'
            logger.info("Updated at received is:::")
            logger.info(updated_at)
            query = f"SHOW TABLES IN iceberg_catalog.{schema_frag}"
            logger.info(f"Executing SQL Query: {query}")
            available_tables = [ row.tableName for row in spark.sql(query).collect()]
            logger.info(f"Available tables in schema: {available_tables}")
            count =0
            for table in inter_table:
                logger.info(f"Processing table: {table}")
                result = re.search(r"(?<=sds_ei__).*", table)
                entity = result.group().strip() 
                logger.info(f"Extracted entity: {entity}")   
                fragment_table_name = f'sds_ei__fragment__{entity}'
                resolver_table_name = f'sds_ei__resolver__{entity}'
                logger.info(f"Checking for tables: {fragment_table_name}, {resolver_table_name}")                
                if fragment_table_name in available_tables and resolver_table_name in available_tables :
                    logger.info(f"Tables {fragment_table_name} and {resolver_table_name} found. Proceeding with count validation.")
                    df_frag = spark.sql(f"SELECT * FROM iceberg_catalog.{schema_frag}.{fragment_table_name} WHERE updated_at = {updated_at}")
                    df_resolver =  spark.sql(f"SELECT * FROM iceberg_catalog.{schema_frag}.{resolver_table_name} WHERE updated_at = {updated_at}")
                    frag_count = df_frag.count()
                    resolver_count = df_resolver.count()
                    logger.info(f"{entity} - Fragment Count: {frag_count}, Resolver Count: {resolver_count}")
                    if count==0 :                        
                        if (frag_count == resolver_count):
                            #print(f"{entity} - Resolver Count: {resolver_count}, Fragment Count: {frag_count} - PASS")
                            data = [Row(CLASS=entity, TEST_CASE=TEST_CASE, STATUS="PASS")]
                            out_txt = spark.createDataFrame(data)
                            count=count +1
                            
                        else:
                            data = [Row(CLASS=entity, TEST_CASE=TEST_CASE, STATUS="FAIL")]
                            print(f"{entity} - Resolver Count: {resolver_count}, Fragment Count: {frag_count} - FAIL")
                            out_txt = spark.createDataFrame(data)
                            count=count +1
                            mismatch_flag = True    
                    else :
                        if (frag_count == resolver_count):
                            #print(f"{entity} - Resolver Count: {resolver_count}, Fragment Count: {frag_count} - PASS")
                            out_txt = CommonUtilities.add_row(spark,out_txt, {"CLASS": entity, "TEST_CASE": TEST_CASE, "STATUS": "PASS"})
                        else:
                            print(f"{entity} - Resolver Count: {resolver_count}, Fragment Count: {frag_count} - FAIL")
                            out_txt = CommonUtilities.add_row(spark,out_txt, {"CLASS": entity, "TEST_CASE": TEST_CASE, "STATUS": "FAIL"})
                            mismatch_flag = True  
            
            
            sys.stdout = original_stdout
            fail_messages = collector.get_content()
                          

        return  mismatch_flag, out_txt, fail_messages
    

    def publish_count(self, spark, deployement_config, publish_config,out_txt, fail_messages):
        try:
            original_stdout = sys.stdout 
            # schema_publish = 'kg_publish_edm_sit'
            schema_publish=publish_config['outputTableInfo']['outputTableName'].split('.')[0]
            publish_table = deployement_config['config_value']['spark_job_configs']['source_models']['publisher']
            logger.info(f"Retrieved entity publish table list: {publish_table}")
            query=f"SHOW TABLES IN iceberg_catalog.{schema_publish}"
            logger.info(f"Executing SQL Query: {query}")
            available_tables = [row.tableName for row in spark.sql(query).collect()]
            logger.info(f"Available tables in schema: {available_tables}")
            updated_at = '1723118399999'  
            # updated_at = Common.UPDATED_AT
            TEST_CASE = 'Publish Table Count'
            mismatch_flag = False  
            for table in publish_table:
                if table in available_tables:
                    logger.info(f"Tables {table} found. Proceeding with count validation.")
                    df_publish = spark.sql(f"SELECT * FROM iceberg_catalog.{schema_publish}.{table} WHERE updated_at = {updated_at}")
                    publish_count = df_publish.count()

                    if publish_count > 0:
                        logger.info("publish count is non zero")
                        out_txt = CommonUtilities.add_row(spark,out_txt, {"TABLE": table, "TEST_CASE": TEST_CASE, "STATUS": "PASS"})
                    else:
                        logger.info("publish count is zero")
                        out_txt = CommonUtilities.add_row(spark,out_txt, {"TABLE": table, "TEST_CASE": TEST_CASE, "STATUS": "FAIL"})
                        mismatch_flag = True  
                else:
                    logger.info(f"Table {table} not found")
                    out_txt = CommonUtilities.add_row(spark,out_txt, {"TABLE": table, "TEST_CASE": TEST_CASE, "STATUS": "FAIL"})
                    mismatch_flag = True 
            sys.stdout = original_stdout 
            fail_messages_new = collector.get_content()
            logger.info(f"fail_messages: {fail_messages}")
            logger.info(f"fail_messages_new: {fail_messages_new}")
            # fail_messages += "\n" + "".join(fail_messages_new)
            fail_messages.extend(fail_messages_new)
            logger.info(f"fail_messages::::{fail_messages}")
            return mismatch_flag, out_txt, fail_messages
        except Exception as e:
            logger.error("Exception occurred in publish_count", exc_info=True)
            return True, out_txt, [f"Error processing the DataFrame: {e}"]     
               
    
    def generate_report(self,out_txt,fail_messages):
        
        logger.info("Entered report generation function")
        current_directory = os.getcwd()
        folder_name = 'output'
        folder_path = os.path.join(current_directory, folder_name)
        logger.info(f"Current directory: {current_directory}")
        logger.info(f"Output folder path: {folder_path}")
        os.makedirs(folder_path, exist_ok=True)
        
        # Define file paths
        pass_file = os.path.join(folder_path, 'pass.txt')
        fail_file = os.path.join(folder_path, 'fail.txt')
        
        #FAIl File write       
        with open(fail_file, "w") as f:
                f.write("\n".join(fail_messages) + "\n")


    def graph_properties_val(self, spark, context):
        validator = GraphPropertiesValidator(spark)
        mismatch_flag, pass_output, fail_output = validator.run_validation(spark)
        return mismatch_flag, pass_output, fail_output
    
    # For Loaders - Check if Temporary properties exist and get the fields & expressions if yes
    def get_temp_props(self, config_data, loader_name=None):
        try:
            logger.info("\n\n:::Checking for temporaryProperties in config_data:::")
            temp_props = config_data.get("config_value", {}).get("temporaryProperties", None)

            if not temp_props:
                logger.info(f"XXX No temporaryProperties found in the config for loader '{loader_name}' XXX")
                return {}

            temp_dict = {
                prop.get("colName"): prop.get("colExpr")
                for i, prop in enumerate(temp_props)
                if "colName" in prop and "colExpr" in prop
            }
            return temp_dict

        except Exception as e:
            self.handle_webdriver_exception(e)

    # To create the column event_timestamp_epoch in the SRDM when override is present
    def create_event_timestamp_col(self, spark, srdm_schema_name, srdm_table_name, col_expr):
        try:
            full_table_path = f"iceberg_catalog.{srdm_schema_name}.{srdm_table_name}"
            logger.info(f"\n\n***Reading full SRDM table: {full_table_path}\n")

            srdm = spark.sql(f"SELECT * FROM {full_table_path}")
            # Check for count to ensure records are there to begin with
            srdm_count = srdm.count()
            logger.info(f"\n\nSRDM total count before any filtrations: {srdm_count}")

            srdm_with_override = srdm.withColumn("event_timestamp_epoch_override", expr(col_expr))
            # Convert epoch (ms) to timestamp and store in a new column
            srdm_with_override = srdm_with_override.withColumn("event_timestamp_override_ts", expr("to_timestamp(event_timestamp_epoch_override / 1000)"))
            srdm_with_override.createOrReplaceTempView("srdm_with_override")

            # To preview event_timestamp_epoch_override values:
            distinct_values = srdm_with_override.select("event_timestamp_epoch_override").distinct().limit(5).collect()
            logger.info("\nDistinct 'event_timestamp_epoch_override' values (sample):")
            for row in distinct_values:
                logger.info(f"{row['event_timestamp_epoch_override']}")

            logger.info(f"\n'\nevent_timestamp_epoch_override' column created successfully for table: {full_table_path}")
            use_override_col = True

            return use_override_col,srdm_with_override

        except Exception as e:
            logger.error(f"Error while creating 'event_timestamp_epoch_override' column: {e}")
            self.handle_webdriver_exception(e)

    def set_srdm_and_loader_schema(self):
        # to give the srdm and loader schema
        return "srdm_edm_sit", "kg_edm_sit"

    def extract_table_name_from_config(self, unformatted_srdm_name):
        """
        Extracts the table name from a schema-qualified table string like:
        '<%SCHEMA%>.table_name' → 'table_name'
        """
        return unformatted_srdm_name.split('.')[-1] if '.' in unformatted_srdm_name else unformatted_srdm_name    

    def get_timestamp_filters(self, spark, loader_path):
        try:
            loader_df = spark.sql(f"SELECT MAX(updated_at_ts) as max_ts FROM {loader_path}").first()
            if not loader_df:
                raise ValueError(f"Could not retrieve updated_at_ts from {loader_path}")

            max_ts = loader_df["max_ts"]
            if max_ts is None:
                raise ValueError(f"No 'updated_at_ts' found in {loader_path}")
            # Ensure timestamp is timezone-aware in UTC
            if max_ts.tzinfo is None or max_ts.tzinfo.utcoffset(max_ts) is None:
                max_ts = max_ts.replace(tzinfo=timezone.utc)
            # Convert to IST
            ist = pytz.timezone('Asia/Kolkata')
            max_ts_ist = max_ts.astimezone(ist)

            # Set event timestamp to 23:59:59.999 IST on previous day
            event_timestamp_dt = datetime.combine(
            max_ts_ist.date() - timedelta(days=1), time(23, 59, 59, 999000), tzinfo=ist
            )

            # Set parsed_interval_ts to next day 06:00:00.000 IST from event timestamp
            parsed_interval_ts_dt = datetime.combine(
                event_timestamp_dt.date() + timedelta(days=1), time(6, 0, 0), tzinfo=ist
            )

            # Format to string
            event_timestamp_value = event_timestamp_dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            parsed_interval_ts_value = parsed_interval_ts_dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

            logger.info(f"\n\nEvent timestamp (IST): {event_timestamp_value}")
            logger.info(f"Parsed interval timestamp (IST): {parsed_interval_ts_value}")

            return event_timestamp_value, parsed_interval_ts_value

        except Exception as e:
            logger.error(f"Error fetching max timestamps from loader: {e}")
            raise


    def read_srdm_w_date_filter(
        self, spark,
        srdm_override_table : str,
        loader_config_data: dict,
        srdm_schema_name: str,
        loader_schema_name: str,
        use_override_col: bool):
        """
        Reads SRDM and loader tables with a date-based filter.
        Returns: Tuple[DataFrame, DataFrame]: Filtered SRDM and loader Spark DataFrames.
        """
        
        unformatted_srdm = loader_config_data['config_value']["dataSource"]["srdm"]
        unformatted_loader = loader_config_data['config_value']["outputTable"]

        srdm_table_name = self.extract_table_name_from_config(unformatted_srdm)
        loader_table_name = self.extract_table_name_from_config(unformatted_loader)

        loader_path = f"iceberg_catalog.{loader_schema_name}.{loader_table_name}"

        srdm_override_table  = srdm_override_table if use_override_col else f"iceberg_catalog.{srdm_schema_name}.{srdm_table_name}"

        timestamp_col = "event_timestamp_override_ts" if use_override_col else "event_timestamp_ts"

        event_timestamp_value, parsed_interval_ts_value = self.get_timestamp_filters(spark, loader_path)
        filter_expr_srdm = f"{timestamp_col} <= '{event_timestamp_value}' AND parsed_interval_timestamp_ts <= '{parsed_interval_ts_value}'"
        filter_expr_loader = f"updated_at_ts = '{event_timestamp_value}'"

        logger.info(f"\n\nReading SRDM with date filter: {filter_expr_srdm}")
        logger.info(f"SRDM path: {srdm_override_table}")
        logger.info(f"Loader path: {loader_path}")
        logger.info(f"use_override_col: {use_override_col}")

        # Log the SQL queries before execution
        logger.info(f"\n\n---SRDM Query: SELECT * FROM {srdm_override_table} WHERE {filter_expr_srdm}")
        logger.info(f"\n\n---Loader Query: SELECT * FROM {loader_path} WHERE {filter_expr_loader}")
        
        # Execute queries with timestamp filters
        srdm_filtered = spark.sql(f"SELECT * FROM {srdm_override_table} WHERE {filter_expr_srdm}")
        loader_filtered = spark.sql(f"SELECT * FROM {loader_path} WHERE {filter_expr_loader}")

        logger.info("\n\n***Date-based filter applied and tables read successfully.")

        return srdm_filtered, loader_filtered

    def add_primary_key_col_to_srdm(self, srdm_df, loader_config, loader_temp_props):
        """
        Adds 'primary_key_test' column to the SRDM DataFrame based on the expression
        provided in loader_config['config_value']['primaryKey'].

        Conditions:
        - If the expression uses temp props, those are added as columns first.
        - If it's a direct column reference, use col("column_name").
        - Otherwise, use expr(primary_key_expr).
        """
        primary_key_expr = loader_config["config_value"].get("primaryKey", "").strip()
        temp_props_keys = list(loader_temp_props.keys())

        logger.info(f"\n\n=== Primary Key Expression ===\n{primary_key_expr}\n")

        if not primary_key_expr:
            raise ValueError("Primary key expression is empty or missing in loader config.")

        # Case 1: If the expression contains any of the temp prop keys
        used_temp_props = [k for k in temp_props_keys if k in primary_key_expr]
        if used_temp_props:
            for temp_col in used_temp_props:
                srdm_df = srdm_df.withColumn(temp_col, expr(loader_temp_props[temp_col]))
            srdm_df = srdm_df.withColumn("primary_key_test", expr(primary_key_expr))
            logger.info(f"Primary key expression uses temp props: {used_temp_props}")

        # Case 2: If the expression directly maps to one existing column name
        elif primary_key_expr in srdm_df.columns:
            srdm_df = srdm_df.withColumn("primary_key_test", col(primary_key_expr))
            logger.info(f"Primary key expression directly maps to column: {primary_key_expr}")

        # Case 3: Expression not covered by above — treat as a Spark SQL expression
        else:
            srdm_df = srdm_df.withColumn("primary_key_test", expr(primary_key_expr))
            logger.info(f"Primary key derived using Spark SQL expression: {primary_key_expr}")

        logger.info("\n\nApplying primary key expression on DataFrame alias: srdm_filtered_df")
        logger.info(f"\n\nwithColumn('primary_key_test', expr(\"\"\"{primary_key_expr}\"\"\"))")
        
        # Show sample values using logging
        sample_keys = srdm_df.select("primary_key_test").distinct().limit(5).collect()
        logger.info("Sample 'primary_key_test' values:")
        if not sample_keys:
            logger.info("No values found in 'primary_key_test'.")
        for row in sample_keys:
            logger.info(f"{row['primary_key_test']}")

        # Check for count to ensure records are there to begin with
        srdm_count = srdm_df.select("primary_key_test").distinct().count()
        logger.info(f"\n\nSRDM distinct count: {srdm_count}")

        return srdm_df

    def add_filterby_to_srdm(self, srdm_df, filter_expr):
        try:
            logger.info(f"\nApplying 'filterBy' expression: {filter_expr}")
            srdm_df = srdm_df.filter(expr(filter_expr))

            # Optional: Log a sample after filtering
            sample = srdm_df.limit(5).collect()
            logger.info("Sample rows after filterBy:")
            for row in sample:
                logger.info(row.asDict())

            return srdm_df
        
        except Exception as e:
            logger.error(f"Error applying 'filterBy': {e}")
            raise

    def compare_counts(self, srdm_filtered_df, loader_filtered_df, inv_model):
        srdm_count = srdm_filtered_df.select("primary_key_test").distinct().count()
        loader_count = loader_filtered_df.count()

        logger.info(f"SRDM distinct primary_key_test count for '{inv_model}': {srdm_count}")
        logger.info(f"Loader count for '{inv_model}': {loader_count}")

        if srdm_count == loader_count:
            logger.info(f"\n\n+++ Counts match for loader '{inv_model}'")
        else:
            msg = (f"\nXXX  BUG: Count mismatch for loader '{inv_model}' — SRDM distinct count: {srdm_count}, Loader count: {loader_count}")
            logger.warning(msg)
            self.count_mismatches.append(msg)


    def validate_loader_count(self, spark, inventory_model_names):
        headers = api_utils.set_headers()
        all_temp_properties = {}
        self.count_mismatches = []

        for inv_model in inventory_model_names:
            try:
                    loader_url = readConfig.get_loader_url(inv_model)
                    logger.info("\n\nCurrent loader endpoint:\n" + loader_url)

                    # Get loader config
                    loader_config = apiclient.get(loader_url, headers=headers)

                    # Get temp props dict for this loader
                    loader_temp_props = self.get_temp_props(loader_config, inv_model)
                    all_temp_properties[inv_model] = loader_temp_props

                    logger.info("\nTemp Properties extraction complete for '%s'", inv_model)

                    # Optional: log all temp props at once
                    logger.info("\n\n=== All Extracted Temporary Properties ===\n%s", json.dumps(all_temp_properties, indent=4))

                    use_override_col = False

                    unformatted_srdm_name = loader_config['config_value']["dataSource"]["srdm"]
                    srdm_table_name = self.extract_table_name_from_config(unformatted_srdm_name)
                    srdm_schema_name, loader_schema_name = self.set_srdm_and_loader_schema()

                    # Check for 'event_timestamp_epoch' in temp props and create column if needed (to check for override, basically)
                    if "event_timestamp_epoch" in dict(loader_temp_props):            
                        use_override_col, _ = self.create_event_timestamp_col(spark,
                            srdm_schema_name,
                            srdm_table_name,
                            col_expr=dict(loader_temp_props)["event_timestamp_epoch"]
                        )

                    # Proceed to next step
                    srdm_filtered_df, loader_filtered_df = self.read_srdm_w_date_filter(spark,
                    "srdm_with_override",
                    loader_config,
                    srdm_schema_name,
                    loader_schema_name,
                    use_override_col)

                    # Check for count to ensure records are there to begin with
                    srdm_count = srdm_filtered_df.count()
                    logger.info(f"\n\n***SRDM total count after timestamp_filter: {srdm_count}")

                    # Step: Apply filterBy if present
                    filter_expr = loader_config.get("config_value", {}).get("filterBy")
                    if filter_expr:
                        srdm_filtered_df = self.add_filterby_to_srdm(srdm_filtered_df, filter_expr)
                    else:
                        logger.info("'filterBy' not present in loader config — skipping filter step.")

                    srdm_filtered_df = self.add_primary_key_col_to_srdm(srdm_filtered_df, loader_config, loader_temp_props)

                    self.compare_counts(srdm_filtered_df, loader_filtered_df, inv_model)

            except Exception as e:
                logger.error(f"Error processing loader {inv_model}: {e}")
                # Continue to next loader without breaking the loop
                continue

        # Final assertion            
        if self.count_mismatches:
            error_msg = "\nLoader count validation failed with the following mismatches:\n"
            error_msg += "\n".join(self.count_mismatches)
            raise AssertionError(error_msg)

    def fetch_and_print_required_configs(self, config_type):
        url = readConfig.get_all_deployed_items()
        headers = api_utils.set_headers()
        data = apiclient.get(url, headers=headers)

        if not isinstance(data, list):
            logger.info("Failed to fetch valid data. Expected a list.")
            return []

        # Determine config item type based on input - could be extended to publisher, relationship_disambiguation etc
        type_map = {
            "entities": "data_dictionary_config",
            "relationships": "relationship_data_dictionary_config",
        }
        config_item_type = type_map.get(config_type.lower())

        if not config_item_type:
            logger.warning(f"Unrecognized config_type: '{config_type}'. No filter applied.")
            return []

        filtered_configs = [
            item.get("name")
            for item in data
            if item.get("config_item_type") == config_item_type
        ]

        logger.info(f"\n=== Found {len(filtered_configs)} '{config_type}' configs ===")
        for idx, name in enumerate(filtered_configs, 1):
            logger.info(f"{idx}. {name}")

        return filtered_configs

    def log_and_track_violation(self, msg):
        logger.info(msg)
        self.presence_violations.append(msg)
    
    def extract_entity_name(self, full_name):
            return full_name.split("__")[0]
    
    def fetch_json(self, url: str):
        headers = api_utils.set_headers()
        response = requests.get(url, headers=headers, verify=True)
        response.raise_for_status()
        return response.json()
    
    def check_data_dict_flag(self, ent_or_rel_name: str, ent_or_rel_type):
        url = readConfig.get_deployed_data_dict(ent_or_rel_name, ent_or_rel_type)
        try:
            data = self.fetch_json(url)
            is_available, data = data.get("config_value", {}).get("is_available_in_datalake"), data.get("config_value", {})
            logger.info(f"\n\n--- Dictionary level is_available_in_datalake: {is_available}\n\n")

            if is_available is None:
                msg = f"\n\nAvailabilty flag missing! 'is_available_in_datalake' flag is missing for {ent_or_rel_name} at dictionary level."
                logger.info(msg)
                self.presence_violations.append(msg)

            return is_available, data
        except Exception as e:
            logger.info(f"ALERT! Could not fetch data dictionary for {ent_or_rel_name}: {e}")
            return None, {}
        
    def fetch_entity_or_rel_configs(self, ent_or_rel_current, entity_or_rel):
        def try_fetch_config(url):
            try:
                return self.fetch_json(url)
            except requests.exceptions.HTTPError as e:
                if "404" not in str(e):
                    logger.info(f"! Error fetching config from {url}: {e}")
                return None
        
        # Check if it's from em:  containing 'assessment' or 'finding'
        is_from_em = any(key in ent_or_rel_current for key in ["assessment", "finding"])

        # Determine prefixes based on entity_or_rel and the name
        if entity_or_rel == "relationship":
            prefixes = ["sds_em__rel"] if is_from_em else ["sds_ei__rel", "sds_em__rel"]
        else:
            prefixes = ["sds_em"] if is_from_em else ["sds_ei", "sds_em"]

        for prefix in prefixes:
            logger.info(f"--- {ent_or_rel_current}")
            ent_or_rel_name_check = f"{prefix}__{ent_or_rel_current}"

            publish_url = readConfig.get_ent_rel_spark_job_with_prefix(ent_or_rel_name_check, 'publish')
            inter_url = readConfig.get_ent_rel_spark_job_with_prefix(ent_or_rel_name_check, 'inter')

            logger.info(f"+++ Trying publish URL: {publish_url}")
            logger.info(f"+++ Trying inter URL:   {inter_url}")

            publish_data = try_fetch_config(publish_url)
            inter_data = try_fetch_config(inter_url)

            if publish_data or inter_data:
                logger.info(f">>> Using prefix '{prefix}' for {entity_or_rel} {ent_or_rel_current}")
                logger.info(f"%%% Publish config: {prefix}__{ent_or_rel_current}__publish")
                logger.info(f"%%% Inter config: {prefix}__{ent_or_rel_current}" if inter_data else "XXX Inter config not used for this entity.")
                return publish_data, inter_data
            else:
                missing = [k for k, v in {'publish': publish_data, 'inter': inter_data}.items() if not v]
                logger.warning(f"--- Prefix '{prefix}': Missing config(s): {', '.join(missing)}")

        logger.warning(f"\n\n!!! No usable configs found for {entity_or_rel} {ent_or_rel_current} using any prefix: {prefixes}")
        return None, None
    
    def check_olap_flags(self, publish_data, inter_data):
        is_olap = publish_data.get("config_value", {}).get("isOLAPTable", True)

        is_fragment_olap = inter_data.get("output", {}).get("isFragmentOLAPTable", False) if inter_data else False

        if not is_olap:
            logger.info("XXX Skipping publish : isOLAPTable is False in publish config.")
        if not is_fragment_olap:
            logger.info("XXX Skipping inter: isFragmentOLAPTable is False in inter config.")

        logger.info(f"FLAGS: is_olap:{is_olap}, is_fragment_olap: {is_fragment_olap}\n\n ")
        return is_olap, is_fragment_olap
    
    def parse_publish_table(self, data):
        full_table_name = data["outputTableInfo"]["outputTableName"]
        schema, table = full_table_name.split(".")
        # entity = table.replace("sds_ei__", "").replace("__publish", "")
        entity_or_rel = re.sub(r"^sds_[a-z]+__", "", table).replace("__publish", "")
        return schema, table, entity_or_rel

    def parse_inter_table(self, data):
        full_table_name = data["output"]["fragmentLocation"]
        schema, table = full_table_name.split(".")
        # entity = table.replace("sds_ei__", "")
        entity_or_rel = re.sub(r"^sds_[a-z]+__", "", table)
        return schema, table, entity_or_rel

    def table_exists_in_hive(self, spark, schema: str, table: str) -> bool:
        try:
            tables = spark.sql(f"SHOW TABLES IN iceberg_catalog.{schema} LIKE '{table}'").collect()
            return len(tables) > 0
        except Exception as e:
            logger.info(f"\n\nALERT! Error checking table existence: iceberg_catalog.{schema}.{table} - {e}")
            return False
        
    def get_table_columns(self, spark, schema: str, table: str) -> set:
        try:
            columns_df = spark.sql(f"DESCRIBE iceberg_catalog.{schema}.{table}")
            return {row['col_name'] for row in columns_df.collect()}
        except Exception as e:
            logger.info(f"\n\nALERT! Could not describe table {schema}.{table}: {e}")
            return set()
        
    def get_column_type(self, spark, schema, table, column_name):
        try:
            describe_df = spark.sql(f"DESCRIBE iceberg_catalog.{schema}.{table}")
            row = describe_df.filter(describe_df.col_name == column_name).collect()
            if row:
                return row[0]["data_type"]
            else:
                logger.info(f"\n\nALERT! Column '{column_name}' not found in table {schema}.{table}")
        except Exception as e:
            logger.info(f"\n\nALERT! Error fetching column type for {column_name} in {schema}.{table}: {e}")
        return None
    
    TYPE_MAPPING = {
        "stringtype": "string",
        "integertype": "int",
        "string": "string",
        "struct": "string",
        "array<struct>": "string",
        "list<struct>": "string",
        "int": "int",
        "integer":"int",
        "long": "int",
        "double": "float",
        "decimal": "float",
        "float": "float",
        "list<double>": "list<float>",
        "list<float>": "list<float>",
        "list<decimal>": "list<float>",
        "list<int>": "list<int>",
        "list<long>": "list<int>",
        "list<string>": "list<string>",
        "list<array>": "string",
        "array<list>": "string",
        "array<double>": "list<float>",
        "array<float>": "list<float>",
        "array<decimal>": "list<float>",
        "array<int>": "list<int>",
        "array<long>": "list<int>",
        "array<string>": "list<string>",
        "array<array>": "string",
        "list<list>": "string",
        "timestamp": ["timestamp", "string"],
        "timestamptz": "timestamp",
        "datetime.date": "timestamp",
        "boolean": "boolean"
    }
    
    def check_field_type_match(self, field_name, dd_field_type, iceberg_type):
        dd_type_lower = dd_field_type.lower()
        iceberg_type_lower = iceberg_type.lower()

        # Get expected mapped types from DD
        expected_types = self.TYPE_MAPPING.get(dd_type_lower, dd_type_lower)
        if isinstance(expected_types, str):
            expected_types = [expected_types]

        # Map Iceberg type
        iceberg_type_converted = self.TYPE_MAPPING.get(iceberg_type_lower, "string")

        # Compare
        if iceberg_type_converted not in expected_types:
            msg = (f"\nType Mismatch! Field '{field_name}': \nType mismatch - data dictionary type is '{dd_field_type}', "
                f"but Iceberg type is '{iceberg_type_converted}' (raw: '{iceberg_type}')")
            logger.info(msg)
            self.type_mismatch_errors.append(msg)
        
    def validate_field_level(self, spark, name, field_dict, publish_schema, publish_table, inter_config_data, publish_exists, entity_or_rel):
        logger.info(f"\n\n---- Starting field-level validation for {entity_or_rel} {name.title()}")

        publish_columns = self.get_table_columns(spark, publish_schema, publish_table) if publish_exists else set()
        fragment_columns, fragment_table, fragment_schema, fragment_table_name = self._get_fragment_table_details(
            spark, inter_config_data
        )

        if entity_or_rel == "relationship":
            attributes = field_dict.get("relationship_attributes", {})
        else:
            attributes = field_dict.get("attributes", {})

        if not attributes:
            logger.info(f"!!! No '{'relationship_attributes' if entity_or_rel == 'relationship' else 'attributes'}' found in field_dict for: {name}")
            return
        
        for attr_name, attr_meta in attributes.items():
            expected = attr_meta.get("is_available_in_datalake")
            if expected is None:
                msg = f"Availability flag missing!: Field '{attr_name}' has missing or null 'is_available_in_datalake' flag."
                logger.info(msg)
                self.presence_violations.append(msg)
                continue  # Skip this field from further validation since is_available_in_datalake is missing

            self._validate_field(
                spark, attr_name, attr_meta, publish_columns, fragment_columns,
                publish_schema, publish_table, fragment_schema, fragment_table_name, fragment_table
            )

    def _get_fragment_table_details(self, spark, inter_config_data):
        fragment_columns, fragment_table = set(), None
        fragment_schema = fragment_table_name = None
        if inter_config_data:
            fragment_location = inter_config_data.get("output", {}).get("fragmentLocation")
            if fragment_location:
                try:
                    fragment_schema, fragment_table_name = fragment_location.split(".")
                    fragment_columns = self.get_table_columns(spark, fragment_schema, fragment_table_name)
                    fragment_table = fragment_location
                except Exception as e:
                    msg = f"\n\nALERT! Unable to fetch columns from fragment table '{fragment_location}': {e}"
                    logger.info(msg)
                    self.presence_violations.append(msg)
        return fragment_columns, fragment_table, fragment_schema, fragment_table_name

    def _validate_field(self, spark, attr_name, attr_meta, publish_columns, fragment_columns,
                        publish_schema, publish_table, fragment_schema, fragment_table_name, fragment_table):

        group = attr_meta.get("group")
        expected = attr_meta.get("is_available_in_datalake")
        dd_field_type = attr_meta.get("type")

        in_publish = attr_name in publish_columns
        in_fragment = attr_name in fragment_columns if fragment_table else None

        present = self._evaluate_field_presence(group, in_publish, in_fragment, fragment_table, attr_name)

        self._check_presence(attr_name, expected, present, in_publish, in_fragment,
                            publish_schema, publish_table, fragment_table, group)        
        if present:
            iceberg_type = self.get_column_type(spark, publish_schema, publish_table, attr_name)
            if not iceberg_type and fragment_table:
                iceberg_type = self.get_column_type(spark, fragment_schema, fragment_table_name, attr_name)
            if iceberg_type:
                self.check_field_type_match(attr_name, dd_field_type, iceberg_type)

    def _evaluate_field_presence(self, group, in_publish, in_fragment, fragment_table, attr_name):
        if group == "enrichment":
            return in_publish
        if fragment_table:
            return in_publish and in_fragment
        if in_publish:
            logger.info(f"$$$ Valid: Fragment table absent, but field '{attr_name}' exists in publish table.")
        return in_publish

    def _check_presence(self, attr_name, expected, present, in_publish, in_fragment, publish_schema, publish_table, fragment_table, group):
        if expected is True and not present:
            missing_parts = []
            if not in_publish:
                missing_parts.append(f"publish table '{publish_schema}.{publish_table}'")
            if fragment_table and not in_fragment:
                missing_parts.append(f"fragment table '{fragment_table}'")
            msg = f"XXX  BUG: Field '{attr_name}' marked as available in datalake but not found in {' and '.join(missing_parts)}."
            logger.info(msg)
            self.presence_violations.append(msg)
        elif expected is False and present:
            msg = f"XXX  BUG: Field '{attr_name}' NOT marked as available in datalake but exists in table."
            logger.info(msg)
            self.presence_violations.append(msg)
        else:
            msg_type = "+++" if group == "enrichment" else ">>>"
            logger.info(f"{msg_type} Field '{attr_name}' is consistent with availability flag.")


    def validate_entity_availability(
        self, is_available, publish_exists, inter_exists,
        field_dict, publish_schema, publish_table, inter_data, entity, inter_needed, entity_or_rel, spark
        ):
        # --- Publish Table Check ---
        if is_available is True:
            if publish_exists:
                logger.info("\n>>> Valid: is_available_in_datalake is True and required table exists.")
            else:
                msg = ("\nXXX  BUG: is_available_in_datalake is True but publish table does not exist.")
                logger.info(msg)
                self.presence_violations.append(msg)

        elif is_available is False:
            if not publish_exists:
                logger.info(">>> Valid: is_available_in_datalake is False and publish table is missing.")
            else:
                msg = ("XXX  BUG: is_available_in_datalake is False but publish table exists.")
                logger.info(msg)
                self.presence_violations.append(msg)
        else:
            logger.info("??? is_available_in_datalake is missing or null.")

        # --- Inter (Fragment) Table Check, only if needed ---
        if inter_needed:
            if is_available is True:
                if inter_exists:
                    logger.info(">>> Valid: Fragment is required and Fragment table exists.")
                else:
                    msg = ("XXX BUG: Fragment table is required but Fragment table does not exist.")
                    logger.info(msg)
                    self.presence_violations.append(msg)
            elif is_available is False:
                if not inter_exists:
                    logger.info(">>> Valid: is_available_in_datalake is False and fragment table is missing.")
                else:
                    msg = "XXX BUG: is_available_in_datalake is False but fragment table exists."
                    logger.info(msg)
                    self.presence_violations.append(msg)

        # --- Field-level validation ---
        if field_dict:
            self.validate_field_level(spark, entity, field_dict, publish_schema, publish_table, inter_data, publish_exists, entity_or_rel)

    def all_entities_or_rel_dd(self, spark, deployed_entities, entity_or_rel):
        self.type_mismatch_errors = []
        self.presence_violations = []

        for i, name in enumerate(deployed_entities, 1):
            current_name = self.extract_entity_name(name)

            logger.info("\n\n==============================")
            logger.info(f"----- [{i}/{len(deployed_entities)}] {entity_or_rel.title()} Name: {current_name}")
            logger.info("==============================")

            try:
                self._process_entity_or_rel_validation(spark, current_name,entity_or_rel)
            except Exception as e:
                logger.warning(f"Validation error for {entity_or_rel} {current_name}: {e}")

        self._assert_if_errors_exist()

    def check_isInverse_flag(self, rel_name):
        try:
            url = readConfig.get_deployed_data_dict(rel_name, "relationship")
            data = self.fetch_json(url)
            config_value = data.get("config_value", {})
            is_inverse = config_value.get("isInverse")
            rel_name = data.get("inverse_relationship_name") if is_inverse else rel_name
            return is_inverse, rel_name
        except Exception as e:
            msg = f"\nisInverse flag missing! Could not fetch isInverse info for {rel_name}: \n{e}"
            logger.info(msg)
            self.presence_violations.append(msg)
            return None, rel_name

    def _process_entity_or_rel_validation(self, spark, current_name, entity_or_rel):
        is_available, field_dict = self.check_data_dict_flag(current_name, entity_or_rel)

        if entity_or_rel == "relationship":
            is_inverse, current_name = self.check_isInverse_flag(current_name)
            logger.info(f"\n\n--- isInverse = {is_inverse}. Using rel name '{current_name}' for validation.")

        # Fetch configs
        publish_data, inter_data = self.fetch_entity_or_rel_configs(current_name, entity_or_rel)

        if not publish_data:
            logger.info("\n\nALERT! Exiting validation due to missing publish config - API endpoint not found.")
            return

        olap_pub, olap_inter = self.check_olap_flags(publish_data, inter_data)
        if not olap_pub:
            logger.info("XXX  Skipping relationship validation: OLAP flag is False\n")
            return

        # Unified table parsing
        publish_schema, publish_table, _ = self.parse_publish_table(publish_data)
        inter_schema = inter_table = None
        if olap_inter and inter_data:
            inter_schema, inter_table, _ = self.parse_inter_table(inter_data)

        logger.info(f"000 {entity_or_rel.title()}: {current_name}")
        logger.info(f"*** Publish Table: {publish_schema}.{publish_table}")
        logger.info(f"*** Fragment Table: {inter_schema}.{inter_table}" if inter_schema and inter_table else "*** Fragment Table: Not available")

        publish_exists = self.table_exists_in_hive(spark, publish_schema, publish_table)
        inter_exists = self.table_exists_in_hive(spark, inter_schema, inter_table)

        logger.info(">>> Publish table exists in Iceberg." if publish_exists else "XXX  Publish table NOT found in Iceberg.")
        logger.info(">>> Fragment table exists in Iceberg." if inter_exists else "XXX  Fragment table NOT found in Iceberg.")

        inter_needed = bool(inter_data) and olap_inter

        self.validate_entity_availability(
            is_available,
            publish_exists,
            inter_exists,
            field_dict,
            publish_schema,
            publish_table,
            inter_data,
            current_name,
            inter_needed,
            entity_or_rel,
            spark
        )

    def _assert_if_errors_exist(self):
        if self.type_mismatch_errors or self.presence_violations:
            error_msg = "\nValidation failed with the following issues:\n"
            if self.presence_violations:
                error_msg += "\n--- Presence Violations ---\n" + "\n".join(self.presence_violations)
            if self.type_mismatch_errors:
                error_msg += "\n--- Type Mismatches ---\n" + "\n".join(self.type_mismatch_errors)
            raise AssertionError(error_msg)

    
class GraphPropertiesValidator:

    GREEN = "\033[92m"
    RED = "\033[91m"
    RESET = "\033[0m"

    def __init__(self, spark):
        self.spark = spark
        self.entities, self.relationships = self.get_deployed_entities_and_relationships()

    def get_deployed_entities_and_relationships(self):
        headers = api_utils.set_headers()
        url = readConfig.get_all_deployed_publish()
        response = apiclient.get(url, headers=headers)
        logger.info(f"API called (publish): {url}")

        if not isinstance(response, list):
            raise Exception(f"Unexpected response format while fetching config metadata: {response}")

        config_names = [item.get("name", "") for item in response]

        entities = []
        relationships = []
        self.name_to_prefix = {}  # map of entity/rel name to prefix like sds_em or sds_ei

        for name in config_names:
            if not name.endswith("__publish"): #Check for OLAP flag before considering those entities and relationships
                continue

            try:
                # Skip if OLAP flag is not true
                config_url = readConfig.get_isOLAP_tag(name)
                config_resp = apiclient.get(config_url, headers=headers)
                is_olap = config_resp.get("isOLAPTable", True)  # default to False

                if not is_olap:
                    logger.info(f"!!! Skipping config {name} — isOLAPTable is False or not set.")
                    continue

            except Exception as e:
                logger.warning(f"Error checking isOLAPTable for {name}. Skipping. Error: {e}")
                continue

            matching = next((item for item in response if item.get("name", "") == name), None)
            solution = matching.get("solution", "ei") if matching else "ei"
            prefix = f"sds_{solution}"

            # Extract relationship names
            if "__rel__" in name and name.endswith("__publish"):
                rel = name.split("__rel__")[-1].replace("__publish", "")
                relationships.append(rel)
                self.name_to_prefix[rel] = prefix
            # Extract entity names
            elif name.endswith("__publish"):
                entity = name.split("__")[-2]
                entities.append(entity)
                self.name_to_prefix[entity] = prefix

        logger.info("="*40)
        logger.info("Extracting deployed entities and relationships from publisher configs")
        logger.info("="*40 + "\n")
        logger.info(f"Deployed Entities ({len(entities)}): {entities}")
        logger.info(f"Deployed Relationships ({len(relationships)}): {relationships}")

        return entities, relationships

    def handle_webdriver_exception(self, exception):
        error_msg = str(exception).split('\n')[0]  # Get just the first line of error
        tb = traceback.format_exc().split('Stacktrace:')[0]  # Get Python traceback without Selenium stacktrace
        logging.error(f"Error occurred: {error_msg}\nTraceback:\n{tb}")
        raise exception

    @staticmethod
    # Convert snake_case to Title Case with spaces
    def format_name(name):
        return name.replace("_", " ").title()

    def table_exists_in_iceberg(self, spark, full_table_path):
        try:
            schema, table = full_table_path.replace("iceberg_catalog.", "").split(".", 1)
            kg_validator = KnowledgeGraphValidation()
            return kg_validator.table_exists_in_hive(spark, schema, table)
        except Exception as e:
            logger.warning(f"Failed to check existence for {full_table_path}: {e}")
            return False
        
    def get_schema_by_table_type(self, table_type, domain_type):
        """
        Fetches and returns the appropriate schema name for the given table type and domain type.
        Args:
            table_type (str): 'fragment' or 'publish'
            domain_type (str): 'entity' or 'relationship'
        Returns: str: schema name (e.g., 'kg_fragment_cloud_run')
        """
        names_to_check = self.entities if domain_type == "entity" else self.relationships

        for name in names_to_check:
            try:
                headers = api_utils.set_headers()

                if table_type == 'fragment':
                    url = readConfig.get_fragment_url(name)
                    response = apiclient.get(url, headers=headers)
                    logger.info(f"API called (fragment): {url}")

                    if response.get("status") != "ERROR":
                        location = response["output"]["fragmentLocation"]
                        schema_name =  location.split(".")[0]
                        break  # Stop checking after successful match
                    else:
                        logger.warning(f"Fragment API error for '{name}': {response.get('detail')}")

                elif table_type == 'publish':
                    if domain_type == "relationship":
                        prefixed_name = f"{self.get_prefix(name)}__rel__{name}"
                    else:
                        prefixed_name = f"{self.get_prefix(name)}__{name}"
                    url = readConfig.get_ent_rel_spark_job_with_prefix(prefixed_name, 'publish')
                    response = apiclient.get(url, headers=headers)
                    logger.info(f"API called (publish): {url}")

                    location = response.get("outputTableInfo", {}).get("outputTableName")
                    if location:
                        schema_name = location.split(".")[0]
                        break
                    else:
                        logger.warning(f"Missing 'outputTableInfo' or 'outputTableName' in publish config for {name}")

            except (KeyError, TypeError) as e:
                logger.warning(f"Config structure issue for '{name}' [{table_type}]: {e}")
            except Exception as e:
                logger.warning(f"Failed to fetch schema for '{name}' [{table_type}]: {e}")
        if not schema_name:
            logger.error(f"Could not determine {table_type} schema for any {domain_type}.")
        return schema_name

    def run_validation(self, spark):
        TEST_CASE = "Validation of Graph Properties"
        mismatch_flag = False
        results = []
        catalog_prefix = "iceberg_catalog"
        self.resolver_outliers = []
        self.table_missing_outliers = []
        self.invalids = [] 

        try:
            # --- Entities ---
            fragment_schema_entity = self.get_schema_by_table_type("fragment", "entity")
            publish_schema_entity = self.get_schema_by_table_type("publish", "entity")

            total_entities = len(self.entities)
            for index, entity in enumerate(self.entities, 1):
                logger.info("\n")
                logger.info("=" * 70)
                logger.info(f"Starting validation for ENTITY ({index}/{total_entities}): {entity.upper()}")
                logger.info("=" * 70 + "\n")

                logger.info(f"Using fragment schema '{fragment_schema_entity}' for entity: {entity}")
                logger.info(f"Using publish schema '{publish_schema_entity}' for entity: {entity}")

                results += self.validate_domain_tables(
                    spark,
                    entity,
                    fragment_schema_entity,
                    publish_schema_entity,
                    catalog_prefix,
                    TEST_CASE,
                    domain_type="entity"
                )

            # --- Relationships ---
            # fragment_schema_rel = self.get_schema_by_table_type("fragment", "relationship") #Relationship frament table not in scope
            publish_schema_rel = self.get_schema_by_table_type("publish", "relationship")

            total_relationships = len(self.relationships)
            for idx, relationship in enumerate(self.relationships, 1):
                logger.info("\n" + "=" * 80)
                logger.info(f"Starting validation for RELATIONSHIP ({idx}/{total_relationships}): {relationship.upper()}")
                logger.info("=" * 80 + "\n")

                logger.info(f"Using publish schema '{publish_schema_rel}' for relationship: {relationship}")

                results += self.validate_domain_tables(
                    spark,
                    relationship,
                    "fragment_schema_rel",
                    publish_schema_rel,
                    catalog_prefix,
                    TEST_CASE,
                    domain_type="relationship"
                )

        except Exception as e:
            self.handle_webdriver_exception(e)

        result_df = spark.createDataFrame([Row(**r) for r in results])
        pass_output = result_df.filter("STATUS = 'PASS'")
        fail_output = result_df.filter("STATUS = 'FAIL'")
        mismatch_flag = not fail_output.rdd.isEmpty()

        # Print general invalid entries
        invalid_rows = fail_output.collect()
        if invalid_rows:
            logger.warning("\n\n\n--- SUMMARY OF INVALID ENTRIES ---")
            for row in invalid_rows:
                logger.warning(f"[INVALID] {row.TABLE_NAME} | {row.PROPERTY_NAME} = {row.ACTUAL_VALUE} (Expected: {row.EXPECTED_VALUE})")

        # Print resolver outliers if any
        if hasattr(self, "resolver_outliers") and self.resolver_outliers:
            mismatch_flag = True  # force failure
            logger.warning("\n\n\n--- RESOLVER GRAPH_ID MISMATCHES ---")
            for gid in self.resolver_outliers:
                logger.warning(f"[resolver_graph_id_mismatch] {gid}")

        return mismatch_flag, pass_output, fail_output

    def get_prefix(self, name):
        # return "sds_em" if any(x in name for x in ["finding", "assessment"]) else "sds_ei"
        return self.name_to_prefix.get(name, "sds_ei") 
    
    def snake_case(self, name):
        return name.replace(" ", "_").lower()

    def validate_domain_tables(self, spark, name, fragment_schema, publish_schema, prefix, test_case, domain_type):
        """Validates graph_id and graph.property for entity tables (fragment, resolver, publish)."""
        results = []
        prefix_used = self.get_prefix(name)
        formatted = self.format_name(name)
        snake_name = self.snake_case(name)

        # Fragment and Resolver Table (only for entities)
        if domain_type == "entity":
            fragment_tbl = f"{prefix}.{fragment_schema}.{prefix_used}__fragment__{snake_name}"
            resolver_tbl = f"{prefix}.{fragment_schema}.{prefix_used}__resolver__{snake_name}"
            results += self.validate_table_group(spark, fragment_tbl, "Fragment", formatted, test_case, domain_type)
            results += self.validate_table_group(spark, resolver_tbl, "Resolver", formatted, test_case, domain_type, is_edge=True)

        # Publish Table
        if domain_type == "relationship":
            publish_tbl = f"{prefix}.{publish_schema}.{prefix_used}__rel__{snake_name}__publish"
        else:
            publish_tbl = f"{prefix}.{publish_schema}.{prefix_used}__{snake_name}__publish"
        results += self.validate_table_group(spark, publish_tbl, "Publish", formatted, test_case, domain_type)

        if domain_type == "entity":
            # Validate resolver to graph_id mappings
            results += self.validate_resolver_mappings(
                spark,
                fragment_tbl,
                resolver_tbl,
                publish_tbl,
                entity=name,
                test_case=test_case
            )

        return results

    def validate_table_group(self, spark, table_path, table_type_label, name_fmt, test_case, domain_type, is_edge=False):
        """Central function to check graph_id and graph.property name format + value match."""
        results = []

        logger.info(f"--- Validating {table_type_label} table: {table_path}")

        # Extract entity name from table_path
        try:
            table_parts = table_path.split(".")[-1].split("__")
            entity_name = table_parts[-2]
        except Exception as e:
            logger.warning(f"Couldn't extract entity name from table_path: {table_path}. Error: {e}")
            entity_name = None

        # Handle missing table
        if not self.table_exists_in_iceberg(spark, table_path):
            if table_type_label.lower() == "fragment" and entity_name in ["finding", "assessment"]:
                logger.warning(f"Skipping missing Fragment table for entity '{entity_name}' — allowed.")
            else:
                warning_msg = f"Missing {table_type_label} table: {table_path}"
                logger.warning(warning_msg)
                self.table_missing_outliers.append({
                    "TABLE_NAME": table_path,
                    "TABLE_TYPE": table_type_label,
                    "ENTITY": entity_name or "UNKNOWN",
                    "REASON": "Missing required table"
                })
            return results  # return empty but don't fail

        # Only validate graph_id for applicable types
        should_check_graph_id = (
            (domain_type == "entity") or
            (domain_type == "relationship" and table_type_label == "publish")
        )

        if should_check_graph_id:
            valid, mismatch_count = self.graph_id_val(spark, table_path, table_type_label, domain_type)
            results.append({
                "TABLE_TYPE": domain_type,
                "TABLE_NAME": table_path,
                "PROPERTY_NAME": "graph_id",
                "EXPECTED_VALUE": f"sha2(...) for {table_type_label}",
                "ACTUAL_VALUE": f"{mismatch_count} mismatches" if not valid else "All matched",
                "STATUS": "PASS" if valid else "FAIL",
                "TEST_CASE": "Graph ID Column Validation"
            })

        # Graph Property Format Check
        expected_props = self.get_expected_properties(name_fmt, table_type_label, domain_type)
        # results.append(self.validate_graph_property_names(spark, table_path, expected_props, test_case, domain_type))
        results += self.validate_graph_property_names(spark, table_path, expected_props, test_case, domain_type)

        return results


    def graph_id_val(self, spark, table_path, table_type, domain_type):
        """Validates graph_id based on table_type and domain_type (entity vs relationship)."""
        try:
            df = spark.sql(f"SELECT * FROM {table_path}")
            df = df.withColumn("updated_at_fmt", date_format(col("updated_at_ts"), "yyyy-MM-dd").substr(1, 10))
            df = df.withColumn("updated_at_fmt", concat_ws(" ", col("updated_at_fmt"), lit("23:59:59.999")))

            if domain_type == "entity":
                if table_type.lower() == "fragment":
                    df = df.withColumn("graph_id_exp", sha2(concat(col("p_id"), lit("Fragment"), col("updated_at_fmt")), 256))
                elif table_type.lower() == "resolver":
                    df = df.withColumn("graph_id_exp", sha2(concat(col("p_id"), col("disambiguated_p_id"), col("updated_at_fmt")), 256))
                elif table_type.lower() == "publish":
                    df = df.withColumn("graph_id_exp", sha2(concat(col("p_id"), col("updated_at_fmt")), 256))

            elif domain_type == "relationship":
                if table_type.lower() == "publish":
                    df = df.withColumn("graph_id_exp", sha2(concat(col("relationship_id"), col("updated_at_fmt")), 256))

            # matches = df.filter(col("graph_id") == col("graph_id_exp"))
            mismatches = df.filter(col("graph_id") != col("graph_id_exp"))

            mismatch_count = mismatches.count()

            if mismatch_count > 0:
                logger.warning(f"MISMATCH! [graph_id_val] {mismatch_count} mismatches in {table_path} for table type '{table_type}' (domain: {domain_type})")
                sample = mismatches.select("p_id", "graph_id", "graph_id_exp", "updated_at_ts").limit(5).collect()
                for row in sample:
                    logger.warning(f"[Sample Mismatch] p_id: {row['p_id']} | updated_at_ts: {row['updated_at_ts']} | expected: {row['graph_id_exp']} | actual: {row['graph_id']}")
                return False, mismatch_count
            else:
                logger.info(f"{self.GREEN}ALL MATCH!! [graph_id_val] All graph_id values matched for {table_path}{self.RESET}")
                return True, 0

        except Exception as e:
            logger.error(f"[graph_id_val] Error processing {table_path}: {e}")
            return False, -1

    def get_expected_properties(self, formatted_name, table_type, domain_type):
        """
        Returns expected graph properties based on table type and domain type.
        Args:
            formatted_name (str): Human-readable name of the table (e.g., "Cloud Account").
            table_type (str): One of "Fragment", "Resolver", or "Publish".
            domain_type (str): Either "entity" or "relationship".

        Returns dict: key-value pairs of expected table properties.
        """

        if table_type == "Resolver":
            # Only applies to entities
            return {
                "graph.edge.name": f"{formatted_name} Has Fragment",
                "graph.edge.source.name": f"{formatted_name}",
                "graph.edge.target.name": f"Fragment {formatted_name}"
            }

        elif table_type == "Fragment":
            return {
                "graph.vertex.name": f"Fragment {formatted_name}"
            }

        elif table_type == "Publish":
            if domain_type == "entity":
                return {
                    "graph.vertex.name": f"{formatted_name}"
                }
            elif domain_type == "relationship":
                return {
                    "graph.edge.name": f"{formatted_name}",
                    "graph.edge.source.name": f"{formatted_name}",
                    "graph.edge.target.name": f"{formatted_name}"
                }

        # If we ever get here, it's a configuration error
        logger.warning(f"Unexpected table/domain type combo: {table_type}, {domain_type}")
        return {}


    def validate_graph_property_names(self, spark, table, expected_props, test_case, table_type):
        try:
            props = spark.sql(f"SHOW TBLPROPERTIES {table}").collect()
            prop_dict = {row['key']: row['value'] for row in props}

            for key, expected_val in expected_props.items():
                actual_val = prop_dict.get(key, None)
                status = "PASS" if actual_val == expected_val else "FAIL"

                if status == "PASS":
                    logger.info(f"{self.GREEN}VALID table properties  | {table} | {key} = {actual_val}{self.RESET}")
                else:
                    logger.warning(f"{self.RED}INVALID table properties | {table} | {key} = {actual_val} (Expected: {expected_val}){self.RESET}")
                    self.invalids.append({
                        "TABLE_TYPE": table_type,
                        "TABLE_NAME": table,
                        "PROPERTY_NAME": key,
                        "EXPECTED_VALUE": expected_val,
                        "ACTUAL_VALUE": actual_val,
                        "STATUS": "FAIL",
                        "TEST_CASE": test_case
                    })

                return self.invalids

        except Exception as e:
            logger.error(f"[ERROR] Could not fetch tblproperties for {table}: {e}")
            return {
                "TABLE_TYPE": table_type,
                "TABLE_NAME": table,
                "PROPERTY_NAME": "tblproperties",
                "EXPECTED_VALUE": str(expected_props),
                "ACTUAL_VALUE": "Could not fetch",
                "STATUS": "FAIL",
                "TEST_CASE": test_case
            }

    # Correlation through Resolver validation 

    def validate_resolver_mappings(self, spark, fragment_tbl, resolver_tbl, publish_tbl, entity, test_case):
        "Validate that there are no unmatched or unused graph_id in the resolver-fragment or resolver-publish combination"
        results = []

        # Check for fragment table existence - findings and assessments won't have fragment tables
        if not self.table_exists_in_iceberg(spark, fragment_tbl):
            if entity in ["finding", "assessment"]:
                logger.warning(f"Skipping resolver validation — fragment table missing for allowed entity: {entity}")
            else:
                logger.error(f"Missing fragment table required for resolver validation: {fragment_tbl}")
                self.table_missing_outliers.append({
                    "TABLE_NAME": fragment_tbl,
                    "TABLE_TYPE": "Fragment",
                    "ENTITY": entity,
                    "REASON": "Missing fragment table required for resolver mapping"
                })
            return results # Skip resolver validation if fragment table missing

        fragment_df = spark.table(fragment_tbl).select("graph_id").distinct()
        resolver_df = spark.table(resolver_tbl).select("source_graph_id", "target_graph_id").distinct()
        publish_df = spark.table(publish_tbl).select("graph_id").distinct()

        # Validate Left: publish vs resolver.source_graph_id
        left_diff = publish_df.join(resolver_df, publish_df.graph_id == resolver_df.source_graph_id, "left_anti")
        if not left_diff.rdd.isEmpty():
            logger.warning(f"[RESOLVER MAPPING] Missing source_graph_ids in resolver for entity: {entity}")
            for row in left_diff.collect():
                logger.warning(f"[OUTLIER] publish.graph_id not found in resolver.source_graph_id: {row.graph_id}")
                self.resolver_outliers.append(row.graph_id)
            results.append({
                "ENTITY": entity,
                "TABLE_NAME": resolver_tbl,
                "VALIDATION": "resolver_source_graph_id",
                "STATUS": "FAIL"
            })
        else:
            logger.info(f"{self.GREEN}ALL MATCH! [RESOLVER MAPPING] All publish.graph_id values matched with resolver.source_graph_id for entity: {entity}{self.RESET}")
            results.append({
                "ENTITY": entity,
                "TABLE_NAME": resolver_tbl,
                "VALIDATION": "resolver_source_graph_id",
                "STATUS": "PASS"
            })

        # Validate Right: fragment vs resolver.target_graph_id
        right_diff = fragment_df.join(resolver_df, fragment_df.graph_id == resolver_df.target_graph_id, "left_anti")
        if not right_diff.rdd.isEmpty():
            logger.warning(f"{self.RED}[RESOLVER MAPPING] Missing target_graph_ids in resolver for entity: {entity}{self.RESET}")
            for row in right_diff.collect():
                logger.warning(f"{self.RED}[OUTLIER] fragment.graph_id not found in resolver.target_graph_id: {row.graph_id}{self.RESET}")
                self.resolver_outliers.append(row.graph_id)
            results.append({
                "ENTITY": entity,
                "TABLE_NAME": resolver_tbl,
                "VALIDATION": "resolver_target_graph_id",
                "STATUS": "FAIL"
            })
        else:
            logger.info(f"[RESOLVER MAPPING] All fragment.graph_id values matched with resolver.target_graph_id for entity: {entity}")
            results.append({
                "ENTITY": entity,
                "TABLE_NAME": resolver_tbl,
                "VALIDATION": "resolver_target_graph_id",
                "STATUS": "PASS"
            })

        return results
