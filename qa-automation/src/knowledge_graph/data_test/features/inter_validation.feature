Feature: Inter Source Validation



@person @runner.continue_after_failed_step @DF-T3007 @DF-T3008 @DF-T3009 @DF-T3010 @DF-T3011 @DF-T3012 @DF-T3013 @DF-T3014 @DF-T3015 @DF-T3016 @DF-T3017 @DF-T3018
Scenario: To Validate the Inter Source Disambiguation - person
   Given  The deployement and person inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report



@host @runner.continue_after_failed_step @DF-T3019 @DF-T3020 @DF-T3021 @DF-T3022 @DF-T3023 @DF-T3024 @DF-T3025 @DF-T3026 @DF-T3027 @DF-T3028 @DF-T3029 @DF-T3030
Scenario: To Validate the Inter Source Disambiguation - host
   Given  The deployement and host inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report


@vulnerability @runner.continue_after_failed_step @DF-T3031 @DF-T3032 @DF-T3033 @DF-T3034 @DF-T3035 @DF-T3036 @DF-T3037 @DF-T3038 @DF-T3039 @DF-T3040 @DF-T3041 @DF-T3042
Scenario: To Validate the Inter Source Disambiguation - vulnerability
   Given  The deployement and vulnerability inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report



@cloud_compute @runner.continue_after_failed_step @DF-T3047 @DF-T3048 @DF-T3049 @DF-T3050 @DF-T3051 @DF-T3052 @DF-T3053 @DF-T3054 @DF-T3043 @DF-T3044 @DF-T3045 @DF-T3046
Scenario: To Validate the Inter Source Disambiguation - cloud_compute
   Given  The deployement and cloud_compute inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report


@cloud_container @runner.continue_after_failed_step @DF-T3055 @DF-T3056 @DF-T3057 @DF-T3058 @DF-T3059 @DF-T3060 @DF-T3061 @DF-T3062 @DF-T3063 @DF-T3064 @DF-T3065 @DF-T3066
Scenario: To Validate the Inter Source Disambiguation - cloud_container
   Given  The deployement and cloud_container inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report


@cloud_storage @runner.continue_after_failed_step @DF-T3067 @DF-T3068 @DF-T3069 @DF-T3070 @DF-T3071 @DF-T3072 @DF-T3073 @DF-T3074 @DF-T3075 @DF-T3076 @DF-T3077 @DF-T3078
Scenario: To Validate the Inter Source Disambiguation - cloud_storage
   Given  The deployement and cloud_storage inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report


@cloud_account @runner.continue_after_failed_step @DF-T3079 @DF-T3080 @DF-T3081 @DF-T3082 @DF-T3083 @DF-T3084 @DF-T3085 @DF-T3086 @DF-T3087 @DF-T3088 @DF-T3089 @DF-T3090
Scenario: To Validate the Inter Source Disambiguation - cloud_account
   Given  The deployement and cloud_account inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report


@finding @runner.continue_after_failed_step @DF-T3091 @DF-T3092 @DF-T3093 @DF-T3094 @DF-T3095 @DF-T3096 @DF-T3097 @DF-T3098 @DF-T3099 @DF-T3100 @DF-T3101 @DF-T3102
Scenario: To Validate the Inter Source Disambiguation - finding
   Given  The deployement and finding inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report  

@identity @runner.continue_after_failed_step @DF-T3103 @DF-T3104 @DF-T3105 @DF-T3106 @DF-T3107 @DF-T3108 @DF-T3109 @DF-T3110 @DF-T3111 @DF-T3112 @DF-T3113 @DF-T3114
Scenario: To Validate the Inter Source Disambiguation - identity
   Given  The deployement and identity inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@assessment @runner.continue_after_failed_step @DF-T3115 @DF-T3116 @DF-T3117 @DF-T3118 @DF-T3119 @DF-T3120 @DF-T3121 @DF-T3122 @DF-T3123 @DF-T3124 @DF-T3125 @DF-T3126
Scenario: To Validate the Inter Source Disambiguation - assessment
   Given  The deployement and assessment inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report


@compliance_standard @runner.continue_after_failed_step @DF-T3127 @DF-T3128 @DF-T3129 @DF-T3130 @DF-T3131 @DF-T3132 @DF-T3133 @DF-T3134 @DF-T3135 @DF-T3136 @DF-T3137 @DF-T3138
Scenario: To Validate the Inter Source Disambiguation - compliance_standard
   Given  The deployement and compliance_standard inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report


@security_control @runner.continue_after_failed_step @DF-T3139 @DF-T3140 @DF-T3141 @DF-T3142 @DF-T3143 @DF-T3144 @DF-T3145 @DF-T3146 @DF-T3147 @DF-T3148 @DF-T3149
Scenario: To Validate the Inter Source Disambiguation - security_control
   Given  The deployement and security_control inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report



