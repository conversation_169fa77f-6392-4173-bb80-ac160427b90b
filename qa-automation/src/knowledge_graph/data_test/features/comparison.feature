Feature: Compare two output

@compare_loader_srdm @runner.continue_after_failed_step @DF-T2746 @DF-T2747 @DF-T2748 @DF-T2749 @DF-T2750 @DF-T2751 @DF-T2752 @DF-T2753 @DF-T2754 @DF-T2755 @DF-T2756 @DF-T2757 @DF-T2758 @DF-T2759 @DF-T2760 @DF-T2761 @DF-T2762 @DF-T2763 @DF-T2764 @DF-T2765 @DF-T2766 @DF-T2767 @DF-T2768 @DF-T2769 @DF-T2770 @DF-T2771 @DF-T2772 @DF-T2773 @DF-T2774 @DF-T2775 @DF-T2776 @DF-T2777 @DF-T2778 @DF-T2779 @DF-T2780 @DF-T2781 @DF-T2782 @DF-T2783 @DF-T2784 @DF-T2785 @DF-T2786 @DF-T2792 @DF-T879 @DF-T880 @DF-T881 @DF-T882 @DF-T883

Scenario: To compare the loader output with the expected output 
   Given  The run output and the expected output is read - normal - loader - sds_ei__vulnerability__qualys_knowledgebase__qid_cve_id
   Then   validate if run output and expected output is the same
   And    generate the comparsion report


@compare_loader_srdm_delta @runner.continue_after_failed_step @DF-T2744 @DF-T2793 @DF-T2794 @DF-T2795 @DF-T2796 @DF-T2797 @DF-T2798 @DF-T2799 @DF-T2800 @DF-T2801 @DF-T2802 @DF-T2803 @DF-T2804 @DF-T2805 @DF-T879 @DF-T880 @DF-T881 @DF-T882 @DF-T883

Scenario: To compare the loader output with the expected output 
   Given  The run output and the expected output is read - delta  - loader - sds_ei__vulnerability__qualys_knowledgebase__qid_cve_id_delta
   Then   validate if run output and expected output is the same
   And    generate the comparsion report


@compare_loader_struct @runner.continue_after_failed_step @DF-T2787 @DF-T2788 @DF-T2791 @DF-T2961 @DF-T2962 @DF-T879 @DF-T880 @DF-T881 @DF-T882 @DF-T883
Scenario: To compare the loader output with the expected output 
   Given  The run output and the expected output is read - normal - loader - sds_ei__cloud_compute__aws_resource_details__aws_eks_cluster_key
   Then   validate if run output and expected output is the same
   And    generate the comparsion report


@compare_loader_struct_delta @runner.continue_after_failed_step @DF-T2960 @DF-T879 @DF-T880 @DF-T881 @DF-T882 @DF-T883 
Scenario: To compare the loader output with the expected output 
   Given  The run output and the expected output is read - delta - loader - sds_ei__cloud_compute__aws_resource_details__aws_eks_cluster_key_delta
   Then   validate if run output and expected output is the same
   And    generate the comparsion report


@compare_loader_non_srdm @runner.continue_after_failed_step @DF-T2789 @DF-T2790 @DF-T879 @DF-T880 @DF-T881 @DF-T882 @DF-T883 
Scenario: To compare the loader output with the expected output 
   Given  The run output and the expected output is read - normal - loader - sds_em__finding_evidence
   Then   validate if run output and expected output is the same
   And    generate the comparsion report

