Feature: View Test Cases

@view @runner.continue_after_failed_step @DF-T2975

Scenario: Loader is run and the snapshot is saved
   Given  The config is read - loader - view
   Then   The Snapshot is read
   Then   The Snapshot is written


@view_inv @runner.continue_after_failed_step @DF-T2975

Scenario: Loader is run and snapshot is compared with previous snapshot 
   Given  The config is read - loader - view_inv
   Then   The Snapshot is read
   Then   The Snapshot are compared
   Then   Check inv_srdm snapshot not changed and View changed
   Then   The Snapshot is written


@view_inv @runner.continue_after_failed_step @DF-T2975

Scenario: Loader is run and snapshot is compared with previous snapshot 
   Given  The config is read - loader - view_inv
   Then   The Snapshot is read
   Then   The Snapshot are compared
   Then   Check Both inv_srdm snapshot and View are unchanged
   Then   The Snapshot is written



@view_temp @runner.continue_after_failed_step @DF-T2976 
Scenario: Loader is run and snapshot is compared with previous snapshot 
   Given  The config is read - loader - view_temp
   Then   The Snapshot is read
   Then   The Snapshot are compared
   Then   Check Both inv_srdm snapshot and View are changed
   Then   The Snapshot is written

@view_enrich @runner.continue_after_failed_step @DF-T2977
Scenario: Loader is run and snapshot is compared with previous snapshot 
   Given  The config is read - loader - view_enrich
   Then   The Snapshot is read
   Then   The Snapshot are compared
   Then   Check Both inv_srdm snapshot and View are changed
   Then   The Snapshot is written

@view_srdm @runner.continue_after_failed_step @DF-T2978
Scenario: Loader is run and snapshot is compared with previous snapshot 
   Given  The config is read - loader - view_srdm
   Then   The Snapshot is read
   Then   The Snapshot are compared
   Then   Check Both inv_srdm snapshot and View are changed
   Then   The Snapshot is written


@view_srdm_add @runner.continue_after_failed_step 
Scenario: Add a row to the SRDM
   Given  The config is read - loader - view_srdm
   Then   One Record is added to SRDM


@view_srdm_compare @runner.continue_after_failed_step @DF-T2979
Scenario: Loader is run and snapshot is compared with previous snapshot 
   Given  The config is read - loader - view_srdm
   Then   The Snapshot is read
   Then   The Snapshot are compared
   Then   Check Both inv_srdm snapshot and View are changed
   Then   The Snapshot is written

@view_srdm_remove @runner.continue_after_failed_step 
Scenario: Added SRDM row is removed 
   Given  The config is read - loader - view_srdm
   Then   Added Record is removed from SRDM







   