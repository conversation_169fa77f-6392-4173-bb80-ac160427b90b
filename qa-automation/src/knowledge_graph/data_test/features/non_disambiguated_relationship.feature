Feature: Non-Disambiguated Relationship Validation

@non_disambiguated_relationship @EGS-T7486 @EGS-T7487 @EGS-T7488 @EGS-T7489 @EGS-T7490 @EGS-T7491 @EGS-T7492 @EGS-T7493 @EGS-T7494 @runner.continue_after_failed_step
Scenario: To validate aws_iam_security_center_permission_set_assignment__person_has_identity
    Given   the "aws_iam_security_center_permission_set_assignment__person_has_identity" non dis rel config is read
    Then    validate if relationship_id for non dis rel is unique
    Then    check that no negative values are present for recency and lifetime for non dis rel
    Then    validate if the block variables for non dis rel are unique
    Then    validate the fields relationship_name and inverse_relationship_name for non dis rel
    Then    validate the fields relationship_first_seen_date and relationship_last_seen_date for non dis rel
    Then    validate the fields relationship_origin, source_entity_class and target_entity_class for non dis rel
    Then    validate the fields source_display_label and target_display_label for non dis rel
    Then    validate the common relationship fields for non dis rel
    Then    validate the optional attributes if any for non dis rel