Feature: Intra Source Validation

@sds_ei__account__aws_cloudtrail @runner.continue_after_failed_step @DF-T3150 @DF-T3151 @DF-T3152 @DF-T3153 @DF-T3154 @DF-T3155 @DF-T3156 @DF-T3157 @DF-T3158 @DF-T3159 @DF-T3160 @DF-T3161
Scenario: To Validate the Intra Source Disambiguation - sds_ei__account__aws_cloudtrail
   Given  The deployement and sds_ei__account__aws_cloudtrail intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__account__aws__iam_security_center_permission_set_assignment @runner.continue_after_failed_step @DF-T3162 @DF-T3163 @DF-T3164 @DF-T3165 @DF-T3166 @DF-T3167 @DF-T3168 @DF-T3169 @DF-T3170 @DF-T3171 @DF-T3172 @DF-T3173
Scenario: To Validate the Intra Source Disambiguation - sds_ei__account__aws__iam_security_center_permission_set_assignment
   Given  The deployement and sds_ei__account__aws__iam_security_center_permission_set_assignment intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__account__ms_azure_ad @runner.continue_after_failed_step @DF-T3174 @DF-T3175 @DF-T3176 @DF-T3177 @DF-T3178 @DF-T3179 @DF-T3180 @DF-T3181 @DF-T3182 @DF-T3183 @DF-T3184 @DF-T3185
Scenario: To Validate the Intra Source Disambiguation - sds_ei__account__ms_azure_ad
   Given  The deployement and sds_ei__account__ms_azure_ad intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__application__ms_defender @runner.continue_after_failed_step @DF-T3186 @DF-T3187 @DF-T3188 @DF-T3189 @DF-T3190 @DF-T3191 @DF-T3192 @DF-T3193 @DF-T3194 @DF-T3195 @DF-T3196 @DF-T3197
Scenario: To Validate the Intra Source Disambiguation - sds_ei__application__ms_defender
   Given  The deployement and sds_ei__application__ms_defender intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__cloud_account__aws @runner.continue_after_failed_step @DF-T3198 @DF-T3199 @DF-T3200 @DF-T3201 @DF-T3202 @DF-T3203 @DF-T3204 @DF-T3205 @DF-T3206 @DF-T3207 @DF-T3208 @DF-T3209
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_account__aws
   Given  The deployement and sds_ei__cloud_account__aws intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__cloud_account__ms_azure @runner.continue_after_failed_step @DF-T3210 @DF-T3211 @DF-T3212 @DF-T3213 @DF-T3214 @DF-T3215 @DF-T3216 @DF-T3217 @DF-T3218 @DF-T3219 @DF-T3220 @DF-T3221
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_account__ms_azure
   Given  The deployement and sds_ei__cloud_account__ms_azure intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report
   
   

@sds_ei__cloud_compute__aws @runner.continue_after_failed_step @DF-T3222 @DF-T3223 @DF-T3224 @DF-T3225 @DF-T3226 @DF-T3227 @DF-T3228 @DF-T3229 @DF-T3230 @DF-T3231 @DF-T3232 @DF-T3233
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_compute__aws
   Given  The deployement and sds_ei__cloud_compute__aws intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__cloud_compute__ms_azure @runner.continue_after_failed_step @DF-T3234 @DF-T3235 @DF-T3236 @DF-T3237 @DF-T3238 @DF-T3239 @DF-T3240 @DF-T3241 @DF-T3242 @DF-T3243 @DF-T3244 @DF-T3245
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_compute__ms_azure
   Given  The deployement and sds_ei__cloud_compute__ms_azure intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__cloud_compute__ms_defender @runner.continue_after_failed_step @DF-T3246 @DF-T3247 @DF-T3248 @DF-T3249 @DF-T3250 @DF-T3251 @DF-T3252 @DF-T3253 @DF-T3254 @DF-T3255 @DF-T3256 @DF-T3257
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_compute__ms_defender
   Given  The deployement and sds_ei__cloud_compute__ms_defender intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__cloud_compute__qualys @runner.continue_after_failed_step @DF-T3258 @DF-T3259 @DF-T3260 @DF-T3261 @DF-T3262 @DF-T3263 @DF-T3264 @DF-T3265 @DF-T3266 @DF-T3267 @DF-T3268 @DF-T3269
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_compute__qualys
   Given  The deployement and sds_ei__cloud_compute__qualys intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__cloud_compute__wiz @runner.continue_after_failed_step @DF-T3270 @DF-T3271 @DF-T3272 @DF-T3273 @DF-T3274 @DF-T3275 @DF-T3276 @DF-T3277 @DF-T3278 @DF-T3279 @DF-T3280 @DF-T3281
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_compute__wiz
   Given  The deployement and sds_ei__cloud_compute__wiz intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__cloud_container__aws @runner.continue_after_failed_step @DF-T3282 @DF-T3283 @DF-T3284 @DF-T3285 @DF-T3286 @DF-T3287 @DF-T3288 @DF-T3289 @DF-T3290 @DF-T3291 @DF-T3292 @DF-T3293
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_container__aws
   Given  The deployement and sds_ei__cloud_container__aws intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__cloud_container__ms_azure @runner.continue_after_failed_step @DF-T3294 @DF-T3295 @DF-T3296 @DF-T3297 @DF-T3298 @DF-T3299 @DF-T3300 @DF-T3301 @DF-T3302 @DF-T3303 @DF-T3304 @DF-T3305
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_container__ms_azure
   Given  The deployement and sds_ei__cloud_container__ms_azure intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__cloud_container__wiz @runner.continue_after_failed_step @DF-T3306 @DF-T3307 @DF-T3308 @DF-T3309 @DF-T3310 @DF-T3311 @DF-T3312 @DF-T3313 @DF-T3314 @DF-T3315 @DF-T3316 @DF-T3317
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_container__wiz
   Given  The deployement and sds_ei__cloud_container__wiz intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report



@sds_ei__cloud_storage__aws @runner.continue_after_failed_step @DF-T3318 @DF-T3319 @DF-T3320 @DF-T3321 @DF-T3322 @DF-T3323 @DF-T3324 @DF-T3325 @DF-T3326 @DF-T3327 @DF-T3328 @DF-T3329
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_storage__aws
   Given  The deployement and sds_ei__cloud_storage__aws intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__cloud_storage__ms_azure @runner.continue_after_failed_step @DF-T3330 @DF-T3331 @DF-T3332 @DF-T3333 @DF-T3334 @DF-T3335 @DF-T3336 @DF-T3337 @DF-T3338 @DF-T3339 @DF-T3340 @DF-T3341
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_storage__ms_azure
   Given  The deployement and sds_ei__cloud_storage__ms_azure intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__host__aws @runner.continue_after_failed_step @DF-T3342 @DF-T3343 @DF-T3344 @DF-T3345 @DF-T3346 @DF-T3347 @DF-T3348 @DF-T3349 @DF-T3350 @DF-T3351 @DF-T3352 @DF-T3353
Scenario: To Validate the Intra Source Disambiguation - sds_ei__host__aws
   Given  The deployement and sds_ei__host__aws intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__host__crowdstrike @runner.continue_after_failed_step @DF-T3354 @DF-T3355 @DF-T3356 @DF-T3357 @DF-T3358 @DF-T3359 @DF-T3360 @DF-T3361 @DF-T3362 @DF-T3363 @DF-T3364 @DF-T3365
Scenario: To Validate the Intra Source Disambiguation - sds_ei__host__crowdstrike
   Given  The deployement and sds_ei__host__crowdstrike intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__host__ms_azure_ad @runner.continue_after_failed_step @DF-T3366 @DF-T3367 @DF-T3368 @DF-T3369 @DF-T3370 @DF-T3371 @DF-T3372 @DF-T3373 @DF-T3374 @DF-T3375 @DF-T3376 @DF-T3377
Scenario: To Validate the Intra Source Disambiguation - sds_ei__host__ms_azure_ad
   Given  The deployement and sds_ei__host__ms_azure_ad intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__host__ms_azure @runner.continue_after_failed_step @DF-T3378 @DF-T3379 @DF-T3380 @DF-T3381 @DF-T3382 @DF-T3383 @DF-T3384 @DF-T3385 @DF-T3386 @DF-T3387 @DF-T3388 @DF-T3389
Scenario: To Validate the Intra Source Disambiguation - sds_ei__host__ms_azure
   Given  The deployement and sds_ei__host__ms_azure intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__host__ms_defender @runner.continue_after_failed_step @DF-T3390 @DF-T3391 @DF-T3392 @DF-T3393 @DF-T3394 @DF-T3395 @DF-T3396 @DF-T3397 @DF-T3398 @DF-T3399 @DF-T3400 @DF-T3401
Scenario: To Validate the Intra Source Disambiguation - sds_ei__host__ms_defender
   Given  The deployement and sds_ei__host__ms_defender intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__host__qualys @runner.continue_after_failed_step @DF-T3402 @DF-T3403 @DF-T3404 @DF-T3405 @DF-T3406 @DF-T3407 @DF-T3408 @DF-T3409 @DF-T3410 @DF-T3411 @DF-T3412 @DF-T3413
Scenario: To Validate the Intra Source Disambiguation - sds_ei__host__qualys
   Given  The deployement and sds_ei__host__qualys intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__host__tenable_sc @runner.continue_after_failed_step @DF-T3414 @DF-T3415 @DF-T3416 @DF-T3417 @DF-T3418 @DF-T3419 @DF-T3420 @DF-T3421 @DF-T3422 @DF-T3423 @DF-T3424 @DF-T3425
Scenario: To Validate the Intra Source Disambiguation - sds_ei__host__tenable_sc
   Given  The deployement and sds_ei__host__tenable_sc intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__host__wiz @runner.continue_after_failed_step @DF-T3426 @DF-T3427 @DF-T3428 @DF-T3429 @DF-T3430 @DF-T3431 @DF-T3432 @DF-T3433 @DF-T3434 @DF-T3435 @DF-T3436 @DF-T3437
Scenario: To Validate the Intra Source Disambiguation - sds_ei__host__wiz
   Given  The deployement and sds_ei__host__wiz intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report



@sds_ei__identity__active_directory @runner.continue_after_failed_step @DF-T3438 @DF-T3439 @DF-T3440 @DF-T3441 @DF-T3442 @DF-T3443 @DF-T3444 @DF-T3445 @DF-T3446 @DF-T3447 @DF-T3448 @DF-T3449
Scenario: To Validate the Intra Source Disambiguation - sds_ei__identity__active_directory
   Given  The deployement and sds_ei__identity__active_directory intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__identity__aws_cloudtrail @runner.continue_after_failed_step @DF-T3450 @DF-T3451 @DF-T3452 @DF-T3453 @DF-T3454 @DF-T3455 @DF-T3456 @DF-T3457 @DF-T3458 @DF-T3459 @DF-T3460 @DF-T3461
Scenario: To Validate the Intra Source Disambiguation - sds_ei__identity__aws_cloudtrail
   Given  The deployement and sds_ei__identity__aws_cloudtrail intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__identity__aws__iam_list_users @runner.continue_after_failed_step @DF-T3462 @DF-T3463 @DF-T3464 @DF-T3465 @DF-T3466 @DF-T3467 @DF-T3468 @DF-T3469 @DF-T3470 @DF-T3471 @DF-T3472 @DF-T3473
Scenario: To Validate the Intra Source Disambiguation - sds_ei__identity__aws__iam_list_users
   Given  The deployement and sds_ei__identity__aws__iam_list_users intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__identity__aws__iam_security_center_permission_set_assignment @runner.continue_after_failed_step @DF-T3474 @DF-T3475 @DF-T3476 @DF-T3477 @DF-T3478 @DF-T3479 @DF-T3480 @DF-T3481 @DF-T3482 @DF-T3483 @DF-T3484 @DF-T3485
Scenario: To Validate the Intra Source Disambiguation - sds_ei__identity__aws__iam_security_center_permission_set_assignment
   Given  The deployement and sds_ei__identity__aws__iam_security_center_permission_set_assignment intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__identity__ms_azure_ad @runner.continue_after_failed_step @DF-T3486 @DF-T3487 @DF-T3488 @DF-T3489 @DF-T3490 @DF-T3491 @DF-T3492 @DF-T3493 @DF-T3494 @DF-T3495 @DF-T3496 @DF-T3497
Scenario: To Validate the Intra Source Disambiguation - sds_ei__identity__ms_azure_ad
   Given  The deployement and sds_ei__identity__ms_azure_ad intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report



@sds_ei__person__ms_azure_ad @runner.continue_after_failed_step @DF-T3498 @DF-T3499 @DF-T3500 @DF-T3501 @DF-T3502 @DF-T3503 @DF-T3504 @DF-T3505 @DF-T3506 @DF-T3507 @DF-T3508 @DF-T3509
Scenario: To Validate the Intra Source Disambiguation - sds_ei__person__ms_azure_ad
   Given  The deployement and sds_ei__person__ms_azure_ad intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__host__winevents @runner.continue_after_failed_step @DF-T3510 @DF-T3511 @DF-T3512 @DF-T3513 @DF-T3514 @DF-T3515 @DF-T3516 @DF-T3517 @DF-T3518 @DF-T3519 @DF-T3520 @DF-T3521
Scenario: To Validate the Intra Source Disambiguation - sds_ei__host__winevents
   Given  The deployement and sds_ei__host__winevents intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__vulnerability__qualys @runner.continue_after_failed_step @DF-T3522 @DF-T3523 @DF-T3524 @DF-T3525 @DF-T3526 @DF-T3527 @DF-T3528 @DF-T3529 @DF-T3530 @DF-T3531 @DF-T3532 @DF-T3533
Scenario: To Validate the Intra Source Disambiguation - sds_ei__vulnerability__qualys
   Given  The deployement and sds_ei__vulnerability__qualys intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__account__active_directory @runner.continue_after_failed_step @DF-T3534 @DF-T3535 @DF-T3536 @DF-T3537 @DF-T3538 @DF-T3539 @DF-T3540 @DF-T3541 @DF-T3542 @DF-T3543 @DF-T3544 @DF-T3545
Scenario: To Validate the Intra Source Disambiguation - sds_ei__account__active_directory
   Given  The deployement and sds_ei__account__active_directory intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__account__aws__iam_list_users @runner.continue_after_failed_step @DF-T3546 @DF-T3547 @DF-T3548 @DF-T3549 @DF-T3550 @DF-T3551 @DF-T3552 @DF-T3553 @DF-T3554 @DF-T3555 @DF-T3556 @DF-T3557
Scenario: To Validate the Intra Source Disambiguation - sds_ei__account__aws__iam_list_users
   Given  The deployement and sds_ei__account__aws__iam_list_users intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__cloud_account__wiz @runner.continue_after_failed_step @DF-T3558 @DF-T3559 @DF-T3560 @DF-T3561 @DF-T3562 @DF-T3563 @DF-T3564 @DF-T3565 @DF-T3566 @DF-T3567 @DF-T3568 @DF-T3569
Scenario: To Validate the Intra Source Disambiguation - sds_ei__cloud_account__wiz
   Given  The deployement and sds_ei__cloud_account__wiz intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report

@sds_ei__host__itop @runner.continue_after_failed_step @DF-T3570 @DF-T3571 @DF-T3572 @DF-T3573 @DF-T3574 @DF-T3575 @DF-T3576 @DF-T3577 @DF-T3578 @DF-T3579 @DF-T3580 @DF-T3581
Scenario: To Validate the Intra Source Disambiguation - sds_ei__host__itop
   Given  The deployement and sds_ei__host__itop intra config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   And    generate the inter report