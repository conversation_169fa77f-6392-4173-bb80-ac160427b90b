from behave import userdata
from behave.model import Scenario
from src.utilities.data_test.common import Common
from behave.model_core import Status
from core.jira.tc_execution_status import jira_status_update
from core.common.common_utils import CommonUtils
from src.utilities.common.readProperties import ReadConfig
from core.common.logging_utils import setup_logger
from core.api.api_client import APIClient
import logging
import traceback
logger = setup_logger(__name__)
from src.utilities.common.api_utils import APIUtils


common = Common()
common_utils=CommonUtils()
readConfig=ReadConfig()
apiclient = APIClient()
api_utils= APIUtils()

exception_list = ['non_disambiguated_relationship','inter']


def before_all(context):
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logging.getLogger('seleniumwire').setLevel(logging.ERROR)
    logging.getLogger('urllib3').setLevel(logging.ERROR)
    logging.getLogger('selenium').setLevel(logging.ERROR)
    logging.getLogger('hpack.hpack').setLevel(logging.ERROR)
    logging.getLogger('hpack').setLevel(logging.ERROR)
    logging.getLogger('py4j').setLevel(logging.ERROR)
    logging.getLogger('py4j.clientserver').setLevel(logging.ERROR)
    logger.info("Reached start of before all")
    # ENV = common_utils.get_config_value('data_setup', 'environment')
    ENV = readConfig.get_env()
    logger.info("Env retrieved:")
    logger.info(ENV)
    if ENV=="AKS":
        context.spark = common.create_sparksession_azure_dev()
    else:
        context.spark = common.create_spark_session()    #EKS
    context.i=0
    logger.info("Reached end of before all")
    context.status = ""
    url = readConfig.get_deployment_url()
    headers = api_utils.set_headers()
    # logger.info(ssl_verify)
    context.deployement_config = apiclient.get(url, headers=headers)
    #logger.info(f"Inter Table name processing: {context.deployement_config}")
    context.inter_table = context.deployement_config['config_value']['spark_job_configs']['source_models']['intersource_disambiguated_models'] 
    context.intra_table = context.deployement_config['config_value']['spark_job_configs']['source_models']['intrasource_disambiguated_models'] 
    

def after_step(context, step):
    logger.info("Reached start of after step")
    # tag = str(context.scenario.tags[context.i])
    # context.i = context.i+1
    # if('config is read' not in step.name):
    #     status = context.status
    #     if 'non_disambiguated_relationship' in context.scenario.tags:
    #         jira_status_update(tag, status,"EGS", "EGS_Data_ND_Rel")
    #     elif 'inter' in context.scenario.tags:
    #         jira_status_update(tag, status,"EGS", "EGS_Data_Inter")
    # context.status = ""
    # logger.info("Reached end of after step")

def _get_jira_status(context, scenario):
    # Prefer context.status if set, else fall back to scenario.status
    if hasattr(context, 'status'):
        if context.status in ("Not Executed", "Fail", "Pass"):
            return context.status
    if scenario.status == Status.passed:
        return "Pass"
    if scenario.status == Status.failed:
        return "Fail"
    if scenario.status == Status.skipped:
        return "Blocked"
    return str(scenario.status)

def _log_scenario_result(scenario):
    logger.info("\n ---------------------------------------------------------------------------")
    if scenario.status == 'passed':
        logger.info(f"Scenario '{scenario.tags}' passed")
    elif scenario.status == 'failed':
        logger.info(f"Scenario '{scenario.tags}' failed due to exception")
    logger.info("\n ---------------------------------------------------------------------------")
    logger.info("Reached end of after scenario")

def after_scenario(context, scenario):
    logger.info("Reached start of after scenario")
    if any(string in context.scenario.tags for string in exception_list):
        return

    tag = str(scenario.tags)
    output_list = tag.strip('[]').replace("'", "").split(", ")
    status = _get_jira_status(context, scenario)
    logger.info("Scenario status:", scenario.status)

    for tc in output_list:
        jira_status_update(tc, status, "DF", "DF-R28")

    _log_scenario_result(scenario)

def after_all(context):
    logger.info("Reached start of after scenario")
    common.stopsparksession(context.spark)
    logger.info("Reached end of after scenario")
