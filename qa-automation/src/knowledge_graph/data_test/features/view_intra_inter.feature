Feature: View Inter/Intra Cases

@sds_ei__host_cd @runner.continue_after_failed_step @DF-T3603

Scenario: inter is run and the snapshot is saved
   Given  The inter/intra config is read - inter - sds_ei__host_cd
   Then   The inter/intra Snapshot is read
   Then   The inter/intra Snapshot is written


@sds_ei__host_no_change @runner.continue_after_failed_step @DF-T3603


Scenario: Loader is run and snapshot is compared with previous snapshot 
   Given  The inter/intra config is read - inter - sds_ei__host_cd
   Then   The inter/intra Snapshot is read
   Then   The inter/intra Snapshot are compared
   Then   Check snapshot is not changed
   Then   The inter/intra Snapshot is written


@sds_ei__host_conf_cg @runner.continue_after_failed_step @DF-T3600

Scenario: Loader is run and snapshot is compared with previous snapshot 
   Given  The inter/intra config is read - inter - sds_ei__host_conf_cg
   Then   The inter/intra Snapshot is read
   Then   The inter/intra Snapshot are compared
   Then   Check Snapshot have changed
   Then   The inter/intra Snapshot is written


@sds_ei__host_cd_inp_chng @runner.continue_after_failed_step @DF-T3601


Scenario: Loader is run and snapshot is compared with previous snapshot 
   Given  The inter/intra config is read - inter - sds_ei__host_cd_inp_chng
   Then   The inter/intra Snapshot is read
   Then   The inter/intra Snapshot are compared
   Then   Check Snapshot have changed
   Then   The inter/intra Snapshot is written




@loader_row_addition @runner.continue_after_failed_step 
Scenario: Add a row to the loader
   Given  The inter/intra config is read - inter - sds_ei__host_cd_inp_chng
   Then   One Record is added to loader


@row_add_compare @runner.continue_after_failed_step @DDF-T3602

Scenario: Loader is run and snapshot is compared with previous snapshot 
   Given  The inter/intra config is read - inter - sds_ei__host_cd_inp_chng
   Then   The inter/intra Snapshot is read
   Then   The inter/intra Snapshot are compared
   Then   Check Snapshot have changed
   Then   The inter/intra Snapshot is written

@loader_row_remove @runner.continue_after_failed_step 
Scenario: Added loader row is removed 
   Given  The inter/intra config is read - inter - sds_ei__host_cd_inp_chng
   Then   Added Record is removed from loader







   