from behave import given, then
from core.common.common_utils import CommonUtils
from src.utilities.common.common_utilities import CommonUtilities
from src.knowledge_graph.data_test.tests.comparison import OuputComparison
from src.utilities.common.readProperties import ReadConfig
from src.utilities.data_test.common import Common
from core.common.logging_utils import setup_logger
from core.api.api_client import APIClient
from src.utilities.common.api_utils import APIUtils
import os ,json
 

utils_common = CommonUtils()
utils_common_data = CommonUtilities()
mismatch_error_message = "Test Case Failed"
readConfig = ReadConfig()
env = readConfig.get_config('data_setup', 'environment')
apiclient = APIClient()
api_utils= APIUtils()
logger = setup_logger(__name__)




@given(u'The run output and the expected output is read - {run_type} - {folder_name} - {config}')
def read_json(context, run_type, folder_name, config):
    config = str(config)
    context.folder_name = str(folder_name)
    run = str(run_type)
    json_file_path = os.path.join(
    "src", "knowledge_graph", "resources", "config",
    context.folder_name,
    f"{config}.json")

    config = utils_common.jsonreader(json_file_path)

    logger.info(f"JSON File : {config}")

    context.output_output_comarison = OuputComparison(context.spark, config, run, context)



@then('validate if run output and expected output is the same')
def compare_the_output(context):
    
    context.report_file, mismatch_flag = context.output_output_comarison.output_comparison()
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

@then('generate the comparsion report')
def copytooutputfolder(context):
    context.output_output_comarison.generate_report(context.report_file,context)

    

        

