from behave import given, then
from core.common.common_utils import CommonUtils
from src.utilities.common.common_utilities import CommonUtilities
from src.knowledge_graph.data_test.tests.view_intra_inter import ViewInterComparison
from src.utilities.common.readProperties import ReadConfig
from src.utilities.data_test.common import Common
from core.common.logging_utils import setup_logger
from core.api.api_client import APIClient
from src.utilities.common.api_utils import APIUtils
import os, json

# Initialize utilities and configurations
utils_common = CommonUtils()
utils_common_data = CommonUtilities()
readConfig = ReadConfig()
env = readConfig.get_config('data_setup', 'environment')
apiclient = APIClient()
api_utils = APIUtils()
logger = setup_logger(__name__)

# Constants
STATUS_PASS = "Pass"
STATUS_FAIL = "Fail"


@given(u'The inter/intra config is read - {folder_name} - {config}')
def read_json(context, folder_name, config):
    """Read JSON configuration file and initialize snapshot comparison."""
    try:
        config = str(config)
        context.folder_name = str(folder_name)
        
        json_file_path = os.path.join(
            "src", "knowledge_graph", "resources", "config",
            context.folder_name,
            f"{config}.json"
        )
        
        if not os.path.exists(json_file_path):
            raise FileNotFoundError(f"Config file not found: {json_file_path}")
        
        context.config_data = utils_common.jsonreader(json_file_path)
        logger.info(f"[CONFIG] JSON File loaded: {context.config_data}")
        
        context.snap_shot_inter_comparison = ViewInterComparison(context.config_data)
        
    except Exception as e:
        logger.error(f"[ERROR] Error reading config: {str(e)}")
        context.status = STATUS_FAIL
        raise


@then('The inter/intra Snapshot is read')
def read_the_snapshot(context):
    """Read the snapshot data using Spark."""
    try:
        context.snapshot_df = context.snap_shot_inter_comparison.snapshot_read(context.spark)
        logger.info("[SUCCESS] Snapshot read successfully")
        
    except Exception as e:
        logger.error(f"[ERROR] Error reading snapshot: {str(e)}")
        context.status = STATUS_FAIL
        raise


@then('The inter/intra Snapshot is written')
def save_the_snapshot(context):
    """Write the snapshot data."""
    try:
        context.snap_shot_inter_comparison.snapshot_write(context.snapshot_df)
        logger.info("[SUCCESS] Snapshot written successfully")
        
    except Exception as e:
        logger.error(f"[ERROR] Error writing snapshot: {str(e)}")
        context.status = STATUS_FAIL
        raise


@then('The inter/intra Snapshot are compared')
def compare_the_output(context):
    """Compare snapshots and generate comparison dataframe."""
    try:
        context.compare_df = context.snap_shot_inter_comparison.snapshot_compare(
            context.spark, context.snapshot_df
        )
        logger.info("[SUCCESS] Snapshot comparison completed")
        
    except Exception as e:
        logger.error(f"[ERROR] Error comparing snapshots: {str(e)}")
        context.status = STATUS_FAIL
        raise


@then('Check Snapshot have changed')
def check_change(context):
    try:
        mismatch_flag = context.snap_shot_inter_comparison.check_change(context.compare_df)
        logger.info(f"[RESULT] mismatch_flag in steps: {mismatch_flag}")
        
        #Set status based on mismatch flag
        if mismatch_flag:
            context.status = STATUS_FAIL
            logger.error("[STEP_FAIL] Test failed - validation did not pass")
        else:
            context.status = STATUS_PASS
            logger.info("[STEP_PASS] Test passed - validation successful")
            
    except Exception as e:
        logger.error(f"[ERROR] Error checking single change: {str(e)}")
        context.status = STATUS_FAIL
        raise


@then('Check snapshot is not changed')
def check_no_change(context):
    try:
        mismatch_flag = context.snap_shot_inter_comparison.check_no_change(context.compare_df)
        logger.info(f"[RESULT] mismatch_flag in steps: {mismatch_flag}")
        
        if mismatch_flag:
            context.jira_status = STATUS_FAIL
            logger.error("[STEP_FAIL] Test failed - both components did not change as expected")
        else:
            context.jira_status = STATUS_PASS
            logger.info("[STEP_PASS] Test passed - both components changed as expected")
            
    except Exception as e:
        logger.error(f"[ERROR] Error checking both changes: {str(e)}")
        context.jira_status = STATUS_FAIL
        raise
    
    
    
@then('One Record is added to loader')
def inp_row_addition(context):
    """Add one record to loader."""
    try:
     context.snap_shot_inter_comparison.inp_row_addition(context.spark)
  
    except Exception as e:
        logger.error(f"[ERROR] Error writing loader: {str(e)}")
        raise   
    
@then('Added Record is removed from loader')
def inp_removal(context):
    """Added Record is removed from loader"""
    try:
     context.snap_shot_inter_comparison.inp_removal(context.spark)
  
    except Exception as e:
        logger.error(f"[ERROR] Error writing loader: {str(e)}")
        raise   