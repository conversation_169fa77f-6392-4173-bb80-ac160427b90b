from behave import given, then
from src.knowledge_graph.data_test.tests.knowledge_validation import KnowledgeGraphValidation
from src.utilities.data_test.common import Common
from core.common.common_utils import CommonUtils
from core.api.api_client import APIClient
import re
from src.utilities.common.readProperties import ReadConfig 
from src.utilities.common.api_utils import APIUtils
from core.common.logging_utils import setup_logger 
import json

logger = setup_logger(__name__)


common=CommonUtils()
mismatch_error_message = "Test Case Failed"
readConfig = ReadConfig()
env = readConfig.get_config('data_setup', 'environment')
apiclient = APIClient()
api_utils= APIUtils()
kg_val_utils = KnowledgeGraphValidation()


@given('The deployement and inter config is read')
def read_configs_json(context):
    logger.info("entered function to read configss::::::")
    # ssl_verify_str=readConfig.get_config_value('url','ssl_verify', True)
    # ssl_verify = ssl_verify_str.lower() in ('true', '1', 'yes')
    url = readConfig.get_deployment_url()
    headers = api_utils.set_headers()
    logger.info(url)
    logger.info(headers)
    # logger.info(ssl_verify)
    context.deployement_config = apiclient.get(url, headers=headers)
    logger.info(f"Deployment Config Response: {context.deployement_config}")
    if 'config_value' not in context.deployement_config:
        logger.error("'config_value' key is missing in deployment config response!")
        raise KeyError("'config_value' key is missing in deployment config response!")									
    inter_table = context.deployement_config['config_value']['spark_job_configs']['source_models']['intersource_disambiguated_models']   
    logger.info(f"Extracted inter_table: {inter_table}")
    url = readConfig.get_spark_jobs_url() + "/" + inter_table[0]
    headers = api_utils.set_headers()
    context.inter_config = apiclient.get(url, headers=headers)
    publish_table=context.deployement_config['config_value']['spark_job_configs']['source_models']['publisher']
    url=readConfig.get_spark_jobs_url() + "/" + publish_table[0]
    headers=api_utils.set_headers()
    context.publish_config = apiclient.get(url, headers=headers)

    logger.info(f"Inter Config Response: {context.inter_config}")
    context.kg_validation = KnowledgeGraphValidation()


@then('Validate if the inidvidual fragments tables are available and the count are correct')
def fragment_count_compare(context):   
    mismatch_flag, context.pass_output, context.fail_output = context.kg_validation.fragment_count_comp(context.spark, context.deployement_config,context.inter_config)
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"
        
        
@then('Validate the publish count')
def publish_count_check(context):   
    mismatch_flag, context.pass_output, context.fail_output = context.kg_validation.publish_count(context.spark, context.deployement_config, context.publish_config,context.pass_output, context.fail_output)
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

   
@then('Generate the report')
def generate_report(context):   
    context.kg_validation.generate_report(context.pass_output,context.fail_output)
    print("File Written Successfully")

@given('The graph properties are read and validated')
def val_graph_props(context):
    logger.info("\n\n----Moving into graph_properties_val function")
    # kg_val_utils.graph_properties_val(context.spark, catalog_schema)
    mismatch_flag, context.pass_output, context.fail_output = kg_val_utils.graph_properties_val(context.spark, context)    
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

@given('The deployment config is read')
# To read deployment config, save the response and print it neatly
def read_deployment_config_json(context):
    logger.info("\n\n:::Reading Deployment Config:::")
    url = readConfig.get_deployment_url()
    headers = api_utils.set_headers()
    logger.info(f"URL: {url}")
    logger.info(f"Headers: {headers}")
    context.deployment_config = apiclient.get(url, headers=headers)

    if 'config_value' not in context.deployment_config:
        logger.error("'config_value' key is missing in deployment config response")
        raise KeyError("'config_value' key is missing in deployment config response")	
    return context.deployment_config


@then('Extract relationship model names from deployment config')
def get_rel_model_names(context):
    logger.info("\n\n---Extracting Relationship Model Names from Deployment Config---")
    deplymnt_json = read_deployment_config_json(context)

    try:
        rel_model_names = (
            deplymnt_json["config_value"]
                        ["spark_job_configs"]
                        ["source_models"]
                        ["relationship_models"]
        )
        logger.info("\n:::Relationship Model Names:::\n" + json.dumps(rel_model_names, indent=4))
        context.rel_model_names = rel_model_names

    except KeyError as e:
        logger.error(f"Missing expected key in deployment config: {e}")
        raise

    
@then('Extract inventory model names from deployment config')
def get_inv_model_names(context):
    logger.info("\n\n---Extracting Inventory Model Names from Deployment Config---")
    deplymnt_json = read_deployment_config_json(context)

    try:
        inventory_model_names = (
            deplymnt_json["config_value"]
                        ["spark_job_configs"]
                        ["source_models"]
                        ["inventory_models"]
        )
        logger.info("\n:::Inventory Model Names:::\n" + json.dumps(inventory_model_names, indent=4))
        context.inventory_model_names = inventory_model_names

    except KeyError as e:
        logger.error(f"Missing expected key in deployment config: {e}")
        raise	

@then('Validate all loader counts against corresponding SRDMs')
def validate_all_loader_count(context):
    logger.info("\n\n---Validating Loader Counts---")
    inventory_model_names = context.inventory_model_names
    try:
        kg_val_utils.validate_loader_count(inventory_model_names)
    except KeyError as e:
        logger.error(f"Missing expected key in deployment config: {e}")
        raise

@given("all deployed {config_type} are acquired")
def get_all_deployed_dicts(context, config_type):
    logger.info(f"\n\n---Getting all deployed {config_type}---")
    try:
        context.deployed_entities = kg_val_utils.fetch_and_print_required_configs(config_type)
    except KeyError as e:
        logger.error(f"Missing expected key in deployment config: {e}")
        raise

@then("each {entity_or_rel} data dictionary is validated")
def validate_entities_dd(context, entity_or_rel):
    try:
        kg_val_utils.all_entities_or_rel_dd(context.spark, context.deployed_entities, entity_or_rel)
    except KeyError as e:
        logger.error(f"Missing expected key in deployment config: {e}")
        raise
									
										
																		 
					  
																															   
																														 
																 
		   
																																		
																																  
																 
