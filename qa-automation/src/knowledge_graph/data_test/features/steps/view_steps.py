from behave import given, then
from core.common.common_utils import CommonUtils
from src.utilities.common.common_utilities import CommonUtilities
from src.knowledge_graph.data_test.tests.view import ViewSnapshotComparison
from src.utilities.common.readProperties import ReadConfig
from src.utilities.data_test.common import Common
from core.common.logging_utils import setup_logger
from core.api.api_client import APIClient
from src.utilities.common.api_utils import APIUtils
import os, json

# Initialize utilities and configurations
utils_common = CommonUtils()
utils_common_data = CommonUtilities()
readConfig = ReadConfig()
env = readConfig.get_config('data_setup', 'environment')
apiclient = APIClient()
api_utils = APIUtils()
logger = setup_logger(__name__)

# Constants
STATUS_PASS = "Pass"
STATUS_FAIL = "Fail"


@given(u'The config is read - {folder_name} - {config}')
def read_json(context, folder_name, config):
    """Read JSON configuration file and initialize snapshot comparison."""
    try:
        config = str(config)
        context.folder_name = str(folder_name)
        
        json_file_path = os.path.join(
            "src", "knowledge_graph", "resources", "config",
            context.folder_name,
            f"{config}.json"
        )
        
        if not os.path.exists(json_file_path):
            raise FileNotFoundError(f"Config file not found: {json_file_path}")
        
        context.config_data = utils_common.jsonreader(json_file_path)
        logger.info(f"[CONFIG] JSON File loaded: {context.config_data}")
        
        context.snapshot_comparison = ViewSnapshotComparison(context.config_data)
        
    except Exception as e:
        logger.error(f"[ERROR] Error reading config: {str(e)}")
        context.status = STATUS_FAIL
        raise


@then('The Snapshot is read')
def read_the_snapshot(context):
    """Read the snapshot data using Spark."""
    try:
        context.snapshot_df = context.snapshot_comparison.snapshot_read(context.spark)
        logger.info("[SUCCESS] Snapshot read successfully")
        
    except Exception as e:
        logger.error(f"[ERROR] Error reading snapshot: {str(e)}")
        context.status = STATUS_FAIL
        raise


@then('The Snapshot is written')
def save_the_snapshot(context):
    """Write the snapshot data."""
    try:
        context.snapshot_comparison.snapshot_write(context.snapshot_df)
        logger.info("[SUCCESS] Snapshot written successfully")
        
    except Exception as e:
        logger.error(f"[ERROR] Error writing snapshot: {str(e)}")
        context.status = STATUS_FAIL
        raise


@then('The Snapshot are compared')
def compare_the_output(context):
    """Compare snapshots and generate comparison dataframe."""
    try:
        context.compare_df = context.snapshot_comparison.snapshot_compare(
            context.spark, context.snapshot_df
        )
        logger.info("[SUCCESS] Snapshot comparison completed")
        
    except Exception as e:
        logger.error(f"[ERROR] Error comparing snapshots: {str(e)}")
        context.status = STATUS_FAIL
        raise


@then('Check inv_srdm snapshot not changed and View changed')
def check_change_in_one(context):
    """Check if only one component (View) has changed while inv_srdm remains unchanged."""
    try:
        mismatch_flag = context.snapshot_comparison.check_change_in_one(context.compare_df)
        logger.info(f"[RESULT] mismatch_flag in steps: {mismatch_flag}")
        
        #Set status based on mismatch flag
        if mismatch_flag:
            context.status = STATUS_FAIL
            logger.error("[STEP_FAIL] Test failed - validation did not pass")
        else:
            context.status = STATUS_PASS
            logger.info("[STEP_PASS] Test passed - validation successful")
            
    except Exception as e:
        logger.error(f"[ERROR] Error checking single change: {str(e)}")
        context.status = STATUS_FAIL
        raise


@then('Check Both inv_srdm snapshot and View are changed')
def check_change_in_both(context):
    """Check if both inv_srdm snapshot and View have changed."""
    try:
        mismatch_flag = context.snapshot_comparison.check_change_in_both(context.compare_df)
        logger.info(f"[RESULT] mismatch_flag in steps: {mismatch_flag}")
        
        if mismatch_flag:
            context.jira_status = STATUS_FAIL
            logger.error("[STEP_FAIL] Test failed - both components did not change as expected")
        else:
            context.jira_status = STATUS_PASS
            logger.info("[STEP_PASS] Test passed - both components changed as expected")
            
    except Exception as e:
        logger.error(f"[ERROR] Error checking both changes: {str(e)}")
        context.jira_status = STATUS_FAIL
        raise
    
    
@then('Check Both inv_srdm snapshot and View are unchanged')
def check_no_change_in_both(context):
    """Check if both inv_srdm snapshot and View have changed."""
    try:
        mismatch_flag = context.snapshot_comparison.check_no_change_in_both(context.compare_df)
        logger.info(f"[RESULT] mismatch_flag in steps: {mismatch_flag}")
        
        if mismatch_flag:
            context.jira_status = STATUS_FAIL
            logger.error("[STEP_FAIL] Test failed - both components changed it should not change")
        else:
            context.jira_status = STATUS_PASS
            logger.info("[STEP_PASS] Test passed - both components did not changed as expected")
            
    except Exception as e:
        logger.error(f"[ERROR] Error checking both changes: {str(e)}")
        context.jira_status = STATUS_FAIL
        raise
    
    
    
@then('One Record is added to SRDM')
def srdm_addition(context):
    """Add one record to SRDM."""
    try:
     context.snapshot_comparison.srdm_addition(context.spark)
  
    except Exception as e:
        logger.error(f"[ERROR] Error writing srdm: {str(e)}")
        raise   
    
@then('Added Record is removed from SRDM')
def srdm_removal(context):
    """Added Record is removed from SRDM"""
    try:
     context.snapshot_comparison.srdm_removal(context.spark)
  
    except Exception as e:
        logger.error(f"[ERROR] Error writing srdm: {str(e)}")
        raise   