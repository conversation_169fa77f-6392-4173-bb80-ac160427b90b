from behave import given, then
from core.common.common_utils import CommonUtils
from src.utilities.common.common_utilities import CommonUtilities
from src.knowledge_graph.data_test.tests.intra_inter_validation import IntraInterValidation
from src.utilities.common.readProperties import ReadConfig
from src.utilities.data_test.common import Common
from core.common.logging_utils import setup_logger
from core.api.api_client import APIClient
from src.utilities.common.api_utils import APIUtils
import tempfile

utils_common = CommonUtils()
utils_common_data = CommonUtilities()
mismatch_error_message = "Test Case Failed"
readConfig = ReadConfig()
env = readConfig.get_config('data_setup', 'environment')
apiclient = APIClient()
api_utils= APIUtils()
logger = setup_logger(__name__)

def update_context_status(context, mismatch_flag):
    if mismatch_flag:
        context.status = "Fail"
    else:
        if not hasattr(context, "status") or context.status != "Fail":
            context.status = "Pass"




@given(u'The deployement and {entity} {type} config is read')
def read_configs_json(context,entity,type):
    #logger.info(f"Deployment Config Response: {context.deployement_config}")
    context.type = type
    if context.type == "inter":
        table_name = f"sds_ei__{entity}" 
    else:
        table_name = entity
    
    logger.info(f"Inter/Intra Table name processing: {table_name}")
    if table_name in context.inter_table or table_name in context.intra_table  :
        url = readConfig.get_spark_jobs_url() + "/" + table_name
        logger.info(f"URL: {url}")
        headers = api_utils.set_headers()
        context.inter_config = apiclient.get(url, headers=headers)
        context.intra_inter_validation = IntraInterValidation(context.spark, context.inter_config, type, context)
        # Create a single temp file for all error output in this scenario
        context.shared_fail_file = tempfile.NamedTemporaryFile(mode='a', delete=False)
        context.shared_fail_file_path = context.shared_fail_file.name
    else:
       context.scenario.skip("Skipping scenario as the provided Inter config is not present !!!")

@then('validate if the disambiguated_p_id count in resolver and final intersource output is the same')
def inter_out_resolver_count_comp(context):  
    mismatch_flag = False
    _, mismatch_flag = context.intra_inter_validation.intra_inter_out_resolver_count_comp(context.shared_fail_file)
    update_context_status(context, mismatch_flag)

@then('validate if the p_id count of output after union of loaders and resolver is the same')
def inter_union_resolver_count_comp(context):
    mismatch_flag = False
    _, mismatch_flag = context.intra_inter_validation.intra_inter_union_resolver_count_comp(context.inter_config, context.type, context.shared_fail_file)
    update_context_status(context, mismatch_flag)

@then('validation of Exception - Validate whether the records with matching candidate keys other than exception case is disambiguated')
def inter_exception_other_matching_keys(context):
    mismatch_flag = False
    _, mismatch_flag = context.intra_inter_validation.intra_inter_exception_other_matching_keys(context.shared_fail_file)
    update_context_status(context, mismatch_flag)
    print("Validation of Exception - Validate whether the records with matching candidate keys other than exception case is disambiguated Done")
    
@then('validate that the records under entity exception should not be disambiguated')
def inter_exception_no_other_matching_keys(context):
    mismatch_flag = False
    _, mismatch_flag = context.intra_inter_validation.intra_inter_exception_no_other_matching_keys(context.shared_fail_file)
    update_context_status(context, mismatch_flag)

@then('validate if all the candidate keys having the same value is disambiguated into one p_id')
def inter_disamb_samekeys_one_p_id(context):
    mismatch_flag = False
    _, mismatch_flag = context.intra_inter_validation.intra_inter_disamb_samekeys_one_p_id( context.shared_fail_file)
    update_context_status(context, mismatch_flag)

@then('validate that all date and time fields are aggregated based on min or max')
def inter_agg(context):
    mismatch_flag = False
    _, mismatch_flag = context.intra_inter_validation.intra_inter_aggregate(context.inter_config, context.shared_fail_file)
    update_context_status(context, mismatch_flag)

@then('check whether the p_id is unique')
def inter_p_id_check(context):
    mismatch_flag = False
    _, mismatch_flag = context.intra_inter_validation.intra_inter_out_p_id_check(context.shared_fail_file)
    update_context_status(context, mismatch_flag)
    
@then('validate that the display_label, primary_key and p_id is not null')
def inter_p_id_primary_key_display_label_check(context):
    mismatch_flag = False
    _, mismatch_flag = context.intra_inter_validation.intra_inter_out_p_id_primary_key_display_label_check(context.shared_fail_file)
    update_context_status(context, mismatch_flag)

@then('validate that lifetime, recency, recent_activity, observed_lifetime are not negative')
def lifetime_recency_recent_activity_observed_lifetime_negative_check(context):
    mismatch_flag = False
    _, mismatch_flag = context.intra_inter_validation.lifetime_recency_recent_activity_observed_lifetime_negative_check(context.shared_fail_file)
    update_context_status(context, mismatch_flag)

@then('validate that in the disambiguated group, a record is having at least one matching candidate key value')
def inter_disamb_at_least_matching_key_in_a_groups(context):
    mismatch_flag = False
    _, mismatch_flag = context.intra_inter_validation.intra_inter_disamb_at_least_matching_key_in_a_group(context.shared_fail_file)
    update_context_status(context, mismatch_flag)

@then('generate the inter report')
def copytooutputfolder(context):
    context.shared_fail_file.close()  # Ensure all output is flushed to disk
    context.intra_inter_validation.generate_report(context.shared_fail_file_path)
    
    
        
        

        

