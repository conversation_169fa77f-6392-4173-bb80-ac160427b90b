from behave import given, then
from core.common.common_utils import CommonUtils
from src.knowledge_graph.data_test.tests.disambiguated_relationship import DisambiguatedRelationshipValidation

utils_common = CommonUtils()

error_message = "Test Case Failed"

@given(u'the "{dis_relationship}" config is read')
def read_json(context, dis_relationship):
    dis_relationship_str = str(dis_relationship)
    json_file = f'src/entity_inventory/data_test/test_data/disambiguated_relationship/{dis_relationship_str}.json'
    context.rel_config = utils_common.jsonreader(json_file)
    context.disambiguated_relationship_validation = DisambiguatedRelationshipValidation(context.spark, context.rel_config)

@then('validate if relationship_id is unique')
def check_relationship_id_uniqueness(context):
    total_count, distinct_count = context.disambiguated_relationship_validation.relationship_id_uniqueness_check() 

    if total_count == 0 or distinct_count == 0:
        context.status = "Not Executed"
    elif total_count == distinct_count:
        context.status = "Pass"
    else:
        context.status = "Fail"

@then('check that no negative values are present for recency and lifetime')
def check_non_negative_values_for_recency_and_lifetime(context):
    neg_counter = context.disambiguated_relationship_validation.non_negative_values_for_recency_and_lifetime_check()
    
    if neg_counter == 0:
        context.status = 'Pass'
    else:
        context.status = 'Fail'

@then('validate if the block variables are unique')
def check_block_variables_uniqueness(context):
    total_count, exp_distinct_count, rel_distinct_count = context.disambiguated_relationship_validation.block_variables_uniqueness_check(context.rel_config)

    if total_count == exp_distinct_count == rel_distinct_count:
        context.status = "Pass"
    else:
        context.status = "Fail"

@then('validate the fields relationship_name and inverse_relationship_name')
def check_rel_name_and_inv_rel_name(context):
    not_matching_count = context.disambiguated_relationship_validation.rel_name_and_inv_rel_name_check()

    if not_matching_count == 0:
        context.status = 'Pass'
    else:
        context.status = 'Fail'

@then('validate the fields relationship_first_seen_date and relationship_last_seen_date')
def check_first_seen_and_last_seen(context):
    not_matching_count = context.disambiguated_relationship_validation.first_seen_and_last_seen_check()
    
    if not_matching_count == 0:
        context.status = 'Pass'
    else:
        context.status = 'Fail'

@then('validate the fields relationship_origin, source_entity_class and target_entity_class')
def check_relationship_origin_source_entity_class_and_target_entity_class(context):
    not_matching_count = context.disambiguated_relationship_validation.relationship_origin_source_entity_class_and_target_entity_class_check()

    if not_matching_count == 0:
        context.status = 'Pass'
    else:
        context.status = 'Fail'

@then('validate the fields source_display_label and target_display_label')
def check_source_display_label_target_display_label(context):
    not_matching_count = context.disambiguated_relationship_validation.source_display_label_target_display_label_check()

    if not_matching_count == 0:
        context.status = 'Pass'
    else:
        context.status = 'Fail'

@then('validate the common relationship fields')
def check_common_relationship_fields(context):
    not_matching_count = context.disambiguated_relationship_validation.common_relationship_fields_check()
    
    if not_matching_count == 0:
        context.status = 'Pass'
    else:
        context.status = 'Fail'

@then('validate the optional attributes if any')
def check_optional_attributes(context):
    master_not_matching_count, optional_attributes_present = context.disambiguated_relationship_validation.optional_attributes_check(context.rel_config)
    
    if(optional_attributes_present == False):
        context.status = "Not Executed"
    elif master_not_matching_count == 0:
        context.status = 'Pass'
    else:
        context.status = 'Fail'
