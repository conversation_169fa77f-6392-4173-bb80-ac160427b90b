Feature: Entity KG Validation

@sanity-test @KG-001 @publish @runner.continue_after_failed_step @DF-T745 @DF-T760
Scenario: To validate fragments
   Given  The deployement and inter config is read
   Then   Validate if the inidvidual fragments tables are available and the count are correct
   Then   Validate the publish count
   And    Generate the report

@sanity-test @graph_properties @KG-002 @runner.continue_after_failed_step
Scenario: To validate graph properties
   Given The graph properties are read and validated

@sanity-test @loader @KG-003 @runner.continue_after_failed_step
Scenario: To validate loader count against SRDM
   Given Extract inventory model names from deployment config
   Then Validate all loader counts against corresponding SRDMs

@sanity-test @entity_dd @KG-004 @runner.continue_after_failed_step
Scenario: To validate entity data dictionary
   Given all deployed entities are acquired
   Then each entity data dictionary is validated

@sanity-test @rel_dd @KG-005 @runner.continue_after_failed_step
Scenario: To validate relationship data dictionary
   Given all deployed relationships are acquired
   Then each relationship data dictionary is validated