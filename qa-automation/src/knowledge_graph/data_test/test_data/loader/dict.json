{"Host": {"AD": {"sdm": "microsoft__active_directory", "output": "sds_ei__host__active_directory__object_guid", "primary_key": "object_guid", "filter": "sam_account_type='MACHINE_ACCOUNT' and (object_guid IS NOT NULL AND TRIM(object_guid) != '')", "condition": "", "display_label_confidence": "fqdn,host_name,azure_ad_device_id,primary_key"}, "AWS_Resource_Details": {"sdm": "aws__resource_details", "output": "sds_ei__host__aws_resource_details__arn", "primary_key": "arn", "filter": "resource_type='AWS::EC2::Instance' AND (arn IS NOT NULL AND TRIM(arn) != '')", "condition": "", "display_label_confidence": "fqdn,dns_name,host_name,primary_key"}, "Defender_Device_Software_Vuln": {"sdm": "microsoft_azure__defender_device_software_vuln", "output": "sds_ei__host__ms_defender_device_tvm_software_vulnerabilities__device_id", "primary_key": "device_id", "filter": "device_id IS NOT NULL AND TRIM(device_id) != ''", "condition": "", "display_label_confidence": "fqdn,dns_name,host_name,primary_key"}, "Intunes": {"sdm": "microsoft__intune", "output": "sds_ei__host__ms_intunes__device_id", "primary_key": "device_id", "filter": "device_id IS NOT NULL AND TRIM(device_id) != ''", "condition": "", "display_label_confidence": "host_name,azure_ad_device_id,primary_key"}, "WinEvents_4624_Computer_Name": {"sdm": "microsoft__windows_security_logs", "output": "sds_ei__host__winevents_4624__computer_name", "primary_key": "computer_name", "filter": "computer_name IS NOT NULL AND TRIM(computer_name) != '' AND computer_name != '-' and event_code='4624'", "condition": "", "display_label_confidence": "fqdn,host_name,primary_key"}, "WinEvents_4624_Workstation_Name": {"sdm": "microsoft__windows_security_logs", "output": "sds_ei__host__winevents_4624__workstation_name", "primary_key": "workstation_name", "filter": "workstation_name IS NOT NULL AND TRIM(workstation_name) != '' AND workstation_name != '-' and event_code='4624'", "condition": "", "display_label_confidence": "fqdn,host_name,primary_key"}, "Winevents_4725_Object_Account_Name": {"sdm": "microsoft__windows_security_logs", "output": "sds_ei__host__winevents_4725__object_account_name", "primary_key": "object_account_name", "filter": "object_account_name LIKE '%\\$' AND (object_account_name IS NOT NULL AND TRIM(object_account_name) != '') and event_code='4725'", "condition": "", "display_label_confidence": "host_name,primary_key"}, "Winevents_4725_Computer_Name": {"sdm": "microsoft__windows_security_logs", "output": " sds_ei__host__winevents_4725__computer_name", "primary_key": "computer_name", "filter": "computer_name IS NOT NULL AND TRIM(computer_name) != '' AND computer_name != '-' and event_code='4725'", "condition": "", "display_label_confidence": "fqdn,host_name,primary_key"}, "Winevents_4726_Computer_Name": {"sdm": "microsoft__windows_security_logs", "output": "sds_ei__host__winevents_4726__computer_name", "primary_key": "computer_name", "filter": "event_code='4726' and (trim('$' FROM subject_account_name)!=trim('$' FROM object_account_domain)) AND object_account_name!='defaultuser0' AND (computer_name IS NOT NULL AND TRIM(computer_name) != '' AND computer_name != '-')", "condition": "", "display_label_confidence": "fqdn,host_name,primary_key"}, "Global_Protect_VPN_device_hostname": {"sdm": "palo_alto__global_protect", "output": "sds_ei__host__globalprotect_vpn__device_hostname", "primary_key": "device_hostname", "filter": "(event_label = 'gateway-auth' AND event_status = 'success') and (device_hostname IS NOT NULL AND TRIM(device_hostname) != '')", "condition": "", "display_label_confidence": "host_name,primary_key"}, "Global_Protect_VPN_client_hostname": {"sdm": "palo_alto__global_protect", "output": "sds_ei__host__globalprotect_vpn__client_hostname", "primary_key": "client_hostname", "filter": "(event_label = 'gateway-auth' AND event_status = 'success') and (client_hostname IS NOT NULL AND TRIM(client_hostname) != '')", "condition": "", "display_label_confidence": "host_name,primary_key"}, "Qualys_Host_list": {"sdm": "qualys__host_list", "output": "sds_ei__host__qualys_host_list__host_id", "primary_key": "host_id", "filter": "host_id IS NOT NULL AND TRIM(host_id) != ''", "condition": "", "display_label_confidence": "fqdn,dns_name,host_name,primary_key"}, "Qualys_Host_Summary": {"sdm": "qualys__host_summary", "output": "sds_ei__host__qualys_host_summary__host_id", "primary_key": "host_id", "filter": "host_id IS NOT NULL AND TRIM(host_id) != ''", "condition": "", "display_label_confidence": "fqdn,dns_name,host_name,primary_key"}, "Qualys_Host_Vulnerability": {"sdm": "qualys__host_vulnerability", "output": "sds_ei__host__qualys_host_vulnerability__host_id", "primary_key": "host_id", "filter": "host_id IS NOT NULL AND TRIM(host_id) != ''", "condition": "", "display_label_confidence": "fqdn,dns_name,host_name,primary_key"}, "Defender_Device_Events": {"sdm": "microsoft_azure__defender_device_events", "output": "sds_ei__host__ms_defender_device_events__device_id", "primary_key": "device_id", "filter": "device_id IS NOT NULL AND TRIM(device_id) != ''", "condition": "", "display_label_confidence": "fqdn,host_name,primary_key"}, "Defender_Device_List": {"sdm": "microsoft_azure__defender_device_list", "output": "sds_ei__host__ms_defender_device_list__id", "primary_key": "id", "filter": "id IS NOT NULL AND TRIM(id) != ''", "condition": "", "display_label_confidence": "fqdn,dns_name,host_name,azure_ad_device_id,primary_key"}, "Defender_Device_Software_Inventory": {"sdm": "microsoft_azure__defender_software", "output": "sds_ei__host__ms_defender_device_software_inventory__device_id", "primary_key": "device_id", "filter": "device_id IS NOT NULL AND TRIM(device_id) != ''", "condition": "", "display_label_confidence": "fqdn,host_name,primary_key"}, "Defender_Device_TVM": {"sdm": "microsoft_azure__defender_tvm_secure_config", "output": "sds_ei__host__ms_defender_tvm__device_id", "primary_key": "device_id", "filter": "device_id IS NOT NULL AND TRIM(device_id) != ''", "condition": "", "display_label_confidence": "fqdn,host_name,primary_key"}, "Azure_Resource_List": {"sdm": "microsoft_azure__resource_list", "output": "sds_ei__host__ms_azure_resource_list__resource_id", "primary_key": "resource_id", "filter": "lower(cloud_resource_type)='virtualmachines' AND lower(cloud_resource_type_sub)!='extensions' AND lower(cloud_resource_category)='compute' AND resource_id IS NOT NULL AND TRIM(resource_id) != ''", "condition": "", "display_label_confidence": "host_name,primary_key"}, "Azure_AD_Devices": {"sdm": "microsoft_azure__ad_device", "output": "sds_ei__host__ms_azure_ad_devices__device_id", "primary_key": "device_id", "filter": "device_id IS NOT NULL AND TRIM(device_id) != ''", "condition": "", "display_label_confidence": "fqdn,host_name,azure_ad_device_id,primary_key"}, "Azure_VDI": {"sdm": "microsoft_azure__vdi", "output": "sds_ei__host__ms_azure_vdi__session_host_azure_vm_id", "primary_key": "session_host_azure_vm_id", "filter": "session_host_azure_vm_id IS NOT NULL AND TRIM(session_host_azure_vm_id) != ''", "condition": "", "display_label_confidence": "fqdn,host_name,primary_key"}}, "Person": {"Success_Factors": {"sdm": "sap__success_factors", "output": "sds_ei__person__successfactors_hr__employee_id", "primary_key": "employee_id", "filter": "employee_id IS NOT NULL AND TRIM(employee_id) != ''", "condition": "", "display_label_confidence": "full_name,primary_key"}, "AD": {"sdm": "microsoft__active_directory", "output": "sds_ei__person__active_directory__object_guid", "primary_key": "object_guid", "filter": "object_guid is not null AND sam_account_type='NORMAL_USER_ACCOUNT' AND (lower(employee_id) NOT LIKE '%lynda%' OR lower(employee_id) NOT LIKE '%service%' OR lower(employee_id) NOT LIKE '%not set%' OR lower(employee_id) NOT LIKE '%tst_ gigap2r1%') AND (employee_id is not null OR (employee_id is null AND NOT (lower(distinguished_name) like '%ou=external%' OR lower(distinguished_name) like '%ou=gen-user%' OR lower(distinguished_name) like '%ou=ops-user%' OR lower(distinguished_name) like '%ou=contract-user%')))", "condition": "", "display_label_confidence": "full_name,primary_key"}, "IGAUsers": {"sdm": "saviynt__user", "output": "sds_ei__person__successfactors_hr__employee_id", "primary_key": "user_name", "filter": "employee_id IS NOT NULL AND TRIM(employee_id) != ''", "condition": "", "display_label_confidence": "full_name,primary_key"}, "IGAAccounts": {"sdm": "saviynt__account", "output": "sds_ei__person__savyint_iga_accounts__user_name", "primary_key": "user_name", "filter": "user_name IS NOT NULL AND TRIM(user_name) != ''", "condition": "", "display_label_confidence": "full_name,primary_key"}, "Intune": {"sdm": "microsoft__intune", "output": "sds_ei__person__ms_intunes__user_id", "primary_key": "user_id", "filter": "user_id IS NOT NULL AND TRIM(user_id) != ''", "condition": "", "display_label_confidence": "full_name,primary_key"}, "ITSM": {"sdm": "servicenow__service_catalog", "output": "sds_ei__person__snow_itsm__sf_id_employee_id", "primary_key": "concat(sf_id,employee_name)", "filter": "LOWER(ticket_name) LIKE '%employee onboarding%' OR LOWER(ticket_name) LIKE '%employee offboarding%' AND NOT (lower(employee_name) = 'jira intergration')", "condition": "", "display_label_confidence": "full_name,primary_key"}, "AD_Users": {"sdm": "microsoft_azure__ad_user", "output": " sds_ei__person__ms_azure_ad_users__aad_id", "primary_key": "aad_id", "filter": "lower(on_premises_distinguished_name) NOT LIKE '%service%' AND lower(on_premises_distinguished_name) NOT LIKE '%ou=meeting%' AND lower(on_premises_distinguished_name) NOT LIKE '%ou=domain%' AND aad_id IS NOT NULL AND TRIM(aad_id) != ''", "condition": "", "display_label_confidence": "full_name,primary_key"}}, "Identity": {"AD_User_Principal_Name": {"sdm": "microsoft__active_directory", "output": " sds_ei__identity__active_directory__user_principal_name", "primary_key": "user_principal_name", "filter": "user_principal_name IS NOT NULL AND TRIM(user_principal_name) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "AD_Object_GUID": {"sdm": "microsoft__active_directory", "output": " sds_ei__identity__active_directory__object_guid", "primary_key": "object_guid", "filter": "object_guid IS NOT NULL AND TRIM(object_guid) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "AD_External_Object_ID": {"sdm": "microsoft__active_directory", "output": "sds_ei__identity__active_directory__external_object_id", "primary_key": "external_object_id", "filter": "external_object_id IS NOT NULL AND TRIM(external_object_id) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "AD_Sam_Acct_Name": {"sdm": "microsoft__active_directory", "output": " sds_ei__identity__active_directory__sam_account_name_with_domain", "primary_key": "CONCAT(REGEXP_EXTRACT(distinguished_name, 'DC[\\\\s]*=[\\\\s]*([^,]*)', 1), '\\\\', sam_account_name)", "filter": "sam_account_type='NORMAL_USER_ACCOUNT' AND distinguished_name IS NOT NULL AND sam_account_name IS NOT NULL", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Sucess_Factors_Work_Email": {"sdm": "sap__successfactors", "output": " sds_ei__identity__successfactors_hr__work_email", "primary_key": "LOWER(work_email)", "filter": "work_email IS NOT NULL AND TRIM(work_email) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Sucess_Factors_external_email_id": {"sdm": "sap__successfactors", "output": " sds_ei__identity__successfactors_hr__external_email_id", "primary_key": "LOWER(external_email_id)", "filter": "external_email_id IS NOT NULL AND TRIM(external_email_id) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Globalprotect_VPN__user_id": {"sdm": "palo_alto__global_protect", "output": " sds_ei__identity__globalprotect_vpn__user_id", "primary_key": "user_id", "filter": "event_label = 'gateway-auth' AND event_status = 'success' AND user_id IS NOT NULL AND TRIM(user_id) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "WinEvents_4624__object_account_name": {"sdm": "microsoft__windows_security_logs", "output": " sds_ei__identity__winevents_4624__object_account_name", "primary_key": "CONCAT(object_account_domain, '\\\\', object_account_name) AS sam_account_name_with_domain", "filter": "object_account_domain IS NOT NULL AND TRIM(object_account_domain) != '' AND object_account_domain !='-' AND object_account_name IS NOT NULL AND TRIM(object_account_name) != '' AND object_account_name !='-' and event_code='4624'", "condition": "", "display_label_confidence": "account_name,primary_key"}, "WinEvents_4624__subject_account_name": {"sdm": "microsoft__windows_security_logs", "output": " sds_ei__identity__winevents_4624__subject_account_name", "primary_key": "CONCAT(subject_account_domain, '\\\\', subject_account_name) AS sam_account_name_with_domain", "filter": "subject_account_domain IS NOT NULL AND TRIM(subject_account_domain) != '' AND subject_account_domain !='-' AND subject_account_name IS NOT NULL AND TRIM(subject_account_name) != '' AND subject_account_name !='-' and event_code='4624'", "condition": "", "display_label_confidence": "account_name,primary_key"}, "WinEvents_4725__subject_account_name": {"sdm": "microsoft__windows_security_logs", "output": " sds_ei__identity__winevents_4725__subject_account_name", "primary_key": "CONCAT(subject_account_domain, '\\\\', subject_account_name) AS sam_account_name_with_domain", "filter": "(trim('$' FROM subject_account_name)!=trim('$' FROM object_account_domain)) AND subject_account_domain IS NOT NULL AND TRIM(subject_account_domain) != '' AND subject_account_domain !='-' AND subject_account_name IS NOT NULL AND TRIM(subject_account_name) != '' AND subject_account_name !='-' and event_code='4725'", "condition": "", "display_label_confidence": "account_name,primary_key"}, "WinEvents_4725__object_account_name": {"sdm": "microsoft__windows_security_logs", "output": " sds_ei__identity__winevents_4725__object_account_name", "primary_key": "CONCAT(object_account_domain, '\\\\', object_account_name) AS sam_account_name_with_domain", "filter": "(trim('$' FROM subject_account_name)!=trim('$' FROM object_account_domain)) AND object_account_domain IS NOT NULL AND TRIM(object_account_domain) != '' AND object_account_domain !='-' AND object_account_name IS NOT NULL AND TRIM(object_account_name) != '' AND object_account_name !='-' and event_code='4725'", "condition": "", "display_label_confidence": "account_name,primary_key"}, "WinEvents_4726__subject_account_name": {"sdm": "microsoft__windows_security_logs", "output": " sds_ei__identity__winevents_4726__subject_account_name", "primary_key": "CONCAT(subject_account_domain, '\\\\', subject_account_name) AS sam_account_name_with_domain", "filter": "(trim('$' FROM subject_account_name)!=trim('$' FROM object_account_domain)) AND object_account_name!= 'defaultuser0' AND subject_account_domain IS NOT NULL AND TRIM(subject_account_domain) != '' AND subject_account_domain !='-' AND subject_account_name IS NOT NULL AND TRIM(subject_account_name) != '' AND subject_account_name !='-' and event_code='4726'", "condition": "", "display_label_confidence": "account_name,primary_key"}, "WinEvents_4726__object_account_name": {"sdm": "microsoft__windows_security_logs", "output": " sds_ei__identity__winevents_4726__object_account_name", "primary_key": "CONCAT(object_account_domain, '\\\\', object_account_name) AS sam_account_name_with_domain", "filter": "(trim('$' FROM subject_account_name)!=trim('$' FROM object_account_domain)) AND object_account_name!= 'defaultuser0' AND object_account_domain IS NOT NULL AND TRIM(object_account_domain) != '' AND object_account_domain !='-' AND object_account_name IS NOT NULL AND TRIM(object_account_name) != '' AND object_account_name !='-' and event_code='4726'", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Savyint_IGA_users__email": {"sdm": "saviynt__user ", "output": " sds_ei__identity__savyint_iga_users__email", "primary_key": "LOWER(email)", "filter": "email IS NOT NULL AND TRIM(email) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Savyint_IGA_users__user_name": {"sdm": "saviynt__user ", "output": " sds_ei__identity__savyint_iga_users__user_name", "primary_key": "LOWER(user_name)", "filter": "user_name IS NOT NULL AND TRIM(user_name) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Savyint_iga_accounts": {"sdm": "saviynt__account", "output": " sds_ei__identity__savyint_iga_accounts__name_end_point", "primary_key": "concat(name,end_point)", "filter": "name IS NOT NULL AND TRIM(name) != '' AND end_point IS NOT NULL AND TRIM(end_point) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "ITSM": {"sdm": "servicenow__service_catalog", "output": " sds_ei__identity__snow_itsm__employee_email", "primary_key": "LOWER(employee_email)", "filter": "employee_email IS NOT NULL AND TRIM(employee_email) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Intunes__azuread_device_id": {"sdm": "microsoft__intune", "output": "sds_ei__identity__ms_intunes__azuread_device_id", "primary_key": "azuread_device_id", "filter": "azuread_device_id IS NOT NULL AND TRIM(azuread_device_id) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Intunes__email": {"sdm": "microsoft__intune", "output": "sds_ei__identity__ms_intunes__email", "primary_key": "LOWER(email)", "filter": "email IS NOT NULL AND TRIM(email) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Intunes__user_principal_name": {"sdm": "microsoft__intune", "output": "sds_ei__identity__ms_intunes__user_principal_name", "primary_key": "user_principal_name", "filter": "user_principal_name IS NOT NULL AND TRIM(user_principal_name) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Intunes__user_id": {"sdm": "microsoft__intune", "output": "sds_ei__identity__ms_intunes__user_id", "primary_key": "user_id", "filter": "user_id IS NOT NULL AND TRIM(user_id) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Defender_Device_List": {"sdm": "microsoft_azure__defender_device_list", "output": "sds_ei__identity__ms_defender_device_list__aad_device_id", "primary_key": "aad_device_id", "filter": "aad_device_id IS NOT NULL AND TRIM(aad_device_id) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Azure_AD_users_aad_id": {"sdm": "microsoft_azure__ad_user", "output": "sds_ei__identity__ms_azure_ad_users__aad_id", "primary_key": "aad_id", "filter": "aad_id IS NOT NULL AND TRIM(aad_id) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Azure_AD_users_sam_account_name": {"sdm": "microsoft_azure__ad_user", "output": "sds_ei__identity__ms_azure_ad_users__sam_account_name", "primary_key": "sam_account_name", "filter": "sam_account_name IS NOT NULL AND TRIM(sam_account_name) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Azure_AD_users_user_principal_name": {"sdm": "microsoft_azure__ad_user", "output": "sds_ei__identity__ms_azure_ad_users__user_principal_name", "primary_key": "user_principal_name", "filter": "user_principal_name IS NOT NULL AND TRIM(user_principal_name) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}, "Azure_AD_Devices": {"sdm": "microsoft_azure__ad_device", "output": "sds_ei__identity__ms_azure_ad_devices__aad_device_id", "primary_key": "aad_device_id", "filter": "aad_device_id IS NOT NULL AND TRIM(aad_device_id) != ''", "condition": "", "display_label_confidence": "account_name,primary_key"}}, "Vulnerability": {"Defender_Device_TVM_Software_Vulnerabilities": {"sdm": "microsoft_azure__defender_device_software_vuln", "output": "sds_ei__vulnerability__ms_defender_device_tvm_software_vulnerabilities__cve_id", "primary_key": "cve_id", "filter": "cve_id IS NOT NULL AND TRIM(cve_id) != ''", "condition": "", "display_label_confidence": "cve_id,vendor_id,primary_key"}, "Qualys_Host_Vulnerability": {"sdm": "qualys__host_vulnerability", "output": "sds_ei__vulnerability__qualys_host_vulnerability__qid", "primary_key": "qid", "filter": "qid IS NOT NULL AND TRIM(qid) != ''", "condition": "", "display_label_confidence": "vendor_id,primary_key"}, "Qualys_Knowledgebase": {"sdm": "qualys__knowledge_base", "output": "sds_ei__vulnerability__qualys_knowledgebase__qid_cve_id", "primary_key": "CONCAT(qid, '||', t.cve.ID)", "condition": "LATERAL VIEW explode(from_json(cve, 'array<struct<ID:string,URL:string>>')) t AS cve", "filter": "", "display_label_confidence": "cve_id,vendor_id,primary_key"}}}