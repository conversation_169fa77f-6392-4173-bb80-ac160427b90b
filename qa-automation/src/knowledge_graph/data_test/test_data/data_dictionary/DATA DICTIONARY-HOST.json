{"caption": "Host", "description": "An independent compute instance where we have visibility or management of the operating system.", "attributes": {"p_id": {"caption": "Entity ID", "description": "The 128 bit unique identifier of every entity in inventory.", "examples": "0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string_t"}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.Could be hostname for a host", "examples": ["HYD-JOHDOE-MOB", "<PERSON>"], "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string_t"}, "class": {"caption": "Class", "description": "The abstract super-type of Entity.", "examples": ["Host", "Person", "Identity", "Vulenrability"], "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string_t"}, "type": {"caption": "Type", "description": "The specific type of the Entity.", "examples": ["Server", "Endpoint"], "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string_t"}, "origin": {"caption": "Origin", "description": "A comma seperated list of source names from which the entity was recorded.", "examples": "['MS Intune','MS Active Directory','WinEvents']", "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "list_t"}, "first_seen_date": {"caption": "First Seen", "description": "The date at which the entity was first observed in the inventory.", "group": "common", "examples": "*************", "ui_visibility": true, "enable_hiding": false, "type": "timestamp_t"}, "last_seen_date": {"caption": "Last Seen", "description": "The date at which the entity was last observed in the inventory.", "group": "common", "examples": "*************", "ui_visibility": true, "enable_hiding": false, "type": "timestamp_t"}, "lifetime": {"caption": "Lifetime", "description": "The number of days an entity has existed", "examples": "5", "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "range_t"}, "recency": {"caption": "Recency", "description": "How long ago (in days) was the entity last observed in the inventory.", "examples": "10", "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "range_t"}, "entity_tag": {"caption": "Entity Tag", "description": "The tag to give context to an entity.", "examples": {"Host Type": "Server", "OS": "Amazon Linux", "BU": "skrill", "Cloud Provider": "AWS"}, "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string_t"}, "host_name": {"caption": "Hostname", "description": "A label that is assigned to a device connected to a computer network.", "examples": ["HYD-JOHDOE-MOB"], "group": "entity_specific", "ui_visibility": true, "type": "hostname_t", "source": ["MS Intune", "MS Active Directory", "Windows Security Logs", "Qualys", "MS Defender", "MS Azure", "AWS", "MS Azure AD"], "derived_field": false, "candidate_key": true}, "os": {"caption": "OS", "description": "The Operating System of the device.", "examples": "Microsoft Windows 10 Enterprise LTSC 2019 64-bit", "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune", "MS Active Directory", "Qualys", "MS Defender", "MS Azure", "MS Azure AD"], "derived_field": true, "candidate_key": false}, "os_platform": {"caption": "OS Platform", "description": "It Classifies the OS into its correcsponding platforms such as Windows, Linux, macOS, Android, iOS", "examples": ["Windows", "Linux", "Android"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["GlobalProtect", "MS Defender", "MS Intune", "MS Active Directory", "Qualys", "MS Azure", "MS Azure AD"], "derived_field": true, "candidate_key": false}, "ip": {"caption": "IP", "description": "IP Address of the device.", "examples": "*************", "group": "entity_specific", "ui_visibility": true, "type": "ip_t", "source": ["Qualys", "MS Defender", "MS Azure"], "derived_field": false, "candidate_key": false}, "fqdn": {"caption": "FQDN", "description": "Fully Qualified Domain Name of the host.", "examples": "hyd-johdoe-mob.corp.prevalent.com", "group": "entity_specific", "ui_visibility": true, "type": "fqdn_t", "source": ["MS Active Directory", "Windows Security Logs", "Qualys", "MS Defender", "MS Azure", "MS Azure AD", "AWS"], "derived_field": false, "candidate_key": true}, "domain": {"caption": "Domain", "description": "The domain of the device.", "examples": "corp.prevalent.com", "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "Windows Security Logs", "Qualys", "MS Defender", "MS Azure", "MS Azure AD", "AWS"], "derived_field": true, "candidate_key": false}, "dns_name": {"caption": "DNS Name", "description": "The Domain Name System, which turns domain names into IP addresses", "examples": "hyd-johdoe-mob.corp.prevalent.com", "group": "entity_specific", "ui_visibility": true, "type": "fqdn_t", "source": ["Qualys", "MS Defender", "AWS"], "derived_field": false, "candidate_key": false}, "netbios": {"caption": "NetBIOS", "description": "Network Basic Input/Output System, is a network service that enables applications on different computers to communicate with each other across a local area network (LAN).", "examples": ["APPOC-7GBPMD3", "SOF-MARMATE-MOB"], "group": "entity_specific", "ui_visibility": true, "type": "hostname_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "host_type": {"caption": "Host Type", "description": "Sub classification of Hosts.", "enum_t": {"0": {"caption": "Unknown"}, "1": {"caption": "Server"}, "2": {"caption": "Mobile"}, "3": {"caption": "Network"}, "4": {"caption": "Endpoint"}, "99": {"caption": "Other"}}, "group": "entity_specific", "ui_visibility": true, "type": "enum_t", "source": ["MS Intune", "Windows Security Logs", "Qualys", "MS Defender", "MS Active Directory", "GlobalProtect", "MS Azure", "MS Azure AD", "AWS"], "derived_field": true, "candidate_key": false}, "azure_ad_device_id": {"caption": "Azure AD Device ID", "description": "The unique identifier for the device in Azure AD.", "examples": ["0a9a511d-7f2b-4e78-a346-a47d80c0e982", "ed7abac2-3603-4c41-946f-7a50cce8b2fa"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune", "MS Active Directory", "MS Defender", "MS Azure AD"], "derived_field": false, "candidate_key": true}, "business_unit": {"caption": "Business Unit", "description": "Business Unit is a separate division within a company that often develops and implements its own processes independently from the core business or brand while still adhering to the overall company policies.", "examples": ["Google", "Paylifecorp", "AWS eCash", "Azure Corp WVD", "Azure Share Services"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys", "MS Azure"], "derived_field": false, "candidate_key": false}, "asset_group": {"caption": "Asset Group", "description": "An asset group is a collection or category of assets that share common characteristics.", "examples": ["Azure US Central", "EU Servers"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "compliance_standard": {"caption": "Compliance Standard", "description": "The practice of adhering strictly to published standards.", "examples": ["PCI", "SOX"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "accessibility": {"caption": "Accessibility", "description": "To find if its an external or internal facing device.", "enum_t": {"0": {"caption": "Unknown"}, "1": {"caption": "Internal"}, "2": {"caption": "External"}}, "group": "entity_specific", "ui_visibility": true, "type": "enum_t", "source": ["Qualys", "AWS"], "derived_field": true, "candidate_key": false}, "model": {"caption": "Model", "description": "Model of the Device.", "examples": "samsung m51", "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune", "MS Azure AD"], "derived_field": false, "candidate_key": false}, "serial_number": {"caption": "Serial Number", "description": "The unique serial number of the device given by the manufacturer.", "examples": ["2c9380a223057ece", "3RK32G2"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": true}, "status": {"caption": "Host Status", "description": "The status of the host", "enum_t": {"0": {"caption": "Unknown"}, "1": {"caption": "Active"}, "2": {"caption": "Disabled"}}, "group": "entity_specific", "ui_visibility": true, "type": "enum_t", "source": ["MS Intune", "MS Active Directory", "Windows Security Logs", "Qualys", "MS Defender", "MS Azure", "MS Azure AD", "AWS"], "derived_field": true, "candidate_key": false}, "activity_status": {"caption": "Activity Status", "description": "Indicates whether the device is active in last 180 days", "enum_t": {"0": {"caption": "Unknown"}, "1": {"caption": "Active"}, "2": {"caption": "Inactive"}}, "group": "entity_specific", "ui_visibility": true, "type": "enum_t", "source": ["MS Intune", "MS Active Directory", "Windows Security Logs", "Qualys", "MS Defender", "MS Azure AD", "MS Azure", "AWS"], "derived_field": true, "candidate_key": false}, "cloud_provider": {"caption": "Cloud Provider", "description": "A cloud service provider is a third-party company offering a cloud-based platform, infrastructure, application or storage services.", "examples": ["AWS", "Azure"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys", "MS Defender", "AWS", "MS Azure"], "derived_field": false, "candidate_key": false}, "cloud_resource_id": {"caption": "Cloud Resource ID", "description": "A unique identifier assigned to a specific resource within a cloud computing environment.", "examples": ["i-00ccc2e17e8486b65", "0b2b9e0b-8036-460a-9f4e-7f58ce27ff93"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "Qualys"], "derived_field": false, "candidate_key": true}, "last_active_date": {"caption": "Last Active Date", "description": "The date at which the device was last active ", "examples": "*************", "group": "entity_specific", "type": "timestamp_t", "ui_visibility": true, "source": ["MS Intune", "MS Active Directory", "Windows Security Logs", "Qualys", "MS Defender"], "derived_field": true, "candidate_key": false}, "event_code": {"caption": "Event Code", "description": "The event code of the WinEvents log.", "examples": ["4624", "4725", "4726"], "group": "source_specific", "ui_visibility": true, "type": "integer_t", "source": ["Windows Security Logs"], "derived_field": false, "candidate_key": false}, "location": {"caption": "Location", "description": "Location of the device.", "examples": "London", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "tags": {"caption": "Tags", "description": "A tag is a piece of information that describes the data or content that it is assigned to.", "examples": ["Cloud Agent", "No Asset Group", "SW:Defender"], "group": "source_specific", "ui_visibility": true, "type": "object_t", "source": ["MS Defender", "Qualys"], "derived_field": false, "candidate_key": false}, "last_login_date": {"caption": "Last Login Date", "description": "The date at which the device was last logged into", "examples": "*************", "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["MS Active Directory", "GlobalProtect", "Windows Security Logs", "MS Azure AD", "MS Azure"], "derived_field": false, "candidate_key": false}, "device_enrolled_date": {"caption": "Enrolled Date", "description": "The date at which the device was enrolled into MS intune", "examples": "*************", "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "compliance_state": {"caption": "Compliance State", "description": "Compliance status of device", "examples": ["compliant", "inGracePeriod", "noncompliant"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune", "MS Azure AD"], "derived_field": false, "candidate_key": false}, "manufacturer": {"caption": "Manufacturer", "description": "The manufacturer of the device.", "examples": "Apple", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune", "MS Azure AD"], "derived_field": false, "candidate_key": false}, "imei": {"caption": "IMEI", "description": "International Mobile Equipment Identity.", "examples": "14582001081166", "group": "source_specific", "ui_visibility": true, "type": "integer_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "managed_device_name": {"caption": "Managed Device Name", "description": "Automatically generated name to identify a device.", "examples": ["bach-Lan.vo_IPhone_7/7/2021_3:10 AM", "bari.gloria_Windows_9/27/2022_6:54 PM"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "device_encryption_status": {"caption": "Device Encryption Status", "description": "The encryption status of the device", "examples": ["Yes", "No"], "group": "source_specific", "ui_visibility": true, "type": "boolean_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "android_security_patch_level": {"caption": "Android Security Patch Level", "description": "The security patch level of the device.", "examples": "2022-02-01", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "eas_device_id": {"caption": "EAS Device ID", "description": "Exchange ActiveSync ID of the device.", "examples": "0A56C5AD26145C8BD18076FB86C2CC64", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "wifi_mac_address": {"caption": "Wi-Fi MAC Address", "description": "Wi-Fi MAC Address of the host", "examples": "0A15F385B86A", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "subscriber_carrier": {"caption": "Subscriber Carrier", "description": "Subscriber Carrier of the Device", "examples": ["AT&T", "JIO 4G", "iPad", "BSNL Mobile"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "meid": {"caption": "MEID", "description": "Mobile Equipment Identifier (MEID) is a globally unique 56-bit identification number for a physical piece of mobile station equipment.", "examples": "35114570828938", "group": "source_specific", "ui_visibility": true, "type": "integer_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "total_storage_space_bytes": {"caption": "Total Storage Space", "description": "Total storage capacity details in bytes.", "examples": "10457448448", "group": "source_specific", "ui_visibility": true, "type": "range_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "free_storage_space_bytes": {"caption": "Free Storage Space", "description": "Free storage capacity details in bytes.", "examples": "23068672", "group": "source_specific", "ui_visibility": true, "type": "range_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "device_id": {"caption": "Device ID", "description": "Unique identifier for the device", "examples": ["0a7e6969-e99d-430d-aecc-78d8749438aa", "134248746"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune", "MS Defender", "MS Azure AD", "Qualys"], "derived_field": false, "candidate_key": false}, "last_sync_date": {"caption": "Last Sync Date", "description": "The date at which the device last completed a successful sync.", "examples": "*************", "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["MS Active Directory", "MS Intune"], "derived_field": false, "candidate_key": false}, "asset_id": {"caption": "Asset ID", "description": "The asset ID of the host.", "examples": "46307729", "group": "source_specific", "ui_visibility": true, "type": "integer_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "ip_addresses": {"caption": "IP Addresses", "description": "The list of IP addresses of the Host.", "examples": ["{'ipAddress': ' 10.32.43.345', 'macAddress' : 'FDSF4323', 'type' : 'Ethernet', 'operationalStatus' : 'Up'}", "{'ipAddress': ' 169.245.43.345', 'macAddress' : 'DSAFHJE09', 'type' : 'Ethernet', 'operationalStatus' : 'Up'}"], "group": "source_specific", "ui_visibility": true, "type": "object_t", "source": ["MS Defender", "AWS"], "derived_field": false, "candidate_key": false}, "managed_by": {"caption": "Managed By", "description": "The service through which the device is managed", "examples": "Intune", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Defender", "MS Azure AD"], "derived_field": false, "candidate_key": false}, "health_status": {"caption": "Health Status", "description": "The device status assigned by the Microsoft Defender to the devices scanned.", "examples": ["Active", "NoSensorData", "ImpairedCommunication", "Inactive"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Defender"], "derived_field": false, "candidate_key": false}, "risk_score": {"caption": "Defender Risk Score", "description": "Risk score as evaluated by Microsoft Defender for Endpoint", "examples": ["Low", "Medium", "Informational"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Defender"], "derived_field": false, "candidate_key": false}, "exposure_level": {"caption": "Defender Exposure Level", "description": "Exposure level as evaluated by Microsoft Defender for Endpoint", "examples": ["Low", "Medium", "Informational"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Defender"], "derived_field": false, "candidate_key": false}, "last_sync_date_recency": {"caption": "Last Sync Date Recency", "description": "How recently was the device last synced.", "examples": "11", "group": "source_specific", "ui_visibility": true, "type": "range_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "when_created_date": {"caption": "When Created Date", "description": "The date when this object was created.", "examples": "*************", "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["MS Active Directory", "MS Azure AD"], "derived_field": false, "candidate_key": false}, "when_changed_date": {"caption": "When Changed Date", "description": "The date when this object was changed.", "examples": "*************", "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["MS Active Directory"], "derived_field": false, "candidate_key": false}, "sam_account_type": {"caption": "SAM Account Type", "description": "A SAM Account Type is a single valued indexed attibute that uniquely defines user objects.", "examples": "NORMAL_USER_ACCOUNT", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory"], "derived_field": false, "candidate_key": false}, "sam_account_name": {"caption": "SAM Account Name", "description": "The logon name used to support clients and servers.", "examples": "<PERSON><PERSON><PERSON><PERSON>", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory"], "derived_field": false, "candidate_key": false}, "user_account_control": {"caption": "User Account Control", "description": "Flags that control the behavior of the user account.", "examples": ["[PASSWD_NOTREQD,WORKSTATION_TRUST_ACCOUNT]", "[WORKSTATION_TRUST_ACCOUNT]"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory"], "derived_field": false, "candidate_key": false}, "description": {"caption": "Description", "description": "Contains the description to display for an object", "examples": "micha<PERSON><PERSON> - Dell Latitude 7490 - 4MR8NV2,QA Engineer", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "MS Intune"], "derived_field": false, "candidate_key": false}, "distinguished_name": {"caption": "Distinguished Name", "description": "Name that uniquely identifies an entry in the directory.", "examples": "CN=802.1X (Wired) 301 Developers,OU=CorporateOld,OU=Groups,OU=Stage,OU=Locations,OU=_Corp,DC=corp,DC=safe,DC=com", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory"], "derived_field": false, "candidate_key": false}, "account_expiration_date": {"caption": "Account Expiration Date", "description": "The date at which the account expires ", "examples": "2017-06-23T23:000:0Z", "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["MS Active Directory"], "derived_field": false, "candidate_key": false}, "subscription_id": {"caption": "Subscription ID", "description": "The subscription ID of the services.", "examples": "2c53d3f7-5cd7-4110-accf-2a975a3cc6d9", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Defender", "MS Azure"], "derived_field": false, "candidate_key": false}, "cloud_vm_id": {"caption": "Cloud VM ID", "description": "It is a unique identifier assigned to a virtual machine instance in a cloud computing environment.", "examples": "7ad3e9d0-8311", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Azure"], "derived_field": false, "candidate_key": false}, "threat_name": {"caption": "Threat Name", "description": "The threat names found by MS Defender.", "examples": ["TrojanDownloader:PowerShell/CobaltStrike.C!ibt", "Exploit:O97M/CVE-2017-11882.RV!MTB "], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Defender"], "derived_field": false, "candidate_key": false}, "ownership": {"caption": "Ownership", "description": "The category of ownership of the device.", "examples": ["company", "personal", "unknown"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune", "MS Azure AD"], "derived_field": false, "candidate_key": false}, "detection_method": {"caption": "Detection Method", "description": "Method of detection by scanners", "examples": "Qualys Cloud Agent", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Defender", "Qualys"], "derived_field": false, "candidate_key": false}, "last_scan_date": {"caption": "Last Scan Date", "description": "Last scanned date of QID in host machine", "examples": "*************", "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["MS Defender", "Qualys"], "derived_field": false, "candidate_key": false}, "private_dns_name": {"caption": "Private DNS Name", "description": "It refers to a Domain Name System (DNS) name that is not publicly available on the Internet, but instead is used within a private network such as a corporate network or a home network.", "examples": "ip-10-86-195-72.eu-west-1.compute.internal", "group": "source_specific", "ui_visibility": true, "type": "fqdn_t", "source": ["AWS"], "derived_field": false, "candidate_key": false}, "cloud_account_id": {"caption": "Cloud Account ID", "description": "A unique identifier assigned to a cloud account within a specific cloud service provider, such as Amazon Web Services (AWS), Microsoft Azure, or Google Cloud Platform (GCP).", "examples": "***********", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["AWS", "MS Azure"], "derived_field": false, "candidate_key": false}, "cloud_resource_type": {"caption": "Cloud Resource Type", "description": "A Cloud resource type refers to a specific type of resource that can be provisioned and managed within a cloud environment.", "examples": ["AWS EC2 Instance", "Azure VM Instance"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["AWS", "MS Azure"], "derived_field": false, "candidate_key": false}, "cloud_region": {"caption": "Cloud Region", "description": "Refers to a geographic location where a cloud service provider has a data center or multiple data centers that can be used to provision and manage cloud resources.", "examples": "eu-west-1, ca-central-1", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["AWS", "MS Azure"], "derived_field": false, "candidate_key": false}, "cloud_availability_zone": {"caption": "Cloud Availability Zone", "description": "An availability zone is a physically separate location within a cloud region that contains one or more data centers.", "examples": ["eu-west-1", "ca-central-1"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["AWS", "MS Azure"], "derived_field": false, "candidate_key": false}, "cloud_resource_state": {"caption": "Cloud Resource State", "description": "It refers to the current status or condition of a cloud resource within a cloud service provider's environment.", "examples": ["running", "stopped"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["AWS", "MS Azure"], "derived_field": false, "candidate_key": false}, "cloud_resource_creation_date": {"caption": "Cloud Resource Creation Date", "description": "The date at which a cloud resource was initially provisioned or created within a cloud service provider's environment.", "examples": "1646891027000", "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["AWS"], "derived_field": false, "candidate_key": false}, "device_state": {"caption": "Device State", "description": "It refers to the current status or condition of a hardware or software device", "examples": "Registered", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Intune"], "derived_field": false, "candidate_key": false}, "onboarding_status": {"caption": "Onboarding Status", "description": "The current state or progress of a new host or device being integrated into an organization or system.", "examples": "Onboarded", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Defender"], "derived_field": false, "candidate_key": false}, "action_type": {"caption": "Action Type", "description": "It refers to a specific action taken by the security software in response to a security threat or issue detected on a device.", "examples": ["AntivirusScanCompleted", "AntivirusDetection", "AntivirusScanCancelled", "AntivirusDetectionActionType"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Defender"], "derived_field": false, "candidate_key": false}}}