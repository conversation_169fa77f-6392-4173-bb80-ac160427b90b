{"caption": "Vulnerability", "description": "The Vulnerability Dictionary defines attributes defined for Vulnerability entity derived from multiple sources.", "attributes": {"p_id": {"caption": "Entity ID", "description": "The 128 bit unique identifier of every entity in inventory.", "examples": "0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string_t"}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it. Could be cve_id for vulnerability", "examples": ["HYD-JOHDOE-MOB", "<PERSON>"], "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string_t"}, "class": {"caption": "Class", "description": "The abstract super-type of Entity.", "examples": ["Host", "Person", "Identity", "Vulenrability"], "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string_t"}, "type": {"caption": "Type", "description": "The specific type of the Entity.", "examples": ["Server", "Endpoint"], "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string_t"}, "first_seen_date": {"caption": "First Seen", "description": "The date at which the entity was first observed in the inventory.", "group": "common", "examples": "1677069469000", "ui_visibility": true, "enable_hiding": false, "type": "timestamp_t"}, "last_seen_date": {"caption": "Last Seen", "description": "The date at which the entity was last observed in the inventory.", "group": "common", "examples": "1677069469000", "ui_visibility": true, "enable_hiding": false, "type": "timestamp_t"}, "lifetime": {"caption": "Lifetime", "description": "The number of days an entity has existed", "examples": "5", "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "range_t"}, "recency": {"caption": "Recency", "description": "How long ago (in days) was the entity last observed in the inventory.", "examples": "10", "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "range_t"}, "origin": {"caption": "Origin", "description": "A comma seperated list of source names from which the entity was recorded.", "examples": "['MS Intune','MS Active Directory','WinEvents']", "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "list_t"}, "entity_tag": {"caption": "Entity Tag", "description": "The tag to give context to an entity.", "examples": "{'Severity':'3'}", "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string_t"}, "title": {"caption": "Title", "description": "The title of the vulnerability.", "examples": "Accellion FTA OS Command Injection Vulnerability", "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "cve_id": {"caption": "CVE ID", "description": "The CVE ID of the vulnerability.", "examples": ["13785", "CVE-2018-0167"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys", "MS Defender"], "derived_field": false, "candidate_key": true}, "v30_severity": {"caption": "CVSSv3.0 Severity", "description": "The severity of the vulnerability.", "examples": ["Critical", "High", "Medium", "Low"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys", "MS Defender"], "derived_field": false, "candidate_key": false}, "vulnerability_type": {"caption": "Vulnerability Type", "description": "The category of vulnerability.", "examples": ["Vulenrability", "information gathered", "potential vulnerability", "Vulnerability or potential vulnerability"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "v30_score": {"caption": "CVSSv3.0 Score", "description": "The Common Vulnerability Scoring System, which gives the severity of a vulnerability.", "examples": "2.0", "group": "entity_specific", "ui_visibility": true, "type": "range_t", "source": ["Qualys", "MS Defender"], "derived_field": false, "candidate_key": false}, "v30_temporal_cvss_score": {"caption": "Temporal CVSSv3.0 Score", "description": "Vulnerability severity that change over the lifetime of a vulnerability.", "examples": "5.6", "group": "entity_specific", "ui_visibility": true, "type": "range_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "v30_vector": {"caption": "CVSSv3.0 Vector", "description": "A compressed textual representation of the values used to derive the CVSS Score. ", "examples": " ", "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "description": {"caption": "Description", "description": "Descripion of the Vulnerability", "examples": ["Patch Available", "Exploit Available", "Malware Associated"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "software_list": {"caption": "Software List", "description": "The softwares affected by Vulnerability", "examples": ["product:dns_server,vendor:cisco"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "patch_available": {"caption": "Patch Available", "description": "Indicating whether vulnerability is patchable or not", "examples": ["True", "False"], "group": "entity_specific", "ui_visibility": true, "type": "boolean_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "exploit_available": {"caption": "Exploit Available", "description": "Indicating whether an exploit is available or not", "examples": ["True", "False"], "group": "entity_specific", "ui_visibility": true, "type": "boolean_t", "source": ["MS Defender"], "derived_field": false, "candidate_key": false}, "recommendation": {"caption": "Recommendation", "description": "Recommendation to remediate the vulnerability.", "examples": "Uninstall JDK V3", "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys", "MS Defender"], "derived_field": false, "candidate_key": false}, "ms_recommended_update": {"caption": "MS Recommended Update", "description": "Recommended update of the vulnerability.", "examples": "Uninstall JDK V3", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Defender"], "derived_field": false, "candidate_key": false}, "ms_recommended_update_id": {"caption": "MS Recommended Update ID", "description": "Recommended update ID of the vulnerability.", "examples": "Uninstall JDK V3", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Defender"], "derived_field": false, "candidate_key": false}, "published_date": {"caption": "Published Date", "description": "It refers to the date when the vulnerability was published.", "examples": "1677069469000", "group": "entity_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["Qualys", "MS Defender"], "derived_field": false, "candidate_key": false}, "last_modified_date": {"caption": "Last Modified Date", "description": "It refers to the date when the vulnerability was last updated.", "examples": "1677069469000", "group": "entity_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "vendor_id": {"caption": "Vendor ID", "description": "It is the ID given by each vendor to the vulnerabilities found in the device.", "examples": "53588336", "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys", "MS Defender"], "derived_field": false, "candidate_key": false}, "vendor_severity": {"caption": "<PERSON><PERSON><PERSON>", "description": "It is the severity assigned by each vendor to the vulnerabilities found in the device.", "examples": ["7", "HIGH"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys", "MS Defender"], "derived_field": false, "candidate_key": false}, "found_in_organisation": {"caption": "Found In Organisation", "description": "To filter the vulnerabilities found in the organisation", "examples": "False", "group": "source_specific", "ui_visibility": false, "type": "string_t", "source": ["Qualys", "MS Defender"], "derived_field": true, "candidate_key": false}, "qualys_pci_flag": {"caption": "Qualys PCI Flag", "description": "The Qualys PCI flag is a security status indicator used by the Qualys vulnerability management and compliance platform to help organisations achieve and maintain compliance with the Payment Card Industry Data Security Standard (PCI DSS).", "examples": "True", "group": "source_specific", "ui_visibility": true, "type": "boolean_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "qualys_consequence": {"caption": "Qualys Consequence", "description": "The Consequence details after exploiting the vulnerabiltiy", "examples": "CGI", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "qualys_category": {"caption": "Qualys Category", "description": "The category the vulnerability is assigned to.", "examples": "By exploiting this vulnerability, the user gains access to computer.", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "qualys_threat_intel": {"caption": "<PERSON><PERSON>ys Threat <PERSON> ", "description": "Field that defines the threat_intelligence_id and the threat_intelligence_value", "examples": "ID:VALUE, 12 : Predicted High risk, 6:High Data Loss ", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}, "bugtraq_id": {"caption": "Bugtraq ID", "description": "A unique identifier assigned to a security vulnerability that is documented and listed in the Bugtraq mailing list, which is a public forum for discussing computer security vulnerabilities and related topics. ", "examples": "1000", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["Qualys"], "derived_field": false, "candidate_key": false}}}