{"block_variables": ["source_p_id", "target_p_id"], "iceberg_catalog_name": "iceberg_catalog", "schema_name": "ei_cloud_2", "is_source_intra_resolver_present": "true", "is_target_intra_resolver_present": "true", "intra_resolver_table_name": "sds_ei_intra_source_resolver", "inter_resolver_table_name": "sds_ei_inter_source_resolver", "source_inter_table_name": "sds_ei__cloud_storage", "target_inter_table_name": "sds_ei__cloud_account", "relationship_table_name": "sds_ei__rel__aws_resource_details__cloud_storage_resource_belongs_to_cloud_account", "override_columns": {"event_timestamp_epoch": "greatest(UNIX_MILLIS(TIMESTAMP(to_timestamp(configuration.launchTime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(configurationItemCaptureTime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(array_max(configuration.NetworkInterfaces.Attachment.AttachTime)))),UNIX_MILLIS(TIMESTAMP(to_timestamp(configuration.lastModified))),ingested_timestamp_epoch)", "event_timestamp_ts": "to_timestamp(event_timestamp_epoch/1000)"}, "temporary_properties": {}, "srdm": {"iceberg_catalog_name": "iceberg_catalog", "schema_name": "srdm", "table_name": "aws__resource_details", "end_event_timestamp_ts": "2024-05-20 23:59:59.999", "start_event_timestamp_ts": "1970-01-01 00:00:00.000", "end_parsed_interval_timestamp_ts": "2024-05-21 06:00:00.000", "columns_to_select": ["id", "event_timestamp_ts", "parsed_interval_timestamp_ts"]}, "loader": {"source_table_name": "sds_ei__cloud_storage__aws_resource_details__arn", "source_primary_key_logic": "lower(arn)", "target_table_name": "sds_ei__cloud_account__aws_resource_details__account_id", "target_primary_key_logic": "lower(accountId)"}, "common_attributes": {"relationship_name": "Cloud Storage Resource Belongs to Cloud Account", "inverse_relationship_name": "Cloud Account Has Cloud Storage Resource", "relationship_origin": "AWS", "relationship_first_seen_date": "min(event_timestamp_epoch)", "relationship_last_seen_date": "max(event_timestamp_epoch)", "source_entity_class": "Cloud Storage", "target_entity_class": "Cloud Account"}, "optional_attributes": {}}