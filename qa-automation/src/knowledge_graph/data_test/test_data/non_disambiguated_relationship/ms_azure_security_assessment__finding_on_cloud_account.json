{"block_variables": ["source_p_id", "target_p_id"], "iceberg_catalog_name": "iceberg_catalog", "schema_name": "ei_cloud_4", "is_source_intra_resolver_present": "true", "is_target_intra_resolver_present": "true", "intra_resolver_table_name": "sds_ei_intra_source_resolver", "inter_resolver_table_name": "sds_ei_inter_source_resolver", "source_inter_table_name": "sds_ei__finding", "target_inter_table_name": "sds_ei__cloud_account", "relationship_table_name": "sds_ei__rel__ms_azure_security_assessment__finding_on_cloud_account", "override_columns": {}, "temporary_properties": {}, "srdm": {"iceberg_catalog_name": "iceberg_catalog", "schema_name": "srdm", "table_name": "microsoft_azure__security_assesment", "end_event_timestamp_ts": "2024-05-29 23:59:59.999", "start_event_timestamp_ts": "1970-01-01 00:00:00.000", "end_parsed_interval_timestamp_ts": "2024-05-30 06:00:00.000", "columns_to_select": ["id", "event_timestamp_ts", "parsed_interval_timestamp_ts"]}, "loader": {"source_table_name": "sds_ei__finding__ms_azure_security_assessment__id", "source_primary_key_logic": "lower(id)", "target_table_name": "sds_ei__cloud_account__ms_azure_security_assessment__account_id", "target_primary_key_logic": "lower(REGEXP_EXTRACT(id, '/([a-zA-Z0-9-]+\\-+[a-zA-Z0-9-]+)/'))"}, "common_attributes": {"relationship_name": "Finding On Cloud Account", "inverse_relationship_name": "Cloud Account Has <PERSON>", "relationship_origin": "MS Azure", "relationship_first_seen_date": "min(event_timestamp_epoch)", "relationship_last_seen_date": "max(event_timestamp_epoch)", "source_entity_class": "Finding", "target_entity_class": "Cloud Account"}, "optional_attributes": {}}