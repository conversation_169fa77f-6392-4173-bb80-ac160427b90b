{"block_variables": ["source_p_id", "target_p_id"], "iceberg_catalog_name": "iceberg_catalog", "schema_name": "ei_cloud_2", "is_source_intra_resolver_present": "true", "is_target_intra_resolver_present": "true", "intra_resolver_table_name": "sds_ei_intra_source_resolver", "inter_resolver_table_name": "sds_ei_inter_source_resolver", "source_inter_table_name": "sds_ei__cloud_storage", "target_inter_table_name": "sds_ei__cloud_account", "relationship_table_name": "sds_ei__rel__azure_queue_storage__cloud_storage_resource_belongs_to_cloud_account", "srdm": {"iceberg_catalog_name": "iceberg_catalog", "schema_name": "srdm", "table_name": "microsoft_azure__queue_storage", "end_event_timestamp_ts": "2024-05-12 23:59:59.999", "start_event_timestamp_ts": "1970-01-01 00:00:00.000", "end_parsed_interval_timestamp_ts": "2024-05-13 06:00:00.000"}, "loader": {"source_table_name": "sds_ei__cloud_storage__ms_azure_queue_storage__id", "source_primary_key_logic": "lower(id)", "target_table_name": "sds_ei__cloud_account__azure_queue_storage__account_id", "target_primary_key_logic": "lower(regexp_extract(id, '/[^/]+/([^/]+)',1))"}, "common_attributes": {"relationship_name": "Cloud Storage Resource Belongs To Cloud Account", "inverse_relationship_name": "Cloud Account Has Cloud Storage Resource", "relationship_origin": "MS Azure", "relationship_first_seen_date": "min(event_timestamp_epoch)", "relationship_last_seen_date": "max(event_timestamp_epoch)", "source_entity_class": "Cloud Storage", "target_entity_class": "Cloud Account"}, "optional_attributes": {}}