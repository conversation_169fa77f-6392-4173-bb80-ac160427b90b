{"Person": {"inp_table_list": {"df_azure": "sds_ei__person__ms_azure", "df_user_id": "sds_ei__person__lookup_project__aad_user_id"}, "resolver_table": "sds_ei_inter_source_resolver", "schema": "ei", "inter_table": "sds_ei__person", "candidate_keys": "employee_id,azure_ad_user_id,email_id,full_name", "updated_at": "1685750399999", "exception_filter": "False", "exception_keys": "", "exception_expression": {}}}