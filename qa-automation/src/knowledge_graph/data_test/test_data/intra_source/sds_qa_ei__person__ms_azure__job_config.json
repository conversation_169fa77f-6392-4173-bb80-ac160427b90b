{"Person": {"sds_ei__person__ms_azure": {"candidate_key": "primary_key", "confidence_order": "df_users_id,df_aad_id", "fields_to_check": "full_name,email_id,employee_id,azure_ad_user_id", "updated_at": "1681862399999", "inp_table_list": {"df_aad_id": "sds_ei__person__ms_azure_ad_users__aad_id", "df_users_id": "sds_ei__person__ms_azure_ad_registered_users__id"}, "intra_table": "sds_ei__person__ms_azure", "resolver_table": "sds_ei_intra_source_resolver", "schema_name": "ei"}}}