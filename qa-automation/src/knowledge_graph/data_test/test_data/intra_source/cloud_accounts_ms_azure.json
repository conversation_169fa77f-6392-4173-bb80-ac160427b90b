{"inp_table_list": {"df_acc": "sds_ei__cloud_account__azure_blob_storage_container__account_id", "df_def": "sds_ei__cloud_account__azure_file_share__account_id", "df_que": "sds_ei__cloud_account__azure_queue_storage__account_id", "df_res": "sds_ei__cloud_account__azure_resource_details__account_id", "df_table": "sds_ei__cloud_account__azure_table_storage__account_id", "df_sec": "sds_ei__cloud_account__ms_azure_security_center_alerts__account_id", "df_sub": "sds_ei__cloud_account__ms_azure_subscriptions__subscriptionid", "df_vir": "sds_ei__cloud_account__azure_virtual_machine__account_id", "df_secur": "sds_ei__cloud_account__ms_azure_security_assessment__account_id"}, "class": "Cloud Accounts", "resolver_table": "sds_ei_intra_source_resolver", "schema": "ei_edm_sit_v4", "intra_table": "sds_ei__cloud_account__azure_aci_container__account_id", "candidate_keys": "primary_key", "min_agg_fields": "first_seen_date,first_found_date", "max_agg_fields": "last_found_date,last_active_date", "updated_at": "*************", "exception_filter": "", "exception_keys": "", "exception_expression": {}}