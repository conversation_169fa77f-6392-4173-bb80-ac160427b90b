{"primaryKey": "temp_primary_key", "origin": "'Qualys <PERSON>'", "filterBy": "temp_compliance_type is not null", "temporaryProperties": [{"colName": "temp_cve_id", "colExpr": "explode_outer(from_json(CVE_LIST.CVE, 'array<struct<ID:string, URL:string>>').ID)", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_primary_key", "colExpr": "concat_ws('||', CAST(QID AS STRING), temp_cve_id)"}, {"colName": "domain_extracted_temp", "colExpr": "REGEXP_EXTRACT(GET_JSON_OBJECT(VENDOR_REFERENCE_LIST, '$.VENDOR_REFERENCE.URL'), 'https?://([^/]+)', 1)"}, {"colName": "temp_cvss_collect", "colExpr": "collect_set(CVSS.VECTOR_STRING) OVER (PARTITION BY temp_primary_key ORDER BY event_timestamp_epoch ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_trusted_domains_list", "colExpr": "array('microsoft.com', 'qualys.com', 'redhat.com', 'ubuntu.com','github.com')"}, {"colName": "temp_event_published_epoch", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(PUBLISHED_DATETIME)))"}, {"colName": "temp_software_product", "colExpr": "regexp_extract(software_list, 'product\":\"([^\"\\\\]+)', 1)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "temp_compliance_type", "colExpr": "COMPLIANCE_LIST.COMPLIANCE.TYPE"}, {"colName": "temp_normalized_compliance_type", "colExpr": "trim(Upper(temp_compliance_type))"}, {"colName": "temp_compliance_category", "colExpr": "CASE WHEN temp_normalized_compliance_type IN ('pci', 'hipaa', 'gdpr') THEN 'Regulatory' WHEN temp_normalized_compliance_type IN ('cis', 'nist', 'iso') THEN 'Standards-Based' ELSE 'Other' END"}, {"colName": "temp_exploit_reference_host", "colExpr": "parse_url(CORRELATION.EXPLT_SRC.EXPLT_LIST.EXPLT.LINK, 'HOST')"}, {"colName": "temp_exploit_reference_host_secondary", "colExpr": "parse_url(CORRELATION.EXPLT_SRC.EXPLT_LIST.EXPLT.Link, 'HOST')", "fieldsSpec": {"caseSensitiveExpression": true}}, {"colName": "event_timestamp_epoch", "colExpr": "GREATEST(UNIX_MILLIS(to_timestamp(PUBLISHED_DATETIME)), UNIX_MILLIS(to_timestamp(LAST_SERVICE_MODIFICATION_DATETIME)), event_timestamp_epoch)"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN LOWER(VULN_TYPE) LIKE '%potential vulnerability%' THEN 'Weakness' WHEN LOWER(VULN_TYPE) LIKE '%vulnerability%'  THEN 'Vulnerability' WHEN LOWER(VULN_TYPE) LIKE '%information gathered%' THEN 'Informational' END"}, {"colName": "description", "colExpr": "regexp_replace(DIAGNOSIS, '<[^>]++>', ' ')"}, {"colName": "first_seen_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "cve_id", "colExpr": "temp_cve_id"}, {"colName": "v30_score", "colExpr": "CVSS_V3.BASE", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "temporal_cvss_score", "colExpr": "CVSS_V3.TEMPORAL", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "v30_vector", "colExpr": "CVSS_V3.VECTOR_STRING"}, {"colName": "v30_severity", "colExpr": "CASE WHEN v30_score>9 THEN 'Critical' WHEN v30_score>7 THEN 'High' WHEN v30_score>4 THEN 'Medium' WHEN v30_score>0 THEN 'Low' WHEN v30_score=0 THEN 'None' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "patch_available", "colExpr": "CASE WHEN (CAST(PATCHABLE AS STRING))==1 THEN true WHEN (CAST(PATCHABLE AS STRING))==0 THEN false ELSE null END", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "exploit_available", "colExpr": "case when lower(DISCOVERY.ADDITIONAL_INFO) like '%exploit%available%' then true else false end"}, {"colName": "last_modified_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(LAST_SERVICE_MODIFICATION_DATETIME)))"}, {"colName": "vendor_severity", "colExpr": "INITCAP(CAST(SEVERITY_LEVEL AS INTEGER))"}, {"colName": "software_list", "colExpr": "INITCAP(CAST(SOFTWARE_LIST AS STRING))"}, {"colName": "qualys_pci_flag", "colExpr": "CAST(PCI_FLAG AS STRING)", "fieldsSpec": {"persistNonNullValue": false}}], "sourceSpecificProperties": [{"colName": "qid", "colExpr": "CAST(QID AS STRING)"}, {"colName": "found_in_organisation", "colExpr": "false", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "category", "colExpr": "CATEGORY", "fieldsSpec": {"caseSensitiveExpression": true}}, {"colName": "category_1", "colExpr": "category", "fieldsSpec": {"caseSensitiveExpression": true, "replaceExpression": false}}, {"colName": "bugtraq_id", "colExpr": "REGEXP_EXTRACT_ALL(CAST(BUGTRAQ_LIST AS STRING), '\"ID\"\\\\s*:\\\\s*([0-9]+)')", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "qualys_threat_intel", "colExpr": "TO_JSON(TRANSFORM(from_json(CAST(THREAT_INTELLIGENCE.THREAT_INTEL AS STRING),'ARRAY<STRUCT<id:STRING,value:String>>'), s -> CASE  WHEN s.id = 1 THEN 'Zero Day' WHEN s.id = 2 THEN 'Exploit_Public' WHEN s.id = 3 THEN 'Active_Attacks' WHEN s.id = 4 THEN 'High_Lateral_Movement' WHEN s.id = 5 THEN 'Easy_Exploit'  WHEN s.id = 6 THEN 'High_Data_Loss' WHEN s.id = 7 THEN 'Denial_of_Service' WHEN s.id = 8 THEN 'No_Patch' WHEN s.id = 9 THEN 'Malware' WHEN s.id = 10 THEN 'Exploit_Kit' WHEN s.id = 11 THEN 'Wormable'  WHEN s.id = 12 THEN 'Predicted_High_Risk' WHEN s.id = 13 THEN 'Privilege_Escalation' WHEN s.id = 14 THEN 'Unauthenticated_Exploitation' WHEN s.id = 15 THEN 'Remote_Code_Execution' WHEN s.id = 16 THEN 'Ransomware'  WHEN s.id = 17 THEN 'Solorigate_Sunburst' WHEN s.id = 18 THEN 'Cisa_Known_Exploited_Vulns' END))"}, {"colName": "event_published_date", "colExpr": "temp_event_published_epoch"}, {"colName": "is_high_risk_untrusted_recent_exploit", "colExpr": "CASE WHEN array_contains(temp_cvss_collect, 'CVSS:2.0/AV:N/AC:L/Au:S/C:N/I:N/A:P/E:U/RL:OF/RC:C') AND NOT array_contains(temp_trusted_domains_list, domain_extracted_temp) THEN true ELSE false END"}, {"colName": "software_product_encoded", "colExpr": "replace(temp_software_product, '+', '%2b')"}, {"colName": "patch_available_date", "colExpr": "CASE WHEN PATCHABLE = 1 THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "min"}}, {"colName": "latest_exploit_published_date", "colExpr": "temp_event_published_epoch", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "cvss_collect", "colExpr": "temp_cvss_collect", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "exploitable_and_non_patchable", "colExpr": "CASE WHEN exploit_available = true AND patch_available = false THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "risk_tag", "colExpr": "CASE WHEN exploitable_and_non_patchable = true AND vendor_severity >= 4 THEN 'High Risk - Actionable' WHEN exploitable_and_non_patchable = true  AND vendor_severity <= 4 THEN 'Moderate Risk - Patch Available' ELSE 'Low/No Risk' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exploitability_combined_score", "colExpr": "CASE WHEN CAST(CVSS.BASE AS FLOAT) >= 9 AND CVSS.TEMPORAL >= 7 THEN 'Critical' WHEN CAST(CVSS.BASE AS FLOAT) >= 7 AND CVSS.TEMPORAL >= 5 THEN 'High' WHEN CAST(CVSS.BASE AS FLOAT) >= 4 AND CVSS.TEMPORAL >= 3 THEN 'Medium' ELSE 'Low' END"}, {"colName": "normalized_software_vendors", "colExpr": "SOFTWARE_LIST", "fieldsSpec": {"aggregateFunction": "collect_set"}}, {"colName": "severity_critical_change_date", "colExpr": "CASE WHEN last_updated_attrs.vendor_severity.last_changed.value IS NULL AND last_updated_attrs.vendor_severity.prev.value IS NULL AND vendor_severity = 5 THEN event_published_date WHEN last_updated_attrs.vendor_severity.last_changed.value = 4 THEN last_updated_attrs.vendor_severity.last_changed.updated_at WHEN last_updated_attrs.vendor_severity.last_changed.value = 3 THEN latest_exploit_published_date END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "days_since_critical", "colExpr": "DATEDIFF(TO_DATE(FROM_UNIXTIME(updated_at / 1000)), TO_DATE(FROM_UNIXTIME(severity_critical_change_date / 1000)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exploit_reference_host", "colExpr": "temp_exploit_reference_host"}, {"colName": "exploit_reference_host_secondary", "colExpr": "temp_exploit_reference_host_secondary"}, {"colName": "compliance_category", "colExpr": "temp_compliance_category"}, {"colName": "qualys_consequence", "colExpr": "UPPER(consequence)"}, {"colName": "enriched_qualys_threat_intel", "colExpr": "threat_intel_value"}, {"colName": "threat_intel_category", "colExpr": "CASE WHEN LOWER(qualys_threat_intel) LIKE '%ransomware%' THEN 'Ransomware' WHEN LOWER(qualys_threat_intel) LIKE '%apt%' THEN 'APT' WHEN LOWER(qualys_threat_intel) LIKE '%malware%' THEN 'Malware' ELSE 'Other' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "threat_id_secondary", "colExpr": "THREAT_INTELLIGENCE.Id", "fieldsSpec": {"caseSensitiveExpression": true, "convertEmptyToNull": false}}, {"colName": "threat_label", "colExpr": "CASE WHEN threat_id = 1 THEN 'Zero Day' WHEN threat_id = 2 THEN 'Exploit Public' WHEN threat_id = 3 THEN 'Active Attacks' WHEN threat_id = 4 THEN 'High Lateral Movement' WHEN threat_id = 5 THEN 'Easy Exploit' WHEN threat_id = 6 THEN 'High Data Loss' WHEN threat_id = 7 THEN 'Denial of Service' WHEN threat_id = 8 THEN 'No Patch' WHEN threat_id = 9 THEN 'Malware' WHEN threat_id = 10 THEN 'Exploit Kit' WHEN threat_id = 11 THEN 'Wormable' WHEN threat_id = 12 THEN 'Predicted High Risk' WHEN threat_id = 13 THEN 'Privilege Escalation' WHEN threat_id = 14 THEN 'Unauthenticated Exploitation' WHEN threat_id = 15 THEN 'Remote Code Execution' WHEN threat_id = 16 THEN 'Ransomware' WHEN threat_id = 17 THEN 'Solorigate Sunburst' WHEN threat_id = 18 THEN 'Cisa Known Exploited Vulns' ELSE NULL END"}, {"colName": "normalized_tags", "colExpr": "Tags.Tags"}, {"colName": "normalized_tags_alt1", "colExpr": "TAGS.Tags"}, {"colName": "normalized_tags_alt2", "colExpr": "tags.Tags"}, {"colName": "case_sense_exploit_reference_host", "colExpr": "parse_url(CORRELATION.EXPLT_SRC.EXPLT_LIST.EXPLT.LINK, 'HOST')"}, {"colName": "meta_info_details", "colExpr": "null_meta_info.details"}], "dataSource": {"name": "Qualys", "feedName": "KnowledgeBase", "srdm": "srdm_test_kg.qualys__knowledge_base"}, "enrichments": [{"lookupInfo": {"preTransform": [{"colName": "threat_intel_value", "colExpr": "UPPER(threat_intel_value)"}], "tableName": "lookup_test_kg.threat_lookup", "enrichmentColumns": ["threat_intel_value"]}, "joinCondition": "s.threat_id = e.threat_id", "sourcePreTransform": [{"colName": "threat_id", "colExpr": "THREAT_INTELLIGENCE.id", "fieldsSpec": {"convertEmptyToNull": false}}]}], "entity": {"name": "Vulnerability", "fieldSpec": {"persistNonNullValue": true}, "lastUpdateFields": ["type", "first_seen_date", "description", "cve_id", "temporal_cvss_score", "v31_severity", "patch_available", "exploit_available", "last_modified_date", "vendor_severity"]}, "outputTableInfo": {"outputTableName": "ei_new_out_kg.sds_ei__vulnerability__qualys_knowledgebase__qid_cve_id_cd", "outputWrittenMode": "viewType"}}