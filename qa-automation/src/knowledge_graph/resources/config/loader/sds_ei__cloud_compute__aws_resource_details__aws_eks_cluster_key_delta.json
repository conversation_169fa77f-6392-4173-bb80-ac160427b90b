{"primaryKey": "CONCAT(COALESCE(ARRAY_EXCEPT(TRANSFORM(tags , x -> (CASE WHEN x.key = 'eks:cluster-name' THEN x.value END)), array(NULL))[0],ARRAY_EXCEPT(TRANSFORM(tags , x -> (CASE WHEN x.key = 'aws:eks:cluster-name' THEN x.value END)), array(NULL))[0]),awsRegion)", "filterBy": "lower(resourceType) in ('aws::autoscaling::autoscalinggroup')", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "'Kubernetes Cluster'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "aws_resource_configuration_change_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [], "sourceSpecificProperties": [{"colName": "aws_resource_configuration_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configurationItemCaptureTime)))"}], "enrichments": [{"lookupInfo": {"tableName": "lookup_test_kg.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.awsRegion = e.awsRegion"}, {"lookupInfo": {"tableName": "lookup_test_kg.account_lookup_mail", "enrichmentColumns": ["account_mail"]}, "joinCondition": "s.accountId_join = e.accountId", "sourcePreTransform": [{"colName": "accountId_join", "colExpr": "concat(accountId, '_A')"}]}, {"lookupInfo": {"tableName": "lookup_test_kg.state_lookup", "enrichmentColumns": ["state_name"]}, "joinCondition": "s.state_id = e.state_id", "sourcePreTransform": [{"colName": "state_id", "colExpr": "configuration.state.value"}]}, {"lookupInfo": {"tableName": "lookup_test_kg.non_existent_lookup", "enrichmentColumns": ["org_unit", "org_name"]}, "joinCondition": "s.orgId = e.org_id"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "srdm_test_kg.aws__resource_details"}, "entity": {"name": "Cloud Compute", "fieldSpec": {"persistNonNullValue": true}, "lastUpdateFields": ["type"]}, "outputTableInfo": {"outputTableName": "ei_new_out_kg.sds_ei__cloud_compute__aws_resource_details__aws_eks_cluster_key", "outputWrittenMode": "viewType"}}