{"primaryKey": "concat_WS(':',assessment_id,finding_primary_key)", "origin": "'Prevalent'", "operationalMode": "dailyReset", "dataSource": {"name": "Prevalent", "feedName": "Finding", "srdm": "srdm_test_kg.sds_em__finding_evidence", "dataIntervalTimestampKey": "parsed_ts", "dataEventTimestampKey": "event_ts", "uniqueRecordIdentifierKey": "finding_id"}, "commonProperties": [{"colName": "display_label", "colExpr": "coalesce(finding_title,primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "first_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "assessment_id", "colExpr": "assessment_id"}, {"colName": "reopened_date", "colExpr": "CASE WHEN status = 'Open' and last_updated_attrs.status.prev.value='Closed' THEN last_updated_attrs.status.last_changed.updated_at ELSE reopened_date END", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "lookup_test_kg.asset_metadata", "enrichmentColumns": ["weightage"]}, "joinCondition": "s.assessment_id = e.assessment_id"}], "entity": {"name": "Finding", "fieldSpec": {"persistNonNullValue": true}, "lastUpdateFields": ["first_seen_date"]}, "outputTableInfo": {"outputTableName": "ei_new_out_kg.sds_em__finding", "outputWrittenMode": "viewType"}}