{"inventoryModelInput": [{"path": "ei_test_kg.sds_ei__host__active_directory__object_guid_cd", "name": "sds_ei__host__active_directory__object_guid"}, {"path": "ei_test_kg.sds_ei__host__ms_defender_cd", "name": "sds_ei__host__ms_defender"}, {"path": "ei_test_kg.sds_ei__host__qualys_cd", "name": "sds_ei__host__qualys"}], "disambiguation": {"candidateKeys": [{"name": "aad_device_id", "exceptionFilter": "aad_device_id = '00000000-0000-0000-0000-000000000000'"}, {"name": "host_name", "exceptionFilter": "(lower(host_name) LIKE '%iphone%' OR lower(host_name) LIKE '%android%' OR lower(host_name) LIKE '%ipad%' OR lower(host_name) LIKE '%macbook%' OR host_name RLIKE '(?i)pro([^a-zA-Z0-9]|$)' OR lower(host_name) LIKE '%galaxy%' OR lower(host_name) LIKE '%samsung%') OR lower(host_name) IN ('wrk','user deleted for this device') OR (lower(os) LIKE '%android%' OR lower(os) LIKE '%appleios%' OR lower(os) LIKE '%tizen%') OR (resource_id IS NOT NULL OR cloud_instance_id IS NOT NULL)"}, "fqdn", "hardware_serial_number", "resource_id", "cloud_instance_id", "netbios"], "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_defender", "sds_ei__host__qualys"], "excludeValues": ["Unknown", "Other", "-", "No Data"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "login_last_user", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "defender_health_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "defender_exposure_level", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "av_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "fw_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "av_block_malicious_code_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "os", "confidenceMatrix": ["sds_ei__host__ms_defender", "sds_ei__host__ms_azure_ad", "host_project_lookup", "sds_ei__host__ms_azure", "sds_ei__host__active_directory__object_guid"]}, {"field": "os_family", "confidenceMatrix": ["sds_ei__host__ms_defender", "sds_ei__host__ms_azure_ad", "host_project_lookup", "sds_ei__host__ms_azure", "sds_ei__host__active_directory__object_guid"]}], "rollingUpFields": ["origin", "defender_threat_name", "defender_action_type", "qualys_id", "defender_id", "qualys_asset_id", "qualys_detection_method", "vm_tracking_method", "vm_product", "defender_detection_method", "win_event_id", "ip", "mac_address", "edr_product", "asset_role", "data_source_dev"], "aggregation": [{"field": "login_last_date", "function": "max"}, {"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "defender_onboarding_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "vm_onboarding_status", "confidenceMatrix": ["true", "false"]}, {"field": "operational_state", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "type", "confidenceMatrix": ["Server", "Network", "Hypervisor", "Mobile", "Printer", "Workstation", "Other"]}]}}, "derivedProperties": [{"colName": "edr_threat_count", "colExpr": "defender_threat_count"}, {"colName": "is_edr_present", "colExpr": "CASE WHEN edr_onboarding_status THEN 1 ELSE 0 END"}], "output": {"disambiguatedModelLocation": "ei_new_out_kg.sds_ei__host_cd", "fragmentLocation": "ei_new_out_kg.sds_ei__fragment__host_cd", "resolverLocation": "ei_new_out_kg.sds_ei__resolver__host_cd"}, "entity": {"name": "Host", "fieldSpec": {"persistNonNullValue": true}, "lastUpdateFields": ["type", "first_seen_date", "business_unit", "department", "description", "location_country", "host_name", "fqdn"], "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(instance_name,fqdn,dns_name,host_name,aad_device_id,hardware_serial_number,ip,primary_key))"}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN 'No Data' WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END"}, {"colName": "business_unit", "colExpr": "CASE WHEN business_unit IS NULL THEN 'No Data' ELSE business_unit END"}], "entitySpecificProperties": [], "sourceSpecificProperties": []}}