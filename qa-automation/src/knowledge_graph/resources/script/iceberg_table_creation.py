from pyspark.sql import SparkSession
from datetime import datetime, timezone
import os
import findspark
findspark.init()
import logging

def setup_logger(name, level=logging.INFO):
    logger = logging.getLogger(name)
    logger.setLevel(level)

    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('[%(asctime)s] [%(levelname)s] %(name)s: %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)

    return logger
logger = setup_logger(__name__)


ICEBERG_WAREHOUSE = os.environ["ICEBERG_WAREHOUSE"]
ICEBERG_CATALOG_URI = os.environ["ICEBERG_CATALOG_URI"]
HADOOP_FS_IMPL = os.environ["HADOOP_FS_IMPL"]
HADOOP_FS_END_POINT = os.environ["HADOOP_FS_END_POINT"]
EXECUTOR_MEMORY = os.environ["EXECUTOR_MEMORY"]
DRIVER_MEMORY = os.environ["DRIVER_MEMORY"]
NAMESPACE = os.environ["NAMESPACE"]
EXECUTOR_INSTANCES = str(os.environ["EXECUTOR_INSTANCES"]).strip("n")
IMAGE = os.environ["IMAGE"]
HOSTNAME = os.environ["HOSTNAME"]
POD_IP = os.getenv("POD_IP")
SERVICE_ACCOUNT = os.environ["SERVICE_ACCOUNT"]
APP_NAME = os.environ["APP_NAME"]
UPDATED_AT = (int(os.environ["UPDATED_AT"]) * 1000) + 999
ICEBERG_CATALOG_TYPE = os.environ["ICEBERG_CATALOG_TYPE"]


spark = SparkSession.builder\
    .appName(APP_NAME) \
    .enableHiveSupport().config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
    .config("spark.kubernetes.namespace",f"{NAMESPACE}") \
    .config("spark.master", "k8s://https://kubernetes.default.svc.cluster.local:443") \
    .config("spark.kubernetes.authenticate.serviceAccountName",SERVICE_ACCOUNT)\
    .config("spark.kubernetes.container.image", f"{IMAGE}") \
    .config("spark.kubernetes.container.image.pullSecrets", "docker-secret") \
    .config("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions") \
    .config("spark.sql.catalog.iceberg_catalog", ICEBERG_CATALOG_TYPE) \
    .config("spark.sql.catalog.iceberg_catalog.warehouse", f"{ICEBERG_WAREHOUSE}") \
    .config("spark.sql.catalog.iceberg_catalog.type", "hive") \
    .config("spark.sql.catalog.iceberg_catalog.uri", f"thrift://{ICEBERG_CATALOG_URI}") \
    .config("spark.hadoop.fs.s3a.impl", HADOOP_FS_IMPL) \
    .config("spark.driver.port","22321")\
    .config("spark.submit.deployMode", "client") \
    .config("spark.blockManager.port","22322")\
    .config("spark.driver.host",f"{POD_IP}") \
    .config("spark.hadoop.fs.s3a.endpoint", f"{HADOOP_FS_END_POINT}") \
    .config("spark.kubernetes.executor.limit.cores","4")\
    .config("spark.executor.cores","1")\
    .config("spark.executor.memory", f"{EXECUTOR_MEMORY}") \
    .config("spark.kubernetes.executor.limit.memory","32g")\
    .config("spark.driver.memory", f"{DRIVER_MEMORY}") \
    .config("spark.executor.instances", f"{EXECUTOR_INSTANCES}") \
    .config("spark.sql.session.timeZone", "UTC") \
    .config("spark.sql.caseSensitive", "true") \
    .getOrCreate()


logger.info("Spark Session Creation Successful")







# spark = SparkSession.builder \
#     .appName("IcebergExample") \
#     .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
#     .config("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions") \
#     .config("spark.sql.catalog.iceberg_catalog", "org.apache.iceberg.spark.SparkCatalog") \
#     .config("spark.sql.catalog.iceberg_catalog.type", "hive") \
#     .config("spark.sql.catalog.iceberg_catalog.uri", "thrift://localhost:9083") \
#     .config("spark.sql.catalog.iceberg_catalog.warehouse", "s3a://product-df-dte-datalake/iceberg/") \
#     .config("spark.hadoop.fs.s3a.aws.credentials.provider", "com.amazonaws.auth.profile.ProfileCredentialsProvider") \
#     .config("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem") \
#     .config("spark.hadoop.fs.s3a.endpoint", "s3-ap-south-1.amazonaws.com") \
#     .config("spark.sql.session.timeZone", "UTC") \
#     .config("spark.driver.memory", "14g") \
#     .config("spark.executor.memory", "8g") \
#     .config("spark.sql.caseSensitive", "true") \
#     .getOrCreate()
    
logger.info(" 🔴 Spark Session created 🔴")


#base_dir = os.path.join(os.path.dirname(__file__), "..", "resources", "data")
#base_dir = os.path.abspath(base_dir)

base_dir = r"src\knowledge_graph\resources\data"



def determine_schema(path_parts):
    path_parts = [p.lower() for p in path_parts]  # make it case-insensitive
    if "output" in path_parts and "delta" in path_parts:
        return "ei_test_delta_kg"
    elif "input" in path_parts and "srdm" in path_parts:
        return "srdm_test_kg"
    elif "lookup" in path_parts:
        return "lookup_test_kg"
    elif "output" in path_parts:
        return "ei_test_kg"
    else:
        return None


for root, dirs, files in os.walk(base_dir):
    
    parquet_files = [f for f in files if f.endswith(".parquet")]
    if parquet_files:
        full_path = os.path.join(root, parquet_files[0])
        rel_path = os.path.relpath(root, base_dir)
        path_parts = rel_path.replace("\\", "/").split("/")

        schema = determine_schema(path_parts)
        logger.info(" 🔴Schema--- 🔴" , schema)
        if not schema:
            continue  

        table_name = path_parts[-1].lower().replace("-", "_")
        logger.info("🔴Table Name--- 🔴" , table_name)


        logger.info(f" 🔴 Reading Parquet files from 🔴: {root}")

        df = spark.read.parquet(os.path.join(root))
        df.writeTo(f"iceberg_catalog.{schema}.{table_name}").createOrReplace()
        logger.info(f"Written to iceberg_catalog.{schema}.{table_name}")


spark.stop()
logger.info("🔴Spark Session stopped 🔴")


        
        
        