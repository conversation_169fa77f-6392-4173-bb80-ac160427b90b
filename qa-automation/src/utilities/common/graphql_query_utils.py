import os
import requests
from core.common.common_utils import CommonUtils
from core.common.readProperties import ReadConfig
import json
from src.utilities.common.graphql_api_utils import GraphqlAPIUtils
from src.discover.api_test.queries.graphql_queries import queries
import ast
import re
import datetime
import json
from graphql import parse
from graphql import parse, print_ast
from graphql.language.ast import ObjectValueNode, ObjectFieldNode, NameNode, StringValueNode, ListValueNode

graphql_req = GraphqlAPIUtils()
common_utils = CommonUtils()
app_json = 'application/json'

class GraphqlQueryUtils():
        
    def read_query_from_file(self, relative_path, file_name):
        try:
            file_path = os.path.join('src', relative_path, file_name)
            with open(file_path, 'r') as file:
                query = file.read()
            return query
        except FileNotFoundError:
            return f"File not found: {file_path}"
        except Exception as e:
            return f"An error occurred: {e}"
        
    def extract_all_graphql_data(self, data):
        extracted_data = [item for sublist in data.values() for item in sublist]
        return extracted_data
    
    def get_updated_at(self, context):
        query_data = self.get_query("updated_at")
        response = graphql_req.get_graphql_responses(context, query_data)   
        key_to_extract =["updated_at"]
        updated_dt = self.extract_values(response, key_to_extract)
        value = updated_dt[0]['updated_at']
        return value
    
    def get_query(self, query_key):
        return queries.get(query_key)
    
    def extract_values(self, response, keys):
        try:
            data = response.get("data", {}) 
            if data:
                result = []
                for field_name, field_data in data.items():
                    # If there are items in the field data (assumes it's a list)
                    if isinstance(field_data, list):
                        for item in field_data:
                            result_item = {}
                            for key in keys:
                                result_item[key] = item.get(key, None)
                            result.append(result_item)
                    else:
                        result_item = {}
                        for key in keys:
                            result_item[key] = field_data.get(key, None)
                        result.append(result_item)
                return result
            else:
                return "No data found."
        except Exception as e:
            graphql_req.handle_api_exception(e)
        
    def update_query(self, context, regex_pattern, new_value, query):
        updated_query = re.sub(regex_pattern, new_value, str(query))
        query_dict = ast.literal_eval(updated_query)
        return query_dict
    
    def fetch_response_with_dt(self, context, keys_to_extract, query):
        try:
            #commented after implementing fetching date in before all
            # new_value = str(self.get_updated_at(context))
            regex_pattern = r'(?<=equals: ")[\d]+'
            new_value = str(context.updated_dt)
            query = self.update_query(context, regex_pattern, new_value, query)
            response  = graphql_req.get_graphql_responses(context, query)
            result = self.extract_values(response, keys_to_extract)
            return result
        except Exception as e:
            graphql_req.handle_api_exception(e)
            
    def fetch_response(self, context, query, keys_to_extract):
        response  = graphql_req.get_graphql_responses(context, query)
        result = self.extract_values(response, keys_to_extract)
        return result
    
    def substract_days(self, epoch, day_count):
        timestamp_s = int(epoch) / 1000
        utc_time = datetime.datetime.fromtimestamp(timestamp_s, tz=datetime.timezone.utc)
        new_date = utc_time - datetime.timedelta(days=day_count)
        new_timestamp_ms = int(new_date.timestamp() * 1000)
        return new_timestamp_ms
    
    def convert_to_graphql_format(self, query):
        query_string = query["query"]
        formatted_query = query_string.replace("\\n", "\n").replace("\\\"", "\"").strip()
        return "query AggrAllMetrOper "+formatted_query
    
    def convert_to_json(self, payload):
        json_payload = json.dumps(payload, indent=2)
        return json_payload
    
    def extract_top_level_filter(self, input_string):
        # Load the JSON input
        data = json.loads(input_string)
        query_string = data.get("query", "")
        # Sanitization
        query_string = query_string.replace("\\n", " ").replace('\\"', '"')
        query_string = query_string.replace("\n", " ")
        # Find all filters
        filters = re.findall(r'filter:\s*(\{[^}]+\})', query_string)
        # Return the last filter (top-level filter)
        if filters:
            return filters[-1]
        
        return None
    
    def create_or_conditions(self, type_list):
        or_conditions = []
        for type_item in type_list:
            or_conditions.append(f'{{field: "type", operator: {{equals: "{type_item.capitalize()}"}}}}')
        return or_conditions

    def construct_top_level_filter(self, or_conditions):
        return f'filter: {{AND: [{{field: "updated_at", operator: {{equals: "1723161599999"}}}}, {{OR: [{", ".join(or_conditions)}]}}]}}'

    def modify_filter_in_query(self, input_string, type_list):
        # Load the JSON input
        data = json.loads(input_string)
        query_string = data.get("query", "")
        # Create OR conditions
        or_conditions = self.create_or_conditions(type_list)
        # Construct new top-level filter
        new_filter = self.construct_top_level_filter(or_conditions)
        # Regex pattern to specifically target the top-level filter in entityGraphFilter
        pattern = r'(entityGraphFilter:\s*{[^}]*)(filter:\s*\{[^}]+\})([^}]*})'
        # Replace only the top-level filter within entityGraphFilter
        modified_query_string = re.sub(pattern, 
                                    lambda match: f'{match.group(1)}{new_filter}{match.group(3)}', 
                                    query_string)
        data['query'] = modified_query_string
        return json.dumps(data)
    
    # def replace_top_level_filter(self, input_string, dynamic_conditions):
    #     # Load the JSON input
    #     data = json.loads(input_string)
    #     query_string = data.get("query", "")
    #     query_string = query_string.replace("\\n", " ").replace('\\"', '"')
    #     query_string = query_string.replace("\n", " ")
    #     filters = re.findall(r'(filter:\s*\{AND:\s*\[[^]]+\]\})', query_string)
    #     if filters:
    #         top_level_filter = filters[-1]
    #         stripped_top_level_filter = top_level_filter[:-2]
    #         print(" TOP Level Filter :", top_level_filter)
    #         or_conditions = [
    #             f"{{field: \"{field}\", operator: {{equals: \"{value.title()}\"}}}}"
    #             for field, values in dynamic_conditions.items()
    #             for value in values
    #         ]
    #         or_condition_str = ", ".join(or_conditions)
    #         new_condition = f"{{OR: [{or_condition_str}]}}"
    #         print("New condition :", new_condition)
    #         updated_filter = stripped_top_level_filter[:-1]+'}' + f", {new_condition}"+']}'
    #         print("updated filter :", updated_filter)
    #         updated_query = query_string.replace(top_level_filter, updated_filter)
    #         data["query"] = updated_query
    #         print("updated query ", data["query"])
    #         return json.dumps(data, indent=2)
    #     return input_string
    
    def replace_top_level_filter(self, input_string, dynamic_conditions):
        # Load the JSON input
        data = json.loads(input_string)
        query_string = data.get("query", "")
        query_string = query_string.replace("\\n", " ").replace('\\"', '"')
        query_string = query_string.replace("\n", " ")
        filters = re.findall(r'(filter:\s*\{AND:\s*\[[^]]+\]\})', query_string)
        if filters:
            top_level_filter = filters[-1]
            stripped_top_level_filter = top_level_filter[:-2]
            print(" TOP Level Filter :", top_level_filter)
            # Separate type conditions from other conditions
            type_conditions = [
                f'{{field: "type", operator: {{equals: "{value}"}}}}'
                for field, values in dynamic_conditions.items()
                if field == 'type'
                for value in values
            ]
            # Handle other conditions separately
            other_conditions = [
                f'{{field: "{field}", operator: {{equals: {False if isinstance(values[0], bool) and not values[0] else values[0]}}}}}'
                for field, values in dynamic_conditions.items()
                if field != 'type' and values
            ]
            # Combine conditions
            or_type_condition_str = ", ".join(type_conditions)
            type_condition = f'{{OR: [{or_type_condition_str}]}}' if type_conditions else ""
            # Construct the new conditions list
            new_conditions = []
            if type_condition:
                new_conditions.append(type_condition)
            new_conditions.extend(other_conditions)
            # Reconstruct the filter
            new_condition_str = ", ".join(new_conditions)
            updated_filter = stripped_top_level_filter[:-1] + '}' + f', {new_condition_str}' + ']}'
            print("New condition :", new_condition_str)
            print("updated filter :", updated_filter)
            updated_query = query_string.replace(top_level_filter, updated_filter)
            data["query"] = updated_query
            return json.dumps(data, indent=2)
        return input_string
    
    def update_dt_with_regex(self, context, query):
        regex_pattern = r'(?<=equals: ")[\d]+'
        new_value = str(context.updated_dt)
        updated_query = self.update_query(context, regex_pattern, new_value, query)
        return updated_query
    
    def extract_values_strawbery_client(self, response, keys):
        try:
            # Extract the first (and only) key dynamically from the response
            if isinstance(response, dict):
                result = []
                # Get the first key in the response dictionary (e.g., "getEntityGraphMetrics")
                dynamic_key = next(iter(response))  # Dynamic key like 'getEntityGraphMetrics'
                field_data = response.get(dynamic_key, [])
                # If the field data is a list, iterate through the list
                if isinstance(field_data, list):
                    for item in field_data:
                        result_item = {}
                        # For each key in keys, get the value from the dictionary
                        for key in keys:
                            result_item[key] = item.get(key, None)
                        result.append(result_item)
                else:
                    result_item = {}
                    for key in keys:
                        result_item[key] = field_data.get(key, None)
                    result.append(result_item)
                return result if result else "No matching data found."
            else:
                return "Response is not a valid dictionary."
        except (KeyError, IndexError) as e:
            return f"Error accessing data: {e}"