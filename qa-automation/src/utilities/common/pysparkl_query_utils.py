import os
import requests
from core.common.common_utils import CommonUtils
from src.utilities.common.readProperties import ReadConfig
import json
import pandas as pd
import numpy as np
from decimal import Decimal
import re

common_utils = CommonUtils()
app_json = 'application/json'

class PysparkQueryUtils():
    
    def extract_all_pyspark_data(self, context, df):
        try:
            pandas_df = df.toPandas()
            result = []
            for index, row in pandas_df.iterrows():
                result_item = {}  # Store the entire row data
                for column, value in row.items():
                    value = self.distinguish_val(value)
                    result_item[column] = value
                result.append(result_item)
            return result
        except Exception as e:
            return f"Error processing the DataFrame: {e}"
        
    def distinguish_val(self, value):
        if isinstance(value, (np.int64, np.int32)):
            value = int(value)
        elif isinstance(value, (np.float64, np.float32)):
            value = float(value)
        elif isinstance(value, np.datetime64):
            value = pd.to_datetime(value)
        elif isinstance(value, (np.bool_, bool)):
            value = bool(value)
        elif isinstance(value, np.str_):
            value = str(value)
        elif isinstance(value, Decimal):
            value = float(value)
        return value
                        
    def extract_values_from_pandas(self, df, keys):
        try:
            pandas_df = df.toPandas()
            result = []
            for index, row in pandas_df.iterrows():
                result_item = {}
                for key in keys:
                    value = row.get(key, None)
                    value = self.distinguish_val(value)
                    result_item[key] = value
                result.append(result_item)
            return result
        except Exception as e:
            return f"Error processing the DataFrame: {e}"
        
    def get_data_frame(self, context, query):
        df = context.spark.sql(query)
        return df
    
    def get_single_count(self, df, key):
        distinct_count = (df.select(key).count())
        return distinct_count
    
    def get_single_data(self, df, key):
        pandas_df = df.toPandas()
        csv_name = 'dataframe_result.csv'
        pandas_df.to_csv(csv_name, index=False, encoding='utf-8')
        with allure.step("Attach PrettyTable to Allure"):
            allure.attach(open(csv_name, 'rb').read(), 'dataframe_result.csv', allure.attachment_type.CSV)
        val = pandas_df[key].iloc[0]
        return val
    
    def add_percentage_field(self, data, key, total):
        try:
            for item in data:
                count = item.get(key, 0)
                percentage = (count / total) * 100
                item['percentage'] = percentage
            return data
        except Exception as e:
            return f"Error adding percentage field: {e}"
        
    def update_pyspark_query(self, context, regex_pattern, new_value, query):
        updated_query = re.sub(regex_pattern, new_value, str(query))
        print("updated query :", updated_query)
        return updated_query
        
