import requests
from core.common.common_utils import CommonUtils
from core.common.readProperties import ReadConfig
from gql import Client, gql
from gql.transport.requests import RequestsHTTPTransport
import logging
import traceback

common_utils = CommonUtils()
app_json = 'application/json'

class GraphqlAPIUtils():
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get_graphql_responses(self, context, query):
        config_path = common_utils.get_file_path("configs/config.ini", 1)
        config = ReadConfig(config_path)
        graphql_url = config.get_graphql_api_url()
        # output_token = self.generate_token()
        url = graphql_url
        headers = {
                'Content-Type': app_json,
                #'Authorization': output_token
                }
        response = requests.post(url, json=query, headers=headers)
        response_json = response.json()
        return response_json
    
    def send_graphql_request(self, query, variables=None):
        config_path = common_utils.get_file_path("configs/config.ini", 1)
        config = ReadConfig(config_path)
        graphql_url = config.get_graphql_api_url()
        parsed_query = gql(query)
        transport = RequestsHTTPTransport(
            url=graphql_url,
            use_json=True,
        )
        client = Client(transport=transport)
        result = client.execute(parsed_query)
        return result
    
    def handle_api_exception(self, exception):
        error_msg = str(exception).split('\n')[0]  # Get just the first line of error
        tb = traceback.format_exc()  # Get full traceback for API errors
        self.logger.error(f"API Error occurred: {error_msg}\nTraceback:\n{tb}")
        raise exception