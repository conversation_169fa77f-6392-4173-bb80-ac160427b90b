import configparser
import os
from core.common.common_utils import CommonUtils
from src.utilities.data_test.constants import CommonConstants

common_utils = CommonUtils()
constants=CommonConstants()
config_path = CommonConstants.CONFIG_PATH

class ReadConfig():

    def __init__(self):
        self.config = configparser.ConfigParser()
        self.config.read(config_path)
        self.base_url = self.get_config_value('url', 'internal_url', True)
    
    def get_config_value(self, section, key, bool_val):
        val = common_utils.get_config_value(section, key, bool_val)
        return val
    
    def get_deployment_url(self):
        base_url = self.get_config_value('url', 'internal_url', True)
        config_endpoint = base_url+'/sds_mgmnt/config-manager/api/v1/config-item/deployment_config?solution=ei'
        return config_endpoint

    def get_spark_jobs_url(self):
        base_url = self.get_config_value('url', 'internal_url', True)
        config_endpoint = base_url+'/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs'
        return config_endpoint
        
    def get_env(self):
        environment = self.config['data_setup']['environment']
        return environment
    
    def get_config(self,key,value):
        config_val = self.config[key][value]
        return config_val
    
    def get_fragment_url(self, entity):
        base_url = self.get_config_value('url', 'internal_url', True)
        config_endpoint = base_url+f'/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/sds_ei__{entity}'
        return config_endpoint
    
    def get_loader_url(self, loader_name):
        return f'{self.base_url}/sds_mgmnt/config-manager/api/v1/config-item/{loader_name}?solution=ei'
    
    def get_all_deployed_items(self):
        return f'{self.base_url}/sds_mgmnt/config-manager/api/v1/config-item/list-configs-meta/?deployed=true'
    
    def get_deployed_data_dict(self, ent_or_rel_name, ent_or_rel_type):
        if ent_or_rel_type == 'entity':
            return f'{self.base_url}/sds_mgmnt/config-manager/api/v1/config-item/{ent_or_rel_name}__data_dictionary?deployed=true'
        if ent_or_rel_type == 'relationship':
            return f'{self.base_url}/sds_mgmnt/config-manager/api/v1/config-item/{ent_or_rel_name}?deployed=true'
    
    def get_ent_publish_spark_job(self, entity):
        return f'{self.base_url}/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/sds_ei__{entity}__publish'
    
    def get_ent_inter_spark_job(self, entity):
        return f'{self.base_url}/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/sds_ei__{entity}'
    
    def get_ent_rel_spark_job_with_prefix(self, prefixed_entity_or_rel, table_type):
        if table_type == 'publish':
            return f'{self.base_url}/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/{prefixed_entity_or_rel}__publish'
        if table_type == 'inter':
            return f'{self.base_url}/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/{prefixed_entity_or_rel}'
        
    def get_all_deployed_publish(self):
        return f'{self.base_url}/sds_mgmnt/config-manager/api/v1/config-item/list-configs-meta/?deployed=true&config_item_type=publisher'
    
    def get_isOLAP_tag(self, name):
        return f'{self.base_url}/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/{name}'
