from selenium.webdriver.common.by import By

class CommonPageLocators:
    
    textbox_username_id = (By.XPATH,"//*[@id='i0116']")
    textbox_password_id = (By.ID,"i0118")
    button_next_xpath = (By.XPATH,"//*[@id='idSIButton9']")
    button_login_id = (By.XPATH,"//*[@id='idSIButton9']")
    button_signed_in_id = (By.XPATH,"//*[@id='idBtn_Back']")
    #Entity Explorer Page
    ei_explore_btn = "//*[text()='ENTITY EXPLORER']/ancestor::div[@id='EUIHome']/following-sibling::div//button"
    