field_name,total_entities,visibility_value,entities,entity_count
aad_device_id,2,true,cloud_compute,1
aad_device_id,2,None,host,1
aad_id,2,false,host,1
aad_id,2,None,identity,1
accessibility,2,true,cloud_compute,1
accessibility,2,None,host,1
account_id,8,true,"account, cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud, network",7
account_id,8,None,host,1
account_name,2,None,account,1
account_name,2,true,cloud_account,1
activity_status,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
activity_status,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
ad_sam_account_type,3,None,"account, identity",2
ad_sam_account_type,3,false,host,1
av_block_malicious_code_status,2,true,cloud_compute,1
av_block_malicious_code_status,2,None,host,1
av_last_scan_date,2,true,cloud_compute,1
av_last_scan_date,2,None,host,1
av_signature_update_date,2,true,cloud_compute,1
av_signature_update_date,2,None,host,1
av_status,2,true,cloud_compute,1
av_status,2,None,host,1
aws_region,4,true,"cloud_compute, cloud_container, cloud_storage",3
aws_region,4,false,host,1
aws_resource_created_date,4,true,"cloud_compute, cloud_container, cloud_storage",3
aws_resource_created_date,4,None,host,1
aws_tags,4,false,"cloud_compute, cloud_container, host",3
aws_tags,4,true,cloud_storage,1
azure_operational_state,2,true,cloud_compute,1
azure_operational_state,2,None,host,1
azure_region,4,true,"cloud_compute, cloud_container, cloud_storage",3
azure_region,4,false,host,1
azure_resource_created_date,4,true,"cloud_compute, cloud_container, cloud_storage",3
azure_resource_created_date,4,None,host,1
class,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
class,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
cloud_provider,10,true,"assessment_cloud, cloud_account, cloud_compute, cloud_container, cloud_storage, compliance_standard, finding_cloud, network, security_control",9
cloud_provider,10,None,host,1
defender_action_type,2,true,cloud_compute,1
defender_action_type,2,None,host,1
defender_detection_method,2,true,cloud_compute,1
defender_detection_method,2,None,host,1
defender_exposure_level,2,true,cloud_compute,1
defender_exposure_level,2,None,host,1
defender_id,2,true,cloud_compute,1
defender_id,2,None,host,1
defender_onboarding_date,2,true,cloud_compute,1
defender_onboarding_date,2,None,host,1
defender_threat_count,2,true,cloud_compute,1
defender_threat_count,2,None,host,1
defender_threat_name,2,true,cloud_compute,1
defender_threat_name,2,None,host,1
display_label,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
display_label,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
edr_last_scan_date,2,true,cloud_compute,1
edr_last_scan_date,2,None,host,1
edr_product,2,true,cloud_compute,1
edr_product,2,None,host,1
first_found_date,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
first_found_date,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
first_seen_date,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
first_seen_date,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
fragments,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
fragments,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
fw_status,2,true,cloud_compute,1
fw_status,2,None,host,1
infrastructure_type,2,None,host,1
infrastructure_type,2,true,network,1
instance_name,2,true,cloud_storage,1
instance_name,2,None,host,1
is_accessible_from_internet,3,true,"cloud_compute, cloud_container",2
is_accessible_from_internet,3,None,host,1
is_container_host,2,true,cloud_compute,1
is_container_host,2,None,host,1
last_active_date,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
last_active_date,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
last_found_date,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
last_found_date,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
last_updated_date,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
last_updated_date,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
lifetime,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
lifetime,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
login_last_date,5,None,"account, host, identity, person",4
login_last_date,5,true,cloud_compute,1
login_last_user,2,true,cloud_compute,1
login_last_user,2,None,host,1
mac_address,2,None,host,1
mac_address,2,true,network,1
native_type,5,true,"cloud_compute, cloud_container, cloud_storage, network",4
native_type,5,None,host,1
observed_lifetime,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
observed_lifetime,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
open_to_all_internet,3,true,"cloud_compute, cloud_container",2
open_to_all_internet,3,None,host,1
operational_state,5,true,"cloud_compute, cloud_container, cloud_storage, network",4
operational_state,5,None,host,1
origin,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
origin,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
os_family,2,true,cloud_compute,1
os_family,2,None,host,1
p_id,15,None,"account, application, assessment_cloud, cloud_account, cloud_compute, cloud_storage, compliance_standard, finding_cloud, host, identity, network, person, security_control, vulnerability",14
p_id,15,true,cloud_container,1
provisioning_state,4,true,"cloud_compute, cloud_storage, network",3
provisioning_state,4,None,host,1
qualys_detection_method,2,true,cloud_compute,1
qualys_detection_method,2,None,host,1
recency,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
recency,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
recent_activity,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
recent_activity,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
region,5,true,"cloud_compute, cloud_container, cloud_storage, network",4
region,5,None,host,1
resource_id,5,true,"cloud_compute, cloud_container, cloud_storage, network",4
resource_id,5,None,host,1
tenable_io_agent_uuid,2,None,cloud_compute,1
tenable_io_agent_uuid,2,false,host,1
tenable_io_asset_plugin_status,2,None,cloud_compute,1
tenable_io_asset_plugin_status,2,false,host,1
tenable_io_asset_updated_at,2,None,cloud_compute,1
tenable_io_asset_updated_at,2,false,host,1
title,3,true,"assessment_cloud, security_control",2
title,3,None,vulnerability,1
type,15,None,"account, application, assessment_cloud, compliance_standard, host, identity, network, person, security_control, vulnerability",10
type,15,true,"cloud_account, cloud_compute, cloud_container, cloud_storage, finding_cloud",5
vendor_severity,2,true,finding_cloud,1
vendor_severity,2,None,vulnerability,1
vm_last_scan_date,2,true,cloud_compute,1
vm_last_scan_date,2,None,host,1
vm_product,2,true,cloud_compute,1
vm_product,2,None,host,1
vulnerability_last_observed_date,3,true,"cloud_compute, cloud_container",2
vulnerability_last_observed_date,3,None,host,1
wiz_last_scan_date,2,true,cloud_compute,1
wiz_last_scan_date,2,None,host,1
wiz_modified_date,3,true,"cloud_compute, cloud_container",2
wiz_modified_date,3,None,host,1
wiz_onboarding_status,2,true,cloud_compute,1
wiz_onboarding_status,2,None,host,1
wiz_operational_state,3,true,"cloud_compute, cloud_container",2
wiz_operational_state,3,None,host,1
zone_availability,4,true,"cloud_compute, cloud_container, cloud_storage",3
zone_availability,4,None,host,1
