from config_merger_api.old.utils.common_utils import merge_config

DEFAULT_SPEC = {
    "persistNonNullValue": True
}


def file_to_spec_mapper(global_entity_config):
    entity_class = global_entity_config.get("entityClass", "")
    default_spec = global_entity_config.get("defaultEntitySpec", DEFAULT_SPEC)
    field_level_spec = global_entity_config.get("fieldLevelSpec", [])
    common_properties = global_entity_config.get("commonProperties", [])
    entity_specific_props = global_entity_config.get("entitySpecificProperties", [])
    last_update_fields = global_entity_config.get("lastUpdateFields", [])
    entityConfig = {"entity_class": entity_class, "default_spec": default_spec,
                    "field_level_spec": field_level_spec, "common_properties": common_properties,
                    "entity_specific_props": entity_specific_props,
                    "last_update_fields": last_update_fields}
    return entityConfig


# adds entityConfig into the config(inventory_data)
def update_entity_config(inventory_data, entity_class, default_spec, last_update_fields):
    inventory_data["entityConfig"] = {
        "fieldSpec": default_spec,
        "entityClass": entity_class,
        "lastUpdateFields": last_update_fields
    }


# adds common properties present in global config into disambiguation/loader configurations.
# If a common field is already present in the config, it will not be overwritten.
def update_common_properties(inventory_data, common_properties):
    if not inventory_data.get("commonProperties"):
        inventory_data["commonProperties"] = common_properties
        return
    common_prop_in_inventory = [common_prop.get('colName') for common_prop in inventory_data.get("commonProperties", [])]
    common_properties_in_config = [common_prop.get('colName') for common_prop in common_properties]
    common_props_config_diff_inv = list(set(common_properties_in_config) - set(common_prop_in_inventory))
    full_common_props = [common_prop for common_prop in common_properties if
                         common_prop.get("colName") in common_props_config_diff_inv]
    inventory_data["commonProperties"] = inventory_data.get("commonProperties", []) + full_common_props


# adds entity specific properties present in global config into disambiguation/loader configurations.
# If a field is already present in the config, it will not be overwritten.
def update_entity_specific_properties(inventory_data, entity_specific_props):
    entity_specific_prop_inventory = [entity_specific_prop.get('colName') for entity_specific_prop in
                                      inventory_data.get("entitySpecificProperties", [])]
    entity_specific_prop_in_config = [entity_specific_prop.get('colName') for entity_specific_prop in
                                      entity_specific_props]
    entity_specific_prop_config_diff_inv = list(
        set(entity_specific_prop_in_config) - set(entity_specific_prop_inventory))
    full_common_props = [entity_specific_prop for entity_specific_prop in entity_specific_props if
                         entity_specific_prop.get("colName") in entity_specific_prop_config_diff_inv]
    inventory_data["entitySpecificProperties"] = inventory_data.get("entitySpecificProperties", []) + full_common_props


def update_field_specs(inventory_data, field_level_spec):
    for field_spec in inventory_data.get("entitySpecificProperties", []):
        col_name = field_spec.get("colName")
        matching_fields = [fs for fs in field_level_spec if
                           fs.get("colName") == col_name and field_spec.get("fieldsSpec") is None]

        if matching_fields:
            field_spec.setdefault("fieldsSpec", {})["persistNonNullValue"] = matching_fields[0].get("fieldsSpec",
                                                                                                    {}).get(
                "persistNonNullValue", False)


def compare_properties(common_properties, entity_specific_properties):
    common_col_names = set(prop["colName"] for prop in common_properties)
    entity_col_names = set(prop["colName"] for prop in entity_specific_properties)
    common_elements = common_col_names.intersection(entity_col_names)
    if common_elements:
        raise Exception("Common elements found between common_properties and entity_specific_properties in global "
                        "entity config: " + ", ".join(
            common_elements))


def compare_properties_in_global_entity_config(solution_global_config, client_global_config):
    if client_global_config != [] and solution_global_config != []:
        solution_global_config = solution_global_config[0]
        client_global_config = client_global_config[0]
        common_properties = solution_global_config.get("commonProperties", [])
        entity_properties = solution_global_config.get("entitySpecificProperties", [])
        client_common_properties = client_global_config.get("commonProperties", [])
        client_entity_properties = client_global_config.get("entitySpecificProperties", [])
        compare_properties(common_properties, client_entity_properties)
        compare_properties(entity_properties, client_common_properties)


def loader_global_config_merger(loader_config, global_entity_config):
    entity_config = file_to_spec_mapper(global_entity_config)
    update_entity_config(loader_config, entity_config.get("entity_class"),
                         entity_config.get("default_spec"), entity_config.get("last_update_fields"))
    update_common_properties(loader_config, entity_config.get("common_properties"))
    update_entity_specific_properties(loader_config, entity_config.get("entity_specific_props"))
    update_field_specs(loader_config, entity_config.get("field_level_spec"))
    return loader_config


def merge_inventory_models(solution_configs, client_configs, config_manipulation):
    merged_configs = merge_config(solution_configs, client_configs)
    loader_config = [i.get("config_value", {}) for i in merged_configs if i['config_item_type'] == 'inventory_models'][0]
    if config_manipulation:
        sol_global_config = [i.get("config_value", {}) for i in solution_configs if i['config_item_type'] == 'global_entity_config']
        client_global_config = [i.get("config_value", {}) for i in client_configs if i['config_item_type'] == 'global_entity_config']
        compare_properties_in_global_entity_config(sol_global_config, client_global_config)
        global_entity_config = [i.get("config_value", {}) for i in merged_configs if i['config_item_type'] == 'global_entity_config']
        if global_entity_config != [] and global_entity_config[0] != {}:
            loader_config = loader_global_config_merger(loader_config, global_entity_config[0])
    return loader_config
