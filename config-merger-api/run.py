import json
import os
from flask import Flask, make_response, request
from flask_cors import CORS
from flask import jsonify
from werkzeug import Response
from distutils.util import strtobool
from flask import Flask, Response, request
from config_merger_api.utils.common_utils import merge_config
from config_merger_api.utils.data_dictionary_utils import merge_data_dictionary, merge_relationship_data_dictionary, update_dictionary_with_ui_config
from config_merger_api.utils.disambiguation_utils import merge_intra_source_disambiguated_models, merge_inter_source_disambiguated_models
from config_merger_api.utils.inventory_models_utils import merge_inventory_models
from config_merger_api.utils.lineage_utils import LineageExtractor
from config_merger_api.utils.relationship_models_utils import merge_relationship_models
from config_merger_api.utils.relationship_disambiguation_utils import merge_relationship_disambiguation
from config_merger_api.utils.entity_rel_enrich_utils import merge_entity_rel_enrich
from config_merger_api.utils.olap_utils import merge_olap_models
from config_merger_api.logger import LogManager, get_logger
from config_merger_api.utils.ei_data_dictionary_update_utils import EntityDatadictionaryUpdate
# Old version imports
from config_merger_api.old.utils.inventory_models_utils import merge_inventory_models as merge_inventory_models__old
from config_merger_api.old.utils.common_utils import merge_config as merge_config__old
from config_merger_api.old.utils.data_dictionary_utils import merge_data_dictionary as merge_data_dictionary__old, merge_relationship_data_dictionary as merge_relationship_data_dictionary__old
from config_merger_api.old.utils.disambiguation_utils import merge_intra_source_disambiguated_models as merge_intra_source_disambiguated_models__old, merge_inter_source_disambiguated_models as merge_inter_source_disambiguated_models__old
from config_merger_api.old.utils.relationship_models_utils import merge_relationship_models as merge_relationship_models__old
from config_merger_api.old.utils.relationship_disambiguation_utils import merge_relationship_disambiguation as merge_relationship_disambiguation__old
from config_merger_api.old.utils.entity_rel_enrich_utils import merge_entity_rel_enrich as merge_entity_rel_enrich__old

# config-upgrade imports
import logging
from config_merger_api.config_upgrade_utils.common_utils import ConfigManager, CustomUtils
from config_merger_api.config_upgrade_utils.data_dictionary_utils import upgrade_data_dictionary
from config_merger_api.config_upgrade_utils.relationship_model_utils import upgrade_relationship_models
from config_merger_api.config_upgrade_utils.relationship_disambiguation_utils import upgrade_relationship_disambiguation_models
from config_merger_api.config_upgrade_utils.intra_disambiguation_utils import upgrade_intra_disambiguation_models
from config_merger_api.config_upgrade_utils.inter_disambiguation_utils import upgrade_inter_disambiguation_models

## config-trigger imports

from config_merger_api.config_trigger_utils.data_dictionary_utils import ConfigTriggerUtils



LogManager()
logger = get_logger()

app = Flask(__name__)
CORS(app)
base_url = "/ei/config-merge/api/v1/"

CONFIG_MERGE_METHOD_MAP = {
    'inventory_models': lambda x, y, z, is_studio_request: merge_inventory_models(x, y, z, is_studio_request),
    'data_dictionary_config': lambda x, y, z, is_studio_request: merge_data_dictionary(x, y),
    'relationship_data_dictionary_config': lambda x, y, z, is_studio_request: merge_relationship_data_dictionary(x, y),
    'dictionary_ui_config_merge': lambda x, y, z, is_studio_request: update_dictionary_with_ui_config(x, y),
    'intrasource_disambiguated_models': lambda x, y, z, is_studio_request: merge_intra_source_disambiguated_models(x, y),
    'intersource_disambiguated_models': lambda x, y, z, is_studio_request: merge_inter_source_disambiguated_models(x, y),
    'relationship_models': lambda x, y, z, is_studio_request: merge_relationship_models(x, y),
    'relationship_disambiguation': lambda x, y, z, is_studio_request: merge_relationship_disambiguation(x, y),
    'entity_rel_enrich': lambda x, y, z, is_studio_request: merge_entity_rel_enrich(x, y),
    'olap_configs': lambda x, y, z, is_studio_request: merge_olap_models(x, y)
}

CONFIG_MERGE_METHOD_MAP_OLD = {
    'inventory_models': lambda x, y, z: merge_inventory_models__old(x, y, z),
    'data_dictionary_config': lambda x, y, z: merge_data_dictionary__old(x, y),
    'relationship_data_dictionary_config': lambda x, y, z: merge_relationship_data_dictionary__old(x, y),
    'intrasource_disambiguated_models': lambda x, y, z: merge_intra_source_disambiguated_models__old(x, y),
    'intersource_disambiguated_models': lambda x, y, z: merge_inter_source_disambiguated_models__old(x, y),
    'relationship_models': lambda x, y, z: merge_relationship_models__old(x, y),
    'relationship_disambiguation': lambda x, y, z: merge_relationship_disambiguation__old(x, y),
    'entity_rel_enrich': lambda x, y, z: merge_entity_rel_enrich__old(x, y)
}
@app.route('/ei/config-merge/health-check')
def health_check():
    return json.dumps({'version': '1.0',
                       'state': 'Application is up and running'})

@app.route("/ei/api/v1/lineage", methods=['POST'])
def get_lineage():
    data = json.loads(request.data)
    try:
        lineage = LineageExtractor(
            data_feed_name=data.get("data_feed_name"),
            data_source_name=data.get("data_source_name"),
        )
        config_lineage = lineage.extract()
        return Response(json.dumps(config_lineage), mimetype='application/json')
    except Exception as e:
        logger.error('%s' % e)
        message = {'status': 'ERROR',
                   'detail': '%s' % e,
                   'developerMsg': '%s' % e}
        return Response(json.dumps(message), status=400, mimetype='application/json')



@app.route("/ei/config-merge/api/v1/config-trigger", methods=['POST'])
def trigger_dag():
    try:
        data = json.loads(request.data)
        logger.info(f"Data dictionary triggered with payload {data}")
        url = os.getenv("STATUS_MANAGER_ENDPOINT", None)
        if not url:
            raise ValueError("No STATUS_MANAGER_ENDPOINT provided or set in environment variables")

        config_manger = ConfigManager(url=url)

        CONFIG_TRIGGER_METHOD_MAP = {
            'data_dictionary_config': lambda x: config_trigger.trigger_data_dictionary(),
            'relationship_data_dictionary_config': lambda x: config_trigger.trigger_data_dictionary()

        }
        config_trigger = ConfigTriggerUtils(config_manger, CONFIG_TRIGGER_METHOD_MAP, data)
        config_item_type = data.get("name")
        if not config_item_type:
            raise ValueError("name is required in the request")
        result = config_trigger.initiate_trigger_process(CONFIG_TRIGGER_METHOD_MAP)
        status_code = 200 if result['current_state'] == 'trigger_started' else 400
        return Response(json.dumps(result), mimetype='application/json', status=status_code)
    except Exception as e:
        logger.error(f"Error triggering DAG: {e}")
        message = {'status': 'ERROR', 'detail': str(e)}
        return Response(json.dumps(message), status=400, mimetype='application/json')



@app.route(base_url + "<config_item_type>", methods=['POST'], defaults={'version': None})
@app.route(base_url + "<version>/" + "<config_item_type>", methods=['POST'])
def config_merge(version, config_item_type):
    try:
        data = json.loads(request.data)
        sol_configs = data["solution_configs"]
        client_configs = data["client_configs"]
        config_manipulation = strtobool(request.args.get("config-manipulation", "false"))
        is_studio_request = strtobool(request.args.get("is_studio_request", "false"))
        merged_config = CONFIG_MERGE_METHOD_MAP.get(config_item_type, lambda x, y, z,is_studio_request: merge_config(x, y)[0]["config_value"])(sol_configs, client_configs, config_manipulation,is_studio_request) if version != "old" else CONFIG_MERGE_METHOD_MAP_OLD.get(config_item_type, lambda x, y, z: merge_config__old(x, y)[0]["config_value"])(sol_configs, client_configs, config_manipulation)
        response = {
            "data": merged_config
        }
        logger.info(f"Config merge successful for {config_item_type}")
        return Response(json.dumps(response), mimetype='application/json')
    except Exception as e:
        logger.error('%s' % e)
        message = {'status': 'ERROR',
                   'detail': '%s' % e,
                   'developerMsg': '%s' % e}
        return Response(json.dumps(message), status=400, mimetype='application/json')

@app.errorhandler(405)
def not_allowed(error):
    return make_response(jsonify({'error': 'Method not allowed'}), 405)

@app.errorhandler(404)
def not_found(error):
    return make_response(jsonify({'error': 'Not found'}), 404)

# Config Schema Upgrade
@app.route('/ei/config-upgrade/api/v1/', methods=['POST'])
def process():
    config_manager = ConfigManager()
    CONFIG_UPGRADE_METHOD_MAP = {
        'inventory_models': lambda x: config_manager.skip_upgrade_function(x),
        'data_dictionary_config': lambda x: upgrade_data_dictionary(x),
        'relationship_data_dictionary_config': lambda x: upgrade_data_dictionary(x),
        'intrasource_disambiguated_models': lambda x: upgrade_intra_disambiguation_models(x),
        'intersource_disambiguated_models': lambda x: upgrade_inter_disambiguation_models(x),
        'relationship_models': lambda x: upgrade_relationship_models(x),
        'relationship_disambiguation': lambda x: upgrade_relationship_disambiguation_models(x),
        'entity_rel_enrich': lambda x: config_manager.skip_upgrade_function(x),
        'global_entity_config': lambda x: config_manager.skip_upgrade_function(x),
        'publisher': lambda x: config_manager.skip_upgrade_function(x)
    }
    custom_utils = CustomUtils(config_manager, CONFIG_UPGRADE_METHOD_MAP)

    result = custom_utils.initiate_upgrade_process(CONFIG_UPGRADE_METHOD_MAP)
    return Response(json.dumps(result), mimetype='application/json', status=202 if result['status'] == 'in_progress' else 500)

# Fixing the data dictionary validation routes to handle GET requests properly

@app.route("/ei/config-merge/api/v1/data_dictionary_update/", methods=['GET'])
def validate_data_dictionary():
    try:
        # Get the 'config_item_type' query parameter, defaulting to "any" if not provided
        config_item_type = request.args.get('config_item_type', 'any')
        validator=EntityDatadictionaryUpdate()
        result = validator.process_api_request("/ei/config-merge/api/v1/data_dictionary_update", config_item_type)
        print(result)
        # Log the received config_item_type
        print(f"Received config_item_type: {config_item_type}")

        return result

    except Exception as e:
        logger.error(f"Error in data dictionary validation for all: {e}")
        message = {'status': 'ERROR',
                   'detail': str(e),
                   'developerMsg': str(e)}
        return Response(json.dumps(message), status=400, mimetype='application/json')



@app.route("/ei/config-merge/api/v1/data_dictionary_update/<name>", methods=['GET'])
def validate_data_dictionary_with_name(name):
    try:
        print(f"Updating data dictionary with name: {name}")
        data_dictionary=str(name)
        validator=EntityDatadictionaryUpdate()
        result = validator.process_single_api_request(data_dictionary)
        print(result)
        return result
    except Exception as e:
        logger.error(f"Error in data dictionary validation with name: {e}")
        message = {'status': 'ERROR',
                   'detail': str(e),
                   'developerMsg': str(e)}
        return Response(json.dumps(message), status=400, mimetype='application/json')
