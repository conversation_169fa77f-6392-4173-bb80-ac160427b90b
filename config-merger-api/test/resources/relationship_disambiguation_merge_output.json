{"data": {"relationshipModels": [{"tableName": "ei_cloud_finalv8.sds_ei__rel__tenable_sc__vulnerability_finding_on_host", "name": "sds_ei__rel__tenable_sc__vulnerability_finding_on_host"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__qualys_host_vulnerability__vulnerability_finding_on_cloud_compute_and_container", "name": "qualys"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender__vulnerability_finding_on_cloud_compute_and_container", "name": "sds_ei__rel__ms_defender__vulnerability_finding_on_cloud_compute_and_container"}], "disambiguation": {"disambiguationGrouping": {"type": "VulnerabilityFinding", "blockVariables": ["source_p_id", "target_p_id", "software_vendor", "software_version"], "statusField": "current"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin", "vendor_status", "qualys_vulnerability_id", "qualys_host_id", "ms_recommended_update", "ms_recommended_update_id"], "aggregation": [{"field": "last_reopened_date", "function": "max"}]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_cloud_compute_and_container", "fragmentLocation": "<%KG_FRAGMENT_SCHEMA%>.sds_ei__rel__fragment__vulnerability_finding_on_cloud_compute_and_container", "resolverLocation": "<%KG_FRAGMENT_SCHEMA%>.sds_ei__rel__resolver__vulnerability_finding_on_cloud_compute_and_container"}}}