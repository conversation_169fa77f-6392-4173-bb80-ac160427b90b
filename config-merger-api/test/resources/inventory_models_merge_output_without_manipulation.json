{"data": {"origin": "'MS Active Directory'", "filterBy": "LOWER(sam_account_type) LIKE '%machine_account%'", "primaryKey": "object_guid", "commonProperties": [{"colExpr": "LEAST(last_active_date,first_found_date,ad_created_date, first_seen)", "colName": "first_seen_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "CASE WHEN ad_distinguished_name IS NULL AND os IS NULL  THEN NULL WHEN LOWER(ad_distinguished_name) LIKE '%server%' OR LOWER(os) LIKE '%server%' THEN 'Server' ELSE 'Endpoint' END", "colName": "type", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "GREATEST(ad_last_sync_date,login_last_date, ad_created_date,ad_account_disabled_date)", "colName": "last_active_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colExpr": "UPPER(regexp_extract(cn,'^([^.]++)'))", "colName": "host_name"}, {"colExpr": "CASE WHEN operating_system IS NULL THEN NULL WHEN LOWER(operating_system) LIKE '%unknown%' THEN NULL WHEN LOWER(operating_system) LIKE '%other%' THEN 'Other' ELSE CONCAT_WS(' ',operating_system,CASE WHEN NOT regexp_like(operating_system_version,'(?i)unknown|other') THEN operating_system_version END) END", "colName": "os"}, {"colExpr": "dns_hostname", "colName": "dns_name"}, {"colExpr": "last_logon_epoch", "colName": "login_last_date"}], "sourceSpecificProperties": [{"colExpr": "sam_account_name", "colName": "ad_sam_account_name"}, {"colExpr": "sam_account_type", "colName": "ad_sam_account_type"}, {"colExpr": "CASE WHEN LOWER(ad_uac) LIKE '%disable%' THEN event_timestamp_epoch ELSE NULL END", "colName": "ad_account_disabled_date", "fieldsSpec": {"aggregateFunction": "min"}}, {"colExpr": "distinguished_name", "colName": "ad_distinguished_name"}, {"colExpr": "CAST(user_account_control AS STRING)", "colName": "ad_uac"}, {"colExpr": "object_guid", "colName": "aad_device_id"}, {"colExpr": "when_created_epoch", "colName": "ad_created_date"}, {"colExpr": "last_logon_synced_epoch", "colName": "ad_last_sync_date"}, {"colExpr": "CASE WHEN ad_uac is NULL THEN NULL WHEN LOWER(ad_uac) LIKE '%disable%' THEN 'Disabled' ELSE 'Active' END", "colName": "ad_operational_status", "fieldsSpec": {"isInventoryDerived": false}}]}}