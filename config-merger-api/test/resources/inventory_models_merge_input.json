{"client_configs": [{"id": 5, "name": "sds_ei__host__active_directory__object_guid", "config_item_type": "inventory_models", "config_item_level": "client", "config_deploy_type": "spark_job_config", "config_value": {"origin": "'MS Active Directory'", "filterBy": "LOWER(sam_account_type) LIKE '%machine_account%'", "primaryKey": "object_guid", "commonProperties": [{"colExpr": "LEAST(last_active_date,first_found_date,ad_created_date, first_seen)", "colName": "first_seen_date", "fieldsSpec": {"isInventoryDerived": true}}]}, "config_schema": ["test"], "entity_name": "host", "solution_name": "EI", "data_source_feed_name": "active_directory", "data_source_name": "microsoft", "version": "1.0", "deployed": false, "edit_lock": false}, {"config_item_type": "global_entity_config", "config_item_level": "client", "config_deploy_type": "spark_job_configs", "entity_name": "host", "solution_name": "ei", "data_source_feed_name": "ad_device", "data_source_name": "microsoft_azure", "config_value": {"entityClass": "Host", "entitySpecificProperties": [{"colName": "ip", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": false}}]}}], "solution_configs": [{"id": 7, "name": "sds_ei__host__active_directory__object_guid", "config_item_type": "inventory_models", "config_item_level": "solution", "config_deploy_type": "spark_job_config", "config_value": {"origin": "'MS Active Directory'", "filterBy": "LOWER(sam_account_type) LIKE '%machine_account%'", "primaryKey": "object_guid", "commonProperties": [{"colExpr": "CASE WHEN ad_distinguished_name IS NULL AND os IS NULL  THEN NULL WHEN LOWER(ad_distinguished_name) LIKE '%server%' OR LOWER(os) LIKE '%server%' THEN 'Server' ELSE 'Endpoint' END", "colName": "type", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "LEAST(last_active_date,first_found_date,ad_created_date)", "colName": "first_seen_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colExpr": "GREATEST(ad_last_sync_date,login_last_date, ad_created_date,ad_account_disabled_date)", "colName": "last_active_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colExpr": "UPPER(regexp_extract(cn,'^([^.]++)'))", "colName": "host_name"}, {"colExpr": "CASE WHEN operating_system IS NULL THEN NULL WHEN LOWER(operating_system) LIKE '%unknown%' THEN NULL WHEN LOWER(operating_system) LIKE '%other%' THEN 'Other' ELSE CONCAT_WS(' ',operating_system,CASE WHEN NOT regexp_like(operating_system_version,'(?i)unknown|other') THEN operating_system_version END) END", "colName": "os"}, {"colExpr": "dns_hostname", "colName": "dns_name"}, {"colExpr": "last_logon_epoch", "colName": "login_last_date"}], "sourceSpecificProperties": [{"colExpr": "sam_account_name", "colName": "ad_sam_account_name"}, {"colExpr": "sam_account_type", "colName": "ad_sam_account_type"}, {"colExpr": "CASE WHEN LOWER(ad_uac) LIKE '%disable%' THEN event_timestamp_epoch ELSE NULL END", "colName": "ad_account_disabled_date", "fieldsSpec": {"aggregateFunction": "min"}}, {"colExpr": "distinguished_name", "colName": "ad_distinguished_name"}, {"colExpr": "CAST(user_account_control AS STRING)", "colName": "ad_uac"}, {"colExpr": "object_guid", "colName": "aad_device_id"}, {"colExpr": "when_created_epoch", "colName": "ad_created_date"}, {"colExpr": "last_logon_synced_epoch", "colName": "ad_last_sync_date"}, {"colExpr": "CASE WHEN ad_uac is NULL THEN NULL WHEN LOWER(ad_uac) LIKE '%disable%' THEN 'Disabled' ELSE 'Active' END", "colName": "ad_operational_status", "fieldsSpec": {"isInventoryDerived": false}}]}, "config_schema": ["test"], "entity_name": "host", "solution_name": "ei", "data_source_feed_name": "active_directory", "data_source_name": "microsoft", "version": "1.1", "deployed": false, "edit_lock": false}, {"config_item_type": "global_entity_config", "config_item_level": "solution", "config_deploy_type": "spark_job_configs", "entity_name": "host", "solution_name": "ei", "data_source_feed_name": "ad_device", "data_source_name": "microsoft_azure", "config_value": {"entityClass": "Host", "commonProperties": [{"colName": "display_label", "colExpr": "coalesce(fqdn,dns_name,host_name,aad_device_id,primary_key)", "fieldsSpec": {"isInventoryDerived": true, "postDisambiguationUpdate": true}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period  THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "postDisambiguationUpdate": true}}, {"colName": "inactivity_period", "colExpr": "180", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "fieldsSpec": {"isInventoryDerived": true, "postDisambiguationUpdate": true}}, {"colName": "department", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os_family", "colExpr": "CASE WHEN os IS NULL OR regexp_like(os,'(?i)unknown|^-$') THEN NULL WHEN LOWER(os) LIKE '%windows%' THEN 'Windows' WHEN LOWER(os) LIKE '%macos%' THEN 'macOS' WHEN lower(os) RLIKE '.*linux.*|.*ubuntu.*|.*centos.*|.*fedora.*|.*webos.*|.*chromeos.*|.*debian.*|.*redhat.*|.*tizen.*|.*panos.*|openwrt|.*embeddedos.*' THEN 'Linux' WHEN lower(os) RLIKE '.*cisco.*|.*fortinet.*|.*juniper.*|.*sonicos.*' THEN 'Network OS' WHEN lower(os) RLIKE '.*ios.*|.*ipados.*|.*ipad.*|.*iphone.*' THEN 'iOS' WHEN LOWER(TRIM(os)) LIKE '%android%' THEN 'Android' ELSE 'Other' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "domain", "colExpr": "regexp_extract(fqdn,'^(?:[^.]++[.])((local|corp|[^.]++[^\\\\r\\\\n]++))$')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "host_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ip", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "dns_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "netbios", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "accessibility", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_mac_address", "colExpr": "from_json(null, 'ARRAY<STRING>')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_version", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_architecture", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_build", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_account_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_state", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_region", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_availability_zone", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_manufacturer", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_model", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_serial_number", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_imei", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_user", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_product", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_status", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_compliance_state", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_enrolled_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_last_sync_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_onboarding_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "edr_product", "colExpr": "cast(null as string)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "edr_last_scan_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_last_scan_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_signature_update_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_block_malicious_code_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "fw_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "cast(null as string)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "vm_onboarding_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "vm_tracking_method", "colExpr": "cast(null as string)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "vm_last_scan_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vulnerability_last_observed_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}], "fieldLevelSpec": [{"colName": "ip", "fieldsSpec": {"persistNonNullValue": false}}], "lastUpdateFields": ["type", "first_seen_date", "business_unit", "department", "description", "location", "host_name", "fqdn", "ip", "netbios", "accessibility", "os", "cloud_provider", "cloud_resource_id", "hardware_model", "hardware_serial_number", "login_last_date", "login_last_user", "edr_onboarding_status", "vm_onboarding_status", "mdm_product", "mdm_status", "mdm_last_sync_date", "edr_last_scan_date", "vm_last_scan_date", "ad_distinguished_name", "ad_last_sync_date", "ad_operational_status", "aad_device_id", "aad_operational_status", "azure_operational_status"]}}]}