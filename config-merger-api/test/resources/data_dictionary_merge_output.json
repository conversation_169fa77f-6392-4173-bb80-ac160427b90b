{"data": {"Host": {"caption": "Host", "description": "An independent compute instance where we have visibility or management of the operating system.", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique ID for the entity assigned by Entity Inventory.", "examples": "0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e", "group": "common", "enable_hiding": true, "type": "string", "solutions": ["EI", "Host"], "width": 45}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "examples": ["HYD-JOHDOE-MOB", "<PERSON>"], "group": "common", "enable_hiding": false, "type": "string", "solutions": ["EI", "Host"], "width": 45}, "class": {"caption": "Class", "description": "The abstract super-type of entity.", "examples": ["Host", "Person", "Identity", "Vulenrability"], "group": "common", "enable_hiding": false, "type": "string", "solutions": ["EI", "Host"], "width": 45}, "type": {"caption": "Type", "description": "The specific type of the entity.", "enum_t": {"0": {"caption": "Server"}, "1": {"caption": "Mobile"}, "2": {"caption": "Network"}, "3": {"caption": "Endpoint"}, "4": {"caption": "Other"}}, "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "origin": {"caption": "Origin", "description": "Data source(s) in which the entity was present.", "examples": "['MS Intune','MS Active Directory','WinEvents']", "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Host"], "width": 15}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Host"], "width": 15}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity inferred from the data.Defaults to First Found.", "examples": "*************", "group": "common", "type": "timestamp", "derived_field": true, "enable_hiding": false, "solutions": ["EI", "Host"], "width": 15}, "last_found_date": {"caption": "Last Found", "description": "Date at which the entity was last found in the ingested data.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Host"], "width": 15}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which an activity has been observed for the entity.", "examples": "*************", "group": "common", "type": "timestamp", "derived_field": true, "enable_hiding": false, "solutions": ["EI", "Host"], "width": 15}, "activity_status": {"caption": "Activity Status", "description": "Indicates whether the entity is active within last 180 days. Logic: Inventory Update - Last Active > 180 days, THEN Inactive ELSE Active.", "enum_t": {"0": {"caption": "Active"}, "1": {"caption": "Inactive"}}, "group": "common", "type": "string", "derived_field": true, "enable_hiding": false, "solutions": ["EI", "Host"], "width": 45}, "lifetime": {"caption": "Lifetime", "description": "The number of days over which the entity was active as inferred from ingested data sources. Logic: Last Active - First Seen.", "examples": "5", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources. Logic: Inventory Update - Last Active.", "examples": "10", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "The number of days over which the entity was present in one or more ingested data sources. Logic: Last Found - First Found.", "examples": "5", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. Logic: Inventory Update - Last Found.", "examples": "10", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "description": {"caption": "Description", "description": "A description of the entity.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Host"], "width": 45}, "business_unit": {"caption": "Business Unit", "description": "The name of the business unit within the organisation that the entity belongs to.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Host"], "width": 45}, "location": {"caption": "Location", "description": "Location of the entity", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Host"], "width": 45}, "department": {"caption": "Department", "description": "Name of the department within the business unit.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Host"], "width": 45}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considering for updating the last updated date of an entity.", "examples": "{aad_device_id,host_name,fqdn,hardware_serial_number,ip,netbios,accessibility,hardware_model,cloud_provider,cloud_resource_id,os}", "group": "common", "type": "string", "data_structure": "struct", "ui_visibility": false, "enable_hiding": true, "solutions": ["EI", "Host"], "width": 45}, "has_identity_count": {"caption": "Count of Identities", "description": "Number of identities associated with Host.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "owned_person_count": {"caption": "Count of Owned Persons", "description": "Number of persons owning the host.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "has_vulnerability_finding_count": {"caption": "Count of Vulnerability Findings", "description": "Number of vulnerability findings associated with host.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "has_open_vulnerability_finding_count": {"caption": "Count of Open Vulnerability Findings", "description": "Number of open vulnerability findings associated with host.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "ui_visibility": false, "type": "integer", "enable_hiding": true, "solutions": ["EI", "Host"], "width": 10}, "host_name": {"caption": "Hostname", "description": "Hostname as inferred from data source.", "examples": ["HYD-JOHDOE-MOB"], "group": "entity_specific", "type": "string", "source": ["MS Intune", "MS Active Directory", "Windows Security Logs", "Qualys", "MS Defender", "MS Azure", "AWS", "MS Azure AD"], "derived_field": false, "candidate_key": true, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "fqdn": {"caption": "FQDN", "description": "Fully qualified domain name of the host.", "examples": "hyd-johdoe-mob.corp.prevalent.com", "group": "entity_specific", "type": "string", "source": ["MS Active Directory", "Windows Security Logs", "Qualys", "MS Defender", "MS Azure", "MS Azure AD", "AWS"], "derived_field": false, "candidate_key": true, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "domain": {"caption": "Domain", "description": "Domain that the host is a member of.", "examples": "corp.prevalent.com", "group": "entity_specific", "type": "string", "source": ["MS Active Directory", "Windows Security Logs", "Qualys", "MS Defender", "MS Azure", "MS Azure AD", "AWS"], "derived_field": true, "solutions": ["EI", "Host"], "width": 45}, "ip": {"caption": "IP", "description": "Set of IP addresses observed for the host from different data sources.", "examples": "*************", "group": "entity_specific", "type": "string", "source": ["Qualys", "MS Defender", "MS Azure"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "dns_name": {"caption": "DNS Name", "description": "DNS name of host.", "examples": "hyd-johdoe-mob.corp.prevalent.com", "group": "entity_specific", "type": "string", "source": ["Qualys", "MS Defender", "AWS"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "netbios": {"caption": "NetBIOS", "description": "NetBIOS name of host.", "examples": ["APPOC-7GBPMD3", "SOF-MARMATE-MOB"], "group": "entity_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "accessibility": {"caption": "Accessibility", "description": "Indicates whether the host is external or internal facing.", "enum_t": {"0": {"caption": "Internal"}, "1": {"caption": "External"}}, "group": "entity_specific", "type": "string", "source": ["Qualys", "AWS"], "derived_field": true, "solutions": ["EI", "Host"], "width": 45}, "mdm_mac_address": {"caption": "MDM MAC Address", "description": "Ethernet Mac Address/Wi-Fi MAC address of host collected from MDM.", "examples": "0A15F385B86A", "group": "entity_specific", "type": "string", "source": ["MS Intune"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "os": {"caption": "OS", "description": "Operating System.", "examples": "Microsoft Windows 10 Enterprise LTSC 2019 64-bit", "group": "entity_specific", "type": "string", "source": ["MS Intune", "MS Active Directory", "Qualys", "MS Defender", "MS Azure", "MS Azure AD"], "derived_field": true, "solutions": ["EI", "Host"], "width": 45}, "os_family": {"caption": "OS Family", "description": "Classifies the OS into its correcsponding platforms such as Windows, Linux, macOS, Android, iOS.", "examples": ["Windows", "Linux", "Android"], "group": "entity_specific", "type": "string", "source": ["GlobalProtect", "MS Defender", "MS Intune", "MS Active Directory", "Qualys", "MS Azure", "MS Azure AD"], "derived_field": true, "solutions": ["EI", "Host"], "width": 45}, "os_version": {"caption": "OS Version", "description": "Version number of the respective OS.", "examples": ["Windows", "Linux", "Android"], "group": "entity_specific", "type": "string", "source": ["GlobalProtect", "MS Defender", "MS Intune", "MS Active Directory", "Qualys", "MS Azure", "MS Azure AD"], "derived_field": true, "solutions": ["EI", "Host"], "width": 45}, "os_architecture": {"caption": "OS Architecture", "description": "OS Architecture.", "examples": ["64-bit", "32-bit"], "group": "entity_specific", "type": "string", "source": ["MS Defender"], "solutions": ["EI", "Host"], "width": 45}, "os_build": {"caption": "OS Build", "description": "Build number of the respective OS.", "examples": ["18209"], "group": "entity_specific", "type": "string", "source": ["MS Defender"], "solutions": ["EI", "Host"], "width": 45}, "cloud_provider": {"caption": "Cloud Provider", "description": "Cloud Provider of the host like AWS/Azure/GCP.", "examples": ["AWS", "Azure"], "group": "entity_specific", "type": "string", "source": ["Qualys", "MS Defender", "AWS", "MS Azure"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "cloud_account_id": {"caption": "Cloud Account ID", "description": "Cloud Account ID assigned by Cloud Platform.", "examples": "***********", "group": "source_specific", "type": "string", "source": ["AWS", "MS Azure"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "cloud_resource_id": {"caption": "Cloud Resource ID", "description": "A unique ID assigned to a specific resource by the respective cloud provider.", "examples": ["i-00ccc2e17e8486b65", "0b2b9e0b-8036-460a-9f4e-7f58ce27ff93"], "group": "entity_specific", "type": "string", "source": ["MS Active Directory", "Qualys"], "derived_field": false, "candidate_key": true, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "cloud_resource_type": {"caption": "Cloud Resource Type", "description": "Cloud resource type like AWS EC2, AWS VM, etc.", "examples": ["AWS EC2 Instance", "Azure VM Instance"], "group": "entity_specific", "type": "string", "source": ["AWS", "MS Azure"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "cloud_resource_state": {"caption": "Cloud Resource State", "description": "Cloud resource state indicating whether the instance is running, stopped, etc. ", "examples": ["running", "stopped"], "group": "entity_specific", "type": "string", "source": ["AWS", "MS Azure"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "cloud_region": {"caption": "Cloud Region", "description": "Geographic location where the instance is created by the cloud service provider.", "examples": "eu-west-1, ca-central-1", "group": "entity_specific", "type": "string", "source": ["AWS", "MS Azure"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "cloud_availability_zone": {"caption": "Cloud Availability Zone", "description": "A physically separate location within a cloud region that contains one or more data centers.", "examples": ["eu-west-1", "ca-central-1"], "group": "entity_specific", "type": "string", "source": ["AWS", "MS Azure"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "hardware_manufacturer": {"caption": "Hardware Manufacturer", "description": "Manufacturer like Apple, Lenovo,Dell, etc.", "examples": "Apple", "group": "entity_specific", "type": "string", "source": ["MS Intune", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "hardware_model": {"caption": "Hardware Model", "description": "Hardware specific version or variant of that host as sepcified by the manufacturer (E.g.: Samsung m51).", "examples": "samsung m51", "group": "entity_specific", "type": "string", "source": ["MS Intune", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "hardware_serial_number": {"caption": "Hardware Serial Number", "description": "An identifier that is uniquely assigned to a host by its manufacturer.", "examples": ["2c9380a223057ece", "3RK32G2"], "group": "entity_specific", "type": "string", "source": ["MS Intune"], "derived_field": false, "candidate_key": true, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "hardware_imei": {"caption": "Hardware IMEI", "description": "International Mobile Equipment Identity of the host.", "examples": "14582001081166", "group": "entity_specific", "type": "integer", "source": ["MS Intune"], "derived_field": false, "solutions": ["EI", "Host"], "width": 10}, "login_last_date": {"caption": "Last Login", "description": "Latest date on which the host has a login activity.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["MS Active Directory", "GlobalProtect", "Windows Security Logs", "MS Azure AD", "MS Azure"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "login_last_user": {"caption": "Last Logged in User", "description": "Last user logged in to the host.", "examples": "james", "group": "entity_specific", "type": "string", "source": ["Windows Security Logs"], "solutions": ["EI", "Host"], "width": 45}, "mdm_product": {"caption": "MDM Product", "description": "Product used for Mobile Device Management on the host.", "examples": "*************", "group": "entity_specific", "type": "string", "source": ["MS Intune"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "mdm_status": {"caption": "MDM Registration Status", "description": "Registration status or condition of a host in MDM.", "examples": "*************", "group": "entity_specific", "type": "string", "source": ["MS Intune"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "mdm_compliance_state": {"caption": "MDM Compliance State", "description": "Compliance status of host in MDM.", "examples": ["compliant", "inGracePeriod", "noncompliant"], "group": "entity_specific", "type": "string", "source": ["MS Intune"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "mdm_enrolled_date": {"caption": "MDM Enrolled", "description": "Date at which the host was enrolled into MDM.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["MS Intune"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "mdm_last_sync_date": {"caption": "MDM Last Sync", "description": "Date at which the host last completed a successful sync with MDM.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["MS Intune"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "edr_product": {"caption": "EDR Product", "description": "Product used for Endpoint Detection and Repsonse (EDR) on the host.", "examples": "MS Defender", "group": "entity_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "edr_status": {"caption": "EDR Status", "description": "Indicates whether the EDR agent has been active within the host in past 7 days.", "examples": "True", "group": "entity_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "edr_last_scan_date": {"caption": "EDR <PERSON>", "description": "Last Scanned date of EDR sources.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["MS Defender", "Qualys"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "av_status": {"caption": "Anti Virus Scan Completed", "description": "Antivirus scan completion status of a host.", "examples": "True", "group": "entity_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "av_last_scan_date": {"caption": "AV <PERSON>an", "description": "Indicates the date on which the antivirus scan has completed on the host.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "av_signature_update_date": {"caption": "AV Signature Update", "description": "Denotes whether the antivirus signature is updated.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "av_block_malicious_code_status": {"caption": "AV Block Malicious Code Setting Enabled", "description": "Denotes whether the antivirus block malicious code setting is enabled or not.", "examples": "True", "group": "entity_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "fw_status": {"caption": "FW Enabled", "description": "Firewall Enable Status.", "examples": "True", "group": "entity_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "vm_product": {"caption": "VM Product", "description": "Product used for Vulnerability Management (VM) on the host.", "examples": "Qualys", "group": "entity_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "vm_status": {"caption": "VM Status", "description": "Indicates whether the VM agent has been active within the host in past 7 days.", "examples": "False", "group": "entity_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "vm_agent": {"caption": "VM Agent", "description": "Indicates whether the VM agent is present in the host.", "examples": "False", "group": "entity_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "vm_last_scan_date": {"caption": "VM <PERSON>", "description": "Latest date on which the vulnerability scan has completed on host.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["Qualys"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "vulnerability_last_observed_date": {"caption": "Vulnerability Last Observed", "description": "Latest date on which any vulnerability was active within the host.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "ad_sam_account_name": {"caption": "AD SAM Account Name", "description": "SAM account name belonging to the machine account associated with this host.", "examples": "SOF-GABBGEO-MOY$", "group": "source_specific", "type": "string", "source": ["MS Active Directory"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "ad_sam_account_type": {"caption": "AD SAM Account Type", "description": "A SAM Account Type is a single valued indexed attibute that uniquely defines user objects in AD.", "examples": "NORMAL_USER_ACCOUNT", "group": "source_specific", "type": "string", "source": ["MS Active Directory"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "ad_account_disabled_date": {"caption": "AD Account Disabled", "description": "Date on which the account is disabled in AD.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Active Directory", "Windows Security Logs"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "ad_distinguished_name": {"caption": "AD Distinguished Name", "description": "Name that uniquely identifies an entry in the AD.", "examples": "CN=802.1X (Wired) 301 Developers,OU=CorporateOld,OU=Groups,OU=Stage,OU=Locations,OU=_Corp,DC=corp,DC=safe,DC=com", "group": "source_specific", "type": "string", "source": ["MS Active Directory"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "ad_uac": {"caption": "AD User Account Control", "description": "AD flags that control the status and behavior of the user account.", "examples": ["[PASSWD_NOTREQD,WORKSTATION_TRUST_ACCOUNT]", "[WORKSTATION_TRUST_ACCOUNT]"], "group": "source_specific", "type": "string", "source": ["MS Active Directory"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "ad_created_date": {"caption": "AD Created", "description": "AD account creation date for host.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Active Directory"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "ad_last_sync_date": {"caption": "AD Last Sync", "description": "Latest date on which AD last sync has happened.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Active Directory", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "ad_operational_status": {"caption": "AD Operational Status", "description": "AD operational status indicating whether the account associated with host is active or disabled.", "enum_t": {"0": {"caption": "Active"}, "1": {"caption": "Disabled"}}, "group": "source_specific", "type": "string", "source": ["MS Active Directory"], "derived_field": true, "solutions": ["EI", "Host"], "width": 45}, "aad_id": {"caption": "AAD ID", "description": "Azure AD ID of the host.", "ui_visibility": false, "examples": ["0a9a511d-7f2b-4e78-a346-a47d80c0e982", "ed7abac2-3603-4c41-946f-7a50cce8b2fa"], "group": "source_specific", "type": "string", "source": ["MS Azure AD"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "aad_device_id": {"caption": "AAD Device ID", "description": "Unique ID assigned by Azure AD for the host.", "examples": ["0a9a511d-7f2b-4e78-a346-a47d80c0e982", "ed7abac2-3603-4c41-946f-7a50cce8b2fa"], "group": "source_specific", "type": "string", "source": ["MS Azure AD", "MS Active Directory", "MS Defender", "MS Azure", "MS Intune"], "derived_field": false, "candidate_key": true, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "aad_subscription_id": {"caption": "AAD Subscription ID", "description": "Subscription ID of the services in Azure.", "examples": "2c53d3f7-5cd7-4110-accf-2a975a3cc6d9", "group": "source_specific", "type": "string", "source": ["MS Azure"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "aad_enrolled_date": {"caption": "AAD Enrolled", "description": "Date at which the host was enrolled into Azure.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Azure AD"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "aad_created_date": {"caption": "AAD Created", "description": "Date on which the account was created in Azure AD.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Azure AD"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "aad_management_service": {"caption": "AAD Management Service", "description": "The service through which the host is managed in Azure AD.", "examples": "Intune", "group": "source_specific", "type": "string", "source": ["MS Azure AD"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "aad_deleted_date": {"caption": "AAD Deleted", "description": "Deleted time of a host from the Azure AD.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Azure AD"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "aad_operational_status": {"caption": "AAD Operational Status", "description": "Azure AD operational state of the entity to detemine whether it is active or disabled.", "enum": {"0": {"caption": "Active"}, "1": {"caption": "Disabled"}}, "group": "source_specific", "type": "string", "source": "MS Azure AD", "derived_field": true, "solutions": ["EI", "Host"], "width": 45}, "aws_created_date": {"caption": "AWS Created", "description": "Created time of a host from AWS.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["AWS"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "aws_config_launch_date": {"caption": "AWS Config Launch", "description": "AWS Config launch date.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["AWS"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "azure_created_date": {"caption": "Azure Created", "description": "Date at which the host was enrolled into Azure.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Azure"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "azure_operational_status": {"caption": "Azure Operational Status", "description": "Current operational state of an entity in Azure AD.", "enum_t": {"0": {"caption": "Active"}, "1": {"caption": "Disabled"}}, "group": "source_specific", "type": "string", "source": ["MS Azure"], "derived_field": true, "solutions": ["EI", "Host"], "width": 45}, "azure_modified_date": {"caption": "Azure Modified", "description": "Date on which the host object is modified in Azure.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Azure"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "win_event_id": {"caption": "Windows Event Code", "description": "Event code of the WinEvents log.", "examples": ["4624", "4725", "4726"], "group": "source_specific", "type": "string", "source": ["Windows Security Logs"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "qualys_id": {"caption": "Qualys ID", "description": "Unique reference number assigned to hosts by Qualys.", "examples": "0a7e6969-e99d-430d-aecc-78d8749438aa", "group": "source_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "qualys_tags": {"caption": "Qualys Tags", "description": "A piece of information from qualys that gives more context to a host.", "examples": "{Internet Facing Assets,Unknown Business Unit,No Asset Group}", "group": "source_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "qualys_asset_id": {"caption": "Qualys Asset ID", "description": "Unique identifier assigned to each asset that is added to a Qualys subscription.", "examples": "46307729", "group": "source_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "qualys_detection_method": {"caption": "Qualys Detection Method", "description": "Method of detection in qualys.", "examples": "Qualys Cloud Agent", "group": "source_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "defender_id": {"caption": "Defender ID", "description": "ID used to track and manage individual hosts within Microsoft Defender.", "examples": "0a7e6969-e99d-430d-aecc-78d8749438aa,134248746", "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "defender_health_status": {"caption": "Defender Health Status", "description": "Host status assigned by the Microsoft Defender to the hosts scanned.", "examples": ["Active", "NoSensorData", "ImpairedCommunication", "Inactive"], "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "defender_detection_method": {"caption": "Defender Detection Method", "description": "Method of detection in defender.", "examples": "Defender Agent", "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "defender_tags": {"caption": "Defender Tags", "description": "A piece of information by EDR that gives more context to a host.", "examples": "", "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "defender_risk_score": {"caption": "Defender Risk Score", "description": "Risk score as evaluated by Microsoft Defender for Endpoint.", "examples": ["Low", "Medium", "Informational"], "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "defender_exposure_level": {"caption": "Defender Exposure Level", "description": "Exposure level as evaluated by Microsoft Defender for Endpoint.", "examples": ["Low", "Medium", "Informational"], "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "defender_subscription_id": {"caption": "Defender Subscription ID", "description": "Defender Subscription ID.", "examples": "2c53d3f7-5cd7-4110-accf-2a975a3cc6d9", "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "defender_threat_name": {"caption": "Defender Threat Name", "description": "Threat names found by MS Defender.", "examples": ["TrojanDownloader:PowerShell/CobaltStrike.C!ibt", "Exploit:O97M/CVE-2017-11882.RV!MTB "], "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "defender_onboarding_status": {"caption": "Defender Onboarding Status", "description": "Defender onboarding status of host.", "examples": "Onboarded", "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "defender_threat_count": {"caption": "Defender Infection Count", "description": "Number of threats that have been detected by Microsoft Defender.", "examples": "9", "group": "source_specific", "type": "integer", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 10}, "defender_management_service": {"caption": "Defender Management Service", "description": "Service through which the host is managed in Defender.", "examples": ["intune"], "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "defender_action_type": {"caption": "Defender Action Type", "description": "Action performed by Antivirus.", "examples": ["AntivirusScanCompleted", "AntivirusDetection", "AntivirusScanCancelled", "AntivirusDetectionActionType"], "group": "source_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "defender_onboarding_date": {"caption": "Defender Onboarding", "description": "Date when a particular host was initially detected or registered within the Microsoft Defender security environment.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Host"], "width": 15}, "intune_ownership_status": {"caption": "Intune Ownership Status", "description": "Ownership category of the host.", "examples": "personal", "group": "source_specific", "type": "string", "source": ["MS Intune", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "intune_encryption_status": {"caption": "Intune Encryption Status", "description": "Encryption status of the host.", "examples": ["true", "false"], "group": "source_specific", "type": "string", "source": ["MS Intune"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "intune_id": {"caption": "Intune ID", "description": "Unique identifier assigned to each enrolled host within the Intune management system.", "examples": "47c8298-24a9-4575-a414-eeb35f205e9d", "group": "source_specific", "type": "string", "source": ["MS Intune"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}, "intune_management_service": {"caption": "Intune Management Service", "description": "Service through which the host is managed in MS Intune.", "examples": "mdm", "group": "source_specific", "type": "string", "source": ["MS Intune"], "derived_field": false, "solutions": ["EI", "Host"], "width": 45}}}, "Person": {"caption": "Person", "description": "The Inventory Dictionary defines attributes and includes references to the events and objects in which they are used.", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique ID for the entity assigned by Entity Inventory.", "examples": "0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e", "group": "common", "enable_hiding": true, "type": "string", "solutions": ["EI", "Person"], "width": 45}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "examples": ["HYD-JOHDOE-MOB", "<PERSON>"], "group": "common", "enable_hiding": false, "type": "string", "solutions": ["EI", "Person"], "width": 45}, "class": {"caption": "Class", "description": "Class of the entity.", "examples": ["Host", "Person", "Identity", "Vulenrability"], "group": "common", "enable_hiding": false, "type": "string", "solutions": ["EI", "Person"], "width": 45}, "type": {"caption": "Type", "description": "The specific type of the entity.", "examples": ["Contingent worker", "Employee", "Senior Manager"], "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "solutions": ["EI", "Person"], "width": 45}, "origin": {"caption": "Origin", "description": "Data source(s) in which the entity was present.", "examples": "['MS Intune','MS Active Directory','WinEvents']", "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "solutions": ["EI", "Person"], "width": 45}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Person"], "width": 15}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Person"], "width": 15}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity inferred from the data.Defaults to First Found.", "examples": "*************", "group": "common", "type": "timestamp", "enable_hiding": false, "derived_field": true, "solutions": ["EI", "Person"], "width": 15}, "last_found_date": {"caption": "Last Found", "description": "Date at which the entity was last found in the ingested data.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Person"], "width": 15}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which an activity has been observed for the entity.", "examples": "*************", "group": "common", "type": "timestamp", "derived_field": true, "enable_hiding": false, "solutions": ["EI", "Person"], "width": 15}, "activity_status": {"caption": "Activity Status", "description": "Indicates whether the entity is active within last 180 days. Logic: Inventory Update - Last Active > 180 days, THEN Inactive ELSE Active.", "enum_t": {"0": {"caption": "Active"}, "1": {"caption": "Inactive"}}, "group": "common", "derived_field": true, "enable_hiding": false, "type": "string", "solutions": ["EI", "Person"], "width": 45}, "lifetime": {"caption": "Lifetime", "description": "The number of days over which the entity was active as inferred from ingested data sources. Logic: Last Active - First Seen.", "examples": "5", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Person"], "step_interval": 1, "width": 10}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days over which the entity was last active as inferred from ingested data sources. Logic: Inventory Update - Last Active.", "examples": "10", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Person"], "step_interval": 1, "width": 10}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources. Logic: Last Found - First Found.", "examples": "5", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Person"], "step_interval": 1, "width": 10}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. Logic: Inventory Update - Last Found.", "examples": "10", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Person"], "step_interval": 1, "width": 10}, "description": {"caption": "Description", "description": "A description of the entity.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Person"], "width": 45}, "business_unit": {"caption": "Business Unit", "description": "Name of the business unit within the organisation that the entity belongs to.", "group": "common", "examples": "Shared costs (SHE)", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Person"], "width": 45}, "location": {"caption": "Location", "description": "Location of the entity", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Person"], "width": 45}, "department": {"caption": "Department", "description": "Name of the department within the business unit.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Person"], "width": 45}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Person"], "step_interval": 1, "width": 10}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considering for updating the last updated date of an entity.", "examples": "{first_name,last_name,middle_name,email_id,person_type,manager_id,employee_id,company,department,azure_ad_user_id,external_email_id,recruit_date,contract_end_date,job_title,job_position_id, legal_entity, organisation_unit_id,business_unit, location, cost_center, job_function, phone_number, address, last_known_termination_date,sf_employee_status,iga_account_status}", "group": "common", "enable_hiding": true, "type": "string", "ui_visibility": false, "data_structure": "struct", "solutions": ["EI", "Person"], "width": 45}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "ui_visibility": false, "enable_hiding": true, "type": "integer", "solutions": ["EI", "Person"], "width": 10}, "has_identity_count": {"caption": "Count of Identities", "description": "Number of identities associated with the Person.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Person"], "step_interval": 1, "width": 10}, "owns_host_count": {"caption": "Count of Owns Host", "description": "Number of hosts owned by the Person.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Person"], "step_interval": 1, "width": 10}, "full_name": {"caption": "Full Name", "description": "Full name of the person.", "examples": "<PERSON><PERSON>", "group": "entity_specific", "type": "string", "source": ["SuccessFactors", "MS Active Directory", "Saviynt IGA", "MS Intune", "ServiceNow ITSM", "MS Azure AD"], "derived_field": false, "candidate_key": true, "data_structure": "list", "solutions": ["EI", "Person"], "width": 45}, "first_name": {"caption": "First Name", "description": "First name of the person.", "examples": "<PERSON><PERSON>", "group": "entity_specific", "type": "string", "source": ["SuccessFactors", "MS Active Directory", "Saviynt IGA", "MS Azure AD"], "derived_field": false, "ui_visibility": false, "solutions": ["EI", "Person"], "width": 45}, "middle_name": {"caption": "Middle Name", "description": "Middle name of the person.", "examples": "<PERSON>", "group": "entity_specific", "type": "string", "source": ["SuccessFactors", "MS Active Directory", "Saviynt IGA"], "derived_field": false, "ui_visibility": false, "solutions": ["EI", "Person"], "width": 45}, "last_name": {"caption": "Last Name", "description": "Last name of the person.", "examples": "<PERSON>", "group": "entity_specific", "type": "string", "source": ["SuccessFactors", "MS Active Directory", "Saviynt IGA", "MS Azure AD"], "derived_field": false, "ui_visibility": false, "solutions": ["EI", "Person"], "width": 45}, "ad_operational_status": {"caption": "AD Operational Status", "description": "AD operational status indicating whether the account is active or disabled.", "enum": {"0": {"caption": "Active"}, "1": {"caption": "Disabled"}}, "group": "source_specific", "type": "string", "source": ["MS Active Directory", "Windows Security Logs", "MS Azure AD"], "derived_field": true, "solutions": ["EI", "Person"], "width": 45}, "email_id": {"caption": "Email ID", "description": "Email ID of the person.", "examples": "sunil.k<PERSON><EMAIL>", "group": "entity_specific", "type": "string", "source": ["SuccessFactors", "MS Active Directory", "Saviynt IGA", "MS Intune", "ServiceNow ITSM", "MS Azure AD"], "derived_field": false, "candidate_key": true, "data_structure": "list", "solutions": ["EI", "Person"], "width": 45}, "manager": {"caption": "Manager", "description": "Name of the manager.", "examples": "<PERSON>", "group": "entity_specific", "type": "string", "source": ["SuccessFactors", "MS Active Directory", "Saviynt IGA", "ServiceNow ITSM"], "derived_field": false, "solutions": ["EI", "Person"], "width": 45}, "manager_id": {"caption": "Manager ID", "description": "Manager ID.", "examples": "1679", "group": "entity_specific", "type": "string", "source": ["SuccessFactors"], "derived_field": false, "ui_visibility": false, "solutions": ["EI", "Person"], "width": 45}, "employee_id": {"caption": "Employee ID", "description": "Employee ID collected from mutiple sources.", "examples": "1057", "group": "entity_specific", "type": "string", "source": ["SuccessFactors", "MS Active Directory", "Saviynt IGA", "ServiceNow ITSM", "MS Azure AD"], "derived_field": false, "candidate_key": true, "data_structure": "list", "solutions": ["EI", "Person"], "width": 45}, "company": {"caption": "Company", "description": "Company an employee belongs to.", "examples": ["Google", "Skrill", "Meritcard"], "group": "entity_specific", "type": "string", "source": ["MS Active Directory", "Saviynt IGA", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Person"], "width": 45}, "external_email_id": {"caption": "External Email ID", "description": "External email ID of the employee.", "examples": "<EMAIL>", "group": "entity_specific", "type": "string", "source": ["Saviynt IGA", "SuccessFactors", "MS Azure AD"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Person"], "width": 45}, "recruit_date": {"caption": "Recruited On", "description": "Recruit date of the employee.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["SuccessFactors", "ServiceNow ITSM"], "derived_field": false, "solutions": ["EI", "Person"], "width": 15}, "last_known_termination_date": {"caption": "Last Known Termination Date", "description": "Termination date of the person.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["SuccessFactors", "ServiceNow ITSM"], "derived_field": false, "ui_visibility": false, "solutions": ["EI", "Person"], "width": 15}, "contract_end_date": {"caption": "Contract End", "description": "Date when an employee's contract expires.", "examples": "2/2/2022", "group": "entity_specific", "type": "timestamp", "source": ["SuccessFactors"], "derived_field": false, "solutions": ["EI", "Person"], "width": 15}, "job_title": {"caption": "Job Title", "description": "Designation details of an employee.", "examples": "Security Data Scientist", "group": "entity_specific", "type": "string", "source": ["SuccessFactors", "MS Active Directory", "Saviynt IGA", "ServiceNow ITSM", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Person"], "width": 45}, "job_position_id": {"caption": "Job Position ID", "description": "Job position ID of the employee.", "examples": "7824", "group": "entity_specific", "type": "string", "source": ["SuccessFactors"], "derived_field": false, "solutions": ["EI", "Person"], "width": 45}, "legal_entity": {"caption": "Legal entity", "description": "Defines the country and default Pay Group, Location, currency, and standard hours for employees within that company.", "examples": ["MERITUS", "PAYS GROUP"], "group": "entity_specific", "type": "string", "source": ["SuccessFactors", "Saviynt IGA"], "derived_field": false, "solutions": ["EI", "Person"], "width": 45}, "organisation_unit_id": {"caption": "Organisation Unit ID", "description": "ID of the organisation unit.", "examples": ["E-cash: card (PSC)", "Google 4 Business (P4B)"], "group": "entity_specific", "type": "string", "source": ["Saviynt IGA"], "derived_field": false, "solutions": ["EI", "Person"], "width": 45}, "cost_center": {"caption": "Cost Center", "description": "Department or function in which the costs occur inside an organization.", "examples": ["Business Development (1020)", "Communications (OP_42)"], "group": "entity_specific", "type": "string", "source": ["SuccessFactors", "Saviynt IGA", "ServiceNow ITSM"], "derived_field": false, "solutions": ["EI", "Person"], "width": 45}, "job_function": {"caption": "Job Function", "description": "Job function details of an employee.", "examples": ["Account Executive", "Business Development Officer"], "group": "entity_specific", "type": "string", "source": ["Saviynt IGA"], "derived_field": false, "solutions": ["EI", "Person"], "width": 45}, "phone_number": {"caption": "Phone Number", "description": "Phone number of an employee.", "examples": "*********", "group": "entity_specific", "type": "string", "source": ["Saviynt IGA", "MS Intune", "ServiceNow ITSM", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Person"], "width": 45}, "address": {"caption": "Address", "description": "Address of an employee.", "examples": "159 Vision Park", "group": "entity_specific", "type": "string", "source": ["Saviynt IGA"], "derived_field": false, "solutions": ["EI", "Person"], "width": 45}, "login_last_date": {"caption": "Last Login", "description": "Date at which the person was last logged in.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["MS Active Directory"], "derived_field": false, "solutions": ["EI", "Person"], "width": 15}, "employee_status": {"caption": "Employee Status", "description": "Employee status of person.", "examples": "Terminated", "group": "entity_specific", "type": "string", "source": ["SuccessFactors"], "derived_field": false, "solutions": ["EI", "Person"], "width": 45}, "termination_date": {"caption": "Terminated On", "description": "Termination date of the Person.", "examples": "Terminated", "group": "entity_specific", "type": "timestamp", "source": ["SuccessFactors"], "derived_field": false, "solutions": ["EI", "Person"], "width": 15}, "ad_created_date": {"caption": "AD Created", "description": "Date of AD account creation.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Active Directory", "ServiceNow ITSM"], "derived_field": false, "solutions": ["EI", "Person"], "width": 15}, "ad_last_sync_date": {"caption": "AD Last Sync", "description": "Latest date of AD sync for the associated account.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Active Directory", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Person"], "width": 15}, "ad_last_password_change_date": {"caption": "AD Last Password Change", "description": "Latest date when the password for a user account was updated.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Active Directory", "Saviynt IGA"], "derived_field": false, "solutions": ["EI", "Person"], "width": 15}, "ad_account_disabled_date": {"caption": "AD Account Disabled", "description": "Date on which the account is disabled in AD.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Active Directory"], "derived_field": false, "solutions": ["EI", "Person"], "width": 15}, "aad_operational_status": {"caption": "AAD Operational Status", "description": "Azure AD operational state of the entity to detemine whether it is active or disabled.", "enum": {"0": {"caption": "Active"}, "1": {"caption": "Disabled"}}, "group": "source_specific", "type": "string", "source": "MS Azure AD", "derived_field": true, "solutions": ["EI", "Person"], "width": 45}, "aad_created_date": {"caption": "AAD Created", "description": "Date when azure ad object was created.", "examples": ["*************", "*************"], "group": "source_specific", "type": "timestamp", "source": ["MS Azure AD"], "derived_field": false, "solutions": ["EI", "Person"], "width": 15}, "aad_deleted_date": {"caption": "AAD Deleted", "description": "Date when the user was deleted.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Azure AD"], "derived_field": false, "solutions": ["EI", "Person"], "width": 15}, "aad_user_id": {"caption": "AAD User ID", "description": "Unique identifier for the user in Azure AD.", "examples": "0d804c40-c98e-45a9-a0a1-cbbc6417bd42", "group": "source_specific", "type": "string", "source": ["MS Active Directory", "MS Intune", "MS Azure AD"], "derived_field": false, "candidate_key": true, "data_structure": "list", "solutions": ["EI", "Person"], "width": 45}, "sf_employee_status": {"caption": "SF Employee Status", "description": "SuccessFactors operational status of an entity to detemine whether the employee is active or terminated.", "examples": ["Active", "Terminated"], "group": "source_specific", "type": "string", "source": ["SuccessFactors"], "solutions": ["EI", "Person"], "width": 45}, "iga_operational_status": {"caption": "IGA Operational Status", "description": "IGA operational status to determine whether the account associated is active or disabled.", "examples": ["Active", "Terminated"], "group": "source_specific", "type": "string", "source": ["Saviynt IGA"], "derived_field": true, "solutions": ["EI", "Person"], "width": 45}}}, "Identity": {"caption": "Identity", "description": "The Inventory Dictionary defines attributes and includes references to the events and objects in which they are used.", "extends": "", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique ID for the entity assigned by Entity Inventory.", "examples": ["0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e", "112bfd47e969ccaad185b1f087dd9c8b6e23bf6f882ccb59343af3476159e8cc"], "group": "common", "enable_hiding": true, "type": "string", "solutions": ["EI", "Identity"], "width": 45}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "examples": ["HYD-JOHDOE-MOB", "<PERSON>"], "group": "common", "enable_hiding": false, "type": "string", "solutions": ["EI", "Identity"], "width": 45}, "class": {"caption": "Class", "description": "Class of the entity.", "examples": ["Host", "Person", "Identity", "Vulerability"], "group": "common", "enable_hiding": false, "type": "string", "solutions": ["EI", "Identity"], "width": 45}, "type": {"caption": "Type", "description": "The specific type of the entity.", "enum": {"0": {"caption": "User Principal Name"}, "1": {"caption": "AD Domain"}, "2": {"caption": "Azure AD"}, "3": {"caption": "Email"}, "4": {"caption": "VPN"}, "5": {"caption": "IGA Saviynt"}, "6": {"caption": "end_point"}}, "group": "common", "examples": ["AD Domain", "Email"], "enable_hiding": false, "type": "string", "data_structure": "list", "solutions": ["EI", "Identity"], "width": 45}, "origin": {"caption": "Origin", "description": "Data source(s) in which the entity was present.", "examples": ["MS Intune", "Defender"], "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "solutions": ["EI", "Identity"], "width": 45}, "first_found_date": {"caption": "First Found", "description": "Date on which the entity was first discovered in the ingested data.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Identity"], "width": 15}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Identity"], "width": 15}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity inferred from the data.Defaults to First Found.", "examples": "*************", "group": "common", "type": "timestamp", "derived_field": true, "enable_hiding": false, "solutions": ["EI", "Identity"], "width": 15}, "last_found_date": {"caption": "Last Found", "description": "Date on which the entity was last found in the ingested data.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Identity"], "width": 15}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which an activity has been observed for the entity.", "examples": "*************", "group": "common", "type": "timestamp", "enable_hiding": false, "derived_field": true, "solutions": ["EI", "Identity"], "width": 15}, "activity_status": {"caption": "Activity Status", "description": "Indicates whether the entity is active within last 180 days. Logic: Inventory Update - Last Active > 180 days, THEN Inactive ELSE Active.", "enum": {"0": {"caption": "Active"}, "1": {"caption": "Inactive"}}, "group": "common", "type": "string", "enable_hiding": false, "derived_field": true, "solutions": ["EI", "Identity"], "width": 45}, "lifetime": {"caption": "Lifetime", "description": "The number of days over which the entity was active as inferred from ingested data sources. Logic: Last Active - First Seen.", "examples": "5", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Identity"], "step_interval": 1, "width": 10}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources. Logic: Inventory Update - Last Active.", "examples": "10", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Identity"], "step_interval": 1, "width": 10}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources. Logic: Last Found - First Found.", "examples": "10", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Identity"], "step_interval": 1, "width": 10}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. Logic: Inventory Update - Last Found.", "examples": "10", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Identity"], "step_interval": 1, "width": 10}, "description": {"caption": "Description", "description": "Description of the entity.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Identity"], "width": 45}, "business_unit": {"caption": "Business Unit", "description": "Name of the business unit within the organisation that the entity belongs to.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Identity"], "width": 45}, "location": {"caption": "Location", "description": "Location of the entity", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Identity"], "width": 45}, "department": {"caption": "Department", "description": "Name of the department within the business unit.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Identity"], "width": 45}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Identity"], "step_interval": 1, "width": 10}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considering for updating the last updated date of an entity.", "examples": "{identity_provider,identity_type,account_name,account_type,status,ad_distinguished_name,ad_when_created_date,ad_account_expires_date,ad_last_password_change_date,ad_sam_account_name_with_domain,user_principal_name,iga_account_id,ad_last_login_date,ad_password_policy,ad_last_sync_date,azure_on_premises_sync_enabled_status,azure_enrollment_type,ad_security_identifier,ad_sam_account_type,ad_member_of,manager}", "group": "common", "type": "string", "ui_visibility": false, "enable_hiding": true, "data_structure": "struct", "solutions": ["EI", "Identity"], "width": 45}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "ui_visibility": false, "enable_hiding": true, "type": "integer", "solutions": ["EI", "Identity"], "width": 10}, "associated_person_count": {"caption": "Count of Associated Persons", "description": "Number of persons associated with the Identity.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Identity"], "step_interval": 1, "width": 10}, "associated_host_count": {"caption": "Count of Associated Hosts", "description": "Number of hosts associated with the Identity.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Identity"], "step_interval": 1, "width": 10}, "identity_provider": {"caption": "Identity Provider", "description": "Service that stores and verifies a user identity.", "enum": {"0": {"caption": "AD"}, "1": {"caption": "AAD"}}, "examples": ["AD", "AAD"], "group": "entity_specific", "type": "string", "source": ["MS Active Directory", "MS Azure AD"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Identity"], "width": 45}, "account_name": {"caption": "Account Name", "description": "Name/login used to access the system or service.", "examples": ["<EMAIL>", "d1624f15-b162-4ae1-a964-3c72ae096c85"], "group": "entity_specific", "type": "string", "data_structure": "list", "source": ["MS Active Directory", "SuccessFactors", "Windows Security Logs", "Saviynt IGA", "GlobalProtect", "MS Intune", "MS Defender", "ServiceNow ITSM", "MS Azure AD"], "derived_field": false, "candidate_key": true, "solutions": ["EI", "Identity"], "width": 45}, "account_type": {"caption": "Account Type", "description": "Type of account like Service Account, Machine Account, etc.", "examples": ["Service Account", "MACHINE_ACCOUNT", "NORMAL_USER_ACCOUNT"], "group": "entity_specific", "type": "string", "source": ["MS Active Directory", "Saviynt IGA", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "manager": {"caption": "Manager", "description": "Name of the manager.", "examples": ["<PERSON>"], "source": ["MS Active Directory", "Saviynt IGA", "SuccessFactors", "ServiceNow ITSM"], "group": "entity_specific", "type": "string", "solutions": ["EI", "Identity"], "width": 45}, "account_expires_date": {"caption": "Account Expires", "description": "Date on which the account expires.", "examples": ["2023-07-09", "Never"], "group": "entity_specific", "type": "string", "source": ["MS Active Directory", "Saviynt IGA"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "login_last_date": {"caption": "Last Login", "description": "Last date at which the user logged in to the account.", "examples": ["*************"], "group": "entity_specific", "type": "timestamp", "source": ["Saviynt IGA", "Windows Security Logs", "MS Active Directory", "MS Intune", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}, "mdm_last_sync_date": {"caption": "MDM Last Sync", "description": "Latest date on which the host last completed a successful sync with MDM.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": "MS Intune", "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}, "mdm_enrolled_date": {"caption": "MDM Enrolled", "description": "Date on which the host was enrolled into MDM.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": "MS Intune", "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}, "account_status": {"caption": "Account Status", "description": "Specifies if the account is disabled or active.", "group": "entity_specific", "type": "string", "source": ["MS Active Directory", "Windows Security Logs", "MS Azure AD", "MS Azure", "Saviynt IGA"], "derived_field": true, "solutions": ["EI", "Identity"], "width": 45}, "ad_operational_status": {"caption": "AD Operational Status", "description": "AD operational status indicating whether the account is active or disabled.", "enum": {"0": {"caption": "Active"}, "1": {"caption": "Disabled"}}, "group": "source_specific", "type": "string", "source": ["MS Active Directory", "Windows Security Logs", "MS Azure AD"], "derived_field": true, "solutions": ["EI", "Identity"], "width": 45}, "ad_security_identifier": {"caption": "AD Security Identifier", "description": "Unique ID that is assigned to an account by AD.", "examples": "S-1-5-21-**********-**********-********-1013", "group": "source_specific", "type": "string", "source": ["MS Azure AD"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "ad_distinguished_name": {"caption": "AD Distinguished Name", "description": "Name that uniquely identifies an entry in the active directory.", "examples": ["CN=DELTA$,CN=Users,DC=corp,DC=lyman,DC=com", "CN=<PERSON><PERSON>,OU=DEV-USER,OU=Users,OU=Montreal,OU=Locations,OU=_Corp,DC=corp,DC=lyman,DC=com"], "group": "source_specific", "type": "string", "source": ["MS Active Directory", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "ad_created_date": {"caption": "AD Created", "description": "Date on which the account was created in AD.", "examples": ["*************", "*************"], "group": "source_specific", "type": "timestamp", "source": ["MS Active Directory"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}, "ad_last_sync_date": {"caption": "AD Last Sync", "description": "Latest date on which the account has synced with AD.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Active Directory", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}, "ad_last_password_change_date": {"caption": "AD Last Password Change", "description": "Latest date on which the password is updated for AD account.", "examples": ["*************", "*************"], "group": "source_specific", "type": "timestamp", "source": ["MS Active Directory", "Saviynt IGA"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}, "user_principal_name": {"caption": "User Principal Name", "description": "Sign in name used by the users when they sign in (log in) to their accounts.", "examples": ["corp\\7e2368c8-5e11-456c-989e-50352f1c48e0", "lyman\\MTL-JONDEJ1-MOB$", "PROD\\TOR-PROD-PSM-03$"], "group": "entity_specific", "type": "string", "source": ["MS Active Directory", "MS Intune", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "ad_sam_account_type": {"caption": "AD SAM Account Type", "description": "SAM Account Type is a single valued indexed attibute that uniquely defines AD user objects.", "examples": ["NORMAL_USER_ACCOUNT"], "group": "source_specific", "type": "string", "source": ["MS Active Directory"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "ad_member_of": {"caption": "AD Member Of", "description": "A multi-valued attribute that contains groups of which the user is a direct member, except for the primary group.", "examples": "CN=VPN,OU=DistributionUnit,OU=SharedMailbox,DC=dir;CN=ActiveDirectory,OU=DistributionUnit,OU=SharedMailbox,DC=dir;CN=InternetAccess,OU=DistributionUnit,OU=SharedMailbox,DC=dir", "group": "source_specific", "type": "string", "source": ["MS Active Directory"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "ad_domain": {"caption": "AD Domain", "description": "Domain of the account.", "examples": ["corp", "lyman", "com"], "group": "source_specific", "type": "string", "source": ["MS Active Directory", "Windows Security Logs", "MS Azure AD", "GlobalProtect"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "ad_sam_account_name": {"caption": "AD SAM Account Name", "description": "Logon name used by AD account.", "examples": ["1E61DE7C6EE24BCF948", "<EMAIL>", "SOF-VESGENITA-MOB$"], "group": "source_specific", "type": "string", "source": ["MS Active Directory", "Windows Security Logs", "Saviynt IGA", "MS Azure AD"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "ad_sam_account_name_with_domain": {"caption": "AD SAM Account Name With Domain", "description": "Logon name used by AD account.", "examples": ["corp\\7e2368c8-5e11-456c-989e-50352f1c48e0", "lyman\\MTL-JONDEJ1LA-MOB$", "PROD\\TOR-PROD-PSM-03$"], "group": "source_specific", "type": "string", "source": ["MS Active Directory", "Windows Security Logs"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "ad_uac": {"caption": "AD User Account Control", "description": "Flags that control the behavior of the user account.", "examples": ["[PASSWD_NOTREQD,WORKSTATION_TRUST_ACCOUNT]", "[WORKSTATION_TRUST_ACCOUNT]"], "group": "source_specific", "type": "string", "source": ["MS Active Directory"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "ad_account_disabled_date": {"caption": "AD Account Disabled", "description": "Date on which the account is disabled in AD.", "examples": "*************", "group": "source_specific", "type": "timestamp", "source": ["MS Active Directory", "Windows Security Logs"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}, "sf_created_date": {"caption": "SF Created", "description": "Date when identity is created with respect to SuccessFactors.", "examples": ["*************", "*************"], "group": "source_specific", "type": "timestamp", "source": "SuccessFactors", "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}, "sf_employee_status": {"caption": "SF Employee Status", "description": "Current SuccessFactors operational status detemining whether the identity is active or disabled.", "enum": {"0": {"caption": "Active"}, "1": {"caption": "Disabled"}}, "group": "source_specific", "type": "string", "source": "SuccessFactors", "derived_field": true, "solutions": ["EI", "Identity"], "width": 45}, "iga_created_date": {"caption": "IGA Created", "description": "Date when IGA object was created.", "examples": ["*************", "*************"], "group": "source_specific", "type": "timestamp", "source": "Saviynt IGA", "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}, "iga_account_id": {"caption": "IGA Account ID", "description": "Unique number used to identify the IGA account.", "examples": ["CN=<PERSON>,OU=GEN-USER,OU=Users,OU=Montreal,OU=Locations,OU=_Corp,DC=corp,DC=lyman,DC=com", "0054G00000AOBOCQA5", "9030"], "group": "source_specific", "type": "string", "source": ["Saviynt IGA"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "iga_operational_status": {"caption": "IGA Operational Status", "description": "IGA operational status of an entity to detemine whether the entity is active or disabled.", "enum": {"0": {"caption": "Active"}, "1": {"caption": "Disabled"}}, "group": "source_specific", "type": "string", "source": "Saviynt IGA", "derived_field": true, "solutions": ["EI", "Identity"], "width": 45}, "iga_account_type": {"caption": "IGA Account Type", "description": "IGA account type.", "examples": "", "group": "source_specific", "type": "string", "source": ["Saviynt IGA"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "aad_operational_status": {"caption": "AAD Operational Status", "description": "Azure AD operational state of the entity to detemine whether it is active or disabled.", "enum": {"0": {"caption": "Active"}, "1": {"caption": "Disabled"}}, "group": "source_specific", "type": "string", "source": "MS Azure AD", "derived_field": true, "solutions": ["EI", "Identity"], "width": 45}, "aad_on_premises_sync_enabled_status": {"caption": "AAD On Premises Sync Enabled Status", "description": "Indicates whether the Azure AD is synced with on-prem AD.", "examples": "True", "group": "source_specific", "source": ["MS Azure AD"], "derived_field": false, "type": "string", "solutions": ["EI", "Identity"], "width": 45}, "aad_enrollment_type": {"caption": "AAD Enrollment Type", "description": "Method by which a user or host is registered and enrolled in a system, program, or service.", "examples": "", "group": "source_specific", "type": "string", "source": ["MS Azure AD"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "aad_created_date": {"caption": "AAD Created", "description": "Date when azure ad object was created.", "examples": ["*************", "*************"], "group": "source_specific", "type": "timestamp", "source": ["MS Azure AD"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}, "aad_password_policy": {"caption": "AAD Password Policy", "description": "A password policy is a set of rules and requirements that an organization or system enforces to ensure the security of passwords used to access its resources.", "examples": "DisablePasswordExpiration", "group": "source_specific", "type": "string", "source": ["MS Azure AD"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 45}, "defender_onboarding_date": {"caption": "Defender Onboarding", "description": "Defender Onboarded date of identity in Defender.", "examples": "1676419633000", "group": "source_specific", "type": "timestamp", "source": ["Defender"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}, "defender_last_seen_date": {"caption": "Defender Last Seen", "description": "Last seen date of identity in De<PERSON>.", "examples": "1676419633000", "group": "source_specific", "type": "timestamp", "source": ["Defender"], "derived_field": false, "solutions": ["EI", "Identity"], "width": 15}}}, "Vulnerability": {"caption": "Vulnerability", "description": "The Vulnerability Dictionary defines attributes defined for Vulnerability entity derived from multiple sources.", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique ID for the entity assigned by Entity Inventory.", "examples": "0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e", "group": "common", "enable_hiding": true, "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "examples": ["CVE-2018-0167"], "group": "common", "enable_hiding": false, "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "class": {"caption": "Class", "description": "Class of the entity.", "examples": ["Host", "Person", "Identity", "Vulnerability"], "group": "common", "enable_hiding": false, "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "type": {"caption": "Type", "description": "The specific type of the entity.", "examples": ["Vulnerability", "Informational"], "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "solutions": ["EI", "Vulnerability"], "width": 45}, "origin": {"caption": "Origin", "description": "Data source(s) in which the entity was present.", "examples": "['MS Intune','MS Active Directory','WinEvents']", "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "solutions": ["EI", "Vulnerability"], "width": 45}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Vulnerability"], "width": 15}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources.", "examples": "*************", "group": "common", "type": "timestamp", "enable_hiding": false, "solutions": ["EI", "Vulnerability"], "width": 15}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity inferred from the data.Defaults to First Found", "examples": "*************", "group": "common", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Vulnerability"], "width": 15}, "last_found_date": {"caption": "Last Found", "description": "Date at which the entity was last found in the ingested data.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp", "solutions": ["EI", "Vulnerability"], "width": 15}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which an activity has been observed for the entity.", "examples": "*************", "group": "common", "type": "timestamp", "enable_hiding": false, "solutions": ["EI", "Vulnerability"], "width": 15}, "activity_status": {"caption": "Activity Status", "description": "Indicates whether the entity is active within last 180 days. Logic: Inventory Update - Last Active > 180 days, THEN Inactive ELSE Active.", "enum": {"0": {"caption": "Unknown"}, "1": {"caption": "Active"}, "2": {"caption": "Inactive"}}, "enable_hiding": false, "group": "common", "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "lifetime": {"caption": "Lifetime", "description": "The number of days over which the entity was active as inferred from ingested data sources. Logic: Last Active - First Seen.", "examples": "5", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Vulnerability"], "step_interval": 1, "width": 10}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources. Logic: Inventory Update - Last Active.", "examples": "10", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Vulnerability"], "step_interval": 1, "width": 10}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources. Logic: Last Found - First Found.", "examples": "10", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Vulnerability"], "step_interval": 1, "width": 10}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. Logic: Inventory Update - Last Found.", "examples": "10", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Vulnerability"], "step_interval": 1, "width": 10}, "description": {"caption": "Description", "description": "Description of the entity.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "business_unit": {"caption": "Business Unit", "description": "Name of the business unit within the organisation that the entity belongs to.", "group": "common", "examples": "Shared costs (SHE)", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "location": {"caption": "Location", "description": "Location of the entity", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "department": {"caption": "Department", "description": "Name of the department within the business unit.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "solutions": ["EI", "Vulnerability"], "step_interval": 1, "width": 10}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considering for updating the last updated date of an entity.", "examples": "{title,cve_id, temporal_cvss_score, vulnerability_type, v30_severity, v30_score, v30_vector, v31_score, v30_impact_score, v31_vector, v31_severity ,v31_impact_score,status,, cwe, description,software_list,patch_avaialable,exploit_available, recommendation, ms_recommended_update, ms_recommended_update_id, published_date, last_modified_date, last_active_date, activity_status}", "group": "common", "ui_visibility": false, "enable_hiding": true, "type": "string", "data_structure": "struct", "solutions": ["EI", "Vulnerability"], "width": 45}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "ui_visibility": false, "type": "integer", "solutions": ["EI", "Vulnerability"], "width": 10}, "associated_hosts_with_open_findings_count": {"caption": "Count of Hosts with Open Vulnerability Findings", "description": "Number of hosts associated with open vulnerability findings.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Vulnerability"], "step_interval": 1, "width": 10}, "associated_hosts_with_findings_count": {"caption": "Count of Hosts with Vulnerability Findings", "description": "Number of hosts associated with vulnerability findings.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "solutions": ["EI", "Vulnerability"], "step_interval": 1, "width": 10}, "title": {"caption": "Title", "description": "Title of the vulnerability.", "examples": "Accellion FTA OS Command Injection Vulnerability", "group": "entity_specific", "type": "string", "source": ["Qualys", "NVD"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "cve_id": {"caption": "CVE ID", "description": "Unique identifier assigned to a specific security vulnerability or exposure in software or hardware products. ", "examples": ["13785", "CVE-2018-0167"], "group": "entity_specific", "type": "string", "source": ["Qualys", "MS Defender", "NVD", "EPSS"], "derived_field": false, "data_structure": "list", "candidate_key": true, "solutions": ["EI", "Vulnerability"], "width": 45}, "v2_score": {"caption": "CVSSv2 Score", "description": "Common Vulnerability Scoring System, which gives the severity of a vulnerability.", "examples": "2.0", "group": "entity_specific", "type": "double", "source": ["NVD"], "derived_field": false, "range_selection": true, "min": 0.0, "max": 10.0, "solutions": ["EI", "Vulnerability"], "step_interval": 0.1, "width": 10}, "v2_vector": {"caption": "CVSSv2 Vector", "description": "A compressed textual representation of the values used to derive the CVSS Score.", "examples": "AV:N/AC:L/Au:N/C:C/I:C/A:C", "group": "entity_specific", "type": "string", "source": ["NVD"], "derived_field": false, "ui_visibility": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "v2_severity": {"caption": "CVSSv2 Severity", "description": "Severity of the vulnerability.", "examples": ["Critical", "High", "Medium", "Low"], "group": "entity_specific", "type": "string", "source": ["NVD"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "v2_exploitability": {"caption": "CVSSv2 Exploitability", "description": "A metric that measures the ease or difficulty with which a security vulnerability can be exploited by an attacker.", "examples": "1.1", "group": "entity_specific", "type": "double", "source": ["NVD"], "derived_field": false, "range_selection": true, "min": 0.0, "max": 10.0, "solutions": ["EI", "Vulnerability"], "step_interval": 0.1, "width": 10}, "v2_impact_score": {"caption": "CVSSv2 Impact Score", "description": "A metric that quantifies the potential consequences or impact of a security vulnerability if successfully exploited by an attacker.", "examples": ["10.0", "7.0"], "group": "entity_specific", "type": "double", "source": ["NVD"], "derived_field": false, "range_selection": true, "min": 0.0, "max": 10.0, "solutions": ["EI", "Vulnerability"], "step_interval": 0.1, "width": 10}, "v30_score": {"caption": "CVSSv3.0 Score", "description": "Common Vulnerability Scoring System, which gives the severity of a vulnerability.", "examples": "2.0", "group": "entity_specific", "type": "double", "range_selection": true, "source": ["Qualys", "MS Defender"], "derived_field": false, "min": 0.0, "max": 10.0, "solutions": ["EI", "Vulnerability"], "step_interval": 0.1, "width": 10}, "temporal_cvss_score": {"caption": "CVSSv3.0 Temporal Score", "description": "Vulnerability severity that change over the lifetime of a vulnerability.", "examples": "5.6", "group": "entity_specific", "type": "double", "range_selection": true, "source": ["Qualys"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "step_interval": 0.1, "width": 10}, "v30_vector": {"caption": "CVSSv3.0 Vector", "description": "Compressed textual representation of the values used to derive the CVSS Score. ", "examples": "CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:H/A:H/E:U/RL:O/RC:R", "group": "entity_specific", "type": "string", "source": ["Qualys", "NVD"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "v30_severity": {"caption": "CVSSv3.0 Severity", "description": "Severity of the vulnerability.", "examples": ["Critical", "High", "Medium", "Low"], "group": "entity_specific", "type": "string", "source": ["Qualys", "NVD"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "v30_exploitability": {"caption": "CVSSv3.0 Exploitability", "description": "A metric that measures the ease or difficulty with which a security vulnerability can be exploited by an attacker.", "examples": "1.1", "group": "entity_specific", "type": "double", "source": ["NVD"], "derived_field": false, "range_selection": true, "min": 0.0, "max": 10.0, "solutions": ["EI", "Vulnerability"], "step_interval": 0.1, "width": 10}, "v30_impact_score": {"caption": "CVSSv3.0 Impact Score", "description": "A metric that quantifies the potential consequences or impact of a security vulnerability if successfully exploited by an attacker.", "examples": ["10.0", "7.0"], "group": "entity_specific", "type": "double", "source": ["NVD"], "derived_field": false, "range_selection": true, "min": 0.0, "max": 10.0, "solutions": ["EI", "Vulnerability"], "step_interval": 0.1, "width": 10}, "v31_score": {"caption": "CVSSv3.1 Score", "description": "Common Vulnerability Scoring System, which gives the severity of a vulnerability.", "examples": "2.0", "group": "entity_specific", "type": "double", "source": ["NVD"], "derived_field": false, "range_selection": true, "min": 0.0, "max": 10.0, "solutions": ["EI", "Vulnerability"], "step_interval": 0.1, "width": 10}, "v31_vector": {"caption": "CVSSv3.1 Vector", "description": "Compressed textual representation of the values used to derive the CVSS Score.", "examples": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:C", "group": "entity_specific", "type": "string", "source": ["NVD"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "v31_severity": {"caption": "CVSSv3.1 Severity", "description": "Severity of the vulnerability.", "examples": ["Critical", "High", "Medium", "Low"], "group": "entity_specific", "type": "string", "source": ["NVD", "MS Defender"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "v31_exploitability": {"caption": "CVSSv3.1 Exploitability", "description": "A metric that measures the ease or difficulty with which a security vulnerability can be exploited by an attacker.", "examples": "1.1", "group": "entity_specific", "type": "double", "source": ["NVD"], "derived_field": false, "range_selection": true, "min": 0.0, "max": 10.0, "solutions": ["EI", "Vulnerability"], "step_interval": 0.1, "width": 10}, "v31_impact_score": {"caption": "CVSSv3.1 Impact Score", "description": "A metric that quantifies the potential consequences or impact of a security vulnerability if successfully exploited by an attacker.", "examples": "5,8", "group": "entity_specific", "type": "double", "source": ["NVD"], "derived_field": false, "range_selection": true, "min": 0.0, "max": 10.0, "solutions": ["EI", "Vulnerability"], "step_interval": 0.1, "width": 10}, "software_list": {"caption": "Software List", "description": "Softwares affected by Vulnerability.", "examples": ["product:dns_server,vendor:cisco"], "group": "entity_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "candidate_key": false, "ui_visibility": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "patch_available": {"caption": "Patch Available", "description": "Indicating whether vulnerability is patchable or not.", "examples": ["true", "false"], "group": "entity_specific", "type": "string", "source": ["Qualys", "MS Defender"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "exploit_available": {"caption": "Exploit Available", "description": "Indicating whether an exploit is available or not.", "examples": ["true", "false"], "group": "entity_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "recommendation": {"caption": "Recommendation", "description": "Recommendation to remediate the vulnerability.", "examples": "Uninstall JDK V3", "group": "entity_specific", "type": "string", "source": ["Qualys", "MS Defender"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "ms_recommended_update": {"caption": "Microsoft Recommended Update", "description": "Recommended update of the vulnerability.", "examples": "Uninstall JDK V3", "group": "entity_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "ms_recommended_update_id": {"caption": "Microsoft Recommended Update ID", "description": "Recommended update ID of the vulnerability.", "examples": "Uninstall JDK V3", "group": "entity_specific", "type": "string", "source": ["MS Defender"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "published_date": {"caption": "Published On", "description": "Date when the vulnerability was published.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["Qualys", "NVD"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 15}, "last_modified_date": {"caption": "Last Modified", "description": "Date when the vulnerability was last updated.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["Qualys", "NVD"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 15}, "cisa_exploit_add_date": {"caption": "CISA Exploit Addition", "description": "Date that the exploit was added to CISA KEV.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["NVD"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 15}, "cisa_action_due_date": {"caption": "CISA Action Due", "description": "Date that CISA say an action should be completed by.", "examples": "*************", "group": "entity_specific", "type": "timestamp", "source": ["NVD"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 15}, "cisa_required_action": {"caption": "CISA Required Action", "description": "Action reported in CISA KEV.", "examples": "Apply updates per vendor instructions", "group": "entity_specific", "type": "string", "source": ["NVD"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "epss": {"caption": "EPSS", "description": "Exploit Prediction Scoring System.", "examples": "0.00885", "group": "entity_specific", "type": "double", "source": ["Qualys"], "derived_field": false, "range_selection": true, "ui_visibility": false, "solutions": ["EI", "Vulnerability"], "step_interval": 0.1, "width": 10}, "epss_percentile": {"caption": "EPSS Percentile", "description": "Percentile of the EPSS score.", "examples": "0.274", "group": "entity_specific", "type": "double", "source": ["EPSS"], "derived_field": false, "range_selection": true, "step_interval": 0.01, "solutions": ["EI", "Vulnerability"], "width": 10}, "cwe": {"caption": "CWE", "description": "Common Weakness Enumeration for the Vulnerability.", "examples": "CWE-427", "group": "entity_specific", "type": "string", "source": ["NVD"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Vulnerability"], "width": 45}, "cpe": {"caption": "CPE", "description": "The Common Platform Enumeration (CPE) identifier for the software affected by the vulnerability.", "examples": "", "group": "entity_specific", "type": "string", "source": ["NVD"], "derived_field": false, "data_structure": "list", "ui_visibility": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "normalized_severity": {"caption": "Severity", "description": "This field is a coalesce of  CVSS V31 Severity, CVSS V30 Severity and CVSS V2 severity respectively.", "examples": ["HIGH"], "group": "entity_specific", "type": "string", "source": ["Qualys", "MS Defender"], "derived_field": false, "ui_visibility": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "vendor_severity": {"caption": "<PERSON><PERSON><PERSON>", "description": "Severity assigned by <PERSON><PERSON><PERSON> to the vulnerabilities found on the host.", "examples": ["7", "HIGH"], "group": "entity_specific", "type": "string", "source": ["Qualys", "MS Defender"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "vulnerability_first_observed_date": {"caption": "Vulnerability First Observed", "description": "Time at which the vulnerability was first observed on a host.", "group": "entity_specific", "examples": "*************", "type": "timestamp", "source": ["Qualys ", "MS Defender"], "solutions": ["EI", "Vulnerability"], "width": 15}, "vendor_id": {"caption": "Vendor ID", "description": "ID given by each vendor to the vulnerabilities found in the host.", "examples": "53588336", "group": "source_specific", "type": "string", "source": ["Qualys", "MS Defender"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Vulnerability"], "width": 45}, "found_in_organisation": {"caption": "Found In Organisation", "description": "To filter the vulnerabilities found in the organisation.", "examples": "False", "group": "source_specific", "ui_visibility": false, "type": "string", "source": ["Qualys", "MS Defender", "NVD", "EPSS"], "derived_field": true, "solutions": ["EI", "Vulnerability"], "width": 45}, "qualys_pci_flag": {"caption": "Qualys PCI Flag", "description": "A security status indicator used by the Qualys vulnerability management and compliance platform to help organisations achieve and maintain compliance with the Payment Card Industry Data Security Standard (PCI DSS).", "examples": "True", "group": "source_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "qualys_consequence": {"caption": "Qualys Consequence", "description": "Consequence details after exploiting the vulnerabiltiy.", "examples": "CGI", "group": "source_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "qualys_category": {"caption": "Qualys Category", "description": "Category the vulnerability is assigned to.", "examples": "By exploiting this vulnerability, the user gains access to computer.", "group": "source_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}, "qualys_threat_intel": {"caption": "<PERSON><PERSON>ys Threat <PERSON> ", "description": "Threat Intels reported by Qualys.", "examples": "ID:VALUE, 12 : Predicted High risk, 6:High Data Loss ", "group": "source_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Vulnerability"], "width": 45}, "bugtraq_id": {"caption": "Bugtraq ID", "description": "A unique identifier assigned to a security vulnerability that is documented and listed in the Bugtraq mailing list, which is a public forum for discussing computer security vulnerabilities and related topics. ", "examples": "1000", "group": "source_specific", "type": "string", "source": ["Qualys"], "derived_field": false, "data_structure": "list", "solutions": ["EI", "Vulnerability"], "width": 45}, "nvd_status": {"caption": "NVD Status", "description": "Status of vulnerability in the NVD Catalogue.", "examples": "Analyzed", "group": "source_specific", "type": "string", "source": ["NVD"], "derived_field": false, "solutions": ["EI", "Vulnerability"], "width": 45}}}}}