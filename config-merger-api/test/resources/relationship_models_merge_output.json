{"data": {"name": "Vulnerability Finding On Cloud Compute and Container", "origin": "Qualys Host Vulnerability", "inverseRelationshipName": "Cloud Compute and Container Has Vulnerability Finding", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "interSourcePath": "<%KG_FRAGMENT_SCHEMA%>.sds_ei__resolver__vulnerability", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.qualys__host_vulnerability", "origin": "Qualys", "sourceLoaderConfPath": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__vulnerability__qualys_host_vulnerability__qid"], "targetLoaderConfPath": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_compute_and_container__qualys_host_vulnerability__host_id"], "enrichments": [{"lookupInfo": {"tableName": "<%LOOKUP_SCHEMA_NAME%>.qualys__host_vulnerability_software", "enrichmentColumns": ["software_version", "software_product", "software_vendor"]}, "joinCondition": "s.qid = e.qid AND e.host_id = s.host_id AND s.results = e.results"}]}], "optionalAttributes": [{"name": "relationship_first_seen_date", "exp": "first_found_datetime_epoch", "occurrence": "FIRST"}, {"name": "relationship_last_seen_date", "exp": "UNIX_MILLIS(TIMESTAMP(to_timestamp(last_found_datetime)))", "occurrence": "LAST"}, {"name": "last_reopened_date", "exp": "greatest(UNIX_MILLIS(TIMESTAMP(to_timestamp(first_reopened_datetime))),UNIX_MILLIS(TIMESTAMP(to_timestamp(cast(last_reopened_datetime_epoch as bigint)))))", "occurrence": "LAST"}, {"name": "initial_status", "exp": "'Open'", "occurrence": "FIRST"}, {"name": "current_status", "exp": "CASE WHEN lower(status)='fixed' OR (datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(UNIX_MILLIS(TIMESTAMP(to_timestamp(last_found_datetime)))/1000))) > 180  )  THEN 'Closed' ELSE 'Open' END", "occurrence": "LAST"}, {"name": "vendor_status", "exp": "status", "occurrence": "LAST"}, {"name": "qualys_vulnerability_id", "exp": "qid", "occurrence": "COLLECT"}, {"name": "qualys_host_id", "exp": "host_id", "occurrence": "COLLECT"}, {"name": "software_vendor", "exp": "software_vendor", "occurrence": "LAST"}, {"name": "software_name", "exp": "software_product", "occurrence": "LAST"}, {"name": "software_version", "exp": "software_version", "occurrence": "LAST"}, {"name": "inactivity_period", "exp": "180", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id", "first_found_datetime_epoch", "software_vendor", "software_product", "software_version"]}, "interTargetPath": "<%KG_FRAGMENT_SCHEMA%>.sds_ei__resolver__cloud_compute_and_container"}}