{"client_configs": [{"config_item_type": "ui_config", "config_item_level": "client", "config_deploy_type": "orchestration_shared_fs", "entity_name": "host", "solution_name": "ei", "data_source_feed_name": "ad_device", "data_source_name": "microsoft_azure", "config_value": {"string": {"width": 45}}}], "solution_configs": [{"config_item_type": "ui_config", "config_item_level": "solution", "config_deploy_type": "orchestration_shared_fs", "entity_name": "host", "solution_name": "ei", "data_source_feed_name": "ad_device", "data_source_name": "microsoft_azure", "config_value": {"string": {"width": 40}, "integer": {"width": 10, "step_interval": 1}, "timestamp": {"width": 15}, "double": {"width": 10, "step_interval": 0.1}}}]}