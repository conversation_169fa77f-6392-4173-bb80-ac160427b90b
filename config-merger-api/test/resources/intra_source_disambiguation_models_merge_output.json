{"data": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad_devices__device_id", "name": "ms_azure_ad_devices__device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad_registered_users__id", "name": "sds_ei__host__ms_azure_ad_registered_users__id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["ms_azure_ad_devices__device_id", "sds_ei__host__ms_azure_ad_registered_users__id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": ["cloud_resource_id", "aad_device_id", "host_name", "fqdn", "hardware_serial_number"]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Azure AD'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "isFragmentOLAPTable": false}, "entityConfig": {"fieldSpec": {"persistNonNullValue": true}, "entityClass": "Host", "lastUpdateFields": ["type", "first_seen_date", "business_unit", "department", "description", "location", "host_name", "fqdn", "ip", "netbios", "accessibility", "os", "cloud_provider", "cloud_resource_id", "hardware_model", "hardware_serial_number", "login_last_date", "login_last_user", "edr_onboarding_status", "vm_onboarding_status", "mdm_product", "mdm_status", "mdm_last_sync_date", "edr_last_scan_date", "vm_last_scan_date", "ad_distinguished_name", "ad_last_sync_date", "ad_operational_status", "aad_device_id", "aad_operational_status", "azure_operational_status"]}, "commonProperties": [{"colName": "display_label", "colExpr": "coalesce(fqdn[0],dns_name,host_name[0],aad_device_id[0],primary_key__resolved)"}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period  THEN 'Inactive' ELSE 'Active' END"}, {"colName": "business_unit"}]}}