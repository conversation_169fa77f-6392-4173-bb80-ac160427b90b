{"client_configs": [{"id": 9, "name": "sds_ei__rel__host_has_identity", "config_item_type": "relationship_disambiguation", "config_item_level": "client", "config_deploy_type": "spark_job_config", "config_value": {"relationshipModels": [{"tableName": "ei_cloud_finalv8.sds_ei__rel__tenable_sc__vulnerability_finding_on_host", "name": "sds_ei__rel__tenable_sc__vulnerability_finding_on_host"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__qualys_host_vulnerability__vulnerability_finding_on_cloud_compute_and_container", "name": "qualys"}], "disambiguation": {"disambiguationGrouping": {"type": "VulnerabilityFinding", "blockVariables": ["source_p_id", "target_p_id", "software_vendor", "software_version"], "statusField": "current"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin", "vendor_status", "qualys_vulnerability_id", "qualys_host_id", "ms_recommended_update", "ms_recommended_update_id"], "aggregation": [{"field": "last_reopened_date", "function": "max"}]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_cloud_compute_and_container"}}, "version": "1.1", "deployed": false, "edit_lock": false}], "solution_configs": [{"id": 8, "name": "sds_ei__rel__host_has_identity", "config_item_type": "relationship_disambiguation", "config_item_level": "solution", "config_deploy_type": "spark_job_config", "config_value": {"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender__vulnerability_finding_on_cloud_compute_and_container", "name": "sds_ei__rel__ms_defender__vulnerability_finding_on_cloud_compute_and_container"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__qualys_host_vulnerability__vulnerability_finding_on_cloud_compute_and_container", "name": "sds_ei__rel__qualys_host_vulnerability__vulnerability_finding_on_cloud_compute_and_container"}], "disambiguation": {"disambiguationGrouping": {"type": "VulnerabilityFinding", "blockVariables": ["source_p_id", "target_p_id", "software_name", "software_vendor", "software_version"], "statusField": "current_status"}, "strategy": {"rollingUpFields": ["origin", "vendor_status", "qualys_vulnerability_id", "qualys_host_id", "ms_recommended_update", "ms_recommended_update_id"], "aggregation": [{"field": "relationship_origin", "function": "max"}]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_cloud_compute_and_container"}}, "version": "1.1", "deployed": false, "edit_lock": false}]}