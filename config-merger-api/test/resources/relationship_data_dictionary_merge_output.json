{"data": {"Person": {"PERSON_OWNS_HOST": {"name": "Person Owns Host", "caption": "Owns Host", "isInverse": false, "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string", "width": 45}, "os": {"caption": "OS", "description": "Operating System details of the host.", "type": "string", "width": 45}, "type": {"caption": "Type", "description": "The specific type of the entity.", "type": "string", "width": 45}}, "relationship_attributes": {"start_epoch": {"caption": "First Seen", "description": "Time at which the person started using the host.", "type": "timestamp", "solutions": ["EI", "Person"], "width": 15}, "end_epoch": {"caption": "Last Seen", "description": "Latest time at which the person to host relation is inferred from data.", "type": "timestamp", "solutions": ["EI", "Person"], "width": 15}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Person"], "width": 45}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Person"], "width": 45}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the person owns the host.", "type": "integer", "range_selection": true, "solutions": ["EI", "Person"], "step_interval": 1, "width": 10}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true, "solutions": ["EI", "Person"], "step_interval": 1, "width": 10}, "origin_relationship": {"caption": "Relationship Origin", "description": "Sources in which the person to host relation is observed.", "type": "string", "data_structure": "list", "solutions": ["EI", "Person"], "width": 45}}}, "PERSON_HAS_IDENTITY": {"name": "Person Has Identity", "caption": "Has Identity", "isInverse": false, "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string", "width": 45}, "identity_provider": {"caption": "Identity Provider", "description": "Identity Provider of the identity.", "type": "string", "width": 45}, "type": {"caption": "Identity Type", "description": "Type of identity like User Principal Name, Azure ID, etc.", "type": "string", "width": 45}, "account_type": {"caption": "Account Type", "description": "The type of account like Service Account, Group Account etc.", "type": "string", "detailed_view_hide": true, "width": 45}}, "relationship_attributes": {"start_epoch": {"caption": "First Seen", "description": "Time at which the identity was first observed for the person.", "type": "timestamp", "solutions": ["EI", "Person"], "width": 15}, "end_epoch": {"caption": "Last Seen", "description": "Time at which the identity was last observed for the person.", "type": "timestamp", "solutions": ["EI", "Person"], "width": 15}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Person"], "width": 45}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Person"], "width": 45}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the identity was seen for the person.", "type": "integer", "range_selection": true, "solutions": ["EI", "Person"], "step_interval": 1, "width": 10}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true, "solutions": ["EI", "Person"], "step_interval": 1, "width": 10}, "origin_relationship": {"caption": "Relationship Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list", "solutions": ["EI", "Person"], "width": 45}}}}, "Host": {"HOST_OWNED_BY_PERSON": {"name": "Host Owned By Person", "caption": "Owned By Person", "isInverse": true, "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string", "width": 45}, "job_title": {"caption": "Job Title", "description": "Job title of the person.", "type": "string", "width": 45}, "aad_operational_status": {"caption": "Status", "description": "The current operational state.", "type": "string", "width": 45}}, "relationship_attributes": {"start_epoch": {"caption": "First Seen", "description": "Time at which the person started using the host.", "type": "timestamp", "solutions": ["EI", "Host"], "width": 15}, "end_epoch": {"caption": "Last Seen", "description": "Latest time at which the person to host relation is observed in data.", "type": "timestamp", "solutions": ["EI", "Host"], "width": 15}, "source_display_label": {"caption": "Target Display Label", "description": "Name of target entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Host"], "width": 45}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Host"], "width": 45}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the host is owned by the person.", "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "origin_relationship": {"caption": "Relationship Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}}}, "HOST_HAS_VULNERABILITY_FINDING": {"name": "Host Has Vulnerability Finding", "caption": "Has Vulnerability Findings", "detail_view_caption": "Has Open Vulnerability Findings", "isInverse": true, "relationship_attributes": {"initial_status": {"caption": "Initial Status", "description": "Initial Status of the vulnerability on host.", "type": "string", "solutions": ["EI", "Host"], "width": 45}, "current_status": {"caption": "Current Status", "description": "Current Status of the vulnerability on host.", "type": "string", "solutions": ["EI", "Host"], "width": 45}, "start_epoch": {"caption": "First Seen", "description": "Time at which the vulnerability was first seen on host as inferred from data source.", "type": "timestamp", "solutions": ["EI", "Host"], "width": 15}, "end_epoch": {"caption": "Last Seen", "description": "Time at which the vulnerability was last seen on host as inferred from data source.", "type": "timestamp", "solutions": ["EI", "Host"], "width": 15}, "lifetime_relationship": {"caption": "Duration", "description": "How long the vulnerability was present on the host.", "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the vulnerability last observed on the host.", "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "software_name": {"caption": "Software Name", "description": "Software Name.", "type": "string", "solutions": ["EI", "Host"], "width": 45}, "software_vendor": {"caption": "Software Vendor", "description": "Sotware Vendor.", "type": "string", "solutions": ["EI", "Host"], "width": 45}, "software_version": {"caption": "Software Version", "description": "Sotware Version.", "type": "string", "solutions": ["EI", "Host"], "width": 45}, "source_display_label": {"caption": "Target Display Label", "description": "Name of target entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Host"], "width": 45}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Host"], "width": 45}, "origin_relationship": {"caption": "Relationship Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}, "contextual_cvss_score": {"caption": "Contextual CVSS Score", "description": "Recalculated CVSS score obtained by reassessing the impact of the consequences after updation of the CVSS vector strings.", "value": "contextual_cvss_score", "type": "double", "range_selection": true, "min": 0, "max": 10, "step_interval": 0.1, "solutions": ["EI", "Host"], "width": 10}, "vulnerability_risk_score": {"caption": "Vulnerability Risk Score", "description": "Score between 0 and 100 for each vulnerability, that we can use to rank vulnerabilities.", "value": "vulnerability_risk_score", "type": "double", "range_selection": true, "min": 0, "max": 100, "step_interval": 0.01, "solutions": ["EI", "Host"], "width": 10}, "software_full_name": {"caption": "Software Full Name", "description": "Concatenated form of Software Name and Software Vendor", "value": "software_full_name", "type": "string", "solutions": ["EI", "Host"], "width": 45}, "sla_duration": {"caption": "Finding Age", "description": "Duration in days between cve first seen and cve last seen on a host", "value": "sla_duration", "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "previous_day_status": {"caption": "Previous Day Status", "description": "Previous day status of the vulnerability on a host", "value": "previous_day_status", "type": "string", "solutions": ["EI", "Host"], "width": 45}, "sla_flag": {"caption": "Breaching SLA", "description": "Flag that determines whether the vulnerabilities are overdue or not as per Severity and the days taken to get patched.", "value": "sla_flag", "type": "integer", "solutions": ["EI", "Host"], "width": 10}}, "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string", "width": 45}, "vendor_severity": {"caption": "<PERSON><PERSON><PERSON>", "description": "Severity of the vulnerability.", "type": "string", "width": 45}}}, "HOST_HAS_IDENTITY": {"name": "Host has Identity", "caption": "Has Identity", "isInverse": false, "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string", "width": 45}, "identity_provider": {"caption": "Identity Provider", "description": "An Identity Provider (IDP) is a system that manages digital identities and provides authentication services for users.", "type": "string", "width": 45}, "type": {"caption": "Identity Type", "description": "The system, service or application, that has an account that is identifed by the username(What is being accessed/logged into).", "type": "string", "width": 45}, "account_type": {"caption": "Account Type", "description": "The type of account being accessed.", "type": "string", "detailed_view_hide": true, "width": 45}}, "relationship_attributes": {"start_epoch": {"caption": "First Seen", "description": "Time at which the identity was first observed on the host.", "type": "timestamp", "solutions": ["EI", "Host"], "width": 15}, "end_epoch": {"caption": "Last Seen", "description": "Time at which the identity was last observed on the host.", "type": "timestamp", "solutions": ["EI", "Host"], "width": 15}, "source_display_label": {"caption": "Source Display Label", "description": "Name of source entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Host"], "width": 45}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Host"], "width": 45}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the identity was observed on the host.", "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true, "solutions": ["EI", "Host"], "step_interval": 1, "width": 10}, "origin_relationship": {"caption": "Relationship Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list", "solutions": ["EI", "Host"], "width": 45}}}}, "Vulnerability": {"VULNERABILITY_FINDING_ON_HOST": {"name": "Vulnerability Finding On Host", "caption": "Findings on Host", "detail_view_caption": "Open Vulnerability Findings on Host", "isInverse": false, "relationship_attributes": {"start_epoch": {"caption": "First Seen", "description": "Time at which the vulnerability was first seen on host as inferred from data source.", "type": "timestamp", "solutions": ["EI", "Vulnerability"], "width": 15}, "end_epoch": {"caption": "Last Seen", "description": "Time at which the vulnerability was last seen on host as inferred from data source.", "type": "timestamp", "solutions": ["EI", "Vulnerability"], "width": 15}, "lifetime_relationship": {"caption": "Duration", "description": "How long the vulnerability was present on the host.", "type": "integer", "range_selection": true, "solutions": ["EI", "Vulnerability"], "step_interval": 1, "width": 10}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the vulnerability last observed on the host.", "type": "integer", "range_selection": true, "solutions": ["EI", "Vulnerability"], "step_interval": 1, "width": 10}, "initial_status": {"caption": "Initial Status", "description": "Initial Status of the vulnerability on host.", "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "current_status": {"caption": "Current Status", "description": "Current Status of the vulnerability on host.", "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "origin_relationship": {"caption": "Relationship Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list", "solutions": ["EI", "Vulnerability"], "width": 45}, "software_name": {"caption": "Software Name", "description": "Sotware name.", "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "software_version": {"caption": "Software Version", "description": "Sotware Version.", "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "software_vendor": {"caption": "Software Vendor", "description": "Sotware Vendor.", "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Vulnerability"], "width": 45}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Vulnerability"], "width": 45}, "contextual_cvss_score": {"caption": "Contextual CVSS Score", "description": "Recalculated CVSS score obtained by reassessing the impact of the consequences after updation of the CVSS vector strings.", "value": "contextual_cvss_score", "type": "double", "range_selection": true, "min": 0, "max": 10, "step_interval": 0.1, "solutions": ["EI", "Vulnerability"], "width": 10}, "vulnerability_risk_score": {"caption": "Vulnerability Risk Score", "description": "Score between 0 and 100 for each vulnerability, that we can use to rank vulnerabilities.", "value": "vulnerability_risk_score", "type": "double", "range_selection": true, "min": 0, "max": 100, "step_interval": 0.01, "solutions": ["EI", "Vulnerability"], "width": 10}, "software_full_name": {"caption": "Software Full Name", "description": "Concatenated form of Software Name and Software Vendor", "value": "software_full_name", "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "sla_duration": {"caption": "Finding Age", "description": "Duration in days between cve first seen and cve last seen on a host.", "value": "sla_duration", "type": "integer", "range_selection": true, "solutions": ["EI", "Vulnerability"], "step_interval": 1, "width": 10}, "previous_day_status": {"caption": "Previous Day Status", "description": "Previous day status of the vulnerability on a host.", "value": "previous_day_status", "type": "string", "solutions": ["EI", "Vulnerability"], "width": 45}, "sla_flag": {"caption": "Breaching SLA", "description": "Flag that determines whether the vulnerabilities are overdue or not as per Severity and the days taken to get patched.", "value": "sla_flag", "type": "integer", "solutions": ["EI", "Vulnerability"], "width": 10}}, "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string", "width": 45}, "type": {"caption": "Type", "description": "The specific type of the entity.", "type": "string", "width": 45}, "os": {"caption": "OS", "description": "The Operating System Details of the host.", "type": "string", "width": 45}}}}, "Identity": {"IDENTITY_ASSOCIATED_WITH_PERSON": {"name": "Identity Associated With Person", "caption": "Associated With Person", "isInverse": true, "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string", "width": 45}, "job_title": {"caption": "Job Title", "description": "The Designation details of the employee.", "type": "string", "width": 45}, "status": {"caption": "Status", "description": "Status of account.", "type": "string", "width": 45}}, "relationship_attributes": {"start_epoch": {"caption": "First Seen", "description": "Time at which the person was first observed with the identity", "type": "timestamp", "solutions": ["EI", "Identity"], "width": 15}, "end_epoch": {"caption": "Last Seen", "description": "Time at which the person was last observed with the identity.", "type": "timestamp", "solutions": ["EI", "Identity"], "width": 15}, "source_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Identity"], "width": 45}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Identity"], "width": 45}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which person was observed with the identity.", "type": "integer", "range_selection": true, "solutions": ["EI", "Identity"], "step_interval": 1, "width": 10}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true, "solutions": ["EI", "Identity"], "step_interval": 1, "width": 10}, "origin_relationship": {"caption": "Relationship Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list", "solutions": ["EI", "Identity"], "width": 45}}}, "IDENTITY_ASSOCIATED_WITH_HOST": {"name": "Identity Associated With Host", "caption": "Associated With Host", "isInverse": true, "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string", "width": 45}, "os": {"caption": "OS", "description": "The Operating System of the Host.", "type": "string", "width": 45}, "type": {"caption": "Host Type", "description": "Sub classification of Host.", "type": "string", "width": 45}}, "relationship_attributes": {"start_epoch": {"caption": "First Seen", "description": "Time at which the host was first observed with the identity.", "type": "timestamp", "solutions": ["EI", "Identity"], "width": 15}, "end_epoch": {"caption": "Last Seen", "description": "Time at which the host was last observed with the identity", "type": "timestamp", "solutions": ["EI", "Identity"], "width": 15}, "source_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Identity"], "width": 45}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true, "solutions": ["EI", "Identity"], "width": 45}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the host was observed with the identity.", "type": "integer", "range_selection": true, "solutions": ["EI", "Identity"], "step_interval": 1, "width": 10}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true, "solutions": ["EI", "Identity"], "step_interval": 1, "width": 10}, "origin_relationship": {"caption": "Relationship Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list", "solutions": ["EI", "Identity"], "width": 45}}}}}}