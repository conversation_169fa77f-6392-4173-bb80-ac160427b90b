{"data": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_intunes__device_id", "name": "sds_ei__host__ms_intunes"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys", "name": "sds_ei__host__qualys"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__globalprotect_vpn", "name": "sds_ei__host__globalprotect_vpn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__winevent", "name": "sds_ei__host__winevents"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_resource_details__arn", "name": "sds_ei__host__aws_resource_details__arn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad", "name": "sds_ei__host__ms_azure_ad"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure", "name": "sds_ei__host__ms_azure"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender", "name": "sds_ei__host__ms_defender"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__winevents", "name": "sds_ei__host__winevents"}], "disambiguation": {"candidateKeys": ["fqdn", {"name": "aad_device_ids", "exceptionFilter": "aad_device_id = '00000000-0000-0000-0000-000000000000'"}, {"name": "host_name", "exceptionFilter": "(lower(host_name) LIKE '%iphone%' OR lower(host_name) LIKE '%android%' OR lower(host_name) LIKE '%ipad%' OR lower(host_name) LIKE '%macbook%' OR host_name RLIKE '(?i)pro([^a-zA-Z0-9]|$)' OR lower(host_name) LIKE '%galaxy%' OR lower(host_name) LIKE '%samsung%') OR lower(host_name) IN ('wrk','user deleted for this device') OR (lower(os) LIKE '%android%' OR lower(os) LIKE '%appleios%' OR lower(os) LIKE '%tizen%')"}, "hardware_serial_number", "cloud_resource_id"], "confidenceMatrix": ["sds_ei__host__ms_defender", "sds_ei__host__ms_intunes", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__aws_resource_details__arn", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "type", "confidenceMatrix": ["sds_ei__host__ms_intunes", "sds_ei__host__ms_defender", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__aws_resource_details__arn", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure"]}], "rollingUpFields": ["defender_action_type", "defender_detection_method", "defender_id", "defender_threat_name", "ip", "last_login_date", "origin", "qualys_asset_id", "qualys_detection_method", "qualys_id", "vm_product", "vm_tracking_method", "win_event_id"], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "mdm_enrolled_date", "function": "min"}, {"field": "vm_last_scan_date", "function": "max"}, {"field": "ad_last_sync_date", "function": "max"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "azure_created_date", "function": "min"}, {"field": "ad_account_disabled_date", "function": "max"}, {"field": "defender_onboarding_date", "function": "min"}, {"field": "vulnerability_last_observed_date", "function": "max"}, {"field": "login_last_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "av_status", "confidenceMatrix": ["false", "true"]}, {"field": "av_block_malicious_code_status", "confidenceMatrix": ["false", "true"]}, {"field": "fw_status", "confidenceMatrix": ["false", "true"]}, {"field": "vm_onboarding_status", "confidenceMatrix": ["true", "false"]}]}}, "derivedProperties": [{"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)>=0 THEN size(defender_threat_name) END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_inter_source_resolver"}, "entityConfig": {"fieldSpec": {"persistNonNullValue": true}, "entityClass": "Host", "lastUpdateFields": ["type", "first_seen_date", "business_unit", "department", "description", "location", "host_name", "fqdn", "ip", "netbios", "accessibility", "os", "cloud_provider", "cloud_resource_id", "hardware_model", "hardware_serial_number", "login_last_date", "login_last_user", "edr_onboarding_status", "vm_onboarding_status", "mdm_product", "mdm_status", "mdm_last_sync_date", "edr_last_scan_date", "vm_last_scan_date", "ad_distinguished_name", "ad_last_sync_date", "ad_operational_status", "aad_device_id", "aad_operational_status", "azure_operational_status"]}, "commonProperties": [{"colName": "display_label", "colExpr": "coalesce(fqdn__resolved,dns_name,host_name__resolved,aad_device_id,primary_key)"}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period  THEN 'Inactive' ELSE 'Active' END"}, {"colName": "business_unit"}]}}