{"client_configs": [{"id": 62266, "name": "entity_rel_enrich", "config_item_type": "entity_rel_enrich", "config_item_level": "client", "config_deploy_type": "spark_job_configs", "config_value": {"countEnriches": [{"sourceFieldName": "has_identity_count", "targetFieldName": "associated_host_count", "relationshipName": "Host as Identity"}], "derivedProperties": [{"colExpr": "cast(case when class='Account' and (array_contains(entitlement_value,'Schema Admins') or array_contains(entitlement_value,'Administrators') or array_contains(entitlement_value,'Remote Desktop Users') or array_contains(entitlement_value,'Domain Admins') or array_contains(entitlement_value,'Enterprise Admins') or array_contains(entitlement_value,'Global Administrator') or  array_contains(entitlement_value,'Application Administrator') or  array_contains(entitlement_value,'Attribute Assignment Administrator') or  array_contains(entitlement_value,'Attribute Definition Administrator') or array_contains(entitlement_value,'Azure AD Joined Device Local Administrator') or  array_contains(entitlement_value,'Billing Administrator') or array_contains(entitlement_value,'Conditional Access Administrator') or array_contains(entitlement_value,'Helpdesk Administrator') or array_contains(entitlement_value,'Intune Administrator') or array_contains(entitlement_value,'License Administrator') or array_contains(entitlement_value,'Privileged Authentication Administrator') or array_contains(entitlement_value,'Privileged Role Administrator') or array_contains(entitlement_value,'Security Administrator') or array_contains(entitlement_value,'Service Support Administrator') or array_contains(entitlement_value,'User Administrator') or array_contains(entitlement_value,'Account Operators') or array_contains(entitlement_value,'Backup Operators') or array_contains(entitlement_value,'Cert Publishers') or array_contains(entitlement_value,'Server Operators')) then 3 when class='Account' and service in ('Azure AD','Active Directory') then 0.2 else 0.5 end as double)", "colName": "role_score"}, {"colExpr": "cast(case when class='Account' then COALESCE(privilege_score, 0) + COALESCE(activity_score, 0) + COALESCE(role_score, 0) + COALESCE(idle_score, 0) else null end as double)", "colName": "account_inherent_score"}, {"colExpr": "cast(case when employee_level='L1' then 1*0.2 when employee_level='L2' then 2*0.2 when employee_level='L3' then 3*0.2 when employee_level='L4' then 4*0.2 when employee_level='L5' then 5*0.2 when employee_level='L6' then 6*0.2 when employee_level='L7' then 7*0.2 when employee_level='L8' then 8*0.2 else 0.2 end as double)", "colName": "emp_level_score"}, {"colExpr": "cast(case when employment_type='Permanent' then 1 when employment_type='Contractor' then 3 when employment_type='Intern' then 2 else 0.2 end as double)", "colName": "emp_type_score"}, {"colExpr": "cast(case when class='Person' then COALESCE(emp_level_score, 0) + COALESCE(emp_type_score, 0) + COALESCE(identity_count_score, 0) + COALESCE(emp_status_score, 0) else null end as double)", "colName": "person_inherent_score"}, {"colExpr": "cast(case when class='Host' and lower(domain) like '%prevalent%' then 0.2 when class='Host' and lower(domain) not like '%prevalent%' then 3 else null end as double)", "colName": "domain_score"}, {"colExpr": "cast(case when class='Host' and lower(fqdn__resolved) in ('dc.prevalent.com','dc2.prevalent.com','dc3.prevalent.com') then 3 else 0.2 end as double)", "colName": "sensitive_role_host_score"}, {"colExpr": "cast(case when class='Host' then COALESCE(device_type_score, 0) + COALESCE(domain_score, 0) + COALESCE(sensitive_role_host_score, 0) + COALESCE(posture_score, 0) else null end as double)", "colName": "host_inherent_score"}]}, "deployed_version_available": true, "draft_version_available": false}], "solution_configs": [{"id": 61594, "name": "entity_rel_enrich", "config_item_type": "entity_rel_enrich", "config_item_level": "solution", "config_deploy_type": "spark_job_configs", "config_value": {"countEnriches": [{"sourceFieldName": "has_identity_count", "targetFieldName": "associated_host_count", "relationshipName": "Host Has Identity"}, {"sourceFieldName": "has_identity_count", "targetFieldName": "associated_person_count", "relationshipName": "Person Has Identity"}, {"sourceFieldName": "owns_host_count", "targetFieldName": "owned_person_count", "relationshipName": "Person Owns Host"}, {"sourceFieldName": "associated_hosts_with_findings_count", "targetFieldName": "has_vulnerability_finding_count", "relationshipName": "Vulnerability Finding On Host"}, {"filter": "current_status='Open'", "sourceFieldName": "associated_hosts_with_open_findings_count", "targetFieldName": "has_open_vulnerability_finding_count", "relationshipName": "Vulnerability Finding On Host"}, {"sourceFieldName": "associated_cloud_compute_with_findings_count", "targetFieldName": "has_vulnerability_finding_count", "relationshipName": "Vulnerability Finding On Cloud Compute"}, {"sourceFieldName": "associated_cloud_container_with_findings_count", "targetFieldName": "has_vulnerability_finding_count", "relationshipName": "Vulnerability Finding On Cloud Container"}, {"filter": "current_status='Open'", "sourceFieldName": "associated_cloud_compute_with_open_findings_count", "targetFieldName": "has_open_vulnerability_finding_count", "relationshipName": "Vulnerability Finding On Cloud Compute"}, {"filter": "current_status='Open'", "sourceFieldName": "associated_cloud_container_with_open_findings_count", "targetFieldName": "has_open_vulnerability_finding_count", "relationshipName": "Vulnerability Finding On Cloud Container"}, {"sourceFieldName": "associated_kubernetes_cluster_count", "targetFieldName": "compute_instance_group_has_kubernetes_count", "relationshipName": "Compute Instance Group Belongs To Kubernetes Cluster"}, {"sourceFieldName": "associated_mapreduce_cluster_count", "targetFieldName": "compute_instance_group_has_mapreduce_count", "relationshipName": "Compute Instance Group Belongs To MapReduce Cluster"}, {"sourceFieldName": "associated_compute_instance_group_count", "targetFieldName": "virtual_machine_has_compute_instance_group_count", "relationshipName": "Virtual Machine Belongs To Compute Instance Group"}, {"sourceFieldName": "associated_container_group_count", "targetFieldName": "container_has_container_group_count", "relationshipName": "Container Belongs To Container Group"}, {"sourceFieldName": "associated_container_service_count", "targetFieldName": "container_has_container_service_count", "relationshipName": "Container Belongs To Container Service"}, {"sourceFieldName": "associated_virtual_machine_count", "targetFieldName": "container_has_virtual_machine_count", "relationshipName": "Container Belongs To Virtual Machine"}, {"sourceFieldName": "associated_compliance_standard_count", "targetFieldName": "associated_security_control_count", "relationshipName": "Security Control Measuring Compliance Standard"}, {"sourceFieldName": "associated_security_control_count", "targetFieldName": "associated_assessment_count", "relationshipName": "Assessment Measuring Security Control"}, {"sourceFieldName": "associated_assessment_count", "targetFieldName": "associated_resources_count", "relationshipName": "Finding Associated With Assessment"}, {"sourceFieldName": "associated_cloud_account_count", "targetFieldName": "associated_cloud_compute_resource_count", "relationshipName": "Cloud Compute Resource Belongs To Cloud Account"}, {"sourceFieldName": "associated_cloud_account_count", "targetFieldName": "associated_cloud_container_resource_count", "relationshipName": "Cloud Container Resource Belongs To Cloud Account"}, {"sourceFieldName": "associated_cloud_account_count", "targetFieldName": "associated_cloud_storage_resource_count", "relationshipName": "Cloud Storage Resource Belongs To Cloud Account"}, {"sourceFieldName": "associated_virtual_machine_count", "targetFieldName": "associated_volume_count", "relationshipName": "Volume Associates To Virtual Machine"}, {"sourceFieldName": "associated_storage_account_has_bucket_count", "targetFieldName": "associated_bucket_count", "relationshipName": "Bucket Belongs To Storage Account"}, {"sourceFieldName": "associated_storage_account_has_file_system_service_count", "targetFieldName": "associated_file_system_service_count", "relationshipName": "File System Service Belongs To Storage Account"}, {"sourceFieldName": "associated_storage_account_has_queue_service_count", "targetFieldName": "associated_queue_service_count", "relationshipName": "Queue Service Belongs To Storage Account"}, {"sourceFieldName": "associated_storage_account_has_table_service_count", "targetFieldName": "associated_table_service_count", "relationshipName": "Table Service Belongs To Storage Account"}, {"filter": "rel_finding_status='Open'", "sourceFieldName": "associated_cloud_storage_resource_count", "targetFieldName": "associated_open_finding_count", "relationshipName": "Finding On Cloud Storage Resource"}, {"filter": "rel_finding_status='Open'", "sourceFieldName": "associated_cloud_account_count", "targetFieldName": "associated_open_finding_count", "relationshipName": "Finding On Cloud Account"}, {"filter": "rel_finding_status='Open'", "sourceFieldName": "associated_cloud_compute_resource_count", "targetFieldName": "associated_open_finding_count", "relationshipName": "Finding On Cloud Compute Resource"}, {"filter": "rel_finding_status='Open'", "sourceFieldName": "associated_cloud_container_resource_count", "targetFieldName": "associated_open_finding_count", "relationshipName": "Finding On Cloud Container Resource"}, {"sourceFieldName": "associated_cloud_account_count_with_assessment", "targetFieldName": "associated_assessment_count", "relationshipName": "Assessment Associated With Cloud Account"}, {"sourceFieldName": "associated_cloud_account_count_with_control", "targetFieldName": "associated_security_control_count", "relationshipName": "Security Control Associated With Cloud Account"}, {"sourceFieldName": "associated_cloud_account_count_with_standard", "targetFieldName": "associated_compliance_standard_count", "relationshipName": "Compliance Standard Associated With Cloud Account"}, {"sourceFieldName": "running_on_host_count", "targetFieldName": "hosting_application_count", "relationshipName": "Application Running On Host"}, {"sourceFieldName": "has_account_count", "targetFieldName": "associated_identity_count", "relationshipName": "Identity Has Account"}, {"sourceFieldName": "associated_application_with_findings_count", "targetFieldName": "has_vulnerability_finding_count", "relationshipName": "Vulnerability Finding On Application"}, {"filter": "current_status='Open'", "sourceFieldName": "associated_application_with_open_findings_count", "targetFieldName": "has_open_vulnerability_finding_count", "relationshipName": "Vulnerability Finding On Application"}, {"sourceFieldName": "associated_cloud_resource_count", "targetFieldName": "associated_host_count", "relationshipName": "Host Corresponds To Cloud"}], "entityTableName": "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei__publish_transformer__entity_inventory_temp", "outputTableInfo": {"outputTableName": "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei__publish_transformer__entity_inventory", "partitionColumns": ["class"]}, "derivedProperties": [{"colExpr": "CASE WHEN class='Vulnerability' and activity_status IS NOT NULL THEN CASE WHEN greatest(associated_hosts_with_open_findings_count,associated_cloud_compute_with_open_findings_count,associated_cloud_container_with_open_findings_count,associated_application_with_open_findings_count,0) >= 1 THEN 'Active' ELSE 'Inactive' END ELSE activity_status END", "colName": "activity_status"}, {"colExpr": "case when class='Person' then has_identity_count else null end", "colName": "person_has_identity_count"}, {"colExpr": "max(person_has_identity_count) over(partition by (CASE WHEN class='Person' THEN 1 ELSE p_id END))", "colName": "max_person_has_identity_count"}, {"colExpr": "case when class='Account' and recent_activity>30 then recent_activity-30 else null end", "colName": "idle_date"}, {"colExpr": "max(idle_date) over(partition by (CASE WHEN class='Account' THEN 1 ELSE p_id END))", "colName": "max_idle_date"}, {"colExpr": "cast(case when class='Account' and privilege_account='true' then 3 when class='Account' and privilege_account='false' then 0.2 when class='Account' then 0.5 else null end as double)", "colName": "privilege_score"}, {"colExpr": "cast(case when class='Account' and recent_activity>30 then (idle_date/max_idle_date) * 3  when class='Account' then 0.5 else null end as double)", "colName": "idle_score"}, {"colExpr": "cast(case when class='Account' and activity_status='Active' then 0.2 when class='Account' and activity_status='Inactive' then 3 when class='Account' then 0.5 else null end as double)", "colName": "activity_score"}, {"colExpr": "cast(case when class='Account' then COALESCE(privilege_score, 0) + COALESCE(activity_score, 0) + COALESCE(idle_score, 0) else null end as double)", "colName": "account_inherent_score"}, {"colExpr": "cast(case when class='Person' then has_identity_count/max_person_has_identity_count else null end as double)", "colName": "identity_count_score"}, {"colExpr": "1", "colName": "out_of_office_score"}, {"colExpr": "cast(case when class='Person' and employee_status='Active' then 3 when class='Person' and employee_status='Terminated' then 1 else 0.5 end as double)", "colName": "emp_status_score"}, {"colExpr": "cast(case when class='Person' then COALESCE(identity_count_score, 0) + COALESCE(emp_status_score, 0) else null end as double)", "colName": "person_inherent_score"}, {"colExpr": "cast(case when class='Identity' and location_accessed_flag=True then 3 when class='Identity' and location_accessed_flag=False then 0.2 else 1 end as double)", "colName": "multi_login_score"}, {"colExpr": "cast(case when class='Identity' and is_mfa_enabled=True then 0.2 when class='Identity' and is_mfa_enabled=False then 3 else 0.5 end as double)", "colName": "mfa_score"}, {"colExpr": "cast(case when class='Identity' and ownership='External' then 3 when class='Identity' and ownership='Corp' then 1 else null end as double)", "colName": "ownership_score"}, {"colExpr": "1", "colName": "failed_login_score"}, {"colExpr": "cast(case when class='Identity' and password_never_expire=True then 3 when class='Identity' and password_never_expire=False then 0.2 else 1 end as double)", "colName": "password_never_expire_score"}, {"colExpr": "cast(case when class='Identity' and type='Human' then 0.2 when class='Identity' and type='Non-Human' then 3 else null end as double)", "colName": "machine_score"}, {"colExpr": "cast(case when class='Identity' then COALESCE(multi_login_score, 0) + COALESCE(mfa_score, 0) + COALESCE(ownership_score, 0) + COALESCE(password_never_expire_score, 0) + COALESCE(machine_score, 0) else null end as double)", "colName": "identity_inherent_score"}, {"colExpr": "cast(case when class='Host' then case when type='Endpoint' then 1 when type='Server' then 2 when type='Mobile' then 3 else null end else null end as double)", "colName": "device_type_score"}, {"colExpr": "case when last_active_date-av_signature_update_date <= 86400000 then True else False end", "colName": "av_score"}, {"colExpr": "cast(case when class='Host' then case when edr_onboarding_status=True and vm_onboarding_status=True and av_score=True then 0.2 when edr_onboarding_status=True and vm_onboarding_status=True and av_score=False then 2 when edr_onboarding_status=False and vm_onboarding_status=True then 3 when edr_onboarding_status=True and vm_onboarding_status=False and av_score=True then 2 else null end else null end as double)", "colName": "posture_score"}, {"colExpr": "cast(case when class='Host' then COALESCE(device_type_score, 0) + COALESCE(posture_score, 0) else null end as double)", "colName": "host_inherent_score"}, {"colExpr": "cast(case when class='Account' then cast(account_inherent_score as double) when class='Person' then cast(person_inherent_score as double) when class='Identity' then cast(identity_inherent_score as double) when class='Host' then cast(host_inherent_score as double) else null end as double)", "colName": "inherent_score"}], "relationshipTableName": "<%EI_PUBLISH_SCHEMA_NAME%>.sds_ei__publish_transformer__relationship"}, "deployed_version_available": true, "draft_version_available": false}]}