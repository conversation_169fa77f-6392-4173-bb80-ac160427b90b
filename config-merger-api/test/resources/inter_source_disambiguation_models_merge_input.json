{"client_configs": [{"id": 9, "name": "sds_ei__host__ms_defender", "config_item_type": "intrasource_disambiguated_models", "config_item_level": "client", "config_deploy_type": "spark_job_config", "config_value": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid", "name": "device_list"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_software_inventory__device_id", "name": "software"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_tvm__device_id", "name": "tvm"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_events__device_id", "name": "events"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_tvm_software_vulnerabilities__device_id", "name": "vulnerability"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["device_list", "software", "tvm", "events", "vulnerability"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "defender_onboarding_date", "function": "min"}, {"field": "vulnerability_last_observed_date", "function": "max"}], "rollingUpFields": ["defender_threat_name", "defender_action_type"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Actives", "Inactive"]}, {"field": "av_status", "confidenceMatrix": ["false", "true"]}, {"field": "av_block_malicious_code_status", "confidenceMatrix": ["false", "true"]}, {"field": "fw_status", "confidenceMatrix": ["false", "true"]}, {"field": "defender_detection_method", "confidenceMatrix": ["Defender Agent", "Network Scan"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Defender'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}, {"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)>=0 THEN size(defender_threat_name) END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}}, "config_schema": ["test"], "entity_name": "host", "solution_name": "ei", "data_source_feed_name": "active_directory", "data_source_name": "microsoft", "version": "1.1", "deployed": false, "edit_lock": false}, {"id": 8, "name": "sds_ei__host", "config_item_type": "intersource_disambiguated_models", "config_item_level": "client", "config_deploy_type": "spark_job_config", "config_value": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_intunes__device_id", "name": "sds_ei__host__ms_intunes"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys", "name": "sds_ei__host__qualys"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__globalprotect_vpn", "name": "sds_ei__host__globalprotect_vpn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__winevent", "name": "sds_ei__host__winevents"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_resource_details__arn", "name": "sds_ei__host__aws_resource_details__arn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad", "name": "sds_ei__host__ms_azure_ad"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure", "name": "sds_ei__host__ms_azure"}], "disambiguation": {"candidateKeys": ["fqdn", {"name": "aad_device_ids", "exceptionFilter": "aad_device_id = '00000000-0000-0000-0000-000000000000'"}, {"name": "host_name", "exceptionFilter": "(lower(host_name) LIKE '%iphone%' OR lower(host_name) LIKE '%android%' OR lower(host_name) LIKE '%ipad%' OR lower(host_name) LIKE '%macbook%' OR host_name RLIKE '(?i)pro([^a-zA-Z0-9]|$)' OR lower(host_name) LIKE '%galaxy%' OR lower(host_name) LIKE '%samsung%') OR lower(host_name) IN ('wrk','user deleted for this device') OR (lower(os) LIKE '%android%' OR lower(os) LIKE '%appleios%' OR lower(os) LIKE '%tizen%')"}, "hardware_serial_number", "cloud_resource_id"], "confidenceMatrix": ["sds_ei__host__ms_defender", "sds_ei__host__ms_intunes", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__aws_resource_details__arn", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "type", "confidenceMatrix": ["sds_ei__host__ms_intunes", "sds_ei__host__ms_defender", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__aws_resource_details__arn", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure"]}], "rollingUpFields": ["origin", "defender_threat_name", "defender_action_type", "qualys_id", "defender_id", "qualys_asset_id", "qualys_detection_method", "vm_tracking_method", "vm_product", "defender_detection_method", "win_event_id", "ip", "last_login_date"], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "mdm_enrolled_date", "function": "min"}, {"field": "vm_last_scan_date", "function": "max"}, {"field": "ad_last_sync_date", "function": "max"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "azure_created_date", "function": "min"}, {"field": "ad_account_disabled_date", "function": "max"}, {"field": "defender_onboarding_date", "function": "min"}, {"field": "vulnerability_last_observed_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "av_status", "confidenceMatrix": ["false", "true"]}, {"field": "av_block_malicious_code_status", "confidenceMatrix": ["false", "true"]}, {"field": "fw_status", "confidenceMatrix": ["false", "true"]}, {"field": "vm_onboarding_status", "confidenceMatrix": ["true", "false"]}]}}, "derivedProperties": [{"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)>=0 THEN size(defender_threat_name) END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_inter_source_resolver"}}, "config_schema": ["test"], "entity_name": "host", "solution_name": "ei", "data_source_feed_name": "active_directory", "data_source_name": "microsoft", "version": "1.1", "deployed": false, "edit_lock": false}], "solution_configs": [{"id": 7, "name": "sds_ei__host__ms_azure_ad", "config_item_type": "intrasource_disambiguated_models", "config_item_level": "solution", "config_deploy_type": "spark_job_config", "config_value": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad_devices__device_id", "name": "sds_ei__host__ms_azure_ad_devices__device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad_registered_users__id", "name": "sds_ei__host__ms_azure_ad_registered_users__id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__host__ms_azure_ad_devices__device_id", "sds_ei__host__ms_azure_ad_registered_users__id"], "excludeValues": ["Unknown", "Other", "-"]}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Azure AD'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}}, "config_schema": ["test"], "entity_name": "host", "solution_name": "ei", "data_source_feed_name": "active_directory", "data_source_name": "microsoft", "version": "1.1", "deployed": false, "edit_lock": false}, {"id": 9, "name": "sds_ei__host__ms_defender", "config_item_type": "intrasource_disambiguated_models", "config_item_level": "solution", "config_deploy_type": "spark_job_config", "config_value": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_list__id", "name": "device_list"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_software_inventory__device_id", "name": "software"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_tvm__device_id", "name": "tvm"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_events__device_id", "name": "events"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_tvm_software_vulnerabilities__device_id", "name": "vulnerability"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["device_list", "software", "tvm", "events", "vulnerability"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "defender_onboarding_date", "function": "min"}, {"field": "vulnerability_last_observed_date", "function": "max"}], "rollingUpFields": ["defender_threat_name", "defender_action_type"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "av_status", "confidenceMatrix": ["false", "true"]}, {"field": "av_block_malicious_code_status", "confidenceMatrix": ["false", "true"]}, {"field": "fw_status", "confidenceMatrix": ["false", "true"]}, {"field": "defender_detection_method", "confidenceMatrix": ["Defender Agent", "Network Scan"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Defender'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}, {"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)>=0 THEN size(defender_threat_name) END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}}, "config_schema": ["test"], "entity_name": "host", "solution_name": "ei", "data_source_feed_name": "active_directory", "data_source_name": "microsoft", "version": "1.1", "deployed": false, "edit_lock": false}, {"id": 8, "name": "sds_ei__host", "config_item_type": "intersource_disambiguated_models", "config_item_level": "solution", "config_deploy_type": "spark_job_config", "config_value": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid", "name": "sds_ei__host__active_directory__object_guid"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender", "name": "sds_ei__host__ms_defender"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_intunes__device_id", "name": "sds_ei__host__ms_intunes"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys", "name": "sds_ei__host__qualys"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__globalprotect_vpn", "name": "sds_ei__host__globalprotect_vpn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__winevents", "name": "sds_ei__host__winevents"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_resource_details__arn", "name": "sds_ei__host__aws_resource_details__arn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad", "name": "sds_ei__host__ms_azure_ad"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure", "name": "sds_ei__host__ms_azure"}], "disambiguation": {"candidateKeys": ["fqdn", {"name": "aad_device_id", "exceptionFilter": "aad_device_id = '00000000-0000-0000-0000-000000000000'"}, {"name": "host_name", "exceptionFilter": "(lower(host_name) LIKE '%iphone%' OR lower(host_name) LIKE '%android%' OR lower(host_name) LIKE '%ipad%' OR lower(host_name) LIKE '%macbook%' OR host_name RLIKE '(?i)pro([^a-zA-Z0-9]|$)' OR lower(host_name) LIKE '%galaxy%' OR lower(host_name) LIKE '%samsung%') OR lower(host_name) IN ('wrk','user deleted for this device') OR (lower(os) LIKE '%android%' OR lower(os) LIKE '%appleios%' OR lower(os) LIKE '%tizen%')"}, "hardware_serial_number", "cloud_resource_id"], "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_defender", "sds_ei__host__ms_intunes", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__aws_resource_details__arn", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "type", "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_intunes", "sds_ei__host__ms_defender", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__aws_resource_details__arn", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure"]}], "rollingUpFields": ["origin", "defender_threat_name", "defender_action_type", "qualys_id", "defender_id", "qualys_asset_id", "qualys_detection_method", "vm_tracking_method", "vm_product", "defender_detection_method", "win_event_id", "ip"], "aggregation": [{"field": "login_last_date", "function": "max"}, {"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "mdm_enrolled_date", "function": "min"}, {"field": "vm_last_scan_date", "function": "max"}, {"field": "ad_last_sync_date", "function": "max"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "azure_created_date", "function": "min"}, {"field": "ad_account_disabled_date", "function": "max"}, {"field": "defender_onboarding_date", "function": "min"}, {"field": "vulnerability_last_observed_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "av_status", "confidenceMatrix": ["false", "true"]}, {"field": "av_block_malicious_code_status", "confidenceMatrix": ["false", "true"]}, {"field": "fw_status", "confidenceMatrix": ["false", "true"]}, {"field": "vm_onboarding_status", "confidenceMatrix": ["true", "false"]}]}}, "derivedProperties": [{"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)>=0 THEN size(defender_threat_name) END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_inter_source_resolver"}}, "config_schema": ["test"], "entity_name": "host", "solution_name": "ei", "data_source_feed_name": "active_directory", "data_source_name": "microsoft", "version": "1.1", "deployed": false, "edit_lock": false}, {"config_item_type": "global_entity_config", "config_item_level": "solution", "config_deploy_type": "spark_job_configs", "entity_name": "host", "solution_name": "ei", "data_source_feed_name": "ad_device", "data_source_name": "microsoft_azure", "config_value": {"entityClass": "Host", "commonProperties": [{"colName": "inactivity_period", "colExpr": "30", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "display_label", "colExpr": "coalesce(fqdn,dns_name,host_name,aad_device_id,primary_key)", "fieldsSpec": {"isInventoryDerived": true, "postDisambiguationUpdate": true}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period  THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "postDisambiguationUpdate": true}}, {"colName": "business_unit", "fieldsSpec": {"isInventoryDerived": true, "postDisambiguationUpdate": true}}, {"colName": "department", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os_family", "colExpr": "CASE WHEN os IS NULL OR regexp_like(os,'(?i)unknown|^-$') THEN NULL WHEN LOWER(os) LIKE '%windows%' THEN 'Windows' WHEN LOWER(os) LIKE '%macos%' THEN 'macOS' WHEN lower(os) RLIKE '.*linux.*|.*ubuntu.*|.*centos.*|.*fedora.*|.*webos.*|.*chromeos.*|.*debian.*|.*redhat.*|.*tizen.*|.*panos.*|openwrt|.*embeddedos.*' THEN 'Linux' WHEN lower(os) RLIKE '.*cisco.*|.*fortinet.*|.*juniper.*|.*sonicos.*' THEN 'Network OS' WHEN lower(os) RLIKE '.*ios.*|.*ipados.*|.*ipad.*|.*iphone.*' THEN 'iOS' WHEN LOWER(TRIM(os)) LIKE '%android%' THEN 'Android' ELSE 'Other' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "fqdn", "colExpr": "LOWER(CASE WHEN regexp_like(dns_name,'^(?!:\\\\/\\\\/)(?=.{1,255}$)((.{1,63}[.]){1,127}(?![0-9]*$)[a-z0-9-A-Z]+[.]?)$') THEN dns_name END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "domain", "colExpr": "regexp_extract(fqdn,'^(?:[^.]++[.])((local|corp|[^.]++[^\\\\r\\\\n]++))$')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "host_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ip", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "dns_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "netbios", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "accessibility", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_mac_address", "colExpr": "from_json(null, 'ARRAY<STRING>')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_version", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_architecture", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_build", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_account_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_state", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_region", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_availability_zone", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_manufacturer", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_model", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_serial_number", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_imei", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_user", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_product", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_status", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_compliance_state", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_enrolled_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_last_sync_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_onboarding_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "edr_product", "colExpr": "cast(null as string)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "edr_last_scan_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_last_scan_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_signature_update_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_block_malicious_code_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "fw_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "cast(null as string)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "vm_onboarding_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "vm_tracking_method", "colExpr": "cast(null as string)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "vm_last_scan_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vulnerability_last_observed_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}], "fieldLevelSpec": [{"colName": "ip", "fieldsSpec": {"persistNonNullValue": false}}], "lastUpdateFields": ["type", "first_seen_date", "business_unit", "department", "description", "location", "host_name", "fqdn", "ip", "netbios", "accessibility", "os", "cloud_provider", "cloud_resource_id", "hardware_model", "hardware_serial_number", "login_last_date", "login_last_user", "edr_onboarding_status", "vm_onboarding_status", "mdm_product", "mdm_status", "mdm_last_sync_date", "edr_last_scan_date", "vm_last_scan_date", "ad_distinguished_name", "ad_last_sync_date", "ad_operational_status", "aad_device_id", "aad_operational_status", "azure_operational_status"]}}]}