[{"name": "sds_ei__cloud_compute_and_container__ms_defender", "config_value": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute_and_container__ms_defender_device_list__id", "name": "sds_ei__cloud_compute_and_container__ms_defender_device_list__id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute_and_container__ms_defender_device_software_inventory__device_id", "name": "sds_ei__cloud_compute_and_container__ms_defender_device_software_inventory__device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute_and_container__ms_defender_tvm__device_id", "name": "sds_ei__cloud_compute_and_container__ms_defender_tvm__device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute_and_container__ms_defender_device_events__device_id", "name": "sds_ei__cloud_compute_and_container__ms_defender_device_events__device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute_and_container__ms_defender_device_tvm_software_vulnerabilities_delta__device_id", "name": "sds_ei__cloud_compute_and_container__ms_defender_device_tvm_software_vulnerabilities_delta__device_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__cloud_compute_and_container__ms_defender_device_list__id", "sds_ei__cloud_compute_and_container__ms_defender_device_software_inventory__device_id", "sds_ei__cloud_compute_and_container__ms_defender_tvm__device_id", "sds_ei__cloud_compute_and_container__ms_defender_device_events__device_id", "sds_ei__cloud_compute_and_container__ms_defender_device_tvm_software_vulnerabilities_delta__device_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "defender_exposure_level", "confidenceMatrix": []}], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "defender_onboarding_date", "function": "min"}], "rollingUpFields": ["defender_threat_name", "defender_action_type"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "av_status", "confidenceMatrix": ["false", "true"]}, {"field": "av_block_malicious_code_status", "confidenceMatrix": ["false", "true"]}, {"field": "fw_status", "confidenceMatrix": ["false", "true"]}, {"field": "defender_detection_method", "confidenceMatrix": ["Defender Agent", "Network Scan"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Defender'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}, {"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)>=0 THEN size(defender_threat_name) END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute_and_container__ms_defender", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "filter": "resource_id IS NOT NULL"}}}, {"name": "sds_ei__host__ms_defender", "config_value": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_list__id", "name": "device_list"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_software_inventory__device_id", "name": "software"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_tvm__device_id", "name": "tvm"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_events__device_id", "name": "events"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender_device_tvm_software_vulnerabilities_delta__device_id", "name": "vulnerability"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["device_list", "software", "tvm", "events", "vulnerability"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "defender_health_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": []}, {"field": "defender_exposure_level", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": []}], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "defender_onboarding_date", "function": "min"}, {"field": "vulnerability_last_observed_date", "function": "max"}], "rollingUpFields": ["defender_threat_name", "defender_action_type"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "av_status", "confidenceMatrix": ["false", "true"]}, {"field": "av_block_malicious_code_status", "confidenceMatrix": ["false", "true"]}, {"field": "fw_status", "confidenceMatrix": ["false", "true"]}, {"field": "defender_detection_method", "confidenceMatrix": ["Defender Agent", "Network Scan"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Defender'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}, {"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)>=0 THEN size(defender_threat_name) END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}}}, {"name": "sds_ei__identity__ms_azure_ad", "config_value": {"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_azure_ad_users__sam_account_name", "name": "sds_ei__identity__ms_azure_ad_users__sam_account_name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_azure_ad_users__aad_id", "name": "sds_ei__identity__ms_azure_ad_users__aad_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_azure_ad_users__user_principal_name", "name": "sds_ei__identity__ms_azure_ad_users__user_principal_name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_azure_ad_devices__aad_device_id", "name": "sds_ei__identity__ms_azure_ad_devices__aad_device_id"}], "disambiguation": {"candidateKeys": ["account_name"], "confidenceMatrix": ["sds_ei__identity__ms_azure_ad_users__sam_account_name", "sds_ei__identity__ms_azure_ad_users__aad_id", "sds_ei__identity__ms_azure_ad_users__user_principal_name", "sds_ei__identity__ms_azure_ad_devices__aad_device_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"rollingUpFields": ["identity_provider", "type"], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "login_last_date", "function": "max"}, {"field": "ad_last_sync_date", "function": "max"}, {"field": "aad_created_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Azure AD'"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_azure_ad", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}}}]