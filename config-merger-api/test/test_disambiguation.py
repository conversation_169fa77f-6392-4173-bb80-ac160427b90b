# import unittest
# from config_merger_api.utils.common_utils import merge_dictionaries
# from config_merger_api.utils.disambiguation_utils import intra_rollup_fields_mapper,inter_candidate_keys_mapper,process_and_write_to_solution_file,extract_and_combine_strategy_values,replace_confidence_matrix_recursively,filter_disambiguation_folder
# # from config_merger_api.utils.lineage_utils import _ConfigMatcher
# from config_merger_api.utils.relationship_disambiguation_utils import merge_relationship_disambiguation
# case = unittest.TestCase()
#
# class TestDisambiguation:
#     # Successfully extracts lineage when valid data_source_name and data_feed_name are provided
#     # Correctly merges relationship disambiguation configurations when both solution and client models are present
#     def test_merge_with_both_solution_and_client_models(self):
#         solution_configs = [
#             {
#                 'config_item_type': 'relationship_disambiguation',
#                 'config_value': {
#                     'disambiguation': {
#                         'strategy': {
#                             'fieldLevelConfidenceMatrix': [{'field': 'name', 'confidence': 0.9}],
#                             'rollingUpFields': ['name']
#                         }
#                     }
#                 }
#             }
#         ]
#         client_configs = [
#             {
#                 'config_item_type': 'relationship_disambiguation',
#                 'config_value': {
#                     'disambiguation': {
#                         'strategy': {
#                             'fieldLevelConfidenceMatrix': [{'field': 'name', 'confidence': 0.8}],
#                             'rollingUpFields': ['address']
#                         }
#                     }
#                 }
#             }
#         ]
#         expected_merged_config = {
#             'disambiguation': {
#                 'strategy': {
#                     'fieldLevelConfidenceMatrix': [{'field': 'name', 'confidence': 0.9}],
#                     'rollingUpFields': ['name', 'address']
#                 }
#             }
#         }
#         result = merge_relationship_disambiguation(solution_configs, client_configs)
#         assert result == expected_merged_config
#
#     # Correctly identifies matches when both data source and feed names match
#     def test_inventory_models_correct_match(self):
#         config_matches = {
#             "data_source_name": {"dataSource.name": "source1"},
#             "data_feed_name": {"dataSource.feedName": "feed1"}
#         }
#         values_to_check = {
#             "data_source_name": "source1",
#             "data_feed_name": "feed1"
#         }
#         config_matcher = self._ConfigMatcher(config_matches, values_to_check, "test_config")
#         assert config_matcher.inventory_models() is True
#
#     # Removes specified string from 'confidenceMatrix' key in nested dictionaries
#     def test_remove_string_from_confidence_matrix(self):
#         obj = {
#             "level1": {
#                 "confidenceMatrix": ["remove_me", "keep_me"],
#                 "level2": {
#                     "confidenceMatrix": ["remove_me", "another_value"]
#                 }
#             }
#         }
#         expected = {
#             "level1": {
#                 "confidenceMatrix": ["keep_me"],
#                 "level2": {
#                     "confidenceMatrix": ["another_value"]
#                 }
#             }
#         }
#         result = replace_confidence_matrix_recursively(obj, "remove_me")
#         assert result == expected
#
#         # Correctly filters out inventory models with matching paths in client configs
#     def test_filter_matching_paths(self):
#         solution_configs = [
#             {
#                 "inventoryModelInput": [
#                     {"path": "/path/to/model1", "name": "model1"},
#                     {"path": "/path/to/model2", "name": "model2"}
#                 ]
#             }
#         ]
#         client_configs = [
#             {
#                 "inventoryModelInput": [
#                     {"path": "/path/to/model1"}
#                 ]
#             }
#         ]
#         expected_output = [
#             {
#                 "inventoryModelInput": [
#                     {"path": "/path/to/model2", "name": "model2"}
#                 ]
#             }
#         ]
#         result = filter_disambiguation_folder(solution_configs, client_configs)
#         assert result == expected_output
#
#         # Extracts strategy values correctly when all keys are present in data
#     def test_extracts_strategy_values_with_all_keys(self):
#         data = {
#             "disambiguation": {
#                 "strategy": {
#                     "fieldLevelConfidenceMatrix": [{"field": "field1"}, {"field": "field2"}],
#                     "valueConfidence": [{"field": "field3"}],
#                     "aggregation": [{"field": "field4"}],
#                     "rollingUpFields": ["field5"]
#                 }
#             }
#         }
#         expected_result = {"field1", "field2", "field3", "field4", "field5"}
#         result = extract_and_combine_strategy_values(data)
#         assert set(result) == expected_result
#
#         # Returns a list of unique candidate keys from a valid model
#     def test_unique_candidate_keys_from_valid_model(self):
#         model = {
#             "disambiguation": {
#                 "candidateKeys": [
#                     {"name": "key1"},
#                     {"name": "key2"},
#                     {"name": "key1"}
#                 ]
#             }
#         }
#         result = inter_candidate_keys_mapper(model)
#         assert set(result) == {"key1", "key2"}
#
#
#     # Processes client and solution configurations correctly
#     # def test_processes_configurations_correctly(self):
#     #     client_config = {
#     #         "disambiguation": {
#     #             "strategy": {
#     #                 "fieldLevelConfidenceMatrix": [{"field": "name"}, {"field": "email"}],
#     #                 "valueConfidence": [{"field": "address"}],
#     #                 "aggregation": [{"field": "phone"}],
#     #                 "rollingUpFields": ["name", "email"]
#     #             }
#     #         }
#     #     }
#     #     solution_config = {
#     #         "disambiguation": {
#     #             "strategy": {
#     #                 "fieldLevelConfidenceMatrix": [{"field": "name"}, {"field": "phone"}],
#     #                 "valueConfidence": [{"field": "email"}],
#     #                 "aggregation": [{"field": "address"}],
#     #                 "rollingUpFields": ["phone", "address"]
#     #             }
#     #         }
#     #     }
#     #     expected_solution_config = {
#     #         "disambiguation": {
#     #             "strategy": {
#     #                 "fieldLevelConfidenceMatrix": [{"field": "phone"}],
#     #                 "valueConfidence": [],
#     #                 "aggregation": [{"field": "address"}],
#     #                 "rollingUpFields": ["phone", "address"]
#     #             }
#     #         }
#     #     }
#     #     result = process_and_write_to_solution_file(client_config, solution_config)
#     #     assert result == [expected_solution_config]
#
#     # Handles empty client or solution configurations gracefully
#     def test_handles_empty_configurations(self):
#         client_config = {}
#         solution_config = {
#             "disambiguation": {
#                 "strategy": {
#                     "fieldLevelConfidenceMatrix": [{"field": "name"}],
#                     "valueConfidence": [{"field": "email"}],
#                     "aggregation": [{"field": "address"}],
#                     "rollingUpFields": ["name", "email"]
#                 }
#             }
#         }
#         expected_solution_config = {
#             "disambiguation": {
#                 "strategy": {
#                     "fieldLevelConfidenceMatrix": [{"field": "name"}],
#                     "valueConfidence": [{"field": "email"}],
#                     "aggregation": [{"field": "address"}],
#                     "rollingUpFields": ["name", "email"]
#                 }
#             }
#         }
#         result = process_and_write_to_solution_file(client_config, solution_config)
#         assert result == [expected_solution_config]
#
#         # Processes a list of disambiguated models and extracts candidate keys and rollup fields
#     def test_extracts_candidate_keys_and_rollup_fields(self):
#         intrasource_disambiguated_models = [
#             {
#                 "disambiguation": {
#                     "candidateKeys": [{"name": "key1"}, {"name": "key2"}],
#                     "strategy": {
#                         "rollingUpFields": ["field1", "field2"]
#                     }
#                 }
#             },
#             {
#                 "disambiguation": {
#                     "candidateKeys": ["key3"],
#                     "strategy": {
#                         "rollingUpFields": ["field3"]
#                     }
#                 }
#             }
#         ]
#         expected_rollup_fields = ["field1", "field2", "field3"]
#         expected_candidate_keys = ["key1", "key2", "key3"]
#
#         rollup_fields, candidate_keys = intra_rollup_fields_mapper(intrasource_disambiguated_models)
#
#         assert set(rollup_fields) == set(expected_rollup_fields)
#         assert set(candidate_keys) == set(expected_candidate_keys)
#         # Correctly identifies matching source and target loaders
#     # def test_identifies_matching_loaders(self):
#     #     config_matches = {
#     #         "inventory_models": {
#     #             "inputSourceInfo.sourceLoaderConfPath": ["sds_ei123?param=value"],
#     #             "inputSourceInfo.targetLoaderConfPath": ["sds_ei123?param=value"]
#     #         }
#     #     }
#     #     values_to_check = {
#     #         "inventory_models": ["sds_ei123"]
#     #     }
#     #     matcher = self._ConfigMatcher(config_matches, values_to_check, "test_config")
#     #     assert matcher.relationship_models() is True
#     def extract_and_combine_strategy_values(config):
#         # Sample mock function for testing
#         return config.get("fields", [])
#
#     # Test case
#
#     def test_process_and_write_to_solution_file(self):
#         client_config = {
#             "fields": ["fieldA", "fieldB", "fieldC"]
#         }
#         solution_config = {
#             "fields": ["fieldB", "fieldC", "fieldD"],
#             "disambiguation": {
#                 "strategy": {
#                     "fieldLevelConfidenceMatrix": [{"field": "fieldB"}, {"field": "fieldD"}],
#                     "aggregation": [{"field": "fieldC"}, {"field": "fieldE"}],
#                     "valueConfidence": [{"field": "fieldA"}],
#                     "rollingUpFields": ["fieldB", "fieldC", "fieldD"]
#                 }
#             }
#         }
#
#         expected_solution_config = {
#             "fields": ["fieldB", "fieldC", "fieldD"],
#             "disambiguation": {
#                 "strategy": {
#                     "fieldLevelConfidenceMatrix": [{"field": "fieldD"}],  # "fieldB" removed
#                     "aggregation": [{"field": "fieldE"}],  # "fieldC" removed
#                     "valueConfidence": [{"field": "fieldA"}],  # No common field, remains the same
#                     "rollingUpFields": ["fieldD"]  # Only "fieldD" should remain after removing common fields
#                 }
#             }
#         }
#
#         result = _ConfigMatcher.process_and_write_to_solution_file(client_config, solution_config)
#         assert result[0] == expected_solution_config, "The solution config was not modified as expected."
#
