from config_merger_api.utils.common_utils import merge_config

DEFAULT_SPEC = {
    "persistNonNullValue": True
}


def file_to_spec_mapper(global_entity_config):
    entity_class = global_entity_config.get("entityClass", "")
    default_spec = global_entity_config.get("defaultEntitySpec", DEFAULT_SPEC)
    field_level_spec = global_entity_config.get("fieldLevelSpec", [])
    common_properties = global_entity_config.get("commonProperties", [])
    source_specific_properties = global_entity_config.get("sourceSpecificProperties", [])
    entity_specific_props = global_entity_config.get("entitySpecificProperties", [])
    last_update_fields = global_entity_config.get("lastUpdateFields", [])
    
    return {
        "entity_class":entity_class,
        "default_spec": default_spec,
        "field_level_spec": field_level_spec, 
        "common_properties": common_properties,
        "entity_specific_props": entity_specific_props,
        "source_specific_properties":source_specific_properties,
        "last_update_fields": last_update_fields
    }


def update_entity_config(inventory_data,entity_class, default_spec, last_update_fields):
    inventory_data["entity"].update( {
        "name":entity_class,
        "fieldSpec": default_spec,
        "lastUpdateFields": last_update_fields
    })


def update_properties(inventory_data, properties, enrichments, inventory_key):
    enrichment_columns = []
    if enrichments:
        for enrichment in enrichments:
            enrichment_columns.extend(enrichment.get("lookupInfo", {}).get("enrichmentColumns", []))
    
    current_properties = [prop.get('colName') for prop in inventory_data.get(inventory_key, [])] + enrichment_columns
    config_properties = [prop.get('colName') for prop in properties]
    properties_diff_inv = list(set(config_properties) - set(current_properties))
    full_properties = [prop for prop in properties if prop.get("colName") in properties_diff_inv]
    inventory_data[inventory_key] = inventory_data.get(inventory_key, []) + full_properties


def update_common_properties(inventory_data, common_properties, enrichments):
    update_properties(inventory_data, common_properties, enrichments, "commonProperties")


def update_entity_specific_properties(inventory_data, entity_specific_props, enrichments):
    update_properties(inventory_data, entity_specific_props, enrichments, "entitySpecificProperties")


def update_field_specs(inventory_data, field_level_spec):
    for field_spec in inventory_data.get("entitySpecificProperties", []):
        col_name = field_spec.get("colName")
        matching_fields = [fs for fs in field_level_spec if fs.get("colName") == col_name and field_spec.get("fieldsSpec") is None]

        if matching_fields:
            field_spec.setdefault("fieldsSpec", {})["persistNonNullValue"] = matching_fields[0].get("fieldsSpec", {}).get("persistNonNullValue", False)


def compare_properties(common_properties, entity_specific_properties):
    common_col_names = set(prop["colName"] for prop in common_properties)
    entity_col_names = set(prop["colName"] for prop in entity_specific_properties)
    common_elements = common_col_names.intersection(entity_col_names)
    
    if common_elements:
        raise Exception("Common elements found between common_properties and entity_specific_properties in global "
                        "entity config: " + ", ".join(common_elements))


def compare_properties_in_global_entity_config(solution_global_config, client_global_config):
    if client_global_config != [] and solution_global_config != []:
        solution_global_config = solution_global_config[0]
        client_global_config = client_global_config[0]
        
        common_properties = solution_global_config.get("commonProperties", [])
        entity_properties = solution_global_config.get("entitySpecificProperties", [])
        client_common_properties = client_global_config.get("commonProperties", [])
        client_entity_properties = client_global_config.get("entitySpecificProperties", [])
        
        compare_properties(common_properties, client_entity_properties)
        compare_properties(entity_properties, client_common_properties)


def loader_global_config_merger(loader_config, global_entity_config):
    entity_config = file_to_spec_mapper(global_entity_config)
    print("Before update:", loader_config.get("entity", "No entity"))
    update_entity_config(loader_config,entity_config.get("entity_class"),entity_config.get("default_spec"), entity_config.get("last_update_fields"))
    print("After update:", loader_config.get("entity", "No entity"))
    update_common_properties(loader_config, entity_config.get("common_properties"), loader_config.get("enrichments"))
    update_entity_specific_properties(loader_config, entity_config.get("entity_specific_props"), loader_config.get("enrichments"))
    update_field_specs(loader_config, entity_config.get("field_level_spec"))
    
    return loader_config


def filter_by_computation_phase(properties, dis_type='loader'):
    # Determine the valid phases based on dis_type
    if dis_type == 'loader':
        valid_phases = ['loader', 'all']
    else:  # Default to 'inter'
        valid_phases = ['inter', 'all']
    
    filtered_properties = []
    for prop in properties:
        # Extract computationPhase (defaults to 'loader' if not present)
        comp_phase = prop.get('fieldsSpec', {}).get('computationPhase', 'loader').lower()
        
        # Debugging: Print computationPhase and colName for each property
        print(f"Checking prop: {prop.get('colName')} with computationPhase: {comp_phase}")
        
        # Apply filtering logic based on dis_type
        if comp_phase in valid_phases:
            filtered_properties.append(prop)
    
    return filtered_properties


def merge_inventory_models(solution_configs, client_configs, config_manipulation,is_studio_request):
    merged_configs = merge_config(solution_configs, client_configs)
    #print(f"sourceSpecificProperties : {sourceSpecificProperties}")
    loader_config = [i.get("config_value", {}) for i in merged_configs if i['config_item_type'] == 'inventory_models'][0]
    sourceSpecificProperties=loader_config.get('sourceSpecificProperties', [])
    print(f"sourceSpecificProperties : {sourceSpecificProperties}")
    if 'entity' not in loader_config:
            loader_config['entity'] = {}
    if config_manipulation:
        sol_global_config = [i.get("config_value", {}) for i in solution_configs if i['config_item_type'] == 'global_entity_config']
        client_global_config = [i.get("config_value", {}) for i in client_configs if i['config_item_type'] == 'global_entity_config']
        compare_properties_in_global_entity_config(sol_global_config, client_global_config)
        
        global_entity_config = [i.get("config_value", {}) for i in merged_configs if i['config_item_type'] == 'global_entity_config']
        if is_studio_request and global_entity_config and global_entity_config[0]:
            for k in ['commonProperties', 'entitySpecificProperties']:
                global_entity_config[0][k] = [
                    {**p, 'fieldsSpec': {**p.get('fieldsSpec', {}), 'computationPhase': 'all'}}
                    for p in global_entity_config[0].get(k, [])
                    if k != 'entitySpecificProperties' or not (
                            isinstance(p.get('colExpr'), str) and
                            p['colExpr'].strip().lower().replace(" ", "").startswith('cast(nullas')
                    )
                ]
        if global_entity_config != [] and global_entity_config[0] != {}:
            config_data = global_entity_config[0]
            config_data['entitySpecificProperties'] = filter_by_computation_phase(config_data.get('entitySpecificProperties', []), 'loader')
            config_data['commonProperties'] = filter_by_computation_phase(config_data.get('commonProperties', []), 'loader')
            config_data['sourceSpecificProperties'] = filter_by_computation_phase(config_data.get('sourceSpecificProperties', []), 'inter')
            loader_config = loader_global_config_merger(loader_config, config_data)
    
    return loader_config
