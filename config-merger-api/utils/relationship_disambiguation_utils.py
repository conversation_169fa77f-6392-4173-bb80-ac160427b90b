from config_merger_api.utils.common_utils import merge_config
from config_merger_api.utils.disambiguation_utils import process_and_write_to_solution_file


def merge_relationship_disambiguation(solution_configs, client_configs):
    sol_relationship_model = [i.get("config_value", {}) for i in solution_configs if
                              i['config_item_type'] == 'relationship_disambiguation']
    client_relationship_model = [i.get("config_value", {}) for i in client_configs if
                                 i['config_item_type'] == 'relationship_disambiguation']
    if sol_relationship_model != [] and client_relationship_model != []:
        sol_relationship_model = process_and_write_to_solution_file(
            client_relationship_model[0], sol_relationship_model[0])
    merged_configs = merge_config(sol_relationship_model, client_relationship_model)
    rel_disambiguation_merged_configs = merged_configs[0] if merged_configs != [] else {}
    rel_disambiguation_merged_configs.setdefault("output", {})["isFragmentOLAPTable"]=False
    if rel_disambiguation_merged_configs.get("output", {}).get("isFragmentOLAPTable", True) and rel_disambiguation_merged_configs.get("output", {}).get("fragmentLocation") is None:
        rel_disambiguation_merged_configs.setdefault("output", {})["fragmentLocation"] = rel_disambiguation_merged_configs["output"]["disambiguatedModelLocation"].replace("<%EI_SCHEMA_NAME%>.sds_ei__rel","<%KG_FRAGMENT_SCHEMA%>.sds_ei__rel__fragment")
    if rel_disambiguation_merged_configs.get("output", {}).get("resolverLocation") is None:
        rel_disambiguation_merged_configs.setdefault("output", {})["resolverLocation"] = rel_disambiguation_merged_configs["output"]["disambiguatedModelLocation"].replace("<%EI_SCHEMA_NAME%>.sds_ei__rel","<%KG_FRAGMENT_SCHEMA%>.sds_ei__rel__resolver")
    return rel_disambiguation_merged_configs
