from __future__ import annotations

import importlib.resources
import json
import logging
import os
from functools import cached_property, reduce
from itertools import chain

from typing import List, Dict, Any, Iterable

from config_merger_api.utils.http_utils import HttpUtils


class ConfigManagerException(Exception):
    pass


class _ConfigMatcher:
    LOGGER = logging.getLogger()

    def __init__(self,
                 config_matches: Dict[str, Dict[str, Any]],
                 values_to_check: Dict[str, List[str] | str],
                 config_name: str
                 ):

        self.config_matches = config_matches
        self.values_to_check = values_to_check
        self.config_name = config_name

    @staticmethod
    def _check_for_matches(search_value: str, iterable: Iterable):
        return search_value in iterable

    def handle_logging(self, matched:bool, info_msg:str):
        if matched:
            self.LOGGER.info(f"Match successfully found for {info_msg} in {self.config_name}")
        else:
            self.LOGGER.error(f"Failed to find match for {info_msg} in {self.config_name}")

    def inventory_models(self):
        matched_source = self.config_matches.get("data_source_name", {}).get("dataSource.name")
        matched_feed = self.config_matches.get("data_feed_name", {}).get("dataSource.feedName")
        expected_source = self.values_to_check.get("data_source_name")
        expected_feed = self.values_to_check.get("data_feed_name")
        match_found = expected_source == matched_source and expected_feed == matched_feed
        self.handle_logging(matched=match_found, info_msg=f"source {expected_source} and feed {expected_feed}")
        return match_found

    def intrasource_disambiguated_models(self):
        name_matches = self.config_matches.get("inventory_models", {}).get("inventoryModelInput.name", [])
        table_matches = [item.split(".")[-1] for item in self.config_matches.get("inventory_models", {}).get("inventoryModelInput.path", [])]
        match_found = any([val for val in self.values_to_check.get("inventory_models", []) +
                           self.values_to_check.get("intrasource_disambiguated_models", [])
                           if self._check_for_matches(val, name_matches + table_matches)])
        self.handle_logging(matched=match_found, info_msg="inventory_models")
        return match_found

    def intersource_disambiguated_models(self):
        return self.intrasource_disambiguated_models()

    def relationship_models(self):
        source_loaders = self.config_matches.get("inventory_models", {}).get("inputSourceInfo.sourceLoaderConfPath", [])
        target_loaders = self.config_matches.get("inventory_models", {}).get("inputSourceInfo.targetLoaderConfPath", [])
        available_loaders = self.values_to_check.get("inventory_models", [])
        for source, target in zip(source_loaders, target_loaders):
            source = [f"sds_ei{item.split('sds_ei')[1].split('?')[0]}" for item in source]
            target = [f"sds_ei{item.split('sds_ei')[1].split('?')[0]}" for item in target]
            source_available = any(
                source_loader for source_loader in source if self._check_for_matches(source_loader, available_loaders))
            target_available = any(
                target_loader for target_loader in target if self._check_for_matches(target_loader, available_loaders))

            if source_available and target_available:
                self.handle_logging(matched=True, info_msg="inventory_models")
                return True
        self.handle_logging(matched=False, info_msg="inventory_models")
        return False

    def relationship_disambiguation(self):
        names_matches = self.config_matches.get("relationship_models", {}).get("relationshipModels.name", [])
        tables_matches = [item.split(".")[-1] for item in self.config_matches.get("relationship_models", {}).get("relationshipModels.tableName", [])]
        matched = any(
            [val for val in self.values_to_check.get("relationship_models", []) if
             self._check_for_matches(val, names_matches + tables_matches)])
        self.handle_logging(matched=matched, info_msg="relationship models")
        return matched

class LineageExtractor:
    LOGGER = logging.getLogger()

    def __init__(self,
                 data_source_name: str = None,
                 data_feed_name: str = None):

        self.data_source_name = data_source_name
        self.data_feed_name = data_feed_name

        if not (self.data_feed_name and self.data_source_name):
            raise ValueError("Both data_source_name or data_feed_name must be provided")

    @cached_property
    def config_manager_hook(self):
        url = os.getenv("CONFIG_MANAGER_ENDPOINT")
        if not url:
            raise ValueError("No env variable with name 'CONFIG_MANGER_ENDPOINT' set for ei config api")
        self.LOGGER.info(f"Setting config manager endpoint as {url}")
        return HttpUtils(url=url)

    @cached_property
    def lineage_metadata(self) -> dict:
        """
        lineage_metadata dict is the source of truth for the entity inventory config dependencies. All dependencies
        are decided based on the value of this variable. Each key corresponds to which type of ei config is used. Its
        value is a dict with keys - 1. 'dependencies' - This is a dict of what all type of configs are dependencies,
        ie upstream dependencies
        """
        with importlib.resources.open_text("config_merger_api.utils", "ei_lineage.json") as file:
            data = json.load(file)
        self.LOGGER.info(f"Setting lineage dict as {json.dumps(data)}")
        return data

    def get_config(self, config_item_type: str, name: str,deployed: bool = None) -> dict:
        try:
            params = {"config_item_type": config_item_type}
            if deployed is not None:
                params["deployed"] = deployed
            response = self.config_manager_hook.execute_api_call(method="GET", endpoint=name,
                                                                 params=params)
            return response.get("config_value")
        except Exception as e:
            raise ConfigManagerException(str(e))

    def get_config_items(self, config_item_type: str, deployed: bool = False):
        items = []
        for available in ["true", "false"]:
            params = {"config_item_type": config_item_type, "is_available": available}
            if deployed :
                params["deployed"] = deployed
            response = self.config_manager_hook.execute_api_call(method="GET", params=params)
            items.append(response)
        return list(chain.from_iterable(items))

    def get_config_item_dict(self, config_item_type):
        return [
            {"name": item.get("name"), "config_value": self.get_config(config_item_type, name=item.get("name"))}
            for item in self.get_config_items(config_item_type)
        ]

    def get_deployed_config_item_dict(self, config_item_type):
        return [
            {"name": item.get("name"), "config_value": self.get_config(config_item_type, name=item.get("name"),deployed=True)}
            for item in self.get_config_items(config_item_type,deployed=True)
        ]

    @staticmethod
    def select_key(config_value: dict, key_list: list[str]) -> List[dict] | dict:
        return reduce(
            lambda config_val, key: config_val.get(key) if config_val else None,
            key_list, config_value
        )

    def get_value_from_config_key(self, config_value: dict, key: str):
        matched_value = []
        if "." not in key:
            matched_value = config_value.get(key)
        else:
            key_list = key.split(".")
            outer_val = self.select_key(config_value, key_list[:-1])
            if isinstance(outer_val, list) and outer_val and isinstance(outer_val[0], dict):
                matched_value = [item.get(key_list[-1]) for item in outer_val]
            elif isinstance(outer_val, dict) and outer_val:
                matched_value = outer_val.get(key_list[-1])
        return matched_value

    def extract_keys_from_config(self, config_value: dict, dependencies: dict):
        matches = {}
        for dependency_type, keys_to_match in dependencies.items():
            if isinstance(keys_to_match, str):
                keys_to_match = [keys_to_match]
            matched_values = {key: self.get_value_from_config_key(config_value, key) for key in keys_to_match}
            matches[dependency_type] = matched_values

        return matches

    def extract(self):
        lineage = {
            "data_source_name": self.data_source_name,
            "data_feed_name": self.data_feed_name
        }

        for config_item_type, dependencies in self.lineage_metadata.items():
            self.LOGGER.info(f"Going to find matched for {config_item_type} in lineage")
            config_item_list = self.get_config_item_dict(config_item_type)

            self.LOGGER.info(f"Successfully obtained all config values for {config_item_type} from config manager api")
            matched_configs = []
            if isinstance(dependencies, bool) and dependencies:
                matched_configs = [item.get("name") for item in config_item_list]

            elif isinstance(dependencies, dict):
                config_item_keys = [config_item.get("config_value") for config_item in config_item_list]
                extracted_config_items = zip(config_item_list, map(lambda x, deps=dependencies: self.extract_keys_from_config(x, deps), config_item_keys))
                matched_configs = [item[0].get("name") for item in extracted_config_items if
                                   getattr(_ConfigMatcher(config_matches=item[1], values_to_check=lineage, config_name=item[0].get("name")),
                                           config_item_type)()]
            self.LOGGER.info(f"{config_item_type} got matches for {','.join(matched_configs)} in lineage")
            lineage[config_item_type] = list(set(matched_configs))

        if lineage.get("relationship_models", []):
            lineage["entity_rel_enrich"] = list(set(item.get("name") for item in self.get_config_item_dict("entity_rel_enrich")))

        if lineage.get("inventory_models", []):
            global_configs = self.get_config_item_dict("global_entity_config")
            entities_present = [item.split("__")[1] for item in lineage.get("inventory_models", [])]
            global_entities = [item.get("name") for item in global_configs if
                               item.get("name").split("_entity_config")[0] in entities_present]
            lineage["global_entity_config"] = list(set(global_entities))

        publisher_models = [item.get("name") for item in self.get_config_item_dict("publisher")]
        if not lineage.get("intersource_disambiguated_models", []):
            publisher_models = []
        elif not lineage.get("relationship_models", []):
            publisher_models = [item for item in publisher_models if "relationship" not in publisher_models]

        lineage["publisher"] = list(set(publisher_models))

        return lineage
