{"inventory_models": {"data_source_name": "dataSource.name", "data_feed_name": "dataSource.feedName"}, "intrasource_disambiguated_models": {"inventory_models": ["inventoryModelInput.name", "inventoryModelInput.path"]}, "intersource_disambiguated_models": {"inventory_models": ["inventoryModelInput.name", "inventoryModelInput.path"], "intrasource_disambiguated_models": ["inventoryModelInput.name", "inventoryModelInput.path"]}, "relationship_models": {"inventory_models": ["inputSourceInfo.sourceLoaderConfPath", "inputSourceInfo.targetLoaderConfPath"]}, "relationship_disambiguation": {"relationship_models": ["relationshipModels.name", "relationshipModels.tableName"]}}