from config_merger_api.utils.common_utils import merge_config



def merge_entity_rel_enrich(solution_configs, client_configs):
    sol_entity_rel_enrich = [i.get("config_value", {}) for i in solution_configs if
                              i['config_item_type'] == 'entity_rel_enrich']
    client_entity_rel_enrich = [i.get("config_value", {}) for i in client_configs if
                                 i['config_item_type'] == 'entity_rel_enrich']
    if(sol_entity_rel_enrich == []):
        merged_configs = client_entity_rel_enrich
    else:
        merged_configs=merge_config(sol_entity_rel_enrich, client_entity_rel_enrich)
    entity_rel_merged_configs=merged_configs[0] if merged_configs != [] else {}
    return entity_rel_merged_configs

